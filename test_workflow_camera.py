#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试工作流编辑器相机功能的脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
from wirevsion.ui.camera_utils import CameraManager
from loguru import logger

def test_workflow_camera():
    """测试工作流编辑器相机功能"""
    print("测试工作流编辑器相机功能...")
    
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 测试相机管理器单例
        manager1 = CameraManager()
        manager2 = CameraManager.get_instance()
        
        print(f"相机管理器单例测试: {manager1 is manager2}")
        
        # 测试相机初始化
        success = manager1.init_camera()
        print(f"相机初始化: {'成功' if success else '失败'}")
        
        if success:
            # 测试获取帧
            success, frame = manager1.get_frame()
            print(f"获取相机帧: {'成功' if success and frame is not None else '失败'}")
            
            if success and frame is not None:
                print(f"相机帧尺寸: {frame.shape}")
        
        # 创建工作流编辑器
        print("创建工作流编辑器...")
        editor = ModernWorkflowEditor()
        
        # 检查相机管理器是否正确初始化
        print(f"工作流编辑器相机管理器: {editor.camera_manager is not None}")
        print(f"相机管理器单例一致性: {editor.camera_manager is manager1}")
        
        # 检查实时显示定时器
        has_timer = hasattr(editor, '_realtime_timer')
        print(f"实时显示定时器: {'已创建' if has_timer else '未创建'}")
        
        if has_timer:
            is_active = editor._realtime_timer.isActive()
            print(f"定时器状态: {'运行中' if is_active else '已停止'}")
        
        print("✓ 工作流编辑器相机功能测试完成")
        
        # 清理
        editor.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始测试工作流编辑器相机功能...")
    print("=" * 50)
    
    success = test_workflow_camera()
    
    print("=" * 50)
    if success:
        print("✓ 所有测试通过！工作流编辑器相机功能正常。")
    else:
        print("✗ 测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
