#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
基础应用程序测试

测试 PyQt6 基本功能是否正常
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
    from PyQt6.QtCore import Qt, QTimer
    from PyQt6.QtGui import QColor, QFont
    
    print("✅ PyQt6 基础模块导入成功")
    
    class TestWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("PyQt6 测试窗口")
            self.setGeometry(100, 100, 400, 300)
            
            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # 创建布局
            layout = QVBoxLayout(central_widget)
            
            # 添加标签
            label = QLabel("🎉 PyQt6 运行正常！")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setFont(QFont("Arial", 16))
            label.setStyleSheet("color: green; padding: 20px;")
            layout.addWidget(label)
            
            # 测试颜色
            color_label = QLabel("颜色测试")
            color_label.setStyleSheet(f"color: {QColor(255, 0, 0).name()}; font-size: 14px;")
            layout.addWidget(color_label)
            
            print("✅ 测试窗口创建成功")
    
    def main():
        print("🚀 启动 PyQt6 基础测试...")
        
        app = QApplication(sys.argv)
        
        window = TestWindow()
        window.show()
        
        print("✅ 应用程序启动成功")
        print("📝 如果看到窗口，说明 PyQt6 工作正常")
        
        # 5秒后自动关闭
        QTimer.singleShot(5000, app.quit)
        
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(main())
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
