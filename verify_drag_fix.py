#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证节点拖动修复
测试修复后的拖动功能是否正常工作
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import QPointF, Qt
from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
from loguru import logger

class VerifyDragFixWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("节点拖动修复验证")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 添加指导标签
        info_label = QLabel("""
        <h2>节点拖动修复验证</h2>
        <p><b>问题描述：</b>之前点击节点时，节点会跳动到鼠标位置</p>
        <p><b>修复方法：</b></p>
        <ol>
            <li>禁用 ItemSendsScenePositionChanges 标志</li>
            <li>记录鼠标点击位置和节点相对位置</li>
            <li>在移动时保持这个相对位置</li>
            <li>在释放鼠标时恢复标志</li>
        </ol>
        <p><b>测试步骤：</b></p>
        <ol>
            <li>点击节点的<b>边缘位置</b>(而不是中心)</li>
            <li>拖动节点</li>
            <li>观察节点是否跳动到鼠标位置</li>
            <li>验证节点是否平滑移动并保持与鼠标的相对位置</li>
        </ol>
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                border: 1px solid #ddd;
            }
        """)
        main_layout.addWidget(info_label)
        
        # 添加按钮布局
        button_layout = QHBoxLayout()
        
        # 添加重置按钮
        self.reset_button = QPushButton("重置节点位置")
        self.reset_button.setMinimumHeight(40)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
        """)
        self.reset_button.clicked.connect(self.reset_nodes)
        button_layout.addWidget(self.reset_button)
        
        # 添加打印日志按钮
        self.log_button = QPushButton("打印测试结果")
        self.log_button.setMinimumHeight(40)
        self.log_button.setStyleSheet("""
            QPushButton {
                background-color: #34a853;
                color: white;
                border-radius: 5px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #2d9748;
            }
        """)
        self.log_button.clicked.connect(self.print_test_log)
        button_layout.addWidget(self.log_button)
        
        main_layout.addLayout(button_layout)
        
        # 创建工作流编辑器
        self.workflow_editor = ModernWorkflowEditor()
        main_layout.addWidget(self.workflow_editor, 1)
        
        # 初始节点位置
        self.default_positions = [
            ("node1", "input", "测试节点 1", QPointF(-250, -100)),
            ("node2", "process", "测试节点 2", QPointF(0, -100)),
            ("node3", "output", "测试节点 3", QPointF(250, -100)),
            ("node4", "input", "测试节点 4", QPointF(-250, 100)),
            ("node5", "process", "测试节点 5", QPointF(0, 100)),
            ("node6", "output", "测试节点 6", QPointF(250, 100)),
        ]
        
        # 添加测试节点
        self.add_test_nodes()
        
        logger.info("拖动修复验证窗口已初始化")
    
    def add_test_nodes(self):
        """添加测试节点"""
        canvas = self.workflow_editor.canvas
        
        # 清除现有节点
        for node_id in list(canvas.nodes.keys()):
            canvas.remove_node(node_id)
        
        # 添加节点
        for node_id, node_type, title, pos in self.default_positions:
            canvas.add_node(node_id, node_type, title, pos)
            
        # 添加连接
        canvas.create_connection("node1", "node2", "right", "left")
        canvas.create_connection("node2", "node3", "right", "left")
        canvas.create_connection("node4", "node5", "right", "left")
        canvas.create_connection("node5", "node6", "right", "left")
        
        logger.info("测试节点已添加")
    
    def reset_nodes(self):
        """重置节点位置"""
        self.add_test_nodes()
        logger.info("节点位置已重置")
    
    def print_test_log(self):
        """打印测试日志"""
        print("\n" + "=" * 50)
        print("验证节点拖动修复测试结果")
        print("=" * 50)
        
        # 获取当前节点位置
        canvas = self.workflow_editor.canvas
        current_positions = {node_id: node.pos() for node_id, node in canvas.nodes.items()}
        
        print("\n当前节点位置:")
        for node_id, pos in current_positions.items():
            print(f"  • {node_id}: ({pos.x():.1f}, {pos.y():.1f})")
        
        print("\n请回答以下问题:")
        print("1. 点击节点边缘时，节点是否跳动到鼠标位置? [是/否]")
        print("2. 拖动节点时，节点是否保持与鼠标的相对位置? [是/否]")
        print("3. 拖动节点时，连接线是否正确更新? [是/否]")
        print("4. 释放鼠标后，节点位置是否正确? [是/否]")
        print("=" * 50)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示窗口
    window = VerifyDragFixWindow()
    window.show()
    
    # 控制台打印测试说明
    print("\n=== 节点拖动修复验证 ===")
    print("要测试的问题: 节点在点击时会跳动到鼠标位置")
    print("请尝试以下操作:")
    print("1. 点击节点的边缘位置(非中心)")
    print("2. 拖动节点并观察行为")
    print("3. 完成测试后点击'打印测试结果'按钮")
    print("========================\n")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 