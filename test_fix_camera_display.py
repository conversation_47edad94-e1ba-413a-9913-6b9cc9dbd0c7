#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复相机显示和节点点击功能
简化的程序，专注于测试核心功能而非完整应用
"""

import sys
import time
import cv2
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtCore import Qt, QTimer
from loguru import logger

# 配置日志
logger.remove()  # 移除默认处理程序
logger.add(sys.stderr, level="INFO")  # 添加标准错误输出处理程序

class SimpleTestApp(QMainWindow):
    """简单的测试应用程序"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("相机显示测试")
        self.resize(800, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 图像显示标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(640, 480)
        self.image_label.setStyleSheet("background-color: #333;")
        layout.addWidget(self.image_label)
        
        # 按钮区域
        button_layout = QVBoxLayout()
        
        # 相机按钮
        self.camera_button = QPushButton("启动相机")
        self.camera_button.clicked.connect(self.toggle_camera)
        button_layout.addWidget(self.camera_button)
        
        # 高斯模糊按钮
        self.blur_button = QPushButton("高斯模糊")
        self.blur_button.clicked.connect(self.apply_gaussian_blur)
        button_layout.addWidget(self.blur_button)
        
        # 边缘检测按钮
        self.edge_button = QPushButton("边缘检测")
        self.edge_button.clicked.connect(self.apply_edge_detection)
        button_layout.addWidget(self.edge_button)
        
        # 原始图像按钮
        self.original_button = QPushButton("原始图像")
        self.original_button.clicked.connect(self.show_original)
        button_layout.addWidget(self.original_button)
        
        layout.addLayout(button_layout)
        
        # 相机变量
        self.camera = None
        self.camera_active = False
        self.camera_timer = None
        self.current_frame = None
        self.original_frame = None
        
        logger.info("测试应用初始化完成")
    
    def toggle_camera(self):
        """切换相机状态"""
        if not self.camera_active:
            # 启动相机
            self.camera = cv2.VideoCapture(0)
            if not self.camera.isOpened():
                logger.error("无法打开相机")
                return
            
            # 设置相机参数
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            
            # 启动定时器
            self.camera_timer = QTimer(self)
            self.camera_timer.timeout.connect(self.update_frame)
            self.camera_timer.start(30)  # 30ms，约33fps
            
            self.camera_active = True
            self.camera_button.setText("停止相机")
            logger.info("相机已启动")
        else:
            # 停止相机
            if self.camera_timer:
                self.camera_timer.stop()
                self.camera_timer = None
            
            if self.camera:
                self.camera.release()
                self.camera = None
            
            self.camera_active = False
            self.camera_button.setText("启动相机")
            logger.info("相机已停止")
    
    def update_frame(self):
        """更新相机帧"""
        if not self.camera or not self.camera.isOpened():
            return
        
        ret, frame = self.camera.read()
        if not ret:
            logger.warning("无法读取相机帧")
            return
        
        # 保存原始帧
        self.original_frame = frame.copy()
        self.current_frame = frame.copy()
        
        # 显示图像
        self.display_image(frame)
    
    def display_image(self, image):
        """显示图像"""
        # 确保图像为RGB格式
        if image is None:
            return
            
        # 转换BGR到RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建QImage
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
        
        # 创建QPixmap并显示
        pixmap = QPixmap.fromImage(q_image)
        self.image_label.setPixmap(pixmap)
    
    def apply_gaussian_blur(self):
        """应用高斯模糊"""
        if self.current_frame is None:
            logger.warning("没有可用的图像")
            return
            
        # 应用高斯模糊
        blurred = cv2.GaussianBlur(self.original_frame, (15, 15), 0)
        self.current_frame = blurred
        
        # 显示处理后的图像
        self.display_image(blurred)
        logger.info("已应用高斯模糊")
    
    def apply_edge_detection(self):
        """应用边缘检测"""
        if self.current_frame is None:
            logger.warning("没有可用的图像")
            return
            
        # 应用边缘检测
        gray = cv2.cvtColor(self.original_frame, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 100, 200)
        
        # 转换回BGR以便显示
        edges_bgr = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
        self.current_frame = edges_bgr
        
        # 显示处理后的图像
        self.display_image(edges_bgr)
        logger.info("已应用边缘检测")
    
    def show_original(self):
        """显示原始图像"""
        if self.original_frame is None:
            logger.warning("没有可用的原始图像")
            return
            
        self.current_frame = self.original_frame.copy()
        self.display_image(self.current_frame)
        logger.info("已显示原始图像")
    
    def closeEvent(self, event):
        """关闭窗口事件"""
        # 停止相机
        if self.camera_active:
            self.toggle_camera()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = SimpleTestApp()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 