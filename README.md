# WireVision

基于 PyQt6 + OpenCV + YOLO 的机器视觉检测系统

## 功能特性

- 🎯 **多算法支持**: 集成40+种视觉检测算法
- 🖥️ **现代化UI**: 基于PyQt6的现代化用户界面
- 📷 **相机支持**: 支持多种相机设备和图像源
- 🔧 **可视化配置**: 拖拽式工作流编辑器
- 🚀 **高性能**: 优化的图像处理和算法执行
- 🎨 **主题系统**: 统一的UI主题和样式管理

## 系统要求

- Python 3.10+
- PyQt6 6.4.2+
- OpenCV 4.11+
- 支持的操作系统: Windows, macOS, Linux

## 安装

### 使用 Poetry (推荐)

```bash
# 克隆项目
git clone <repository-url>
cd WireVision

# 安装依赖
poetry install

# 激活虚拟环境
poetry shell
```

### 使用 pip

```bash
pip install -r requirements.txt
```

## 快速开始

```bash
# 运行应用程序
python run_app.py

# 或使用 Poetry
poetry run python run_app.py
```

## 项目结构

```
WireVision/
├── wirevsion/              # 主要源代码
│   ├── ui/                 # 用户界面模块
│   ├── core/               # 核心功能模块
│   ├── algorithms/         # 算法实现
│   ├── camera/             # 相机管理
│   ├── config/             # 配置管理
│   └── utils/              # 工具函数
├── resources/              # 资源文件
├── tests/                  # 测试文件
└── docs/                   # 文档
```

## 主要算法模块

### 基础图像处理
- 颜色检测 (Color Detection)
- 阈值处理 (Threshold)
- 形态学操作 (Morphology)
- 边缘检测 (Edge Detection)
- 高斯模糊 (Gaussian Blur)

### 几何检测
- 形状检测 (Shape Detection)
- 线条检测 (Line Detection)
- 圆形检测 (Circle Detection)
- 轮廓检测 (Contour Detection)

### 测量算法
- 距离测量 (Distance Measurement)
- 面积测量 (Area Measurement)
- 角度测量 (Angle Measurement)

### 深度学习
- YOLO 目标检测
- 模板匹配
- 特征匹配

## 开发

### 环境设置

```bash
# 安装开发依赖
poetry install --with dev

# 运行测试
poetry run pytest

# 代码格式化
poetry run black .

# 类型检查
poetry run mypy wirevsion/
```

### 添加新算法

1. 在 `wirevsion/algorithms/` 目录下创建新的算法模块
2. 继承 `BaseAlgorithm` 类
3. 实现必要的方法
4. 在算法管理器中注册

## 配置

应用程序配置文件位于 `config/` 目录下：

- `app_config.yaml` - 应用程序主配置
- `camera_config.yaml` - 相机配置
- `algorithm_config.yaml` - 算法配置

## 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 作者: 张玉龙
- 邮箱: <EMAIL>

## 更新日志

### v0.1.0 (2025-01-30)
- 初始版本发布
- 基础UI框架完成
- 核心算法模块实现
- 相机管理功能
- 工作流编辑器

## 致谢

感谢以下开源项目的支持：
- PyQt6
- OpenCV
- YOLO
- NumPy
- Pillow
