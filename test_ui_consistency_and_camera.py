#!/usr/bin/env python3
"""
WireVision UI一致性和相机预览测试脚本
测试所有算法配置界面的UI一致性和相机预览功能
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from loguru import logger

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog
    from wirevsion.ui.modern_components import THEME_COLORS
    from wirevsion.ui.theme_manager import theme_manager
    from wirevsion.algorithms.registry import AlgorithmRegistry
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    sys.exit(1)


class UIConsistencyTestWindow(QMainWindow):
    """UI一致性和相机预览测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("WireVision UI一致性和相机预览测试")
        self.setGeometry(100, 100, 1200, 800)

        # 设置主题样式
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {THEME_COLORS["dark_bg_app"]};
                color: {THEME_COLORS["text_primary"]};
            }}
        """)

        self.setup_ui()
        self.test_results = {}

    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title = QLabel("WireVision UI一致性和相机预览测试")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet(f"color: {THEME_COLORS['text_title']}; margin-bottom: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 测试按钮区域
        button_layout = QHBoxLayout()

        # UI一致性测试按钮
        self.ui_test_btn = QPushButton("🎨 测试UI一致性")
        self.ui_test_btn.setStyleSheet(theme_manager.get_button_style("primary"))
        self.ui_test_btn.clicked.connect(self.test_ui_consistency)
        button_layout.addWidget(self.ui_test_btn)

        # 相机预览测试按钮
        self.camera_test_btn = QPushButton("📷 测试相机预览")
        self.camera_test_btn.setStyleSheet(theme_manager.get_button_style("info"))
        self.camera_test_btn.clicked.connect(self.test_camera_preview)
        button_layout.addWidget(self.camera_test_btn)

        # 综合测试按钮
        self.full_test_btn = QPushButton("🔍 综合测试")
        self.full_test_btn.setStyleSheet(theme_manager.get_button_style("success"))
        self.full_test_btn.clicked.connect(self.run_full_test)
        button_layout.addWidget(self.full_test_btn)

        layout.addLayout(button_layout)

        # 结果显示区域
        self.result_label = QLabel("点击按钮开始测试...")
        self.result_label.setStyleSheet(f"""
            QLabel {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 20px;
                font-size: 14px;
                font-family: 'Consolas', 'Monaco', monospace;
            }}
        """)
        self.result_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.result_label.setWordWrap(True)
        self.result_label.setMinimumHeight(400)
        layout.addWidget(self.result_label)

    def test_ui_consistency(self):
        """测试UI一致性"""
        self.result_label.setText("🎨 正在测试UI一致性...\n")
        QApplication.processEvents()

        try:
            # 测试算法列表
            algorithms_to_test = [
                ("相机源", "camera_source"),
                ("颜色检测", "color_detection"),
                ("边缘检测", "edge_detection"),
                ("模板匹配", "template_matching"),
                ("文本检测", "text_detection"),
                ("形状检测", "shape_detection"),
                ("人脸检测", "face_detection"),
                ("高斯模糊", "gaussian_blur")
            ]

            results = []
            results.append("=" * 50)
            results.append("UI一致性测试结果")
            results.append("=" * 50)

            for display_name, algorithm_name in algorithms_to_test:
                try:
                    # 创建配置对话框
                    dialog = ModernNodeConfigDialog(
                        algorithm_name=algorithm_name,
                        config_data={},
                        parent=self
                    )

                    # 检查UI组件
                    ui_check = self._check_ui_components(dialog)

                    if ui_check["passed"]:
                        results.append(f"✅ {display_name}: UI一致性通过")
                        results.append(f"   - 主题色系: ✅")
                        results.append(f"   - 按钮样式: ✅")
                        results.append(f"   - 输入控件: ✅")
                        results.append(f"   - 布局间距: ✅")
                    else:
                        results.append(f"❌ {display_name}: UI一致性失败")
                        for issue in ui_check["issues"]:
                            results.append(f"   - {issue}")

                    dialog.close()

                except Exception as e:
                    results.append(f"❌ {display_name}: 测试失败 - {str(e)}")

                QApplication.processEvents()
                time.sleep(0.1)

            results.append("")
            results.append("UI一致性测试完成!")

            self.result_label.setText("\n".join(results))

        except Exception as e:
            self.result_label.setText(f"UI一致性测试失败: {str(e)}")
            logger.error(f"UI一致性测试失败: {e}")

    def test_camera_preview(self):
        """测试相机预览功能"""
        self.result_label.setText("📷 正在测试相机预览功能...\n")
        QApplication.processEvents()

        try:
            results = []
            results.append("=" * 50)
            results.append("相机预览测试结果")
            results.append("=" * 50)

            # 测试相机源算法
            dialog = ModernNodeConfigDialog(
                algorithm_name="camera_source",
                config_data={},
                parent=self
            )

            # 显示对话框
            dialog.show()
            QApplication.processEvents()

            # 测试相机预览功能
            camera_params = {
                "camera_index": 0,
                "width": 640,
                "height": 480,
                "fps": 30
            }

            results.append("🔍 测试相机设备检测...")

            # 测试相机打开
            try:
                import cv2
                cap = cv2.VideoCapture(0)
                if cap.isOpened():
                    results.append("✅ 相机设备可用")

                    # 测试图像捕获
                    ret, frame = cap.read()
                    if ret and frame is not None:
                        results.append("✅ 图像捕获成功")
                        results.append(f"   - 图像尺寸: {frame.shape}")
                        results.append(f"   - 图像类型: {frame.dtype}")

                        # 测试预览功能
                        dialog._preview_camera(camera_params)
                        results.append("✅ 预览功能调用成功")

                        # 测试实时预览
                        dialog._start_live_camera_preview(camera_params)
                        results.append("✅ 实时预览启动成功")

                        # 等待几秒钟观察预览
                        QTimer.singleShot(3000, lambda: dialog._stop_camera_preview())

                    else:
                        results.append("❌ 图像捕获失败")

                    cap.release()
                else:
                    results.append("❌ 相机设备不可用")
                    results.append("   - 请检查相机连接")
                    results.append("   - 请检查相机权限")

            except Exception as e:
                results.append(f"❌ 相机测试失败: {str(e)}")

            results.append("")
            results.append("相机预览测试完成!")

            self.result_label.setText("\n".join(results))

            # 保持对话框打开以便观察
            QTimer.singleShot(5000, dialog.close)

        except Exception as e:
            self.result_label.setText(f"相机预览测试失败: {str(e)}")
            logger.error(f"相机预览测试失败: {e}")

    def run_full_test(self):
        """运行完整测试"""
        self.result_label.setText("🔍 正在运行综合测试...\n")
        QApplication.processEvents()

        # 先运行UI一致性测试
        self.test_ui_consistency()

        # 等待一秒后运行相机测试
        QTimer.singleShot(1000, self.test_camera_preview)

    def _check_ui_components(self, dialog):
        """检查UI组件一致性"""
        issues = []

        try:
            # 检查主题色系使用
            if not hasattr(dialog, 'roi_view'):
                issues.append("缺少ROI视图组件")

            # 检查按钮样式
            buttons = dialog.findChildren(QPushButton)
            for button in buttons:
                style = button.styleSheet()
                if not style or THEME_COLORS["primary"] not in style:
                    issues.append(f"按钮 '{button.text()}' 未使用主题样式")

            # 检查布局
            if not dialog.layout():
                issues.append("缺少主布局")

            return {
                "passed": len(issues) == 0,
                "issues": issues
            }

        except Exception as e:
            return {
                "passed": False,
                "issues": [f"检查失败: {str(e)}"]
            }


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyleSheet(theme_manager.get_global_stylesheet())

    # 创建测试窗口
    window = UIConsistencyTestWindow()
    window.show()

    logger.info("UI一致性和相机预览测试程序已启动")

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
