#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的图像刷新测试脚本

用于测试图像刷新功能，不依赖于复杂的工作流系统
"""

import sys
import os
import time
import numpy as np
import cv2
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QScrollArea
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtCore import Qt, QTimer
from loguru import logger

# 设置日志
logger.add("refresh_test_{time}.log", rotation="100 MB")

class SimpleRefreshTest(QMainWindow):
    """简单的图像刷新测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图像刷新测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        
        # 创建按钮
        self.update_btn = QPushButton("更新图像")
        self.update_btn.clicked.connect(self.update_image)
        control_layout.addWidget(self.update_btn)
        
        self.start_btn = QPushButton("开始连续更新")
        self.start_btn.clicked.connect(self.toggle_continuous_update)
        control_layout.addWidget(self.start_btn)
        
        self.force_refresh_btn = QPushButton("强制刷新")
        self.force_refresh_btn.clicked.connect(self.force_refresh)
        control_layout.addWidget(self.force_refresh_btn)
        
        main_layout.addWidget(control_panel)
        
        # 图像滚动区域
        self.image_scroll_area = QScrollArea()
        self.image_scroll_area.setWidgetResizable(True)
        
        # 图像容器
        self.image_container = QWidget()
        image_container_layout = QVBoxLayout(self.image_container)
        
        # 图像标签
        self.image_view = QLabel()
        self.image_view.setAlignment(Qt.AlignCenter)
        self.image_view.setText("等待图像更新...")
        self.image_view.setMinimumSize(400, 300)
        image_container_layout.addWidget(self.image_view)
        
        self.image_scroll_area.setWidget(self.image_container)
        main_layout.addWidget(self.image_scroll_area, 1)  # 1表示拉伸因子
        
        # 状态栏
        self.statusBar().showMessage("准备就绪")
        
        # 计时器
        self.timer = None
        self.continuous_mode = False
        self.update_count = 0
        
        # 创建初始测试图像
        self.create_test_image()
    
    def create_test_image(self, random_colors=True):
        """创建测试图像
        
        Args:
            random_colors: 是否使用随机颜色
        """
        try:
            # 创建彩色图像
            test_image = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 使用随机颜色或渐变填充
            if random_colors:
                # 随机颜色
                for i in range(10):
                    x1 = np.random.randint(0, 540)
                    y1 = np.random.randint(0, 380)
                    x2 = x1 + np.random.randint(50, 200)
                    y2 = y1 + np.random.randint(50, 200)
                    b = np.random.randint(0, 255)
                    g = np.random.randint(0, 255)
                    r = np.random.randint(0, 255)
                    cv2.rectangle(test_image, (x1, y1), (x2, y2), (b, g, r), -1)
            else:
                # 渐变填充
                for i in range(test_image.shape[0]):
                    for j in range(test_image.shape[1]):
                        # 创建彩色渐变
                        b = int(255 * i / test_image.shape[0])
                        g = int(255 * j / test_image.shape[1])
                        r = int(255 * (i + j) / (test_image.shape[0] + test_image.shape[1]))
                        test_image[i, j] = [b, g, r]
            
            # 添加时间戳
            current_time = time.strftime("%H:%M:%S.%f")[:-3]
            self.update_count += 1
            cv2.putText(
                test_image,
                f"更新 #{self.update_count} - {current_time}",
                (50, 50),
                cv2.FONT_HERSHEY_SIMPLEX,
                1,
                (255, 255, 255),
                2
            )
            
            # 添加边框
            cv2.rectangle(test_image, (20, 20), (620, 460), (0, 255, 0), 2)
            
            # 转换为RGB
            self.test_image_rgb = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
            
            # 更新图像显示
            self.display_image(self.test_image_rgb)
            
            logger.info(f"创建测试图像: 大小={test_image.shape}, 更新计数={self.update_count}")
            
        except Exception as e:
            logger.error(f"创建测试图像失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def display_image(self, image):
        """显示图像
        
        Args:
            image: numpy数组，RGB格式
        """
        try:
            if image is None:
                logger.error("尝试显示空图像")
                return
            
            # 创建QImage
            height, width, channels = image.shape
            bytes_per_line = channels * width
            q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            
            # 检查QImage是否有效
            if q_image.isNull():
                logger.error("创建的QImage为空")
                return
            
            # 转换为QPixmap
            pixmap = QPixmap.fromImage(q_image)
            
            if pixmap.isNull():
                logger.error("创建的QPixmap为空")
                return
            
            # 记录原始大小
            logger.info(f"原始图像大小: {pixmap.width()}x{pixmap.height()}")
            
            # 设置图像
            self.image_view.setPixmap(pixmap)
            self.image_view.repaint()
            
            # 确保事件处理
            QApplication.processEvents()
            
            # 更新状态栏
            self.statusBar().showMessage(f"图像已更新: {time.strftime('%H:%M:%S')}, 大小={width}x{height}")
            
            logger.info(f"图像已设置到显示组件, 显示组件大小: {self.image_view.width()}x{self.image_view.height()}")
            
        except Exception as e:
            logger.error(f"显示图像失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def update_image(self):
        """更新图像"""
        try:
            logger.info("手动更新图像")
            self.create_test_image(random_colors=True)
            self.force_refresh()
        except Exception as e:
            logger.error(f"更新图像失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def force_refresh(self):
        """强制刷新UI"""
        try:
            logger.info("强制刷新UI")
            
            # 刷新图像视图
            if self.image_view:
                self.image_view.update()
                self.image_view.repaint()
            
            # 刷新图像容器
            if self.image_container:
                self.image_container.update()
                self.image_container.repaint()
            
            # 刷新滚动区域
            if self.image_scroll_area:
                self.image_scroll_area.update()
                self.image_scroll_area.repaint()
            
            # 处理事件队列
            QApplication.processEvents()
            
            # 更新状态栏
            self.statusBar().showMessage(f"UI已强制刷新: {time.strftime('%H:%M:%S')}")
            
            logger.info("UI刷新完成")
        except Exception as e:
            logger.error(f"强制刷新UI失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def toggle_continuous_update(self):
        """切换连续更新模式"""
        try:
            if self.continuous_mode:
                # 停止连续更新
                if self.timer:
                    self.timer.stop()
                    self.timer = None
                
                self.continuous_mode = False
                self.start_btn.setText("开始连续更新")
                logger.info("停止连续更新")
                self.statusBar().showMessage("连续更新已停止")
            else:
                # 开始连续更新
                self.continuous_mode = True
                self.start_btn.setText("停止连续更新")
                
                # 启动定时器，每秒更新一次
                self.timer = QTimer(self)
                self.timer.timeout.connect(self.update_image)
                self.timer.start(1000)  # 1000毫秒 = 1秒
                
                logger.info("开始连续更新，频率=1秒/次")
                self.statusBar().showMessage("连续更新已开始，频率=1秒/次")
        except Exception as e:
            logger.error(f"切换连续更新模式失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = SimpleRefreshTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 