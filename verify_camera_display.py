#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机显示验证工具

用于验证相机显示是否正常工作，以及检测可能的问题
"""

import sys
import os
import time
import cv2
import numpy as np
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QComboBox, QProgressBar, QCheckBox,
    QGroupBox, QTabWidget, QMessageBox, QSpinBox, QSplitter, QTextEdit
)
from PyQt6.QtCore import Qt, QTimer, QSize, QThread, pyqtSignal, QMutex
from PyQt6.QtGui import QPixmap, QImage, QFont, QIcon, QTextCursor
from loguru import logger
import PyQt6.QtCore


# 配置日志
log_file = f"camera_verify_{time.strftime('%Y%m%d_%H%M%S')}.log"
logger.add(log_file, rotation="10 MB")
logger.info("相机显示验证工具启动")


class CameraWorker(QThread):
    """相机工作线程，用于在后台捕获相机帧"""
    
    # 信号
    frame_ready = pyqtSignal(object)
    error = pyqtSignal(str)
    stats_updated = pyqtSignal(dict)
    
    def __init__(self, camera_id=0):
        """初始化相机工作线程
        
        Args:
            camera_id: 相机ID
        """
        super().__init__()
        self.camera_id = camera_id
        self.camera = None
        self.running = False
        self.mutex = QMutex()
        self.frame_count = 0
        self.start_time = 0
        self.fps = 0
        self.last_fps_update = 0
        self.frame_dimensions = (0, 0)
        
    def run(self):
        """线程主函数"""
        try:
            # 打开相机
            self.camera = cv2.VideoCapture(self.camera_id)
            
            if not self.camera.isOpened():
                self.error.emit(f"无法打开相机 ID: {self.camera_id}")
                return
                
            # 设置相机参数
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.camera.set(cv2.CAP_PROP_FPS, 30)
            
            # 丢弃前几帧，确保相机稳定
            for _ in range(5):
                self.camera.read()
                time.sleep(0.03)
            
            # 获取实际设置的参数
            width = int(self.camera.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.frame_dimensions = (width, height)
            
            # 设置运行标志和计时器
            self.running = True
            self.frame_count = 0
            self.start_time = time.time()
            self.last_fps_update = self.start_time
            
            # 主循环
            while self.running:
                # 读取一帧
                ret, frame = self.camera.read()
                
                if not ret or frame is None or frame.size == 0:
                    self.error.emit("读取相机帧失败")
                    time.sleep(0.5)  # 短暂延迟避免频繁报错
                    continue
                
                # 更新帧计数和FPS
                self.frame_count += 1
                current_time = time.time()
                elapsed = current_time - self.start_time
                
                # 每秒更新一次FPS
                if current_time - self.last_fps_update >= 1.0:
                    self.fps = self.frame_count / elapsed
                    self.last_fps_update = current_time
                    
                    # 发送统计信息
                    stats = {
                        'fps': self.fps,
                        'frame_count': self.frame_count,
                        'elapsed': elapsed,
                        'width': self.frame_dimensions[0],
                        'height': self.frame_dimensions[1]
                    }
                    self.stats_updated.emit(stats)
                
                # 添加文本信息到帧
                cv2.putText(
                    frame, 
                    f"Camera #{self.camera_id} - FPS: {self.fps:.1f}", 
                    (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 
                    0.7, 
                    (0, 255, 0), 
                    2
                )
                
                # 添加时间戳
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                cv2.putText(
                    frame, 
                    timestamp, 
                    (10, height - 10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 
                    0.5, 
                    (200, 200, 200), 
                    1
                )
                
                # 发送帧
                self.frame_ready.emit(frame.copy())
                
                # 短暂休眠避免占用过多CPU
                self.msleep(5)
                
            except Exception as e:
            error_msg = f"相机线程出错: {str(e)}"
            logger.error(error_msg)
            self.error.emit(error_msg)
            
        finally:
            # 清理资源
            if self.camera:
                self.camera.release()
            self.running = False
            
    def stop(self):
        """停止线程"""
        self.running = False
        self.wait(1000)  # 等待线程结束
        
        # 释放相机
        if self.camera:
            self.camera.release()
            self.camera = None


class VerifyCameraDisplay(QMainWindow):
    """相机显示验证工具主窗口"""
    
    def __init__(self):
        """初始化窗口"""
        super().__init__()
        self.setWindowTitle("WireVsion 相机显示验证工具")
        self.resize(1000, 700)
        
        # 相机工作线程
        self.camera_worker = None
        
        # 测试结果
        self.test_results = {
            'camera_open': False,
            'frame_received': False,
            'display_working': False,
            'fps_acceptable': False,
            'artifacts': False,
            'delay': 0
        }
        
        # 设置UI
        self._setup_ui()
        
        # 设置定时器
        self.fps_timer = QTimer(self)
        self.fps_timer.timeout.connect(self._update_fps_display)
        self.fps_timer.start(1000)  # 每秒更新一次
        
        logger.info("相机显示验证工具初始化完成")
        
    def _setup_ui(self):
        """设置UI界面"""
        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧控制面板
        control_panel = QWidget()
        control_layout = QVBoxLayout(control_panel)
        
        # 相机控制组
        camera_group = QGroupBox("相机控制")
        camera_layout = QVBoxLayout(camera_group)
        
        # 相机选择
        camera_select_layout = QHBoxLayout()
        camera_select_layout.addWidget(QLabel("相机:"))
        self.camera_combo = QComboBox()
        self.camera_combo.addItem("相机 0", 0)
        self.camera_combo.addItem("相机 1", 1)
        self.camera_combo.addItem("相机 2", 2)
        self.camera_combo.addItem("相机 3", 3)
        camera_select_layout.addWidget(self.camera_combo)
        camera_layout.addLayout(camera_select_layout)
        
        # 开始/停止按钮
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始测试")
        self.start_btn.clicked.connect(self.start_camera)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止测试")
        self.stop_btn.clicked.connect(self.stop_camera)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        camera_layout.addLayout(button_layout)
        
        # 拍照按钮
        self.capture_btn = QPushButton("拍照")
        self.capture_btn.clicked.connect(self.capture_image)
        self.capture_btn.setEnabled(False)
        camera_layout.addWidget(self.capture_btn)
        
        # FPS显示
        fps_layout = QHBoxLayout()
        fps_layout.addWidget(QLabel("FPS:"))
        self.fps_label = QLabel("0.0")
        self.fps_label.setMinimumWidth(60)
        fps_layout.addWidget(self.fps_label)
        
        self.fps_progress = QProgressBar()
        self.fps_progress.setRange(0, 30)
        self.fps_progress.setValue(0)
        fps_layout.addWidget(self.fps_progress)
        camera_layout.addLayout(fps_layout)
        
        # 添加相机控制组到控制面板
        control_layout.addWidget(camera_group)
        
        # 测试选项组
        test_group = QGroupBox("测试选项")
        test_layout = QVBoxLayout(test_group)
        
        # 测试选项
        self.test_camera_cb = QCheckBox("测试相机初始化")
        self.test_camera_cb.setChecked(True)
        test_layout.addWidget(self.test_camera_cb)
        
        self.test_display_cb = QCheckBox("测试图像显示")
        self.test_display_cb.setChecked(True)
        test_layout.addWidget(self.test_display_cb)
        
        self.test_fps_cb = QCheckBox("测试帧率性能")
        self.test_fps_cb.setChecked(True)
        test_layout.addWidget(self.test_fps_cb)
        
        self.test_artifacts_cb = QCheckBox("检测图像伪影")
        self.test_artifacts_cb.setChecked(True)
        test_layout.addWidget(self.test_artifacts_cb)
        
        # 添加执行测试按钮
        self.run_tests_btn = QPushButton("执行所有测试")
        self.run_tests_btn.clicked.connect(self.run_all_tests)
        test_layout.addWidget(self.run_tests_btn)
        
        # 添加测试组到控制面板
        control_layout.addWidget(test_group)
        
        # 日志显示
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # 添加日志组到控制面板
        control_layout.addWidget(log_group)
        control_layout.addStretch()
        
        # 右侧图像显示
        display_panel = QWidget()
        display_layout = QVBoxLayout(display_panel)
        
        # 图像标签
        self.image_view = QLabel()
        self.image_view.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_view.setText("等待相机启动...")
        self.image_view.setMinimumSize(640, 480)
        self.image_view.setStyleSheet("background-color: #333333; color: white;")
        
        # 设置objectName便于调试
        self.image_view.setObjectName("verify_image_view")
        
        display_layout.addWidget(self.image_view, 1)
        
        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        display_layout.addLayout(status_layout)
        
        # 添加到分割器
        splitter.addWidget(control_panel)
        splitter.addWidget(display_panel)
        
        # 设置分割器的初始大小
        splitter.setSizes([300, 700])
        
        # 添加分割器到主布局
        main_layout.addWidget(splitter)
        
        # 添加自定义日志处理器
        logger.remove()  # 移除默认处理器
        logger.add(sys.stderr, level="INFO")  # 添加标准错误输出
        logger.add(self._log_handler, level="INFO")  # 添加UI日志处理器
        logger.add(log_file, level="DEBUG")  # 添加文件日志
        
    def _log_handler(self, message):
        """自定义日志处理器，将日志显示在UI上
        
        Args:
            message: 日志消息
        """
        # 将消息添加到日志文本框
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(message + "\n")
        self.log_text.setTextCursor(cursor)
        self.log_text.ensureCursorVisible()
    
    def start_camera(self):
        """启动相机"""
        # 获取选中的相机ID
        camera_id = self.camera_combo.currentData()
        logger.info(f"启动相机 ID: {camera_id}")
        
        # 停止现有的相机工作线程
        if self.camera_worker:
            self.camera_worker.stop()
            self.camera_worker = None
        
        # 创建新的相机工作线程
        self.camera_worker = CameraWorker(camera_id)
        self.camera_worker.frame_ready.connect(self.display_frame)
        self.camera_worker.error.connect(self.handle_error)
        self.camera_worker.stats_updated.connect(self.update_stats)
        
        # 启动线程
        self.camera_worker.start()
        
        # 更新UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.capture_btn.setEnabled(True)
        self.status_label.setText(f"相机 {camera_id} 运行中")
        
        # 更新测试结果
        self.test_results['camera_open'] = True
        
    def stop_camera(self):
        """停止相机"""
        if self.camera_worker:
            logger.info("停止相机")
            self.camera_worker.stop()
            self.camera_worker = None
        
        # 更新UI
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.capture_btn.setEnabled(False)
        self.status_label.setText("相机已停止")
        self.fps_label.setText("0.0")
        self.fps_progress.setValue(0)
        
    def display_frame(self, frame):
        """显示相机帧
        
        Args:
            frame: BGR格式的相机帧
        """
        try:
            # 检查帧是否有效
            if frame is None or frame.size == 0:
                return
                
            # 标记已收到帧
            self.test_results['frame_received'] = True
            
            # 转换为RGB格式
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # 创建QImage
            h, w, c = rgb_frame.shape
            bytes_per_line = c * w
            q_img = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            
            # 创建QPixmap并显示
            pixmap = QPixmap.fromImage(q_img)
            self.image_view.setPixmap(pixmap)
            
            # 标记显示正常
            self.test_results['display_working'] = True
            
            except Exception as e:
            logger.error(f"显示图像时出错: {str(e)}")
            
    def handle_error(self, error_msg):
        """处理相机错误
        
        Args:
            error_msg: 错误消息
        """
        logger.error(f"相机错误: {error_msg}")
        self.status_label.setText(f"错误: {error_msg}")
        
        # 更新测试结果
        if "无法打开相机" in error_msg:
            self.test_results['camera_open'] = False
        elif "读取相机帧失败" in error_msg:
            self.test_results['frame_received'] = False
            
    def update_stats(self, stats):
        """更新统计信息
        
        Args:
            stats: 统计信息字典
        """
        # 更新FPS
        fps = stats.get('fps', 0)
        self.current_fps = fps
        
        # 判断帧率是否可接受
        self.test_results['fps_acceptable'] = fps >= 15.0
        
    def _update_fps_display(self):
        """更新FPS显示"""
        if hasattr(self, 'current_fps'):
            fps_text = f"{self.current_fps:.1f}"
            self.fps_label.setText(fps_text)
            
            # 更新进度条
            self.fps_progress.setValue(min(int(self.current_fps), 30))
            
            # 设置进度条颜色
            if self.current_fps < 15:
                self.fps_progress.setStyleSheet("QProgressBar::chunk { background-color: #FF5555; }")
            elif self.current_fps < 25:
                self.fps_progress.setStyleSheet("QProgressBar::chunk { background-color: #FFAA00; }")
            else:
                self.fps_progress.setStyleSheet("QProgressBar::chunk { background-color: #55AA55; }")
                
    def capture_image(self):
        """截取当前画面"""
        if not self.camera_worker or not hasattr(self, 'current_frame'):
            logger.warning("无法截图：相机未运行或没有图像")
            return
            
        # 创建截图目录
        if not os.path.exists("screenshots"):
            os.makedirs("screenshots")
            
        # 保存图像
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        camera_id = self.camera_combo.currentData()
        filename = f"screenshots/camera_{camera_id}_{timestamp}.png"
        
        # 获取当前显示的pixmap
        pixmap = self.image_view.pixmap()
        if pixmap and not pixmap.isNull():
            # 保存pixmap
            pixmap.save(filename)
            logger.info(f"截图已保存: {filename}")
            self.status_label.setText(f"截图已保存: {filename}")
                    else:
            logger.warning("无法截图：无效的图像")
            
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("开始运行所有测试...")
        
        if not self.camera_worker:
            QMessageBox.warning(self, "测试错误", "请先启动相机")
            return
            
        # 重置测试结果
        self.test_results = {
            'camera_open': False,
            'frame_received': False,
            'display_working': False,
            'fps_acceptable': False,
            'artifacts': False,
            'delay': 0
        }
        
        # 测试相机是否打开
        if self.test_camera_cb.isChecked():
            logger.info("测试相机初始化...")
            if self.camera_worker and self.camera_worker.isRunning():
                self.test_results['camera_open'] = True
                logger.info("✅ 相机初始化测试通过")
            else:
                logger.error("❌ 相机初始化测试失败")
                
        # 测试图像显示
        if self.test_display_cb.isChecked():
            logger.info("测试图像显示...")
            # 检查是否有pixmap
            pixmap = self.image_view.pixmap()
            if pixmap and not pixmap.isNull():
                self.test_results['display_working'] = True
                logger.info("✅ 图像显示测试通过")
                    else:
                logger.error("❌ 图像显示测试失败")
                
        # 测试帧率
        if self.test_fps_cb.isChecked():
            logger.info("测试帧率性能...")
            if hasattr(self, 'current_fps'):
                fps = self.current_fps
                self.test_results['fps_acceptable'] = fps >= 15.0
                if fps >= 15.0:
                    logger.info(f"✅ 帧率测试通过: {fps:.1f} FPS")
                else:
                    logger.warning(f"⚠️ 帧率较低: {fps:.1f} FPS")
            else:
                logger.error("❌ 帧率测试失败: 无法获取帧率")
                
        # 生成测试报告
        self._generate_test_report()
        
    def _generate_test_report(self):
        """生成测试报告"""
        # 检查报告目录
        if not os.path.exists("reports"):
            os.makedirs("reports")
            
        # 创建报告文件
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = f"reports/camera_test_report_{timestamp}.txt"
        
        # 汇总测试结果
        passed_tests = sum(1 for result in self.test_results.values() if result)
        total_tests = len(self.test_results)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        # 创建报告内容
        report = [
            "=== WireVsion 相机显示测试报告 ===",
            f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"相机ID: {self.camera_combo.currentData()}",
            "",
            "测试结果摘要:",
            f"通过测试: {passed_tests}/{total_tests} ({success_rate:.1f}%)",
            "",
            "详细测试结果:",
            f"1. 相机初始化: {'通过' if self.test_results['camera_open'] else '失败'}",
            f"2. 帧接收: {'通过' if self.test_results['frame_received'] else '失败'}",
            f"3. 图像显示: {'通过' if self.test_results['display_working'] else '失败'}",
            f"4. 帧率性能: {'通过' if self.test_results['fps_acceptable'] else '失败'} " +
            f"({getattr(self, 'current_fps', 0):.1f} FPS)",
            "",
            "系统环境:",
            f"Python版本: {sys.version}",
            f"OpenCV版本: {cv2.__version__}",
            f"PyQt5版本: {PyQt6.QtCore.QT_VERSION_STR}",
            "",
            "建议:",
        ]
        
        # 添加建议
        if not self.test_results['camera_open']:
            report.append("- 检查相机连接和驱动程序")
            report.append("- 尝试使用其他相机ID")
        if not self.test_results['frame_received']:
            report.append("- 检查相机驱动程序是否正常")
            report.append("- 尝试重新插拔相机")
        if not self.test_results['display_working']:
            report.append("- 检查图像转换和显示代码")
            report.append("- 确保相机帧格式正确")
        if not self.test_results['fps_acceptable']:
            report.append("- 降低相机分辨率以提高帧率")
            report.append("- 检查系统资源使用情况")
            report.append("- 关闭其他使用相机的应用程序")
            
        # 如果所有测试都通过
        if passed_tests == total_tests:
            report.append("恭喜！所有测试都通过了。相机显示功能工作正常。")
            
        # 写入报告文件
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
            
        logger.info(f"测试报告已保存到: {report_file}")
        
        # 显示测试结果对话框
        self._show_test_report_dialog(passed_tests, total_tests, success_rate)
        
    def _show_test_report_dialog(self, passed_tests, total_tests, success_rate):
        """显示测试报告对话框
        
        Args:
            passed_tests: 通过的测试数量
            total_tests: 总测试数量
            success_rate: 成功率
        """
        # 创建对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("测试报告")
        
        # 设置图标
        if success_rate == 100:
            msg_box.setIcon(QMessageBox.Information)
        elif success_rate >= 60:
            msg_box.setIcon(QMessageBox.Warning)
        else:
            msg_box.setIcon(QMessageBox.Critical)
            
        # 设置文本
        msg_box.setText(f"测试完成: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
        
        # 设置详细信息
        details = [
            "详细测试结果:",
            f"1. 相机初始化: {'✅' if self.test_results['camera_open'] else '❌'}",
            f"2. 帧接收: {'✅' if self.test_results['frame_received'] else '❌'}",
            f"3. 图像显示: {'✅' if self.test_results['display_working'] else '❌'}",
            f"4. 帧率性能: {'✅' if self.test_results['fps_acceptable'] else '⚠️'} " +
            f"({getattr(self, 'current_fps', 0):.1f} FPS)",
        ]
        msg_box.setInformativeText('\n'.join(details))
        
        # 显示对话框
        msg_box.exec_()
        
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 停止相机线程
        if self.camera_worker:
            self.camera_worker.stop()
            
        # 停止FPS计时器
        if self.fps_timer.isActive():
            self.fps_timer.stop()
            
        # 记录日志
        logger.info("相机显示验证工具关闭")
        
        # 接受关闭事件
        event.accept()


def main():
    """主函数"""
    # 设置应用程序
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格提高跨平台一致性
    
    # 设置应用程序图标
    icon_path = os.path.join(os.path.dirname(__file__), "resources", "icons", "app_icon.png")
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
    
    # 创建主窗口
    window = VerifyCameraDisplay()
    window.show()
        
        # 运行应用程序
        return app.exec_()
    

if __name__ == "__main__":
    sys.exit(main()) 