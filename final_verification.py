#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVision 最终验证脚本
确认所有修复都正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有关键模块导入"""
    print("🔍 测试模块导入...")

    try:
        from wirevsion.ui.modern_components import THEME_COLORS
        from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog
        from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
        from wirevsion.ui.theme_manager import theme_manager
        print("✅ 所有核心模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_function_signatures():
    """测试函数签名修复"""
    print("\n📝 测试函数签名修复...")

    try:
        from PyQt5.QtWidgets import QApplication
        from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog

        # 确保QApplication存在
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # 创建模拟节点
        class MockNode:
            def __init__(self):
                self.node_id = "test_node"
                self.node_type = "process"
                self.title = "测试节点"

        mock_node = MockNode()
        dialog = ModernNodeConfigDialog(mock_node)

        # 测试 _preview_generic_algorithm 方法
        if hasattr(dialog, '_preview_generic_algorithm'):
            try:
                dialog._preview_generic_algorithm("test", {}, None)
                print("✅ _preview_generic_algorithm 函数签名正确")
                dialog.close()
                return True
            except TypeError as e:
                print(f"❌ 函数签名错误: {e}")
                dialog.close()
                return False
            except Exception:
                # 其他异常是可以接受的
                print("✅ 函数签名正确（其他异常可忽略）")
                dialog.close()
                return True
        else:
            print("❌ _preview_generic_algorithm 方法不存在")
            dialog.close()
            return False

    except Exception as e:
        print(f"❌ 函数签名测试失败: {e}")
        return False

def test_numpy_imports():
    """测试numpy导入修复"""
    print("\n🔢 测试numpy导入...")

    try:
        import numpy as np

        # 测试ascontiguousarray函数
        test_array = np.array([[1, 2], [3, 4]])
        result = np.ascontiguousarray(test_array)

        if result.flags['C_CONTIGUOUS']:
            print("✅ numpy导入和ascontiguousarray函数正常")
            return True
        else:
            print("❌ ascontiguousarray函数有问题")
            return False

    except Exception as e:
        print(f"❌ numpy测试失败: {e}")
        return False

def test_theme_colors():
    """测试主题颜色"""
    print("\n🎨 测试主题颜色...")

    try:
        from wirevsion.ui.modern_components import THEME_COLORS

        required_colors = [
            'dark_bg_app', 'dark_bg_content', 'dark_bg_card',
            'text_primary', 'text_secondary', 'primary',
            'primary_hover', 'success', 'danger'
        ]

        missing_colors = []
        for color in required_colors:
            if color not in THEME_COLORS:
                missing_colors.append(color)

        if missing_colors:
            print(f"❌ 缺失主题颜色: {missing_colors}")
            return False
        else:
            print("✅ 所有必需的主题颜色都已定义")
            return True

    except Exception as e:
        print(f"❌ 主题颜色测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 WireVision 最终验证开始")
    print("=" * 50)

    tests = [
        ("模块导入", test_imports),
        ("函数签名", test_function_signatures),
        ("numpy导入", test_numpy_imports),
        ("主题颜色", test_theme_colors),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        if test_func():
            passed += 1

    print("\n" + "=" * 50)
    print("📊 最终验证结果")
    print("=" * 50)
    print(f"总测试项: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {(passed/total*100):.1f}%")

    if passed == total:
        print("\n🎉 所有测试通过！WireVision项目修复完成！")
        print("✨ 项目现在处于稳定可用状态")
    else:
        print(f"\n⚠️ 还有 {total - passed} 个问题需要解决")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
