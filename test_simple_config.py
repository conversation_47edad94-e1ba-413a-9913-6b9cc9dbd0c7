#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单测试现代化节点配置对话框
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print('🧪 测试导入...')
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QPointF
        print('✅ PyQt5导入成功')
        
        from wirevsion.ui.modern_components import THEME_COLORS, ModernButton
        print('✅ 现代化组件导入成功')
        
        from wirevsion.ui.modern_workflow_editor import ModernFlowNode
        print('✅ 工作流节点导入成功')
        
        from wirevsion.ui.modern_node_config_dialog import ROIDrawingView
        print('✅ ROI绘制视图导入成功')
        
        from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog
        print('✅ 现代化节点配置对话框导入成功')
        
        return True
        
    except Exception as e:
        print(f'❌ 导入失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_basic_creation():
    """测试基本创建"""
    print('🧪 测试基本创建...')
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QPointF
        from wirevsion.ui.modern_workflow_editor import ModernFlowNode
        from wirevsion.ui.modern_node_config_dialog import ROIDrawingView, ModernNodeConfigDialog
        
        app = QApplication(sys.argv)
        
        # 创建测试节点
        test_node = ModernFlowNode('test_node', 'process', '测试节点', QPointF(0, 0))
        print('✅ 测试节点创建成功')
        
        # 测试ROI绘制视图
        roi_view = ROIDrawingView()
        print('✅ ROI绘制视图创建成功')
        
        # 测试节点配置对话框
        dialog = ModernNodeConfigDialog(test_node)
        print('✅ 现代化节点配置对话框创建成功')
        
        print('🎉 基本创建测试完成！')
        return True
        
    except Exception as e:
        print(f'❌ 基本创建失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print('🚀 开始简单测试...')
    
    success1 = test_imports()
    if success1:
        success2 = test_basic_creation()
        if success2:
            print('🎉 所有测试通过！')
            sys.exit(0)
    
    print('❌ 测试失败')
    sys.exit(1)
