# WireVision 配置界面快速使用指南

## 🚀 快速开始

### 启动测试程序
```bash
cd /Users/<USER>/YXZD/WireVsion
python test_config_dialog.py
```

### 界面布局
- **左侧面板**：算法选择和参数配置
- **右侧面板**：图像预览和ROI绘制

## 📋 功能操作指南

### 1. 模板匹配完整流程

#### 步骤1：选择算法
1. 点击"测试模板匹配配置"按钮
2. 在算法下拉框中选择"模板匹配"

#### 步骤2：绘制ROI区域
1. 点击"绘制ROI"按钮
2. 在预览图像上拖拽绘制矩形区域
3. 松开鼠标完成ROI创建

#### 步骤3：创建模板
1. 确保已绘制ROI区域
2. 点击"📷 创建模板"按钮
3. 选择保存位置和文件名
4. 查看模板预览对话框

#### 步骤4：调整参数
- **匹配方法**：选择TM_CCOEFF_NORMED等
- **匹配阈值**：调整0.0-1.0之间的值
- **最大匹配数**：设置检测数量限制

#### 步骤5：预览结果
1. 点击"👁️ 预览"按钮
2. 查看匹配结果和置信度
3. 绿色框显示匹配位置

### 2. 颜色检测操作流程

#### 步骤1：选择算法
1. 点击"测试颜色检测配置"按钮
2. 选择"颜色检测"算法

#### 步骤2：选择目标颜色
1. 点击"🎨 选择颜色"按钮
2. 在颜色选择器中选择目标颜色
3. 按钮背景会显示选择的颜色

#### 步骤3：设置颜色范围
1. 点击"🌈 颜色范围"按钮
2. 输入颜色容差值（0-255）
3. 容差越大，检测范围越宽

#### 步骤4：预览检测结果
1. 点击"👁️ 预览"按钮
2. 查看颜色检测效果
3. 匹配区域会高亮显示

### 3. 边缘检测操作

#### 基本操作
1. 选择"边缘检测"算法
2. 调整低阈值和高阈值参数
3. 点击预览查看边缘检测效果

### 4. 高斯模糊操作

#### 基本操作
1. 选择"高斯模糊"算法
2. 调整核大小和Sigma值
3. 点击预览查看模糊效果

## 🎨 界面特色功能

### 动态按钮样式
- **颜色选择按钮**：背景显示选择的颜色
- **颜色范围按钮**：颜色强度反映容差大小
- **悬停效果**：所有按钮都有视觉反馈

### ROI管理
- **绘制模式**：鼠标拖拽创建ROI
- **列表管理**：查看和选择已创建的ROI
- **属性编辑**：修改ROI坐标和尺寸
- **批量操作**：添加、删除、清除ROI

### 实时预览
- **即时反馈**：参数修改后立即预览
- **结果显示**：算法处理结果实时更新
- **错误提示**：友好的错误信息和解决建议

## 🔧 常见问题解决

### Q: 预览图像不显示？
**A:** 程序会自动创建测试图像，如果仍不显示，请检查：
- 是否有OpenCV和PyQt5依赖
- 查看控制台错误信息

### Q: 模板匹配没有结果？
**A:** 请检查：
- 是否已创建或加载模板
- 匹配阈值是否过高
- 模板和目标图像是否相似

### Q: 颜色检测效果不好？
**A:** 尝试：
- 调整颜色容差值
- 选择更准确的目标颜色
- 确保光照条件一致

### Q: ROI绘制不响应？
**A:** 确保：
- 已点击"绘制ROI"按钮进入绘制模式
- 在图像区域内拖拽
- 绘制的矩形足够大（>10像素）

## 💡 使用技巧

### 1. 模板匹配优化
- 选择特征明显的区域作为模板
- 使用TM_CCOEFF_NORMED方法获得最佳效果
- 阈值从0.8开始调整

### 2. 颜色检测优化
- 在HSV色彩空间中颜色检测更稳定
- 容差值建议从30开始调整
- 避免选择过于相似的背景色

### 3. 界面操作技巧
- 使用键盘快捷键提高效率
- 保存常用的参数配置
- 利用预览功能实时调试

## 📞 技术支持

如果遇到问题，请：
1. 查看控制台日志信息
2. 检查FEATURE_COMPLETION_SUMMARY.md文档
3. 确认所有依赖包已正确安装

---

**祝您使用愉快！** 🎉
