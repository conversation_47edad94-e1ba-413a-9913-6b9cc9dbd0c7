#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试现代化节点配置对话框
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QPointF

def test_modern_node_config():
    """测试现代化节点配置对话框"""
    print('🧪 测试现代化节点配置对话框...')
    
    try:
        # 导入必要的模块
        from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog, ROIDrawingView
        from wirevsion.ui.modern_workflow_editor import ModernFlowNode
        
        app = QApplication(sys.argv)
        
        # 创建测试节点
        test_node = ModernFlowNode('test_node', 'process', '测试节点', QPointF(0, 0))
        print('✅ 测试节点创建成功')
        
        # 测试ROI绘制视图
        roi_view = ROIDrawingView()
        print('✅ ROI绘制视图创建成功')
        
        # 测试节点配置对话框
        dialog = ModernNodeConfigDialog(test_node)
        print('✅ 现代化节点配置对话框创建成功')
        
        # 测试算法加载
        algorithm_count = dialog.algorithm_combo.count()
        print(f'✅ 算法下拉框项目数: {algorithm_count}')
        
        # 测试ROI功能
        roi_count = dialog.roi_list.count()
        print(f'✅ ROI列表初始化: {roi_count} 项')
        
        # 测试配置数据
        config_data = dialog.config_data
        print(f'✅ 配置数据结构: {list(config_data.keys())}')
        
        # 测试选项卡
        tab_count = dialog.tab_widget.count()
        print(f'✅ 选项卡数量: {tab_count}')
        
        # 测试预览图像加载
        dialog._load_preview_image()
        print('✅ 预览图像加载测试完成')
        
        # 测试ROI添加
        dialog._add_roi()
        roi_count_after = dialog.roi_list.count()
        print(f'✅ ROI添加测试: {roi_count} -> {roi_count_after}')
        
        # 测试配置收集
        dialog._collect_current_config()
        print('✅ 配置收集测试完成')
        
        print('🎉 现代化节点配置对话框测试完成！')
        return True
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_modern_node_config()
    sys.exit(0 if success else 1)
