# final_review_gate.py
import sys
import os

if __name__ == "__main__":
    # 尝试使标准输出无缓冲，以实现更快速的交互响应。
    # 这可能在所有平台上都不起作用，或者如果标准输出不是 TTY，
    # 但对于这种交互式脚本来说是一个良好的实践。
    try:
        sys.stdout = os.fdopen(sys.stdout.fileno(), 'w', buffering=1)
    except Exception:
        pass  # 如果解除缓冲失败则忽略，例如在某些环境中

    try:
        sys.stderr = os.fdopen(sys.stderr.fileno(), 'w', buffering=1)
    except Exception:
        pass  # 忽略

    print("--- 最终审查门户已激活 ---", flush=True)
    print("AI 已完成其主要操作。等待您的审查或进一步的子提示。", flush=True)
    print("输入您的子提示，或以下之一：'TASK_COMPLETE'、'Done'、'Quit'、'q' 来表示完成。", flush=True)
    
    active_session = True
    while active_session:
        try:
            # 发出信号表明脚本已准备好接收输入。
            # AI 不需要解析这个，但它对用户可见性很好。
            print("审查门户等待输入：", end="", flush=True) 
            
            line = sys.stdin.readline()
            
            if not line:  # EOF
                print("--- 审查门户：标准输入已关闭 (EOF)，脚本退出 ---", flush=True)
                active_session = False
                break
            
            user_input = line.strip()

            # 检查退出条件
            if user_input.upper() in ['TASK_COMPLETE', 'DONE', 'QUIT', 'Q']:  # 修改：空字符串不再导致退出
                print(f"--- 审查门户：用户通过 '{user_input.upper()}' 表示完成 ---", flush=True)
                active_session = False
                break
            elif user_input:  # 如果有任何其他非空输入（并且不是完成命令）
                # 这是 AI 将"监听"的关键行。
                print(f"用户审查子提示：{user_input}", flush=True)
            # 如果 user_input 为空（并且不是完成命令），
            # 循环将继续，并且会再次打印"审查门户等待输入："
            
        except KeyboardInterrupt:
            print("--- 审查门户：会话被用户中断 (KeyboardInterrupt) ---", flush=True)
            active_session = False
            break
        except Exception as e:
            print(f"--- 审查门户脚本错误：{e} ---", flush=True)
            active_session = False
            break
            
    print("--- 最终审查门户脚本已退出 ---", flush=True) 