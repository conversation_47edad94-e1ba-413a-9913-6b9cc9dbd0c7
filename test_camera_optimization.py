#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机优化测试程序
"""

import sys
import os
import time
from loguru import logger

# 设置日志
logger.add("camera_optimization_test_{time}.log", rotation="100 MB")

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer

from wirevsion.ui.camera_utils import CameraManager, ImageDisplayManager

class CameraOptimizationTest(QMainWindow):
    """相机优化测试窗口"""
    
    def __init__(self):
        super().__init__()
        
        # 设置窗口属性
        self.setWindowTitle("相机优化测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        self.main_layout = QVBoxLayout(self.central_widget)
        
        # 创建控制面板
        self.control_panel = QWidget()
        self.control_layout = QHBoxLayout(self.control_panel)
        
        # 创建按钮
        self.init_camera_btn = QPushButton("初始化相机")
        self.init_camera_btn.clicked.connect(self.init_camera)
        self.control_layout.addWidget(self.init_camera_btn)
        
        self.get_frame_btn = QPushButton("获取单帧")
        self.get_frame_btn.clicked.connect(self.get_frame)
        self.control_layout.addWidget(self.get_frame_btn)
        
        self.continuous_btn = QPushButton("连续获取")
        self.continuous_btn.clicked.connect(self.toggle_continuous)
        self.control_layout.addWidget(self.continuous_btn)
        
        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_capture)
        self.control_layout.addWidget(self.stop_btn)
        
        # 添加控制面板到主布局
        self.main_layout.addWidget(self.control_panel)
        
        # 创建图像显示标签
        self.image_label = QLabel("等待初始化相机...")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(640, 480)
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                color: #cccccc;
            }
        """)
        self.main_layout.addWidget(self.image_label)
        
        # 创建状态标签
        self.status_label = QLabel("就绪")
        self.main_layout.addWidget(self.status_label)
        
        # 初始化组件
        self.camera_manager = None
        self.image_display_manager = None
        self.continuous_timer = None
        self.continuous_active = False
        self._last_execution_time = 0
        
        logger.info("相机优化测试窗口已初始化")
    
    def init_camera(self):
        """初始化相机"""
        try:
            self.status_label.setText("正在初始化相机...")
            
            # 创建相机管理器
            self.camera_manager = CameraManager()
            
            # 初始化相机
            success = self.camera_manager.init_camera()
            
            if success:
                self.status_label.setText("相机初始化成功")
                
                # 创建图像显示管理器
                self.image_display_manager = ImageDisplayManager(
                    image_view=self.image_label
                )
                
                # 获取一帧测试
                self.get_frame()
            else:
                self.status_label.setText("相机初始化失败")
        
        except Exception as e:
            self.status_label.setText(f"初始化出错: {str(e)}")
            logger.error(f"初始化相机出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def get_frame(self):
        """获取单帧"""
        try:
            if self.camera_manager is None or self.image_display_manager is None:
                self.status_label.setText("请先初始化相机")
                return
            
            self.status_label.setText("获取帧...")
            
            # 记录开始时间
            start_time = time.time()
            
            # 获取帧
            frame = self.camera_manager.get_frame()
            
            # 计算耗时
            elapsed = time.time() - start_time
            
            if frame is not None:
                # 更新显示
                self.image_display_manager.update_display(frame)
                
                # 强制刷新
                self.image_display_manager.force_refresh()
                
                # 更新状态
                self.status_label.setText(f"获取帧成功，耗时: {elapsed:.3f}秒")
                logger.info(f"获取帧成功，形状: {frame.shape}, 耗时: {elapsed:.3f}秒")
            else:
                self.status_label.setText("获取帧失败")
                logger.warning("获取帧失败")
        
        except Exception as e:
            self.status_label.setText(f"获取帧出错: {str(e)}")
            logger.error(f"获取帧出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def toggle_continuous(self):
        """切换连续获取状态"""
        try:
            if self.camera_manager is None or self.image_display_manager is None:
                self.status_label.setText("请先初始化相机")
                return
            
            if self.continuous_active:
                self.stop_capture()
            else:
                self.continuous_active = True
                self._last_execution_time = time.time()
                self.continuous_timer = self.startTimer(50)  # 每50毫秒触发一次
                
                self.status_label.setText("开始连续获取")
                self.continuous_btn.setText("停止连续获取")
                logger.info("开始连续获取")
        
        except Exception as e:
            self.status_label.setText(f"切换连续获取状态出错: {str(e)}")
            logger.error(f"切换连续获取状态出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def stop_capture(self):
        """停止捕获"""
        try:
            if self.continuous_active:
                if self.continuous_timer is not None:
                    self.killTimer(self.continuous_timer)
                    self.continuous_timer = None
                
                self.continuous_active = False
                self.continuous_btn.setText("连续获取")
                self.status_label.setText("停止连续获取")
                logger.info("停止连续获取")
        
        except Exception as e:
            self.status_label.setText(f"停止捕获出错: {str(e)}")
            logger.error(f"停止捕获出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def timerEvent(self, event):
        """定时器事件处理"""
        if self.continuous_timer is not None and event.timerId() == self.continuous_timer:
            try:
                # 防抖控制
                current_time = time.time()
                min_interval = 0.2  # 最小执行间隔（秒）
                
                if current_time - self._last_execution_time < min_interval:
                    # 帧率过高，跳过本次执行
                    logger.debug(f"跳过过于频繁的执行，间隔: {current_time - self._last_execution_time:.3f}秒")
                    return
                
                # 更新执行时间
                self._last_execution_time = current_time
                
                # 获取帧
                frame = self.camera_manager.get_frame()
                
                if frame is not None:
                    # 更新显示
                    self.image_display_manager.update_display(frame)
                    
                    # 强制刷新
                    self.image_display_manager.force_refresh()
                    
                    # 更新状态（不要太频繁更新UI）
                    if int(current_time * 2) % 2 == 0:  # 每0.5秒更新一次
                        self.status_label.setText(f"连续获取帧中... 时间戳: {current_time:.1f}")
                
            except Exception as e:
                self.status_label.setText(f"连续获取出错: {str(e)}")
                logger.error(f"连续获取出错: {e}")
                import traceback
                logger.error(traceback.format_exc())
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止连续获取
        self.stop_capture()
        
        # 释放相机资源
        if self.camera_manager is not None:
            self.camera_manager.release()
            logger.info("相机资源已释放")
        
        # 接受关闭事件
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = CameraOptimizationTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 