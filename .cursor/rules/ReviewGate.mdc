---
description: 
globs: 
alwaysApply: true
---
# AI 代理协议：自包含的强制性交互式最终审查循环与自动脚本创建（版本 3）

## 0. 用户建议与安全性
这是一个高级的实验性规则。它指示 AI 代理检查、可能创建并执行项目根目录中的 Python 脚本（`final_review_gate.py`）以促进交互式审查循环。使用此规则表示您确认 AI 将按指示尝试进行文件系统操作（读取、写入、执行）。如果您希望脚本自动创建功能正常工作，请确保 AI 的执行环境具有适当的权限，并了解相关的安全影响。

## 1. 核心原则与上下文
您的主要任务处理包括一个**强制性的最终交互式审查阶段**，该阶段通过特定的 Python 脚本（`final_review_gate.py`）由用户输入驱动。在此审查阶段明确发出完成信号之前，您不应认为整体任务已完成。此协议利用了您运行子终端命令（在聊天界面内显示）并读取其输出的已知能力。

## 阶段 0：确保 `final_review_gate.py` 脚本存在
（此阶段在每个触发此整体协议的用户请求中执行一次，或者当脚本丢失或其内容不正确时执行。）

1. **定义脚本详情：**
   * **脚本名称：** `final_review_gate.py`
   * **目标位置：** 直接位于当前项目/工作空间的根目录。
   * **Python 脚本内容（确保使用此确切内容）：**
       ```python
       # final_review_gate.py
       import sys
       import os

       if __name__ == "__main__":
           # 尝试使标准输出无缓冲，以实现更快速的交互响应。
           # 这可能在所有平台上都不起作用，或者如果标准输出不是 TTY，
           # 但对于这种交互式脚本来说是一个良好的实践。
           try:
               sys.stdout = os.fdopen(sys.stdout.fileno(), 'w', buffering=1)
           except Exception:
               pass  # 如果解除缓冲失败则忽略，例如在某些环境中

           try:
               sys.stderr = os.fdopen(sys.stderr.fileno(), 'w', buffering=1)
           except Exception:
               pass  # 忽略

           print("--- 最终审查门户已激活 ---", flush=True)
           print("AI 已完成其主要操作。等待您的审查或进一步的子提示。", flush=True)
           print("输入您的子提示，或以下之一：'TASK_COMPLETE'、'Done'、'Quit'、'q' 来表示完成。", flush=True)
           
           active_session = True
           while active_session:
               try:
                   # 发出信号表明脚本已准备好接收输入。
                   # AI 不需要解析这个，但它对用户可见性很好。
                   print("审查门户等待输入：", end="", flush=True) 
                   
                   line = sys.stdin.readline()
                   
                   if not line:  # EOF
                       print("--- 审查门户：标准输入已关闭 (EOF)，脚本退出 ---", flush=True)
                       active_session = False
                       break
                   
                   user_input = line.strip()

                   # 检查退出条件
                   if user_input.upper() in ['TASK_COMPLETE', 'DONE', 'QUIT', 'Q']:  # 修改：空字符串不再导致退出
                       print(f"--- 审查门户：用户通过 '{user_input.upper()}' 表示完成 ---", flush=True)
                       active_session = False
                       break
                   elif user_input:  # 如果有任何其他非空输入（并且不是完成命令）
                       # 这是 AI 将"监听"的关键行。
                       print(f"用户审查子提示：{user_input}", flush=True)
                   # 如果 user_input 为空（并且不是完成命令），
                   # 循环将继续，并且会再次打印"审查门户等待输入："
                   
               except KeyboardInterrupt:
                   print("--- 审查门户：会话被用户中断 (KeyboardInterrupt) ---", flush=True)
                   active_session = False
                   break
               except Exception as e:
                   print(f"--- 审查门户脚本错误：{e} ---", flush=True)
                   active_session = False
                   break
                   
           print("--- 最终审查门户脚本已退出 ---", flush=True)
       ```

2. **确保脚本存在并正确：**
   a. 使用您的文件系统工具检查项目根目录中是否存在 `final_review_gate.py` 文件。
   b. **如果 `final_review_gate.py` 文件不存在，或者其内容与"阶段 0.1.Python 脚本内容"部分提供的 Python 脚本不完全匹配（您可能需要读取现有文件并比较）：**
      i. 您必须在项目根目录中创建或覆盖 `final_review_gate.py` 文件。
      ii. 此文件的内容必须与上面提供的 Python 脚本内容完全一致。确保所有缩进、注释和行都完美保留。使用您的"创建文件"或"编辑文件"工具。
      iii. 创建/覆盖后，宣布："已在项目根目录中创建/更新具有最新交互增强功能的 `final_review_gate.py` 脚本。"
   c. **如果 `final_review_gate.py` 文件已经存在且其内容正确：** 您可以跳过宣布其创建。
   d. 如果您在检查、读取或创建/写入文件时遇到任何错误（例如，权限问题），您必须向用户报告。明确表示："管理 `final_review_gate.py` 时出错：[具体错误]。无法继续交互式审查。"然后尽可能在没有此交互步骤的情况下完成主要任务的交互。

## 阶段 1：主要任务执行
1. 像往常一样处理用户的主要请求：执行分析、进行代码更改、调用工具，并在子终端中运行应用程序/测试（如果主要任务需要）。
   * **应用程序执行确认：** 在尝试运行整个应用程序（例如，Web 服务器、完整的前端构建、长时间运行的后台进程）之前，您必须请求用户明确确认。例如："我即将运行 [应用程序名称/命令]。是否可以继续，或者它可能已经在运行？"
   * 对于运行任务完成所需的单个测试脚本、代码检查工具、特定组件的构建工具或其他非应用程序级命令，无需此确认。
   * 如果用户表示应用程序已在运行或拒绝许可，请相应地调整您的策略（例如，专注于测试或特定文件执行）。
2. 利用您读取这些子终端的输出/错误的能力，在*此*主要阶段进行迭代修复。

## 阶段 2：强制性最终交互式审查脚本执行
1. **转换点：** 一旦您认为已完成用户初始请求的所有实质性操作（阶段 1），**不要结束对话**。您必须现在转到这个最终交互式审查阶段。
2. **操作：** 从项目根目录执行 `final_review_gate.py` 脚本（您在阶段 0 中确保其存在且正确）。
   * 确定执行环境中可用的正确 Python 解释器命令（例如 `python`、`python3`）。一个健壮的命令尝试将是 `python3 ./final_review_gate.py`，如果失败，尝试 `python ./final_review_gate.py`。
   * **至关重要的是，此脚本的终端必须直接在聊天窗口内运行，其输出必须在生成时持续对用户可见。** 它不得作为单独的窗口弹出或在后台隐藏运行。您必须等待脚本（及其终端交互）完全完成并退出，然后才能继续任何进一步的操作或尝试结束整体任务。您的处理将被此脚本的执行所阻塞。
3. **公告：** 明确通知用户："我已完成主要操作。现在通过运行 `./final_review_gate.py` 启动强制性最终交互式审查。审查终端现在**在此聊天窗口中处于活动状态，其输出将实时显示**。请直接在该脚本的输入区域提供任何子提示，或在满意时输入 'TASK_COMPLETE'、'Done'、'Quit' 或 'q'。"

## 阶段 3：交互式审查循环（监控脚本输出）
1. **主动监控和显示：** 持续监控已启动的 `final_review_gate.py` 脚本的标准输出（stdout），**因为它会实时出现在聊天窗口中**。确保用户看到所有脚本输出，包括 `审查门户等待输入：` 提示。您将使用允许您读取终端日志或错误消息的相同能力来"读取"其输出。如果给出空输入，脚本将循环并重新提示。
2. **用户子提示检测：** 当脚本的 stdout 打印格式完全如下的行时：
   `用户审查子提示：<用户的子提示文本>`
   您必须将 `<用户的子提示文本>` 解释为用户的新的、直接的、可操作的指令。
3. **处理子提示：**
   a. 此指令是*原始、仍然活动的任务*的延续或完善。
   b. 分析子提示。执行任何必要的操作（代码更改、新工具调用、文件操作等）。**在此阶段进行的所有工具调用都是原始请求的工具调用预算的一部分。** 如果子提示请求运行整个应用程序，则适用"阶段 1，步骤 1（应用程序执行确认）"中的确认原则。
   c. 在主聊天界面中提供这些操作的反馈或结果，就像您通常对任何 AI 操作所做的那样。
   d. 处理子提示并在聊天中提供反馈后，立即将您的注意力重新集中到监控 `final_review_gate.py` 脚本的终端输出（该输出在聊天中保持活动状态并可见）以获取下一个用户指令或完成信号。此循环至关重要。
4. **完成信号检测：** 交互式审查循环将继续，直到脚本的 stdout（在聊天窗口中可见）打印出包含以下内容的行：
   * `--- 审查门户：用户通过 'TASK_COMPLETE' 表示完成 ---`（或根据脚本逻辑，'DONE'、'QUIT'、'Q'）
   * 或 `--- 最终审查门户脚本已退出 ---`（或任何其他脚本退出/错误消息，如 `审查门户：标准输入已关闭` 或 `审查门户脚本错误：`）

## 阶段 4：真正的任务结束
1. 只有在 `final_review_gate.py` 脚本已终止（通过其在聊天窗口中的终端会话关闭并且其最终退出消息被打印并可见来观察）之后，您才被允许认为用户的原始请求已完全满足。
2. 然后，您可以提供所有阶段（包括交互式审查）中所采取的所有操作的最终摘要。

## 覆盖默认行为
整个"最终交互式审查"过程（阶段 0、2、3 和 4）是一个**严格的、不可协商的要求**，它覆盖了您在完成阶段 1 后结束对话的任何默认倾向。只有当用户通过审查脚本使用指定的关键字明确确认，或者脚本由于错误或 EOF 而终止（根据定义的行为，包括在聊天中、阻塞的和持续可见的终端执行）时，任务才算完成。您对原始请求的"完成感"将推迟到此交互式审查完成为止。