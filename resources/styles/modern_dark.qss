/* 
WireVsion 现代暗色主题样式表
*/

/* 全局样式 */
QWidget {
    background-color: #1e1e1e;
    color: #e0e0e0;
    font-family: "Segoe UI", Arial, sans-serif;
    font-size: 14px;
}

/* 主窗口 */
QMainWindow {
    background-color: #1e1e1e;
}

QMainWindow::separator {
    background-color: #333333;
    width: 1px;
    height: 1px;
}

/* 菜单栏 */
QMenuBar {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border-bottom: 1px solid #333333;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 10px;
}

QMenuBar::item:selected {
    background-color: #3a3a3a;
}

QMenuBar::item:pressed {
    background-color: #3a3a3a;
}

/* 菜单 */
QMenu {
    background-color: #2d2d2d;
    border: 1px solid #444444;
}

QMenu::item {
    padding: 6px 25px 6px 25px;
}

QMenu::item:selected {
    background-color: #3a3a3a;
}

QMenu::separator {
    height: 1px;
    background-color: #444444;
    margin: 4px 0px;
}

/* 工具栏 */
QToolBar {
    background-color: #2d2d2d;
    border-bottom: 1px solid #333333;
    spacing: 2px;
    padding: 2px;
}

QToolBar::handle {
    background-color: #444444;
}

QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 4px;
}

QToolButton:hover {
    background-color: #3a3a3a;
    border: 1px solid #444444;
}

QToolButton:pressed {
    background-color: #444444;
    border: 1px solid #555555;
}

/* 状态栏 */
QStatusBar {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border-top: 1px solid #333333;
}

QStatusBar::item {
    border: none;
}

/* 分割器 */
QSplitter {
    background-color: #1e1e1e;
}

QSplitter::handle {
    background-color: #333333;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* 标签和框架 */
QLabel {
    background-color: transparent;
    color: #e0e0e0;
}

QFrame {
    border: 1px solid #333333;
    border-radius: 4px;
}

/* 按钮 */
QPushButton {
    background-color: #2a6ea6;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #3a7eb6;
}

QPushButton:pressed {
    background-color: #1a5e96;
}

QPushButton:disabled {
    background-color: #555555;
    color: #888888;
}

/* 输入框 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: 1px solid #444444;
    border-radius: 4px;
    padding: 4px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 1px solid #2a6ea6;
}

/* 复选框 */
QCheckBox {
    background-color: transparent;
    color: #e0e0e0;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #444444;
    border-radius: 3px;
}

QCheckBox::indicator:unchecked {
    background-color: #2d2d2d;
}

QCheckBox::indicator:checked {
    background-color: #2a6ea6;
    image: url(resources/icons/check.png);
}

/* 单选框 */
QRadioButton {
    background-color: transparent;
    color: #e0e0e0;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #444444;
    border-radius: 8px;
}

QRadioButton::indicator:unchecked {
    background-color: #2d2d2d;
}

QRadioButton::indicator:checked {
    background-color: #2a6ea6;
    border: 2px solid #2d2d2d;
}

/* 下拉框 */
QComboBox {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: 1px solid #444444;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 100px;
}

QComboBox:hover {
    border: 1px solid #555555;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #444444;
}

QComboBox::down-arrow {
    image: url(resources/icons/down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: 1px solid #444444;
    selection-background-color: #3a3a3a;
}

/* 滚动条 */
QScrollBar:horizontal {
    background-color: #2d2d2d;
    height: 12px;
    margin: 0px 12px 0px 12px;
}

QScrollBar:vertical {
    background-color: #2d2d2d;
    width: 12px;
    margin: 12px 0px 12px 0px;
}

QScrollBar::handle:horizontal, QScrollBar::handle:vertical {
    background-color: #555555;
    border-radius: 4px;
    min-width: 20px;
    min-height: 20px;
}

QScrollBar::handle:horizontal:hover, QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar::add-line, QScrollBar::sub-line {
    background: none;
    border: none;
}

QScrollBar::add-page, QScrollBar::sub-page {
    background: none;
}

/* 标签页 */
QTabWidget {
    background-color: #1e1e1e;
}

QTabWidget::pane {
    border: 1px solid #333333;
    border-radius: 4px;
    top: -1px;
}

QTabBar::tab {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: 1px solid #333333;
    border-bottom-color: transparent;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 15px;
    min-width: 80px;
}

QTabBar::tab:selected {
    background-color: #1e1e1e;
    border-bottom-color: #1e1e1e;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

/* 进度条 */
QProgressBar {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: 1px solid #444444;
    border-radius: 4px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #2a6ea6;
    width: 1px;
}

/* 滑块 */
QSlider::groove:horizontal {
    border: 1px solid #444444;
    height: 6px;
    background-color: #2d2d2d;
    margin: 2px 0;
}

QSlider::handle:horizontal {
    background-color: #2a6ea6;
    border: 1px solid #1a5e96;
    width: 14px;
    margin: -4px 0;
    border-radius: 7px;
}

QSlider::handle:horizontal:hover {
    background-color: #3a7eb6;
}

/* 列表和树 */
QListView, QTreeView, QTableView {
    background-color: #2d2d2d;
    border: 1px solid #444444;
    color: #e0e0e0;
    outline: none;
    selection-background-color: #2a6ea6;
    selection-color: #ffffff;
}

QListView::item, QTreeView::item, QTableView::item {
    padding: 4px;
}

QListView::item:hover, QTreeView::item:hover, QTableView::item:hover {
    background-color: #3a3a3a;
}

QListView::item:selected, QTreeView::item:selected, QTableView::item:selected {
    background-color: #2a6ea6;
    color: #ffffff;
}

QHeaderView {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

QHeaderView::section {
    background-color: #333333;
    color: #e0e0e0;
    padding: 4px;
    border: 1px solid #444444;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

/* 对话框 */
QDialog {
    background-color: #1e1e1e;
}

/* 消息框 */
QMessageBox {
    background-color: #1e1e1e;
}

/* 工具提示 */
QToolTip {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border: 1px solid #444444;
    padding: 4px;
} 