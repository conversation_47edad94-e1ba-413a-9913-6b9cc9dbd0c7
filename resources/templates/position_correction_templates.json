{"algorithm": "position_correction", "name": "位置校正模板", "description": "位置校正算法的常用参数模板", "templates": [{"name": "文档校正", "algorithm": "perspective_transform", "description": "适用于文档扫描的透视校正", "parameters": {"src_points": [[50, 50], [550, 50], [550, 750], [50, 750]], "dst_points": [[0, 0], [500, 0], [500, 700], [0, 700]], "interpolation": "linear", "border_mode": "constant", "border_value": 255}}, {"name": "轻微旋转校正", "algorithm": "rotation_correction", "description": "校正轻微的图像旋转", "parameters": {"angle": 0.0, "auto_detect_angle": true, "detection_method": "hough_lines", "scale": 1.0, "interpolation": "linear", "border_mode": "replicate"}}, {"name": "图像配准", "algorithm": "translation_correction", "description": "基于模板匹配的图像配准", "parameters": {"dx": 0, "dy": 0, "auto_detect": true, "detection_method": "template_matching", "interpolation": "linear", "border_mode": "constant"}}, {"name": "图像缩放标准化", "algorithm": "scale_correction", "description": "将图像缩放到标准尺寸", "parameters": {"scale_x": 1.0, "scale_y": 1.0, "maintain_aspect_ratio": true, "auto_calculate_scale": true, "target_width": 640, "target_height": 480, "interpolation": "area"}}, {"name": "高质量缩放", "algorithm": "scale_correction", "description": "高质量的图像缩放", "parameters": {"scale_x": 2.0, "scale_y": 2.0, "maintain_aspect_ratio": true, "interpolation": "lanc<PERSON>s"}}, {"name": "仿射校正组合", "algorithm": "affine_transform", "description": "组合旋转、缩放、平移的仿射变换", "parameters": {"src_points": [[100, 100], [300, 120], [80, 250]], "dst_points": [[100, 100], [300, 100], [100, 250]], "interpolation": "cubic", "border_mode": "reflect"}}]}