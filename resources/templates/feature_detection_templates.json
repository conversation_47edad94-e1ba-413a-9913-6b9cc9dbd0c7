{"algorithm": "blob_detection", "name": "斑点检测模板", "description": "斑点检测算法的常用参数模板", "templates": [{"name": "小斑点检测", "description": "检测小尺寸的斑点", "parameters": {"min_threshold": 30, "max_threshold": 200, "min_area": 10, "max_area": 500, "min_circularity": 0.3, "filter_by_color": true, "blob_color": 0}}, {"name": "大斑点检测", "description": "检测大尺寸的斑点", "parameters": {"min_threshold": 50, "max_threshold": 220, "min_area": 500, "max_area": 5000, "min_circularity": 0.5, "filter_by_color": true, "blob_color": 0}}, {"name": "高精度圆形检测", "description": "检测高圆度的斑点", "parameters": {"min_threshold": 40, "max_threshold": 180, "min_area": 50, "max_area": 2000, "min_circularity": 0.8, "min_convexity": 0.9, "min_inertia_ratio": 0.5, "filter_by_color": true, "blob_color": 0}}]}