{"algorithm": "bilateral_filter", "name": "双边滤波模板", "description": "双边滤波算法的常用参数模板", "templates": [{"name": "轻度降噪", "description": "适用于轻微噪声的图像", "parameters": {"d": 5, "sigma_color": 50, "sigma_space": 50}}, {"name": "中度降噪", "description": "适用于中等噪声的图像", "parameters": {"d": 9, "sigma_color": 75, "sigma_space": 75}}, {"name": "强力降噪", "description": "适用于噪声较重的图像", "parameters": {"d": 15, "sigma_color": 100, "sigma_space": 100}}, {"name": "保边增强", "description": "在降噪的同时增强边缘", "parameters": {"d": 7, "sigma_color": 150, "sigma_space": 50}}]}