{"algorithm": "dimension_measurement", "name": "尺寸测量模板", "description": "尺寸测量算法的常用参数模板", "templates": [{"name": "像素测量", "description": "以像素为单位进行测量", "parameters": {"pixels_per_unit": 1.0, "unit": "pixel", "measurement_method": "bounding_rect", "min_contour_area": 100, "show_dimensions": true, "precision": 0}}, {"name": "毫米测量", "description": "以毫米为单位进行测量（需要校准）", "parameters": {"pixels_per_unit": 10.0, "unit": "mm", "measurement_method": "min_area_rect", "min_contour_area": 200, "show_dimensions": true, "precision": 2}}, {"name": "高精度卡尺测量", "description": "使用卡尺方法进行高精度测量", "parameters": {"pixels_per_unit": 5.0, "unit": "mm", "measurement_method": "caliper", "min_contour_area": 500, "show_dimensions": true, "precision": 3}}, {"name": "Feret直径测量", "description": "使用Feret直径进行形状分析", "parameters": {"pixels_per_unit": 2.0, "unit": "mm", "measurement_method": "feret_diameter", "min_contour_area": 300, "show_dimensions": true, "precision": 2}}]}