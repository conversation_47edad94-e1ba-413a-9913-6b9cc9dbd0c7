#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PyQt6 迁移验证测试脚本

验证 PyQt5 到 PyQt6 迁移是否成功
"""

import sys
import os
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pyqt6_imports():
    """测试 PyQt6 基础导入"""
    logger.info("🔍 测试 PyQt6 基础导入...")

    try:
        from PyQt6.QtCore import QT_VERSION_STR, Qt, QTimer
        from PyQt6.QtGui import QPixmap, QIcon, QFont
        from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout

        logger.success(f"✅ PyQt6 基础模块导入成功 (版本: {QT_VERSION_STR})")
        return True
    except ImportError as e:
        logger.error(f"❌ PyQt6 基础模块导入失败: {e}")
        return False

def test_opengl_support():
    """测试 OpenGL 支持"""
    logger.info("🔍 测试 OpenGL 支持...")

    try:
        # 尝试导入 PyQt6 的 OpenGL 模块
        from PyQt6 import QtOpenGL
        import OpenGL.GL as gl

        logger.success("✅ OpenGL 基础支持正常")

        # 尝试导入 OpenGL Widget（可能需要额外包）
        try:
            from PyQt6.QtOpenGLWidgets import QOpenGLWidget
            logger.success("✅ OpenGL Widget 支持正常")
        except ImportError:
            logger.warning("⚠️ OpenGL Widget 不可用（可能需要额外安装）")

        return True
    except ImportError as e:
        logger.error(f"❌ OpenGL 支持测试失败: {e}")
        return False

def test_pyqt6_tools():
    """测试 PyQt6 工具"""
    logger.info("🔍 测试 PyQt6 工具...")

    try:
        import qt6_applications
        logger.success("✅ PyQt6 工具可用")
        return True
    except ImportError as e:
        logger.error(f"❌ PyQt6 工具测试失败: {e}")
        return False

def test_enum_usage():
    """测试枚举值使用"""
    logger.info("🔍 测试 PyQt6 枚举值...")

    try:
        from PyQt6.QtCore import Qt

        # 测试对齐方式
        align_center = Qt.AlignmentFlag.AlignCenter
        align_left = Qt.AlignmentFlag.AlignLeft

        # 测试方向
        horizontal = Qt.Orientation.Horizontal
        vertical = Qt.Orientation.Vertical

        # 测试鼠标按钮
        left_button = Qt.MouseButton.LeftButton
        right_button = Qt.MouseButton.RightButton

        # 测试窗口类型
        frameless = Qt.WindowType.FramelessWindowHint

        logger.success("✅ PyQt6 枚举值使用正常")
        return True
    except Exception as e:
        logger.error(f"❌ PyQt6 枚举值测试失败: {e}")
        return False

def test_wirevsion_imports():
    """测试 WireVision 模块导入"""
    logger.info("🔍 测试 WireVision 模块导入...")

    try:
        from wirevsion.ui.modern_components import THEME_COLORS
        from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
        from wirevsion.ui.main_window import MainWindow

        logger.success("✅ WireVision 核心模块导入成功")
        return True
    except ImportError as e:
        logger.error(f"❌ WireVision 模块导入失败: {e}")
        return False

def test_simple_app():
    """测试简单应用程序创建"""
    logger.info("🔍 测试简单应用程序创建...")

    try:
        from PyQt6.QtWidgets import QApplication, QLabel
        from PyQt6.QtCore import Qt

        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # 创建简单窗口
        label = QLabel("PyQt6 迁移测试成功！")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setWindowTitle("PyQt6 测试")
        label.resize(300, 100)

        logger.success("✅ 简单应用程序创建成功")
        return True
    except Exception as e:
        logger.error(f"❌ 简单应用程序创建失败: {e}")
        return False

def test_opencv_integration():
    """测试 OpenCV 集成"""
    logger.info("🔍 测试 OpenCV 集成...")

    try:
        import cv2
        import numpy as np
        from PyQt6.QtGui import QImage

        # 创建测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image[:, :] = [255, 0, 0]  # 红色图像

        # 转换为 QImage
        height, width, channel = test_image.shape
        bytes_per_line = 3 * width
        q_image = QImage(test_image.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)

        # 验证图像创建成功
        if q_image.isNull():
            raise Exception("QImage 创建失败")

        logger.success("✅ OpenCV 集成正常")
        return True
    except Exception as e:
        logger.error(f"❌ OpenCV 集成测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 PyQt6 迁移验证测试开始")
    logger.info("=" * 60)

    tests = [
        ("PyQt6 基础导入", test_pyqt6_imports),
        ("OpenGL 支持", test_opengl_support),
        ("PyQt6 工具", test_pyqt6_tools),
        ("枚举值使用", test_enum_usage),
        ("WireVision 模块", test_wirevsion_imports),
        ("简单应用程序", test_simple_app),
        ("OpenCV 集成", test_opencv_integration),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))

        logger.info("-" * 40)

    # 显示测试结果摘要
    logger.info("\n📊 测试结果摘要")
    logger.info("=" * 60)

    passed = 0
    failed = 0

    for test_name, result in results:
        if result:
            logger.success(f"✅ {test_name}: 通过")
            passed += 1
        else:
            logger.error(f"❌ {test_name}: 失败")
            failed += 1

    logger.info("-" * 60)
    logger.info(f"📈 总计: {len(results)} 个测试")
    logger.info(f"✅ 通过: {passed} 个")
    logger.info(f"❌ 失败: {failed} 个")

    if failed == 0:
        logger.success("\n🎉 所有测试通过！PyQt6 迁移成功！")
        logger.info("\n🔧 后续步骤:")
        logger.info("1. 运行主应用程序: poetry run python run_app.py")
        logger.info("2. 测试所有功能模块")
        logger.info("3. 检查UI显示是否正常")
        return True
    else:
        logger.error(f"\n⚠️ 发现 {failed} 个问题需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
