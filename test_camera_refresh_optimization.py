#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机刷新优化测试脚本

用于测试相机缓存和防抖机制的优化效果
"""

import sys
import os
import time
import numpy as np
import cv2
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QPushButton, QVBoxLayout, QHBoxLayout, QLabel, QSplitter
from PyQt5.QtCore import Qt, QTimer
from loguru import logger

# 设置日志
logger.add("camera_refresh_test_{time}.log", rotation="100 MB")

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wirevsion.ui.camera_utils import CameraManager, UIRefresher, ImageDisplayManager

class CameraRefreshTestWindow(QMainWindow):
    """相机刷新优化测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("相机刷新优化测试")
        self.setGeometry(100, 100, 1000, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 控制面板
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        
        # 创建测试按钮
        self.get_frame_btn = QPushButton("获取单帧")
        self.get_frame_btn.clicked.connect(self.test_get_frame)
        control_layout.addWidget(self.get_frame_btn)
        
        self.test_cache_btn = QPushButton("测试缓存 (连续10次)")
        self.test_cache_btn.clicked.connect(self.test_frame_cache)
        control_layout.addWidget(self.test_cache_btn)
        
        self.continuous_btn = QPushButton("启动连续捕获")
        self.continuous_btn.clicked.connect(self.toggle_continuous_capture)
        control_layout.addWidget(self.continuous_btn)
        
        self.force_refresh_btn = QPushButton("强制刷新")
        self.force_refresh_btn.clicked.connect(self.force_refresh)
        control_layout.addWidget(self.force_refresh_btn)
        
        # 状态标签
        self.status_label = QLabel("准备测试...")
        self.status_label.setStyleSheet("font-size: 14px; color: blue;")
        control_layout.addWidget(self.status_label)
        
        main_layout.addWidget(control_panel)
        
        # 创建分割器，分隔图像区域和日志区域
        splitter = QSplitter(Qt.Vertical)
        
        # 图像区域
        self.image_widget = QWidget()
        image_layout = QVBoxLayout(self.image_widget)
        
        # 图像标签
        self.image_label = QLabel("等待图像...")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                border-radius: 4px;
                color: #aaaaaa;
                font-size: 16px;
            }
        """)
        self.image_label.setMinimumSize(640, 400)
        image_layout.addWidget(self.image_label)
        
        # 添加图像标签
        splitter.addWidget(self.image_widget)
        
        # 日志区域
        self.log_widget = QWidget()
        log_layout = QVBoxLayout(self.log_widget)
        
        # 日志标签标题
        log_title = QLabel("日志输出")
        log_title.setStyleSheet("font-size: 14px; font-weight: bold;")
        log_layout.addWidget(log_title)
        
        # 日志内容
        self.log_text = QLabel("等待测试开始...")
        self.log_text.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 5px;
                font-family: monospace;
                font-size: 12px;
            }
        """)
        self.log_text.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.log_text.setWordWrap(True)
        log_layout.addWidget(self.log_text)
        
        # 添加日志区域
        splitter.addWidget(self.log_widget)
        
        # 设置分割器初始大小
        splitter.setSizes([400, 200])
        
        # 添加分割器到主布局
        main_layout.addWidget(splitter)
        
        # 初始化相机管理器
        self.camera_manager = CameraManager()
        
        # 初始化图像显示管理器
        self.image_display_manager = ImageDisplayManager(
            image_view=self.image_label
        )
        
        # 连续捕获状态
        self.continuous_active = False
        self.continuous_timer = None
        self.capture_count = 0
        
        # 初始化日志
        self.log_entries = []
        self.add_log("系统初始化完成，准备测试相机刷新优化")
    
    def add_log(self, message):
        """添加日志条目"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        # 添加到日志列表
        self.log_entries.append(log_entry)
        
        # 只保留最近的10条日志
        if len(self.log_entries) > 10:
            self.log_entries = self.log_entries[-10:]
        
        # 更新日志显示
        self.log_text.setText("\n".join(self.log_entries))
        
        # 输出到logger
        logger.info(message)
    
    def test_get_frame(self):
        """测试获取单帧"""
        try:
            self.status_label.setText("正在获取相机帧...")
            self.add_log("开始获取相机帧")
            
            # 记录开始时间
            start_time = time.time()
            
            # 获取相机帧
            frame = self.camera_manager.get_frame()
            
            # 计算耗时
            elapsed = time.time() - start_time
            
            if frame is not None:
                # 转换为RGB格式
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 更新图像显示
                self.image_display_manager.update_display(rgb_frame)
                
                # 强制刷新
                self.force_refresh()
                
                # 更新状态
                avg_value = np.mean(frame)
                self.status_label.setText(f"已获取帧: 尺寸={frame.shape}, 平均值={avg_value:.2f}")
                
                # 添加日志
                self.add_log(f"获取帧成功: 尺寸={frame.shape}, 平均值={avg_value:.2f}, 耗时={elapsed:.3f}秒")
            else:
                self.status_label.setText("获取帧失败")
                self.add_log(f"获取帧失败，耗时={elapsed:.3f}秒")
                
        except Exception as e:
            self.status_label.setText(f"错误: {str(e)}")
            self.add_log(f"获取帧出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    def test_frame_cache(self):
        """测试帧缓存"""
        try:
            self.status_label.setText("测试帧缓存中...")
            self.add_log("开始测试帧缓存，连续获取10次帧")
            
            # 禁用按钮
            self.test_cache_btn.setEnabled(False)
            
            # 预先获取一次帧，确保缓存有效
            frame = self.camera_manager.get_frame()
            if frame is not None:
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                self.image_display_manager.update_display(rgb_frame)
                self.force_refresh()
                self.add_log(f"预先获取帧成功: 尺寸={frame.shape}")
            
            # 设置定时器进行10次测试
            self.cache_test_count = 0
            self.cache_test_timer = QTimer(self)
            self.cache_test_timer.timeout.connect(self.run_cache_test_step)
            self.cache_test_timer.start(200)  # 每200毫秒测试一次
            
        except Exception as e:
            self.status_label.setText(f"错误: {str(e)}")
            self.add_log(f"测试帧缓存出错: {str(e)}")
            self.test_cache_btn.setEnabled(True)
            import traceback
            logger.error(traceback.format_exc())
    
    def run_cache_test_step(self):
        """执行一步缓存测试"""
        try:
            self.cache_test_count += 1
            
            # 记录开始时间
            start_time = time.time()
            
            # 获取相机帧
            frame = self.camera_manager.get_frame()
            
            # 计算耗时
            elapsed = time.time() - start_time
            
            if frame is not None:
                # 转换为RGB格式
                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 更新图像显示
                self.image_display_manager.update_display(rgb_frame)
                
                # 强制刷新
                self.force_refresh()
                
                # 判断是否使用了缓存
                cache_status = "使用缓存" if elapsed < 0.05 else "重新获取"
                
                # 更新状态
                self.status_label.setText(f"测试 {self.cache_test_count}/10: {cache_status}, 耗时={elapsed:.3f}秒")
                
                # 添加日志
                self.add_log(f"缓存测试 {self.cache_test_count}/10: {cache_status}, 耗时={elapsed:.3f}秒")
            else:
                self.status_label.setText(f"测试 {self.cache_test_count}/10: 获取帧失败")
                self.add_log(f"缓存测试 {self.cache_test_count}/10: 获取帧失败, 耗时={elapsed:.3f}秒")
            
            # 如果已完成10次测试，停止定时器
            if self.cache_test_count >= 10:
                self.cache_test_timer.stop()
                self.test_cache_btn.setEnabled(True)
                self.add_log("帧缓存测试完成")
                
        except Exception as e:
            self.status_label.setText(f"错误: {str(e)}")
            self.add_log(f"缓存测试步骤出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 停止测试
            if hasattr(self, 'cache_test_timer') and self.cache_test_timer.isActive():
                self.cache_test_timer.stop()
            
            self.test_cache_btn.setEnabled(True)
    
    def toggle_continuous_capture(self):
        """切换连续捕获状态"""
        if self.continuous_active:
            # 停止连续捕获
            if hasattr(self, 'continuous_timer') and self.continuous_timer is not None:
                self.killTimer(self.continuous_timer)
                self.continuous_timer = None
            
            self.continuous_active = False
            self.continuous_btn.setText("启动连续捕获")
            self.status_label.setText(f"连续捕获已停止，共捕获 {self.capture_count} 帧")
            self.add_log(f"连续捕获已停止，共捕获 {self.capture_count} 帧")
            
        else:
            # 开始连续捕获
            self.continuous_active = True
            self.capture_count = 0
            
            # 添加防抖机制：记录上次执行时间
            self._last_capture_time = time.time()
            
            # 使用50毫秒的捕获间隔 - 故意设置很高的刷新率来测试防抖
            self.continuous_timer = self.startTimer(50)
            
            self.continuous_btn.setText("停止连续捕获")
            self.status_label.setText("连续捕获中...")
            self.add_log("开始连续捕获，帧率=50ms/帧 (用于测试防抖)")
    
    def timerEvent(self, event):
        """定时器事件处理"""
        if hasattr(self, 'continuous_timer') and event.timerId() == self.continuous_timer:
            try:
                # 防抖控制
                current_time = time.time()
                min_interval = 0.2  # 最小执行间隔（秒）
                
                if hasattr(self, '_last_capture_time') and current_time - self._last_capture_time < min_interval:
                    # 帧率过高，跳过本次执行
                    return
                
                # 更新执行时间
                self._last_capture_time = current_time
                
                # 获取相机帧
                frame = self.camera_manager.get_frame()
                
                if frame is not None:
                    # 转换为RGB格式
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # 更新图像显示
                    self.image_display_manager.update_display(rgb_frame)
                    
                    # 强制刷新
                    self.force_refresh()
                    
                    # 更新计数
                    self.capture_count += 1
                    
                    # 每5帧更新一次状态
                    if self.capture_count % 5 == 0:
                        self.status_label.setText(f"连续捕获中: 已捕获 {self.capture_count} 帧")
                        
                        # 每10帧添加一次日志
                        if self.capture_count % 10 == 0:
                            self.add_log(f"连续捕获: 已捕获 {self.capture_count} 帧，最新间隔={current_time - getattr(self, '_last_log_time', current_time-1):.3f}秒")
                            self._last_log_time = current_time
                
            except Exception as e:
                self.status_label.setText(f"错误: {str(e)}")
                self.add_log(f"连续捕获出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
    
    def force_refresh(self):
        """强制刷新图像显示"""
        if hasattr(self, 'image_display_manager'):
            self.image_display_manager.force_refresh()
            
            # 处理事件队列
            QApplication.processEvents()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = CameraRefreshTestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 