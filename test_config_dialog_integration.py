#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试现代化节点配置对话框集成
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import QPointF
from wirevsion.ui.modern_workflow_editor import ModernFlowNode
from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试现代化节点配置对话框")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        test_btn = QPushButton("打开现代化配置对话框")
        test_btn.clicked.connect(self.open_config_dialog)
        layout.addWidget(test_btn)
        
        # 创建测试节点
        self.test_node = ModernFlowNode('test_canny', 'process', 'Canny边缘检测', QPointF(0, 0))
    
    def open_config_dialog(self):
        """打开配置对话框"""
        try:
            print("正在打开现代化配置对话框...")
            
            # 创建配置对话框
            dialog = ModernNodeConfigDialog(self.test_node, self)
            
            # 连接信号
            dialog.config_changed.connect(self.on_config_changed)
            
            # 显示对话框
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("配置对话框已确认")
            else:
                print("配置对话框已取消")
                
        except Exception as e:
            print(f"打开配置对话框失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_config_changed(self, config):
        """配置改变事件"""
        print(f"配置已改变: {config}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
