#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVsion主入口程序

启动现代化UI界面
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon
from loguru import logger

# 配置日志
logger.add("logs/wirevsion.log", rotation="10 MB")


def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("WireVsion")
    app.setOrganizationName("WireVsion")
    
    # 启用高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 设置应用程序图标
    app.setWindowIcon(QIcon("resources/icons/logo.svg"))
    
    # 设置全局样式表
    from wirevsion.ui.modern_components import THEME_COLORS
    app.setStyleSheet(f"""
        QMainWindow, QWidget {{
            background-color: {THEME_COLORS["dark_bg_app"]};
            color: {THEME_COLORS["text_primary"]};
        }}
        QScrollBar:vertical {{
            background-color: {THEME_COLORS["dark_bg_app"]};
            width: 8px;
            margin: 0px;
            border-radius: 4px;
        }}
        QScrollBar::handle:vertical {{
            background-color: {THEME_COLORS["dark_border_primary"]};
            border-radius: 4px;
            min-height: 20px;
        }}
        QScrollBar::handle:vertical:hover {{
            background-color: {THEME_COLORS["primary"]};
        }}
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
            background: none;
        }}
        QScrollBar:horizontal {{
            background-color: {THEME_COLORS["dark_bg_app"]};
            height: 8px;
            margin: 0px;
            border-radius: 4px;
        }}
        QScrollBar::handle:horizontal {{
            background-color: {THEME_COLORS["dark_border_primary"]};
            border-radius: 4px;
            min-width: 20px;
        }}
        QScrollBar::handle:horizontal:hover {{
            background-color: {THEME_COLORS["primary"]};
        }}
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            width: 0px;
        }}
        QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
            background: none;
        }}
        QToolTip {{
            background-color: {THEME_COLORS["dark_bg_card"]};
            color: {THEME_COLORS["text_primary"]};
            border: 1px solid {THEME_COLORS["dark_border_primary"]};
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 12px;
        }}
        QTabWidget::pane {{
            border: 1px solid {THEME_COLORS["dark_border_secondary"]};
            background-color: {THEME_COLORS["dark_bg_card"]};
            border-radius: 4px;
        }}
        QTabBar::tab {{
            background-color: {THEME_COLORS["dark_bg_app"]};
            color: {THEME_COLORS["text_secondary"]};
            padding: 8px 12px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            border: 1px solid {THEME_COLORS["dark_border_secondary"]};
            border-bottom: none;
            margin-right: 2px;
        }}
        QTabBar::tab:selected {{
            background-color: {THEME_COLORS["dark_bg_card"]};
            color: {THEME_COLORS["text_primary"]};
            border-bottom: 2px solid {THEME_COLORS["primary"]};
        }}
        QTabBar::tab:hover:!selected {{
            background-color: {THEME_COLORS["dark_surface_hover"]};
        }}
    """)
    
    # 导入主应用程序
    from wirevsion.ui.modern_main_application import ModernMainApplication
    
    # 创建并显示主窗口
    logger.info("启动WireVsion...")
    window = ModernMainApplication()
    window.show()
    
    # 运行应用程序
    logger.info("WireVsion启动完成")
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 