#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI修复验证测试脚本

测试以下修复内容：
1. 删除按钮样式修复（标红显示）
2. 算法配置界面预览功能修复
3. 程序启动时相机自动预览功能修复
"""

import sys
import os
import time
from loguru import logger

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont

try:
    from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
    from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog
    from wirevsion.ui.modern_components import THEME_COLORS
    from wirevsion.camera.camera_manager import CameraManager
    from wirevsion.ui.main_window import MainWindow
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    sys.exit(1)


class UIFixesTestWindow(QMainWindow):
    """UI修复测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("WireVision UI修复验证测试")
        self.setGeometry(100, 100, 1200, 800)

        # 设置主题样式
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {THEME_COLORS["dark_bg_app"]};
                color: {THEME_COLORS["text_primary"]};
            }}
        """)

        self.setup_ui()
        self.test_results = []

    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 标题
        title_label = QLabel("🔧 WireVision UI修复验证测试")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_title"]};
                padding: 20px;
                background-color: {THEME_COLORS["dark_bg_card"]};
                border-radius: 8px;
                margin-bottom: 10px;
            }}
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 测试按钮区域
        button_layout = QVBoxLayout()

        # 测试1: 删除按钮样式
        test1_btn = QPushButton("🗑️ 测试删除按钮样式修复")
        test1_btn.setStyleSheet(self._get_test_button_style())
        test1_btn.setMinimumHeight(50)
        test1_btn.clicked.connect(self.test_delete_button_style)
        button_layout.addWidget(test1_btn)

        # 测试2: 算法配置预览功能
        test2_btn = QPushButton("🖼️ 测试算法配置预览功能")
        test2_btn.setStyleSheet(self._get_test_button_style())
        test2_btn.setMinimumHeight(50)
        test2_btn.clicked.connect(self.test_algorithm_preview)
        button_layout.addWidget(test2_btn)

        # 测试3: 相机自动预览功能
        test3_btn = QPushButton("📷 测试相机自动预览功能")
        test3_btn.setStyleSheet(self._get_test_button_style())
        test3_btn.setMinimumHeight(50)
        test3_btn.clicked.connect(self.test_camera_auto_preview)
        button_layout.addWidget(test3_btn)

        # 测试4: 综合测试
        test4_btn = QPushButton("🚀 运行综合测试")
        test4_btn.setStyleSheet(self._get_test_button_style("success"))
        test4_btn.setMinimumHeight(50)
        test4_btn.clicked.connect(self.run_comprehensive_test)
        button_layout.addWidget(test4_btn)

        layout.addLayout(button_layout)

        # 结果显示区域
        self.result_label = QLabel("等待测试...")
        self.result_label.setStyleSheet(f"""
            QLabel {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }}
        """)
        self.result_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.result_label.setWordWrap(True)
        self.result_label.setMinimumHeight(200)
        layout.addWidget(self.result_label)

    def _get_test_button_style(self, button_type="primary"):
        """获取测试按钮样式"""
        color_map = {
            "primary": THEME_COLORS["primary"],
            "success": THEME_COLORS["success"],
            "warning": THEME_COLORS["warning"],
            "danger": THEME_COLORS["danger"]
        }
        
        bg_color = color_map.get(button_type, THEME_COLORS["primary"])
        
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {THEME_COLORS["text_on_primary_bg"]};
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self._lighten_color(bg_color, 10)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(bg_color, 10)};
            }}
        """

    def _lighten_color(self, color, amount):
        """使颜色变亮"""
        from PyQt5.QtGui import QColor
        qcolor = QColor(color)
        return qcolor.lighter(100 + amount).name()

    def _darken_color(self, color, amount):
        """使颜色变暗"""
        from PyQt5.QtGui import QColor
        qcolor = QColor(color)
        return qcolor.darker(100 + amount).name()

    def log_result(self, message):
        """记录测试结果"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.test_results.append(log_message)
        
        # 更新显示
        result_text = "\n".join(self.test_results[-20:])  # 只显示最近20条
        self.result_label.setText(result_text)
        
        # 滚动到底部
        QApplication.processEvents()

    def test_delete_button_style(self):
        """测试删除按钮样式修复"""
        self.log_result("\n🗑️ 测试1: 删除按钮样式修复")
        self.log_result("-" * 40)

        try:
            # 创建工作流编辑器实例
            workflow_editor = ModernWorkflowEditor()
            
            # 检查清除按钮样式
            if hasattr(workflow_editor, 'clear_display_btn'):
                clear_btn = workflow_editor.clear_display_btn
                style = clear_btn.styleSheet()
                
                # 检查是否包含danger颜色
                if THEME_COLORS["danger"] in style:
                    self.log_result("✅ 删除按钮使用了正确的danger主题色")
                else:
                    self.log_result("❌ 删除按钮未使用danger主题色")
                
                # 检查是否有hover和pressed状态
                if "hover" in style and "pressed" in style:
                    self.log_result("✅ 删除按钮包含交互状态样式")
                else:
                    self.log_result("❌ 删除按钮缺少交互状态样式")
                
                self.log_result("✅ 删除按钮样式修复验证完成")
            else:
                self.log_result("❌ 未找到清除显示按钮")

        except Exception as e:
            self.log_result(f"❌ 删除按钮样式测试失败: {e}")

    def test_algorithm_preview(self):
        """测试算法配置预览功能"""
        self.log_result("\n🖼️ 测试2: 算法配置预览功能")
        self.log_result("-" * 40)

        try:
            # 创建一个模拟的节点对象
            class MockNode:
                def __init__(self):
                    self.node_id = "test_color_detection"
                    self.node_type = "process"
                    self.title = "颜色检测"

            mock_node = MockNode()

            # 创建配置对话框
            dialog = ModernNodeConfigDialog(mock_node)

            # 检查预览相关方法
            preview_methods = [
                '_show_preview_result',
                '_preview_algorithm',
                '_preview_color_detection',
                '_preview_edge_detection',
                '_preview_gaussian_blur',
                '_preview_generic_algorithm'
            ]

            for method_name in preview_methods:
                if hasattr(dialog, method_name):
                    self.log_result(f"✅ {method_name} 方法存在")
                else:
                    self.log_result(f"❌ {method_name} 方法缺失")

            # 检查ROI视图
            if hasattr(dialog, 'roi_view'):
                self.log_result("✅ ROI视图组件存在")
                
                # 检查ROI视图的set_image方法
                if hasattr(dialog.roi_view, 'set_image'):
                    self.log_result("✅ ROI视图支持图像设置")
                else:
                    self.log_result("❌ ROI视图缺少set_image方法")
            else:
                self.log_result("❌ ROI视图组件缺失")

            self.log_result("✅ 算法配置预览功能验证完成")

        except Exception as e:
            self.log_result(f"❌ 算法配置预览测试失败: {e}")

    def test_camera_auto_preview(self):
        """测试相机自动预览功能"""
        self.log_result("\n📷 测试3: 相机自动预览功能")
        self.log_result("-" * 40)

        try:
            # 测试相机管理器
            camera_manager = CameraManager()
            self.log_result("✅ 相机管理器创建成功")

            # 测试主窗口相机设置功能
            main_window = MainWindow()
            
            if hasattr(main_window, 'set_camera_manager'):
                main_window.set_camera_manager(camera_manager)
                self.log_result("✅ 主窗口支持相机管理器设置")
            else:
                self.log_result("❌ 主窗口缺少set_camera_manager方法")

            if hasattr(main_window, 'start_camera_preview'):
                self.log_result("✅ 主窗口支持启动相机预览")
            else:
                self.log_result("❌ 主窗口缺少start_camera_preview方法")

            # 测试工作流编辑器相机初始化
            workflow_editor = ModernWorkflowEditor()
            
            if hasattr(workflow_editor, '_init_camera_for_realtime_display'):
                self.log_result("✅ 工作流编辑器支持相机实时显示初始化")
            else:
                self.log_result("❌ 工作流编辑器缺少相机实时显示初始化方法")

            if hasattr(workflow_editor, '_show_initial_camera_frame'):
                self.log_result("✅ 工作流编辑器支持显示初始相机帧")
            else:
                self.log_result("❌ 工作流编辑器缺少显示初始相机帧方法")

            self.log_result("✅ 相机自动预览功能验证完成")

        except Exception as e:
            self.log_result(f"❌ 相机自动预览测试失败: {e}")

    def run_comprehensive_test(self):
        """运行综合测试"""
        self.log_result("\n🚀 综合测试开始")
        self.log_result("=" * 50)

        # 清空之前的结果
        self.test_results = []

        # 依次运行所有测试
        self.test_delete_button_style()
        QTimer.singleShot(500, self.test_algorithm_preview)
        QTimer.singleShot(1000, self.test_camera_auto_preview)
        QTimer.singleShot(1500, self._show_final_results)

    def _show_final_results(self):
        """显示最终测试结果"""
        self.log_result("\n📊 测试总结")
        self.log_result("=" * 50)
        
        success_count = len([r for r in self.test_results if "✅" in r])
        failure_count = len([r for r in self.test_results if "❌" in r])
        
        self.log_result(f"✅ 成功项目: {success_count}")
        self.log_result(f"❌ 失败项目: {failure_count}")
        
        if failure_count == 0:
            self.log_result("\n🎉 所有测试通过！UI修复成功！")
        else:
            self.log_result(f"\n⚠️ 发现 {failure_count} 个问题需要修复")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("WireVision UI修复测试")
    app.setApplicationVersion("1.0.0")
    
    # 创建并显示测试窗口
    window = UIFixesTestWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
