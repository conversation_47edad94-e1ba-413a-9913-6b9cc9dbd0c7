#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像处理器模块
整合模板匹配、轮廓匹配和颜色检测功能
"""

import cv2
import numpy as np
from loguru import logger
from typing import Tuple, Dict, List, Any, Optional, Union

from wirevsion.config.workflow_config import WorkflowConfig
from wirevsion.image_processing.template_matching import TemplateMatcher
from wirevsion.image_processing.contour_matching import ContourMatcher
from wirevsion.image_processing.color_detection import ColorDetector


class MatcherFactory:
    """
    匹配器工厂类
    根据配置创建适合的匹配器
    """
    
    @staticmethod
    def create_matcher(matcher_type: str = "template") -> Union[TemplateMatcher, ContourMatcher]:
        """
        创建匹配器
        
        Args:
            matcher_type: 匹配器类型，可选值：template(传统模板匹配)，contour(轮廓匹配)
            
        Returns:
            Union[TemplateMatcher, ContourMatcher]: 创建的匹配器对象
        """
        if matcher_type.lower() == "contour":
            return ContourMatcher()
        else:
            # 默认使用传统模板匹配
            return TemplateMatcher()


class ImageProcessor:
    """
    图像处理器
    整合模板匹配、轮廓匹配和颜色检测功能
    """
    
    def __init__(self, matcher_type: str = "template"):
        """
        初始化图像处理器
        
        Args:
            matcher_type: 匹配器类型，可选值：template(传统模板匹配)，contour(轮廓匹配)
        """
        # 创建匹配器
        self.matcher_type = matcher_type
        self.matcher = MatcherFactory.create_matcher(matcher_type)
        self.color_detector = ColorDetector()
        
        # 当前工作流程配置
        self.workflow = None
        
        # 模板图像
        self.template_image = None
        
        # 期望的颜色序列
        self.expected_colors = None
        
        # 是否使用位置修正
        self.use_position_correction = True
        
        # ROI区域，默认为None表示使用整个图像
        self.roi_rect = None
    
    def set_matcher_type(self, matcher_type: str):
        """
        设置匹配器类型并重新创建匹配器
        
        Args:
            matcher_type: 匹配器类型，可选值：template(传统模板匹配)，contour(轮廓匹配)
        """
        if matcher_type != self.matcher_type:
            self.matcher_type = matcher_type
            self.matcher = MatcherFactory.create_matcher(matcher_type)
            
            # 如果已经设置了模板和配置，重新应用到新匹配器
            if self.template_image is not None and self.workflow and self.workflow.template:
                self.matcher.set_template(self.template_image, self.workflow.template)
            
            # 如果设置了ROI，应用到新匹配器
            if self.roi_rect:
                self.matcher.set_roi(*self.roi_rect)
    
    def set_roi(self, x: int, y: int, width: int, height: int):
        """
        设置ROI区域，匹配将仅在此区域内进行
        
        Args:
            x: ROI左上角x坐标
            y: ROI左上角y坐标
            width: ROI宽度
            height: ROI高度
        """
        self.roi_rect = (x, y, width, height)
        if hasattr(self.matcher, 'set_roi'):
            self.matcher.set_roi(x, y, width, height)
    
    def clear_roi(self):
        """清除ROI区域设置，恢复为整图匹配"""
        self.roi_rect = None
        if hasattr(self.matcher, 'clear_roi'):
            self.matcher.clear_roi()
    
    def set_workflow(self, workflow: WorkflowConfig):
        """
        设置工作流程配置
        
        Args:
            workflow: 工作流程配置
        """
        self.workflow = workflow
    
    def set_template(self, template_image: np.ndarray):
        """
        设置模板图像
        
        Args:
            template_image: 模板图像
        """
        self.template_image = template_image
        
        if self.workflow and self.workflow.template:
            self.matcher.set_template(template_image, self.workflow.template)
    
    def set_expected_colors(self, colors: List[List[int]]):
        """
        设置期望的颜色序列
        
        Args:
            colors: 期望颜色列表，BGR格式
        """
        self.expected_colors = colors
    
    def set_position_correction(self, enabled: bool):
        """
        设置是否使用位置修正
        
        Args:
            enabled: 是否启用位置修正
        """
        self.use_position_correction = enabled
    
    def process(self, image: np.ndarray) -> Tuple[bool, Dict[str, Any]]:
        """
        处理图像，执行匹配和颜色检测
        
        Args:
            image: 输入图像
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (处理结果标志, 处理结果信息)
        """
        if image is None:
            return False, {"error": "图像为空"}
        
        if self.workflow is None:
            return False, {"error": "未设置工作流程配置"}
        
        try:
            result = {
                "template_matched": False,
                "template_info": {},
                "roi_detection": {
                    "all_matched": False,
                    "results": []
                },
                "position_corrected": False,
                "offset": (0, 0),
                "final_result": False,
                "matcher_type": self.matcher_type
            }
            
            # 位置偏移量
            offset = (0, 0)
            
            # 执行模板匹配或轮廓匹配
            if self.template_image is not None and self.workflow.template is not None:
                template_matched, template_info = self.matcher.match(image)
                result["template_matched"] = template_matched
                result["template_info"] = template_info
                
                # 如果匹配成功且启用了位置修正，计算位置偏移量
                if template_matched and self.use_position_correction:
                    # 计算匹配位置与配置中ROI位置的偏移量
                    match_x = template_info["x"]
                    match_y = template_info["y"]
                    
                    # 这里假设ROI基准位置为(0,0)，如果有其他基准位置可调整
                    offset = (match_x, match_y)
                    result["position_corrected"] = True
                    result["offset"] = offset
                
                # 如果匹配成功（无论是否进行位置修正），则执行颜色检测
                if template_matched and self.workflow.rois:
                    # 使用期望颜色序列执行颜色顺序检测
                    if self.expected_colors:
                        roi_matched, roi_results = self.color_detector.detect_multi_roi_with_sequence(
                            image, self.workflow.rois, offset, self.expected_colors
                        )
                    else:
                        # 使用普通颜色检测
                        roi_matched, roi_results = self.color_detector.detect_multi_roi(
                            image, self.workflow.rois, offset
                        )
                        
                    result["roi_detection"]["all_matched"] = roi_matched
                    result["roi_detection"]["results"] = roi_results
                    
                    # 设置最终结果
                    result["final_result"] = template_matched and roi_matched
                else:
                    result["final_result"] = template_matched
            else:
                # 如果没有模板，只执行颜色检测
                if self.workflow.rois:
                    # 使用期望颜色序列执行颜色顺序检测
                    if self.expected_colors:
                        roi_matched, roi_results = self.color_detector.detect_multi_roi_with_sequence(
                            image, self.workflow.rois, offset, self.expected_colors
                        )
                    else:
                        # 使用普通颜色检测
                        roi_matched, roi_results = self.color_detector.detect_multi_roi(
                            image, self.workflow.rois, offset
                        )
                        
                    result["roi_detection"]["all_matched"] = roi_matched
                    result["roi_detection"]["results"] = roi_results
                    
                    # 设置最终结果
                    result["final_result"] = roi_matched
            
            return result["final_result"], result
        
        except Exception as e:
            logger.error(f"图像处理出错: {str(e)}")
            return False, {"error": str(e)}
    
    def draw_result(self, image: np.ndarray, result: Dict[str, Any]) -> np.ndarray:
        """
        绘制处理结果
        
        Args:
            image: 输入图像
            result: 处理结果信息
            
        Returns:
            np.ndarray: 绘制结果图像
        """
        if image is None or "error" in result:
            return image.copy() if image is not None else None
        
        # 复制图像
        result_image = image.copy()
        
        # 绘制匹配结果
        if result["template_matched"] and "template_info" in result:
            result_image = self.matcher.draw_match_result(result_image, result["template_info"])
        
        # 绘制ROI检测结果
        if "roi_detection" in result and "results" in result["roi_detection"]:
            roi_results = result["roi_detection"]["results"]
            result_image = self.color_detector.draw_detection_result(result_image, roi_results)
        
        # 绘制位置修正信息
        if result.get("position_corrected", False):
            offset_x, offset_y = result.get("offset", (0, 0))
            cv2.putText(
                result_image, f"偏移量: ({offset_x}, {offset_y})", (10, 60),
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1
            )
        
        # 绘制匹配器类型
        matcher_type = result.get("matcher_type", self.matcher_type)
        matcher_type_text = "轮廓匹配" if matcher_type == "contour" else "模板匹配"
        cv2.putText(
            result_image, f"匹配方式: {matcher_type_text}", (10, 90),
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1
            )
        
        # 绘制最终结果
        final_result = result.get("final_result", False)
        result_text = "检测通过" if final_result else "检测失败"
        result_color = (0, 255, 0) if final_result else (0, 0, 255)
        
        # 在图像顶部绘制结果文本
        cv2.putText(
            result_image, result_text, (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX, 1, result_color, 2
        )
        
        return result_image
