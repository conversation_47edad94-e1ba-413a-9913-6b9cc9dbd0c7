#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
颜色检测处理模块
提供颜色检测的图像处理功能
"""

import cv2
import numpy as np
from loguru import logger
from typing import Tuple, Dict, Any, List, Optional

from wirevsion.config.workflow_config import ROIConfig


class ColorDetector:
    """
    颜色检测器
    提供颜色检测的图像处理功能
    """
    
    def __init__(self):
        """初始化颜色检测器"""
        pass
    
    def detect_roi(self, image: np.ndarray, roi: ROIConfig, offset: Tuple[int, int] = (0, 0)) -> <PERSON><PERSON>[bool, Dict[str, Any]]:
        """
        在指定ROI区域内进行颜色检测
        
        Args:
            image: 输入图像
            roi: ROI配置
            offset: ROI偏移量(x偏移, y偏移)，用于相对于模板位置调整ROI
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (检测结果标志, 检测结果信息)
        """
        if image is None:
            return False, {"error": "图像为空"}
        
        try:
            # 应用偏移量调整ROI位置
            offset_x, offset_y = offset
            roi_x, roi_y = roi.x + offset_x, roi.y + offset_y
            roi_w, roi_h = roi.width, roi.height
            
            # 确保ROI在图像范围内
            h, w = image.shape[:2]
            roi_x = max(0, min(roi_x, w - 1))
            roi_y = max(0, min(roi_y, h - 1))
            roi_w = max(1, min(roi_w, w - roi_x))
            roi_h = max(1, min(roi_h, h - roi_y))
            
            # 提取ROI区域
            roi_img = image[roi_y:roi_y + roi_h, roi_x:roi_x + roi_w]
            
            # 创建颜色掩码
            lower = np.array(roi.color_lower, dtype=np.uint8)
            upper = np.array(roi.color_upper, dtype=np.uint8)
            mask = cv2.inRange(roi_img, lower, upper)
            
            # 计算颜色区域比例
            total_pixels = roi_w * roi_h
            matched_pixels = cv2.countNonZero(mask)
            ratio = matched_pixels / total_pixels if total_pixels > 0 else 0
            
            # 构建结果信息
            result = {
                "roi_name": roi.name,
                "roi_rect": (roi_x, roi_y, roi_w, roi_h),
                "masked_pixels": matched_pixels,
                "total_pixels": total_pixels,
                "match_ratio": ratio,
                "mask": mask,
                "original_rect": (roi.x, roi.y, roi.width, roi.height),
                "offset": offset
            }
            
            # 检测结果（比例大于50%视为匹配）
            matched = ratio > 0.5
            
            return matched, result
        
        except Exception as e:
            logger.error(f"颜色检测出错: {str(e)}")
            return False, {"error": str(e)}
    
    def detect_multi_roi(self, image: np.ndarray, rois: List[ROIConfig], offset: Tuple[int, int] = (0, 0)) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        对多个ROI区域进行颜色检测
        
        Args:
            image: 输入图像
            rois: ROI配置列表
            offset: ROI偏移量(x偏移, y偏移)，用于相对于模板位置调整ROI
            
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (总体检测结果标志, 各ROI检测结果信息列表)
        """
        results = []
        all_matched = True
        
        for roi in rois:
            matched, result = self.detect_roi(image, roi, offset)
            result["matched"] = matched
            results.append(result)
            
            # 如果有一个ROI不匹配，则总体结果为不匹配
            if not matched:
                all_matched = False
        
        return all_matched, results
    
    def detect_multi_roi_with_sequence(self, image: np.ndarray, rois: List[ROIConfig], offset: Tuple[int, int] = (0, 0), expected_colors: List[List[int]] = None) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        对多个ROI区域进行颜色顺序检测
        
        Args:
            image: 输入图像
            rois: ROI配置列表
            offset: ROI偏移量(x偏移, y偏移)，用于相对于模板位置调整ROI
            expected_colors: 期望的颜色列表，如果为None则使用每个ROI自己的颜色范围
            
        Returns:
            Tuple[bool, List[Dict[str, Any]]]: (总体检测结果标志, 各ROI检测结果信息列表)
        """
        results = []
        all_matched = True
        sequence_correct = True
        
        # 先对每个ROI进行单独检测
        for i, roi in enumerate(rois):
            matched, result = self.detect_roi(image, roi, offset)
            
            # 如果提供了期望颜色序列，则进行额外检查
            if expected_colors and i < len(expected_colors):
                # 获取ROI区域的平均颜色
                roi_x, roi_y, roi_w, roi_h = result["roi_rect"]
                roi_img = image[roi_y:roi_y + roi_h, roi_x:roi_x + roi_w]
                avg_color = np.mean(roi_img, axis=(0, 1)).astype(int)
                
                # 计算与期望颜色的差距
                expected = np.array(expected_colors[i])
                color_diff = np.linalg.norm(avg_color - expected)
                
                # 添加颜色序列检测结果
                result["avg_color"] = avg_color.tolist()
                result["expected_color"] = expected_colors[i]
                result["color_diff"] = color_diff
                result["color_matched"] = color_diff < 50  # 设定阈值50，可根据需要调整
                
                # 更新匹配结果
                sequence_matched = result["color_matched"]
                if not sequence_matched:
                    sequence_correct = False
                
                # 最终匹配结果需要同时满足颜色范围和颜色序列
                matched = matched and sequence_matched
            
            result["matched"] = matched
            results.append(result)
            
            # 如果有一个ROI不匹配，则总体结果为不匹配
            if not matched:
                all_matched = False
        
        # 添加序列检测结果
        if expected_colors:
            for result in results:
                result["sequence_correct"] = sequence_correct
        
        return all_matched, results
    
    def draw_detection_result(self, image: np.ndarray, results: List[Dict[str, Any]], matched_color: Tuple[int, int, int] = (0, 255, 0), unmatched_color: Tuple[int, int, int] = (0, 0, 255), thickness: int = 2) -> np.ndarray:
        """
        在图像上绘制检测结果
        
        Args:
            image: 输入图像
            results: 检测结果信息列表
            matched_color: 匹配区域颜色，默认为绿色
            unmatched_color: 不匹配区域颜色，默认为红色
            thickness: 线条粗细
            
        Returns:
            np.ndarray: 绘制结果图像
        """
        result_image = image.copy()
        
        for result in results:
            if "error" in result:
                continue
                
            roi_rect = result.get("roi_rect")
            if roi_rect:
                roi_x, roi_y, roi_w, roi_h = roi_rect
                
                # 根据匹配结果选择颜色
                color = matched_color if result.get("matched", False) else unmatched_color
                
                # 绘制矩形框
                cv2.rectangle(result_image, (roi_x, roi_y), (roi_x + roi_w, roi_y + roi_h), color, thickness)
                
                # 绘制ROI名称和匹配比例
                roi_name = result.get("roi_name", "未命名")
                match_ratio = result.get("match_ratio", 0)
                text = f"{roi_name}: {match_ratio:.2f}"
                
                # 如果有颜色序列检测结果，添加到文本中
                if "avg_color" in result:
                    avg_color = result.get("avg_color")
                    text += f" BGR:{avg_color}"
                
                cv2.putText(result_image, text, (roi_x, roi_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return result_image
