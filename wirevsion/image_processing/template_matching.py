#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模板匹配处理模块
提供模板匹配的图像处理功能
"""

import cv2
import numpy as np
from loguru import logger
from typing import Tuple, Dict, Any, List, Optional

from wirevsion.config.workflow_config import TemplateConfig


class TemplateMatcher:
    """
    模板匹配器
    提供模板匹配的图像处理功能
    """
    
    def __init__(self):
        """初始化模板匹配器"""
        # 模板图像
        self.template_image = None
        
        # 模板配置
        self.template_config = None
    
        # ROI区域，默认为None表示使用整个图像
        self.roi_rect = None
    
    def set_template(self, template_image: np.ndarray, template_config: TemplateConfig):
        """
        设置模板图像和配置
        
        Args:
            template_image: 模板图像
            template_config: 模板配置
        """
        self.template_image = template_image
        self.template_config = template_config
    
    def set_roi(self, x: int, y: int, width: int, height: int):
        """
        设置ROI区域，匹配将仅在此区域内进行
        
        Args:
            x: ROI左上角x坐标
            y: ROI左上角y坐标
            width: ROI宽度
            height: ROI高度
        """
        self.roi_rect = (x, y, width, height)
    
    def clear_roi(self):
        """清除ROI区域设置，恢复为整图匹配"""
        self.roi_rect = None
    
    def match(self, image: np.ndarray) -> Tuple[bool, Dict[str, Any]]:
        """
        在图像中匹配模板
        
        Args:
            image: 输入图像
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (匹配结果标志, 匹配结果信息)
        """
        if image is None:
            return False, {"error": "图像为空"}
        
        if self.template_image is None or self.template_config is None:
            return False, {"error": "未设置模板图像或配置"}
        
        try:
            # 获取匹配方法和阈值
            method = self.template_config.method
            threshold = self.template_config.threshold
            
            # 准备用于匹配的图像
            match_image = image.copy()
            roi_offset_x, roi_offset_y = 0, 0
            
            # 如果设置了ROI区域，则只在ROI区域内进行匹配
            roi_info = {}
            if self.roi_rect:
                x, y, w, h = self.roi_rect
                # 确保ROI在图像范围内
                img_h, img_w = image.shape[:2]
                x = max(0, min(x, img_w - 1))
                y = max(0, min(y, img_h - 1))
                w = max(1, min(w, img_w - x))
                h = max(1, min(h, img_h - y))
                
                # 切割ROI区域
                match_image = image[y:y+h, x:x+w]
                roi_offset_x, roi_offset_y = x, y
                
                # 保存ROI信息
                roi_info = {
                    "roi_x": x,
                    "roi_y": y,
                    "roi_width": w,
                    "roi_height": h
                }
            
            # 执行模板匹配
            template_h, template_w = self.template_image.shape[:2]
            
            # 确保模板小于匹配图像
            if template_h > match_image.shape[0] or template_w > match_image.shape[1]:
                return False, {"error": "模板图像大于ROI区域或输入图像，无法进行匹配"}
            
            result = cv2.matchTemplate(match_image, self.template_image, method)
            
            # 根据匹配方法确定最佳匹配位置
            if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                # 对于平方差方法，值越小表示匹配度越高
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                match_val = min_val
                match_loc = min_loc
                matched = match_val < (1.0 - threshold) if method == cv2.TM_SQDIFF_NORMED else match_val < threshold
            else:
                # 对于相关系数和相关方法，值越大表示匹配度越高
                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                match_val = max_val
                match_loc = max_loc
                matched = match_val > threshold
            
            # 获取模板图像尺寸
            h, w = self.template_image.shape[:2]
            
            # 考虑ROI偏移
            match_x = match_loc[0] + roi_offset_x
            match_y = match_loc[1] + roi_offset_y
            
            # 构建匹配结果信息
            match_info = {
                "x": match_x,
                "y": match_y,
                "width": w,
                "height": h,
                "match_value": match_val,
                "threshold": threshold,
                "method": method,
                "method_name": self._get_method_name(method),
                "using_roi": self.roi_rect is not None,
                "roi_info": roi_info
            }
            
            return matched, match_info
        
        except Exception as e:
            logger.error(f"模板匹配出错: {str(e)}")
            return False, {"error": str(e)}
    
    def _get_method_name(self, method: int) -> str:
        """
        获取匹配方法名称
        
        Args:
            method: 匹配方法代码
            
        Returns:
            str: 方法名称
        """
        method_names = {
            cv2.TM_SQDIFF: "平方差匹配法(CV_TM_SQDIFF)",
            cv2.TM_SQDIFF_NORMED: "归一化平方差匹配法(CV_TM_SQDIFF_NORMED)",
            cv2.TM_CCORR: "相关匹配法(CV_TM_CCORR)",
            cv2.TM_CCORR_NORMED: "归一化相关匹配法(CV_TM_CCORR_NORMED)",
            cv2.TM_CCOEFF: "相关系数匹配法(CV_TM_CCOEFF)",
            cv2.TM_CCOEFF_NORMED: "归一化相关系数匹配法(CV_TM_CCOEFF_NORMED)"
        }
        
        return method_names.get(method, "未知方法")
    
    def draw_match_result(self, image: np.ndarray, match_info: Dict[str, Any]) -> np.ndarray:
        """
        在图像上绘制匹配结果
        
        Args:
            image: 输入图像
            match_info: 匹配结果信息
            
        Returns:
            np.ndarray: 绘制结果图像
        """
        if image is None or "error" in match_info:
            return image.copy() if image is not None else None
        
        # 复制图像
        result_image = image.copy()
        
        # 绘制ROI区域（如果使用了ROI）
        if match_info.get("using_roi", False) and "roi_info" in match_info:
            roi_info = match_info["roi_info"]
            x = roi_info.get("roi_x", 0)
            y = roi_info.get("roi_y", 0)
            w = roi_info.get("roi_width", 0)
            h = roi_info.get("roi_height", 0)
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 255), 2)  # 紫色表示ROI区域
        
        # 获取匹配位置和尺寸
        x = match_info.get("x", 0)
        y = match_info.get("y", 0)
        w = match_info.get("width", 0)
        h = match_info.get("height", 0)
        
        # 绘制矩形框
        cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # 绘制匹配值
        match_value = match_info.get("match_value", 0)
        threshold = match_info.get("threshold", 0)
        cv2.putText(
            result_image, f"匹配分数: {match_value:.4f}, 阈值: {threshold:.2f}",
            (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1
        )
        
        return result_image
    
    def get_match_position(self, match_info: Dict[str, Any]) -> Tuple[int, int, int, int]:
        """
        获取匹配位置矩形
        
        Args:
            match_info: 匹配结果信息
            
        Returns:
            Tuple[int, int, int, int]: (x, y, width, height)
        """
        x = match_info.get("x", 0)
        y = match_info.get("y", 0)
        w = match_info.get("width", 0)
        h = match_info.get("height", 0)
        
        return (x, y, w, h)
