#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轮廓匹配处理模块
提供基于OpenCV轮廓匹配的图像处理功能
"""

import cv2
import numpy as np
from loguru import logger
from typing import Tuple, Dict, Any, List, Optional

from wirevsion.config.workflow_config import TemplateConfig


class ContourMatcher:
    """
    轮廓匹配器
    提供基于OpenCV轮廓匹配的图像处理功能，相比传统模板匹配更加鲁棒
    """
    
    def __init__(self):
        """初始化轮廓匹配器"""
        # 模板图像
        self.template_image = None
        # 模板轮廓
        self.template_contours = None
        # 模板配置
        self.template_config = None
        # 是否为多目标匹配
        self.multi_target = False
        # 显示中间结果
        self.debug = False
        # ROI区域，默认为None表示使用整个图像
        self.roi_rect = None
    
    def set_template(self, template_image: np.ndarray, template_config: TemplateConfig):
        """
        设置模板图像和配置，并提取模板图像的轮廓
        
        Args:
            template_image: 模板图像
            template_config: 模板配置
        """
        self.template_image = template_image
        self.template_config = template_config
        self.multi_target = template_config.multi_target
        
        # 提取模板图像轮廓
        self._extract_template_contours()
    
    def set_roi(self, x: int, y: int, width: int, height: int):
        """
        设置ROI区域，匹配将仅在此区域内进行
        
        Args:
            x: ROI左上角x坐标
            y: ROI左上角y坐标
            width: ROI宽度
            height: ROI高度
        """
        self.roi_rect = (x, y, width, height)
    
    def clear_roi(self):
        """清除ROI区域设置，恢复为整图匹配"""
        self.roi_rect = None
    
    def set_debug(self, debug: bool):
        """
        设置是否显示中间调试结果
        
        Args:
            debug: 是否显示中间调试结果
        """
        self.debug = debug
    
    def _extract_template_contours(self):
        """
        从模板图像中提取轮廓
        """
        if self.template_image is None:
            logger.error("未设置模板图像，无法提取轮廓")
            return
        
        try:
            # 转换为灰度图
            if len(self.template_image.shape) == 3:
                gray_template = cv2.cvtColor(self.template_image, cv2.COLOR_BGR2GRAY)
            else:
                gray_template = self.template_image
            
            # 二值化处理
            _, binary_template = cv2.threshold(gray_template, 127, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
            
            # 提取轮廓
            contours, _ = cv2.findContours(binary_template, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤小轮廓
            min_contour_area = 50  # 可调整的最小轮廓面积
            self.template_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_contour_area]
            
            logger.info(f"从模板图像提取了 {len(self.template_contours)} 个有效轮廓")
            
        except Exception as e:
            logger.error(f"提取模板轮廓时出错: {str(e)}")
            self.template_contours = None
    
    def match(self, image: np.ndarray) -> Tuple[bool, Dict[str, Any]]:
        """
        在图像中查找与模板轮廓匹配的区域
        
        Args:
            image: 输入图像
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (匹配结果标志, 匹配结果信息)
        """
        if image is None:
            return False, {"error": "图像为空"}
        
        if self.template_contours is None or self.template_config is None:
            return False, {"error": "未设置模板轮廓或配置"}
        
        try:
            # 准备用于匹配的图像
            match_image = image.copy()
            roi_offset_x, roi_offset_y = 0, 0
            
            # 如果设置了ROI区域，则只在ROI区域内进行匹配
            roi_info = {}
            if self.roi_rect:
                x, y, w, h = self.roi_rect
                # 确保ROI在图像范围内
                img_h, img_w = image.shape[:2]
                x = max(0, min(x, img_w - 1))
                y = max(0, min(y, img_h - 1))
                w = max(1, min(w, img_w - x))
                h = max(1, min(h, img_h - y))
                
                # 切割ROI区域
                match_image = image[y:y+h, x:x+w]
                roi_offset_x, roi_offset_y = x, y
                
                # 保存ROI信息
                roi_info = {
                    "roi_x": x,
                    "roi_y": y,
                    "roi_width": w,
                    "roi_height": h
                }
            
            # 转换为灰度图
            if len(match_image.shape) == 3:
                gray_image = cv2.cvtColor(match_image, cv2.COLOR_BGR2GRAY)
            else:
                gray_image = match_image
            
            # 二值化处理
            _, binary_image = cv2.threshold(gray_image, 127, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
            
            # 提取图像中的轮廓
            contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤小轮廓
            min_contour_area = 50  # 可调整的最小轮廓面积
            contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_contour_area]
            
            # 如果没有找到足够的轮廓，返回不匹配
            if not contours or not self.template_contours:
                return False, {"error": "未在图像或模板中找到有效轮廓"}
            
            # 匹配结果列表
            matches = []
            
            # 获取匹配阈值
            threshold = self.template_config.threshold
            
            # 对每个模板轮廓，寻找最佳匹配的图像轮廓
            for template_contour in self.template_contours:
                best_match = None
                best_score = 0
                
                for image_contour in contours:
                    # 计算形状匹配分数
                    match_score = cv2.matchShapes(template_contour, image_contour, cv2.CONTOURS_MATCH_I3, 0.0)
                    
                    # 注意：matchShapes的返回值是距离，越小表示越相似
                    # 转换为分数，越大表示越相似
                    similarity_score = 1.0 / (1.0 + match_score)
                    
                    if similarity_score > best_score:
                        best_score = similarity_score
                        best_match = {
                            "contour": image_contour,
                            "score": similarity_score,
                            "area": cv2.contourArea(image_contour),
                            "perimeter": cv2.arcLength(image_contour, True)
                        }
                
                if best_match:
                    # 计算轮廓的外接矩形
                    x, y, w, h = cv2.boundingRect(best_match["contour"])
                    # 考虑ROI偏移
                    best_match["x"] = x + roi_offset_x
                    best_match["y"] = y + roi_offset_y
                    best_match["width"] = w
                    best_match["height"] = h
                    
                    # 偏移轮廓坐标
                    if roi_offset_x > 0 or roi_offset_y > 0:
                        shifted_contour = best_match["contour"].copy()
                        shifted_contour[:,:,0] += roi_offset_x
                        shifted_contour[:,:,1] += roi_offset_y
                        best_match["contour"] = shifted_contour
                        
                    matches.append(best_match)
            
            # 按分数排序
            matches.sort(key=lambda m: m["score"], reverse=True)
            
            # 如果是多目标匹配，返回所有满足阈值的匹配
            if self.multi_target and self.template_config.multi_target:
                valid_matches = [m for m in matches if m["score"] > threshold]
                max_targets = min(self.template_config.max_targets, len(valid_matches))
                matches = valid_matches[:max_targets]
                
                # 构建多目标匹配结果
                multi_match_info = {
                    "matches": matches,
                    "count": len(matches),
                    "threshold": threshold,
                    "avg_score": sum(m["score"] for m in matches) / len(matches) if matches else 0,
                    "using_roi": self.roi_rect is not None,
                    "roi_info": roi_info
                }
                
                # 匹配成功条件：至少有一个有效匹配
                matched = len(matches) > 0
                
                return matched, multi_match_info
            else:
                # 单目标匹配，使用最佳匹配
                if not matches:
                    return False, {"error": "未找到匹配的轮廓"}
                
                best_match = matches[0]
                
                # 构建匹配结果信息
                match_info = {
                    "x": best_match["x"],
                    "y": best_match["y"],
                    "width": best_match["width"],
                    "height": best_match["height"],
                    "match_value": best_match["score"],
                    "threshold": threshold,
                    "area": best_match["area"],
                    "perimeter": best_match["perimeter"],
                    "contour": best_match["contour"],
                    "using_roi": self.roi_rect is not None,
                    "roi_info": roi_info
                }
                
                # 匹配成功条件：分数大于阈值
                matched = best_match["score"] > threshold
                
                return matched, match_info
        
        except Exception as e:
            logger.error(f"轮廓匹配出错: {str(e)}")
            return False, {"error": str(e)}
    
    def draw_match_result(self, image: np.ndarray, match_info: Dict[str, Any]) -> np.ndarray:
        """
        在图像上绘制匹配结果
        
        Args:
            image: 输入图像
            match_info: 匹配结果信息
            
        Returns:
            np.ndarray: 绘制结果图像
        """
        if image is None or "error" in match_info:
            return image.copy() if image is not None else None
        
        # 复制图像
        result_image = image.copy()
        
        # 绘制ROI区域（如果使用了ROI）
        if match_info.get("using_roi", False) and "roi_info" in match_info:
            roi_info = match_info["roi_info"]
            x = roi_info.get("roi_x", 0)
            y = roi_info.get("roi_y", 0)
            w = roi_info.get("roi_width", 0)
            h = roi_info.get("roi_height", 0)
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 255), 2)  # 紫色表示ROI区域
        
        # 多目标匹配
        if "matches" in match_info:
            matches = match_info.get("matches", [])
            for idx, match in enumerate(matches):
                # 获取匹配位置和尺寸
                x = match.get("x", 0)
                y = match.get("y", 0)
                w = match.get("width", 0)
                h = match.get("height", 0)
                score = match.get("score", 0)
                
                # 绘制矩形框
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # 绘制匹配值
                cv2.putText(
                    result_image, f"#{idx+1} 分数: {score:.4f}",
                    (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1
                )
                
                # 绘制轮廓（如果存在）
                if "contour" in match:
                    cv2.drawContours(result_image, [match["contour"]], 0, (0, 255, 255), 1)
            
            # 绘制总匹配数
            cv2.putText(
                result_image, f"匹配数: {len(matches)}/{match_info.get('count', 0)}, 阈值: {match_info.get('threshold', 0):.2f}",
                (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2
            )
        else:
            # 单目标匹配
            # 获取匹配位置和尺寸
            x = match_info.get("x", 0)
            y = match_info.get("y", 0)
            w = match_info.get("width", 0)
            h = match_info.get("height", 0)
            
            # 绘制矩形框
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # 绘制匹配值
            match_value = match_info.get("match_value", 0)
            threshold = match_info.get("threshold", 0)
            cv2.putText(
                result_image, f"匹配分数: {match_value:.4f}, 阈值: {threshold:.2f}",
                (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1
            )
            
            # 绘制轮廓（如果存在）
            if "contour" in match_info:
                cv2.drawContours(result_image, [match_info["contour"]], 0, (0, 255, 255), 1)
        
        return result_image
    
    def get_match_position(self, match_info: Dict[str, Any]) -> Tuple[int, int, int, int]:
        """
        获取匹配位置矩形
        
        Args:
            match_info: 匹配结果信息
            
        Returns:
            Tuple[int, int, int, int]: (x, y, width, height)
        """
        if "matches" in match_info and match_info.get("matches"):
            # 返回最佳匹配结果
            best_match = match_info["matches"][0]
            x = best_match.get("x", 0)
            y = best_match.get("y", 0)
            w = best_match.get("width", 0)
            h = best_match.get("height", 0)
        else:
            # 单目标匹配
            x = match_info.get("x", 0)
            y = match_info.get("y", 0)
            w = match_info.get("width", 0)
            h = match_info.get("height", 0)
        
        return (x, y, w, h) 