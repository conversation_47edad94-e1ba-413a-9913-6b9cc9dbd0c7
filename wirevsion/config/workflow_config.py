#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工作流配置模块
定义工作流程中各模块的配置类
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class TemplateConfig:
    """模板匹配配置类"""
    
    path: str = ""  # 模板图像路径
    method: int = 5  # 默认使用归一化相关系数匹配法(cv2.TM_CCOEFF_NORMED)
    threshold: float = 0.8  # 匹配阈值
    scale_mode: str = "自动"  # 尺度模式：自动、固定、范围
    scale_factor: float = 1.0  # 特征尺度
    angle_min: int = -180  # 角度范围最小值
    angle_max: int = 180  # 角度范围最大值
    scale_min: float = 0.8  # 尺度范围最小值
    scale_max: float = 1.2  # 尺度范围最大值
    timeout: int = 2000  # 超时时间(ms)
    multi_target: bool = False  # 多目标检测
    max_targets: int = 10  # 最大目标数
    sort_mode: str = "分数优先"  # 目标排序方式
    show_box: bool = True  # 显示匹配框
    show_score: bool = True  # 显示得分
    show_position: bool = True  # 显示位置
    show_angle: bool = True  # 显示角度
    matcher_type: str = "template"  # 匹配器类型：template(传统模板匹配)或contour(轮廓匹配)
    extra_data: Dict[str, Any] = field(default_factory=dict)  # 额外数据，如ROI设置等


@dataclass
class PositionCorrectionConfig:
    """位置修正配置类"""
    
    use_points: bool = True  # 使用点坐标模式，False为使用坐标系模式
    reference_point: str = ""  # 参照点
    angle_reference: str = ""  # 角度参照
    x_scale: str = ""  # X方向尺度
    y_scale: str = ""  # Y方向尺度
    offset_x: float = 0.0  # X偏移量
    offset_y: float = 0.0  # Y偏移量
    rotation: float = 0.0  # 旋转角度


@dataclass
class ROIConfig:
    """ROI区域配置类"""
    
    name: str = "ROI"  # ROI名称
    x: int = 0  # 左上角X坐标
    y: int = 0  # 左上角Y坐标
    width: int = 100  # 宽度
    height: int = 100  # 高度
    is_color_roi: bool = False  # 是否为颜色类型ROI
    color_lower: List[int] = field(default_factory=lambda: [0, 0, 0])  # 颜色下限 (BGR)
    color_upper: List[int] = field(default_factory=lambda: [255, 255, 255])  # 颜色上限 (BGR)


@dataclass
class ImageSourceConfig:
    """图像源配置类"""
    
    type: str = "local"  # 图像源类型：local或camera
    local_path: str = ""  # 本地图像路径
    camera_id: str = ""  # 相机ID
    camera_format: str = "MONO8"  # 相机像素格式
    camera_interval: int = 0  # 相机取图间隔(ms)
    auto_capture: bool = True  # 自动切换
    save_images: bool = False  # 方案存图


@dataclass
class WorkflowConfig:
    """工作流程配置类"""
    
    name: str = "流程1"  # 工作流程名称
    image_source: Optional[ImageSourceConfig] = None  # 图像源配置
    template: Optional[TemplateConfig] = None  # 模板匹配配置
    position_correction: Optional[PositionCorrectionConfig] = None  # 位置修正配置
    rois: List[ROIConfig] = field(default_factory=list)  # ROI列表
    
    def __post_init__(self):
        """初始化默认配置"""
        if self.image_source is None:
            self.image_source = ImageSourceConfig()
        if self.template is None:
            self.template = TemplateConfig()
        if self.position_correction is None:
            self.position_correction = PositionCorrectionConfig()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将配置转换为字典
        
        Returns:
            Dict[str, Any]: 字典形式的配置
        """
        return {
            "name": self.name,
            "image_source": {
                "type": self.image_source.type,
                "local_path": self.image_source.local_path,
                "camera_id": self.image_source.camera_id,
                "camera_format": self.image_source.camera_format,
                "camera_interval": self.image_source.camera_interval,
                "auto_capture": self.image_source.auto_capture,
                "save_images": self.image_source.save_images
            },
            "template": {
                "path": self.template.path,
                "method": self.template.method,
                "threshold": self.template.threshold,
                "scale_mode": self.template.scale_mode,
                "scale_factor": self.template.scale_factor,
                "angle_min": self.template.angle_min,
                "angle_max": self.template.angle_max,
                "scale_min": self.template.scale_min,
                "scale_max": self.template.scale_max,
                "timeout": self.template.timeout,
                "multi_target": self.template.multi_target,
                "max_targets": self.template.max_targets,
                "sort_mode": self.template.sort_mode,
                "show_box": self.template.show_box,
                "show_score": self.template.show_score,
                "show_position": self.template.show_position,
                "show_angle": self.template.show_angle,
                "matcher_type": self.template.matcher_type,
                "extra_data": self.template.extra_data
            },
            "position_correction": {
                "use_points": self.position_correction.use_points,
                "reference_point": self.position_correction.reference_point,
                "angle_reference": self.position_correction.angle_reference,
                "x_scale": self.position_correction.x_scale,
                "y_scale": self.position_correction.y_scale,
                "offset_x": self.position_correction.offset_x,
                "offset_y": self.position_correction.offset_y,
                "rotation": self.position_correction.rotation
            },
            "rois": [
                {
                    "name": roi.name,
                    "x": roi.x,
                    "y": roi.y,
                    "width": roi.width,
                    "height": roi.height,
                    "is_color_roi": roi.is_color_roi,
                    "color_lower": roi.color_lower,
                    "color_upper": roi.color_upper
                }
                for roi in self.rois
            ]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowConfig':
        """
        从字典创建配置
        
        Args:
            data: 字典形式的配置
            
        Returns:
            WorkflowConfig: 工作流程配置对象
        """
        config = cls(name=data.get("name", "流程1"))
        
        # 图像源配置
        image_source_data = data.get("image_source", {})
        config.image_source = ImageSourceConfig(
            type=image_source_data.get("type", "local"),
            local_path=image_source_data.get("local_path", ""),
            camera_id=image_source_data.get("camera_id", ""),
            camera_format=image_source_data.get("camera_format", "MONO8"),
            camera_interval=image_source_data.get("camera_interval", 0),
            auto_capture=image_source_data.get("auto_capture", True),
            save_images=image_source_data.get("save_images", False)
        )
        
        # 模板匹配配置
        template_data = data.get("template", {})
        config.template = TemplateConfig(
            path=template_data.get("path", ""),
            method=template_data.get("method", 5),
            threshold=template_data.get("threshold", 0.8),
            scale_mode=template_data.get("scale_mode", "自动"),
            scale_factor=template_data.get("scale_factor", 1.0),
            angle_min=template_data.get("angle_min", -180),
            angle_max=template_data.get("angle_max", 180),
            scale_min=template_data.get("scale_min", 0.8),
            scale_max=template_data.get("scale_max", 1.2),
            timeout=template_data.get("timeout", 2000),
            multi_target=template_data.get("multi_target", False),
            max_targets=template_data.get("max_targets", 10),
            sort_mode=template_data.get("sort_mode", "分数优先"),
            extra_data=template_data.get("extra_data", {}),
            show_box=template_data.get("show_box", True),
            show_score=template_data.get("show_score", True),
            show_position=template_data.get("show_position", True),
            show_angle=template_data.get("show_angle", True),
            matcher_type=template_data.get("matcher_type", "template")
        )
        
        # 位置修正配置
        position_data = data.get("position_correction", {})
        config.position_correction = PositionCorrectionConfig(
            use_points=position_data.get("use_points", True),
            reference_point=position_data.get("reference_point", ""),
            angle_reference=position_data.get("angle_reference", ""),
            x_scale=position_data.get("x_scale", ""),
            y_scale=position_data.get("y_scale", ""),
            offset_x=position_data.get("offset_x", 0.0),
            offset_y=position_data.get("offset_y", 0.0),
            rotation=position_data.get("rotation", 0.0)
        )
        
        # ROI列表
        rois_data = data.get("rois", [])
        config.rois = [
            ROIConfig(
                name=roi_data.get("name", f"ROI_{i+1}"),
                x=roi_data.get("x", 0),
                y=roi_data.get("y", 0),
                width=roi_data.get("width", 100),
                height=roi_data.get("height", 100),
                is_color_roi=roi_data.get("is_color_roi", False),
                color_lower=roi_data.get("color_lower", [0, 0, 0]),
                color_upper=roi_data.get("color_upper", [255, 255, 255])
            )
            for i, roi_data in enumerate(rois_data)
        ]
        
        return config
