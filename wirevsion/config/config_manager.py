#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理器模块
负责管理应用程序配置和检测流程配置
"""

import os
import yaml
import json
from pathlib import Path
from loguru import logger
from typing import Dict, List, Any, Optional

from wirevsion.config.workflow_config import WorkflowConfig


class ConfigManager:
    """
    配置管理器类
    管理应用程序配置和检测流程配置
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为应用程序目录下的 'configs'
        """
        # 设置配置文件目录
        self.config_dir = config_dir if config_dir else str(Path.home() / '.wirevsion' / 'configs')
        
        # 创建配置目录（如果不存在）
        os.makedirs(self.config_dir, exist_ok=True)
        
        # 设置应用程序配置和检测流程配置的路径
        self.app_config_path = os.path.join(self.config_dir, 'app_config.yaml')
        self.workflows_dir = os.path.join(self.config_dir, 'workflows')
        
        # 创建工作流程目录（如果不存在）
        os.makedirs(self.workflows_dir, exist_ok=True)
        
        # 加载应用程序配置
        self.app_config = self._load_app_config()
        
        # 存储工作流程配置
        self.workflows = {}  # {workflow_name: WorkflowConfig}
        
        # 加载所有工作流程配置
        self._load_workflows()
    
    def _load_app_config(self) -> Dict[str, Any]:
        """
        加载应用程序配置
        如果配置文件不存在，则创建默认配置
        
        Returns:
            Dict[str, Any]: 应用程序配置
        """
        try:
            if os.path.exists(self.app_config_path):
                with open(self.app_config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                logger.info(f"已加载应用程序配置: {self.app_config_path}")
                return config if config else self._create_default_app_config()
            else:
                return self._create_default_app_config()
        except Exception as e:
            logger.error(f"加载应用程序配置出错: {str(e)}")
            return self._create_default_app_config()
    
    def _create_default_app_config(self) -> Dict[str, Any]:
        """
        创建默认应用程序配置
        
        Returns:
            Dict[str, Any]: 默认配置
        """
        default_config = {
            'camera': {
                'default_resolution': '1280x720',
                'default_fps': 60
            },
            'ui': {
                'theme': 'default',
                'language': 'zh_CN'
            },
            'detection': {
                'default_template_matching_threshold': 0.8,
                'default_color_detection_tolerance': 20
            }
        }
        
        try:
            with open(self.app_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"已创建默认应用程序配置: {self.app_config_path}")
        except Exception as e:
            logger.error(f"创建默认应用程序配置出错: {str(e)}")
        
        return default_config
    
    def save_app_config(self) -> bool:
        """
        保存应用程序配置
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(self.app_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.app_config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"已保存应用程序配置: {self.app_config_path}")
            return True
        except Exception as e:
            logger.error(f"保存应用程序配置出错: {str(e)}")
            return False
    
    def _load_workflows(self):
        """加载所有工作流程配置"""
        try:
            # 清空当前工作流程配置
            self.workflows = {}
            
            # 获取工作流程目录中的所有 YAML 文件
            workflow_files = [f for f in os.listdir(self.workflows_dir) if f.endswith('.yaml')]
            
            # 加载每个工作流程配置
            for file_name in workflow_files:
                try:
                    file_path = os.path.join(self.workflows_dir, file_name)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                    
                    if config_data and 'name' in config_data:
                        workflow_name = config_data['name']
                        workflow = WorkflowConfig.from_dict(config_data)
                        self.workflows[workflow_name] = workflow
                        logger.info(f"已加载工作流程配置: {workflow_name}")
                except Exception as e:
                    logger.error(f"加载工作流程配置 {file_name} 出错: {str(e)}")
        except Exception as e:
            logger.error(f"加载工作流程配置出错: {str(e)}")
    
    def get_workflow_names(self) -> List[str]:
        """
        获取所有工作流程名称
        
        Returns:
            List[str]: 工作流程名称列表
        """
        return list(self.workflows.keys())
    
    def get_workflow(self, name: str) -> Optional[WorkflowConfig]:
        """
        获取工作流程配置
        
        Args:
            name: 工作流程名称
            
        Returns:
            Optional[WorkflowConfig]: 工作流程配置，如果不存在则返回 None
        """
        return self.workflows.get(name)
    
    def save_workflow(self, workflow: WorkflowConfig) -> bool:
        """
        保存工作流程配置
        
        Args:
            workflow: 工作流程配置
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 将工作流程添加到内存中
            self.workflows[workflow.name] = workflow
            
            # 保存到文件
            file_path = os.path.join(self.workflows_dir, f"{workflow.name}.yaml")
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(workflow.to_dict(), f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"已保存工作流程配置: {workflow.name}")
            return True
        except Exception as e:
            logger.error(f"保存工作流程配置 {workflow.name} 出错: {str(e)}")
            return False
    
    def delete_workflow(self, name: str) -> bool:
        """
        删除工作流程配置
        
        Args:
            name: 工作流程名称
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 从内存中删除
            if name in self.workflows:
                del self.workflows[name]
            
            # 删除文件
            file_path = os.path.join(self.workflows_dir, f"{name}.yaml")
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已删除工作流程配置: {name}")
                return True
            else:
                logger.warning(f"工作流程配置文件不存在: {name}")
                return False
        except Exception as e:
            logger.error(f"删除工作流程配置 {name} 出错: {str(e)}")
            return False
    
    def create_new_workflow(self, name: str) -> WorkflowConfig:
        """
        创建新的工作流程配置
        
        Args:
            name: 工作流程名称
            
        Returns:
            WorkflowConfig: 新创建的工作流程配置
        """
        # 创建新的工作流程配置
        workflow = WorkflowConfig(name=name)
        
        # 添加到内存中
        self.workflows[name] = workflow
        
        return workflow

    def save_config(self):
        """保存所有配置（应用程序和工作流）"""
        logger.info("开始保存所有配置...")
        app_config_saved = self.save_app_config()
        
        workflows_saved_count = 0
        for workflow in self.workflows.values():
            if self.save_workflow(workflow):
                workflows_saved_count += 1
        
        if app_config_saved and workflows_saved_count == len(self.workflows):
            logger.info("所有配置已成功保存")
            return True
        else:
            logger.warning("部分配置保存失败")
            return False
