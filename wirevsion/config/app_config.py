#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用程序配置模块

提供统一的配置管理和访问方式
"""

import os
import json
import yaml
from typing import Dict, Any, Optional
from loguru import logger


class AppConfig:
    """应用程序配置类"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'AppConfig':
        """获取单例实例
        
        Returns:
            AppConfig: 单例实例
        """
        if cls._instance is None:
            cls._instance = AppConfig()
        return cls._instance
    
    def __init__(self):
        """初始化配置类"""
        if AppConfig._instance is not None:
            raise RuntimeError("AppConfig是单例类，请使用get_instance()获取实例")
            
        # 配置数据
        self.config = {
            'camera': {
                'default_id': 0,
                'resolution': '640x480',
                'fps': 30,
                'auto_exposure': True,
                'exposure': -5,
                'brightness': 128,
                'contrast': 128,
                'enable_frame_cache': True,
                'frame_cache_lifetime': 0.016  # 约60FPS
            },
            'ui': {
                'theme': 'dark',
                'font_size': 10,
                'show_fps': True,
                'show_stats': True,
                'auto_refresh': True
            },
            'performance': {
                'enable_monitoring': True,
                'log_interval': 60,  # 秒
                'warning_threshold': 80  # CPU/内存使用率警告阈值
            },
            'workflow': {
                'auto_save': True,
                'auto_save_interval': 300,  # 秒
                'max_history': 10
            }
        }
        
        # 加载配置
        self.load_config()
        
    def load_config(self):
        """从配置文件加载配置"""
        config_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                  "configs", "app_config.yaml")
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    loaded_config = yaml.safe_load(f)
                    
                if loaded_config and isinstance(loaded_config, dict):
                    # 更新配置
                    self._update_dict_recursive(self.config, loaded_config)
                    logger.info(f"从 {config_file} 加载配置成功")
            except Exception as e:
                logger.error(f"加载配置文件出错: {str(e)}")
        else:
            logger.info(f"配置文件 {config_file} 不存在，使用默认配置")
            
    def save_config(self):
        """保存配置到文件"""
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                 "configs")
        
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
            
        config_file = os.path.join(config_dir, "app_config.yaml")
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                
            logger.info(f"配置已保存到 {config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件出错: {str(e)}")
            return False
            
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            section: 配置区域
            key: 配置键
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        if section in self.config and key in self.config[section]:
            return self.config[section][key]
        return default
        
    def set(self, section: str, key: str, value: Any) -> bool:
        """设置配置值
        
        Args:
            section: 配置区域
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 是否成功
        """
        if section not in self.config:
            self.config[section] = {}
            
        self.config[section][key] = value
        return True
        
    def _update_dict_recursive(self, target: Dict, source: Dict):
        """递归更新字典
        
        Args:
            target: 目标字典
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_dict_recursive(target[key], value)
            else:
                target[key] = value
                

# 全局配置访问器
def get_config() -> AppConfig:
    """获取全局配置实例
    
    Returns:
        AppConfig: 配置实例
    """
    return AppConfig.get_instance()
