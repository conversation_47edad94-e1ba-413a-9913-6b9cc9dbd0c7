#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能监测工具

提供用于监控和优化系统性能的工具类
"""

import time
import threading
import psutil
from loguru import logger
from typing import Dict, List, Optional, Callable, Any
from collections import deque


class PerformanceMetric:
    """性能指标类，用于记录和计算性能数据"""
    
    def __init__(self, name: str, window_size: int = 100):
        """初始化性能指标
        
        Args:
            name: 指标名称
            window_size: 滑动窗口大小，用于计算平均值
        """
        self.name = name
        self.window_size = window_size
        self.values = deque(maxlen=window_size)
        self.total_samples = 0
        self.min_value = float('inf')
        self.max_value = float('-inf')
        self.start_time = None
        
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        
    def stop(self) -> float:
        """停止计时并记录值
        
        Returns:
            float: 当前测量值（秒）
        """
        if self.start_time is None:
            return 0.0
            
        elapsed = time.time() - self.start_time
        self.add_value(elapsed)
        return elapsed
        
    def add_value(self, value: float):
        """添加一个值
        
        Args:
            value: 要添加的值
        """
        self.values.append(value)
        self.total_samples += 1
        self.min_value = min(self.min_value, value)
        self.max_value = max(self.max_value, value)
        
    def get_average(self) -> float:
        """获取平均值
        
        Returns:
            float: 平均值
        """
        if not self.values:
            return 0.0
        return sum(self.values) / len(self.values)
        
    def get_stats(self) -> Dict[str, float]:
        """获取统计信息
        
        Returns:
            Dict[str, float]: 包含平均值、最小值、最大值等的字典
        """
        avg = self.get_average()
        return {
            'name': self.name,
            'avg': avg,
            'min': self.min_value if self.min_value != float('inf') else 0,
            'max': self.max_value if self.max_value != float('-inf') else 0,
            'samples': self.total_samples,
            'current': self.values[-1] if self.values else 0
        }
        
    def reset(self):
        """重置指标"""
        self.values.clear()
        self.total_samples = 0
        self.min_value = float('inf')
        self.max_value = float('-inf')
        self.start_time = None
        
    def __str__(self) -> str:
        """返回人类可读的字符串表示"""
        stats = self.get_stats()
        return f"{self.name}: avg={stats['avg']:.6f}s, min={stats['min']:.6f}s, max={stats['max']:.6f}s, samples={stats['samples']}"


class PerformanceMonitor:
    """性能监测器，用于监控系统各部分的性能"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'PerformanceMonitor':
        """获取单例实例
        
        Returns:
            PerformanceMonitor: 单例实例
        """
        if cls._instance is None:
            cls._instance = PerformanceMonitor()
        return cls._instance
    
    def __init__(self):
        """初始化性能监测器"""
        if PerformanceMonitor._instance is not None:
            raise RuntimeError("PerformanceMonitor是单例类，请使用get_instance()获取实例")
            
        self.metrics: Dict[str, PerformanceMetric] = {}
        self.system_monitor_thread = None
        self.stop_monitoring = threading.Event()
        self.system_stats: Dict[str, Any] = {
            'cpu_percent': 0,
            'memory_percent': 0,
            'disk_io': {'read_bytes': 0, 'write_bytes': 0},
            'net_io': {'bytes_sent': 0, 'bytes_recv': 0}
        }
        self.callbacks: List[Callable] = []
        
        # 创建常用的性能指标
        self.create_metric('camera_frame_time')
        self.create_metric('image_process_time')
        self.create_metric('ui_update_time')
        self.create_metric('total_frame_time')
        
        logger.info("性能监测器已初始化")
        
    def create_metric(self, name: str, window_size: int = 100) -> PerformanceMetric:
        """创建性能指标
        
        Args:
            name: 指标名称
            window_size: 滑动窗口大小
            
        Returns:
            PerformanceMetric: 创建的性能指标
        """
        if name in self.metrics:
            return self.metrics[name]
            
        metric = PerformanceMetric(name, window_size)
        self.metrics[name] = metric
        return metric
        
    def get_metric(self, name: str) -> Optional[PerformanceMetric]:
        """获取性能指标
        
        Args:
            name: 指标名称
            
        Returns:
            Optional[PerformanceMetric]: 性能指标对象，如果不存在则返回None
        """
        return self.metrics.get(name)
        
    def start_monitor(self, name: str) -> None:
        """开始监测性能指标
        
        Args:
            name: 指标名称
        """
        metric = self.get_metric(name)
        if metric:
            metric.start()
        else:
            logger.warning(f"性能指标 '{name}' 不存在")
            
    def stop_monitor(self, name: str) -> float:
        """停止监测性能指标
        
        Args:
            name: 指标名称
            
        Returns:
            float: 经过的时间（秒）
        """
        metric = self.get_metric(name)
        if metric:
            return metric.stop()
        else:
            logger.warning(f"性能指标 '{name}' 不存在")
            return 0.0
            
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """获取所有性能指标的统计信息
        
        Returns:
            Dict[str, Dict[str, float]]: 所有指标的统计信息
        """
        return {name: metric.get_stats() for name, metric in self.metrics.items()}
        
    def start_system_monitoring(self, interval: float = 1.0):
        """开始系统监测
        
        Args:
            interval: 监测间隔（秒）
        """
        if self.system_monitor_thread and self.system_monitor_thread.is_alive():
            return
            
        self.stop_monitoring.clear()
        self.system_monitor_thread = threading.Thread(
            target=self._system_monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.system_monitor_thread.start()
        logger.info(f"系统监测已启动，间隔: {interval}秒")
        
    def stop_system_monitoring(self):
        """停止系统监测"""
        if self.system_monitor_thread and self.system_monitor_thread.is_alive():
            self.stop_monitoring.set()
            self.system_monitor_thread.join(timeout=2.0)
            logger.info("系统监测已停止")
            
    def _system_monitor_loop(self, interval: float):
        """系统监测循环
        
        Args:
            interval: 监测间隔（秒）
        """
        last_disk_io = psutil.disk_io_counters()
        last_net_io = psutil.net_io_counters()
        last_time = time.time()
        
        while not self.stop_monitoring.is_set():
            try:
                current_time = time.time()
                elapsed = current_time - last_time
                
                # CPU使用率
                self.system_stats['cpu_percent'] = psutil.cpu_percent()
                
                # 内存使用率
                memory = psutil.virtual_memory()
                self.system_stats['memory_percent'] = memory.percent
                self.system_stats['memory_used'] = memory.used / (1024 * 1024)  # MB
                self.system_stats['memory_total'] = memory.total / (1024 * 1024)  # MB
                
                # 磁盘IO
                current_disk_io = psutil.disk_io_counters()
                disk_read_speed = (current_disk_io.read_bytes - last_disk_io.read_bytes) / elapsed / 1024  # KB/s
                disk_write_speed = (current_disk_io.write_bytes - last_disk_io.write_bytes) / elapsed / 1024  # KB/s
                self.system_stats['disk_io'] = {
                    'read_bytes': current_disk_io.read_bytes,
                    'write_bytes': current_disk_io.write_bytes,
                    'read_speed': disk_read_speed,
                    'write_speed': disk_write_speed
                }
                last_disk_io = current_disk_io
                
                # 网络IO
                current_net_io = psutil.net_io_counters()
                net_send_speed = (current_net_io.bytes_sent - last_net_io.bytes_sent) / elapsed / 1024  # KB/s
                net_recv_speed = (current_net_io.bytes_recv - last_net_io.bytes_recv) / elapsed / 1024  # KB/s
                self.system_stats['net_io'] = {
                    'bytes_sent': current_net_io.bytes_sent,
                    'bytes_recv': current_net_io.bytes_recv,
                    'send_speed': net_send_speed,
                    'recv_speed': net_recv_speed
                }
                last_net_io = current_net_io
                
                # 更新时间
                last_time = current_time
                
                # 调用回调函数
                for callback in self.callbacks:
                    try:
                        callback(self.system_stats)
                    except Exception as e:
                        logger.error(f"性能监测回调函数出错: {e}")
                
                # 等待下一个间隔
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"系统监测循环出错: {e}")
                time.sleep(interval)
                
    def add_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加系统状态回调函数
        
        Args:
            callback: 回调函数，接收系统状态字典作为参数
        """
        if callback not in self.callbacks:
            self.callbacks.append(callback)
            
    def remove_callback(self, callback: Callable):
        """删除系统状态回调函数
        
        Args:
            callback: 要删除的回调函数
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            
    def log_performance_summary(self):
        """记录性能摘要到日志"""
        logger.info("性能监测摘要:")
        for name, metric in self.metrics.items():
            logger.info(f"  {metric}")
            
        logger.info(f"系统状态:")
        logger.info(f"  CPU使用率: {self.system_stats['cpu_percent']}%")
        logger.info(f"  内存使用率: {self.system_stats['memory_percent']}%")
        
        if 'memory_used' in self.system_stats and 'memory_total' in self.system_stats:
            logger.info(f"  内存使用: {self.system_stats['memory_used']:.1f}MB / {self.system_stats['memory_total']:.1f}MB")
            
        if 'disk_io' in self.system_stats and 'read_speed' in self.system_stats['disk_io']:
            logger.info(f"  磁盘读取速度: {self.system_stats['disk_io']['read_speed']:.1f} KB/s")
            logger.info(f"  磁盘写入速度: {self.system_stats['disk_io']['write_speed']:.1f} KB/s")
            
        if 'net_io' in self.system_stats and 'send_speed' in self.system_stats['net_io']:
            logger.info(f"  网络发送速度: {self.system_stats['net_io']['send_speed']:.1f} KB/s")
            logger.info(f"  网络接收速度: {self.system_stats['net_io']['recv_speed']:.1f} KB/s")


# 简便的性能监测装饰器
def measure_performance(metric_name=None):
    """性能监测装饰器
    
    用于测量函数的执行时间
    
    Args:
        metric_name: 性能指标名称，默认为函数名
        
    Returns:
        Callable: 装饰后的函数
    """
    def decorator(func):
        nonlocal metric_name
        if metric_name is None:
            metric_name = func.__name__
            
        def wrapper(*args, **kwargs):
            monitor = PerformanceMonitor.get_instance()
            monitor.start_monitor(metric_name)
            result = func(*args, **kwargs)
            elapsed = monitor.stop_monitor(metric_name)
            return result
            
        return wrapper
        
    return decorator 