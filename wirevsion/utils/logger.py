#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志工具模块
负责配置和管理应用程序日志
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from loguru import logger


def setup_logger(log_level="INFO"):
    """
    设置全局日志配置
    
    Args:
        log_level: 日志级别，默认为 INFO
    """
    # 创建日志目录
    log_dir = Path.home() / '.wirevsion' / 'logs'
    os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名
    today = datetime.now().strftime("%Y-%m-%d")
    log_file = log_dir / f"wirevsion_{today}.log"
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 添加控制台日志处理器
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 添加文件日志处理器
    logger.add(
        log_file,
        rotation="10 MB",
        retention="30 days",
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        encoding="utf-8"
    )
    
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")


def get_logger():
    """
    获取 logger 实例
    
    Returns:
        logger: loguru 的 logger 实例
    """
    return logger
