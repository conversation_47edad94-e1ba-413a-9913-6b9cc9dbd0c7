#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内存管理和性能监控模块
提供内存使用监控和清理功能，防止内存泄漏
"""

import gc
import psutil
import time
import threading
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from collections import deque
from loguru import logger
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

from wirevsion.utils.constants import PerformanceConstants


@dataclass
class MemorySnapshot:
    """内存快照数据类"""
    timestamp: float
    process_memory_mb: float
    system_memory_percent: float
    python_objects_count: int
    large_objects_count: int
    gc_stats: Dict[str, int]


class PerformanceMonitor:
    """
    性能监控器
    监控内存使用和系统性能
    """
    
    def __init__(self, max_snapshots: int = 100):
        """
        初始化性能监控器
        
        Args:
            max_snapshots: 保存的最大快照数量
        """
        self.max_snapshots = max_snapshots
        self.snapshots: deque = deque(maxlen=max_snapshots)
        self.process = psutil.Process()
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.monitor_interval = 5.0  # 监控间隔（秒）
        
        # 性能告警阈值
        self.memory_warning_threshold = 80.0  # 内存使用率告警阈值
        self.memory_critical_threshold = 90.0  # 内存使用率严重告警阈值
        
        # 回调函数
        self.warning_callbacks: List[Callable] = []
        self.critical_callbacks: List[Callable] = []
    
    def start_monitoring(self, interval: float = 5.0):
        """
        开始性能监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring:
            logger.warning("性能监控已在运行中")
            return
        
        self.monitor_interval = interval
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info(f"性能监控已启动，监控间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止性能监控"""
        if not self.monitoring:
            return
        
        self.monitoring = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)
        logger.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                snapshot = self._take_snapshot()
                self.snapshots.append(snapshot)
                
                # 检查告警条件
                self._check_alerts(snapshot)
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logger.error(f"性能监控出错: {str(e)}")
                time.sleep(self.monitor_interval)
    
    def _take_snapshot(self) -> MemorySnapshot:
        """创建内存快照"""
        # 获取进程内存信息
        memory_info = self.process.memory_info()
        process_memory_mb = memory_info.rss / 1024 / 1024
        
        # 获取系统内存使用率
        system_memory = psutil.virtual_memory()
        system_memory_percent = system_memory.percent
        
        # 获取Python对象数量
        python_objects_count = len(gc.get_objects())
        
        # 统计大对象数量（numpy数组等）
        large_objects_count = 0
        for obj in gc.get_objects():
            if isinstance(obj, np.ndarray):
                # 计算数组大小（字节）
                size_bytes = obj.nbytes if hasattr(obj, 'nbytes') else 0
                if size_bytes > 1024 * 1024:  # 大于1MB的数组
                    large_objects_count += 1
        
        # 获取垃圾回收统计
        gc_stats = {}
        for i, count in enumerate(gc.get_count()):
            gc_stats[f"generation_{i}"] = count
        
        return MemorySnapshot(
            timestamp=time.time(),
            process_memory_mb=process_memory_mb,
            system_memory_percent=system_memory_percent,
            python_objects_count=python_objects_count,
            large_objects_count=large_objects_count,
            gc_stats=gc_stats
        )
    
    def _check_alerts(self, snapshot: MemorySnapshot):
        """检查告警条件"""
        memory_percent = snapshot.system_memory_percent
        
        if memory_percent >= self.memory_critical_threshold:
            self._trigger_critical_alert(snapshot)
        elif memory_percent >= self.memory_warning_threshold:
            self._trigger_warning_alert(snapshot)
    
    def _trigger_warning_alert(self, snapshot: MemorySnapshot):
        """触发警告告警"""
        logger.warning(f"内存使用率告警: {snapshot.system_memory_percent:.1f}%")
        for callback in self.warning_callbacks:
            try:
                callback(snapshot)
            except Exception as e:
                logger.error(f"告警回调执行失败: {str(e)}")
    
    def _trigger_critical_alert(self, snapshot: MemorySnapshot):
        """触发严重告警"""
        logger.critical(f"内存使用率严重告警: {snapshot.system_memory_percent:.1f}%")
        for callback in self.critical_callbacks:
            try:
                callback(snapshot)
            except Exception as e:
                logger.error(f"严重告警回调执行失败: {str(e)}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        获取当前性能状态
        
        Returns:
            Dict[str, Any]: 性能状态信息
        """
        if not self.snapshots:
            return {"status": "no_data"}
        
        latest = self.snapshots[-1]
        return {
            "process_memory_mb": latest.process_memory_mb,
            "system_memory_percent": latest.system_memory_percent,
            "python_objects_count": latest.python_objects_count,
            "large_objects_count": latest.large_objects_count,
            "monitoring": self.monitoring
        }
    
    def get_memory_trend(self, minutes: int = 10) -> List[MemorySnapshot]:
        """
        获取指定时间内的内存趋势
        
        Args:
            minutes: 时间范围（分钟）
            
        Returns:
            List[MemorySnapshot]: 内存快照列表
        """
        cutoff_time = time.time() - (minutes * 60)
        return [s for s in self.snapshots if s.timestamp >= cutoff_time]
    
    def add_warning_callback(self, callback: Callable):
        """添加警告回调函数"""
        self.warning_callbacks.append(callback)
    
    def add_critical_callback(self, callback: Callable):
        """添加严重告警回调函数"""
        self.critical_callbacks.append(callback)


class MemoryManager:
    """
    内存管理器
    提供内存清理和优化功能
    """
    
    def __init__(self):
        """初始化内存管理器"""
        self.image_cache: Dict[str, np.ndarray] = {}
        self.max_cache_size = PerformanceConstants.MAX_CACHE_SIZE
        self.cache_access_times: Dict[str, float] = {}
        
        # 自动清理定时器
        self.cleanup_timer: Optional[QTimer] = None
        
    def cache_image(self, key: str, image: np.ndarray) -> bool:
        """
        缓存图像
        
        Args:
            key: 缓存键
            image: 图像数据
            
        Returns:
            bool: 是否成功缓存
        """
        if image is None:
            return False
        
        # 检查内存限制
        image_size_mb = image.nbytes / 1024 / 1024
        if image_size_mb > PerformanceConstants.MAX_IMAGE_MEMORY_MB:
            logger.warning(f"图像过大，无法缓存: {image_size_mb:.1f}MB")
            return False
        
        # 如果缓存已满，清理最旧的条目
        if len(self.image_cache) >= self.max_cache_size:
            self._cleanup_oldest_cache()
        
        # 添加到缓存
        self.image_cache[key] = image.copy()
        self.cache_access_times[key] = time.time()
        
        logger.debug(f"图像已缓存: {key}, 大小: {image_size_mb:.1f}MB")
        return True
    
    def get_cached_image(self, key: str) -> Optional[np.ndarray]:
        """
        从缓存获取图像
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[np.ndarray]: 图像数据，不存在则返回None
        """
        if key in self.image_cache:
            self.cache_access_times[key] = time.time()  # 更新访问时间
            return self.image_cache[key].copy()
        return None
    
    def clear_cache(self):
        """清空所有缓存"""
        self.image_cache.clear()
        self.cache_access_times.clear()
        logger.info("图像缓存已清空")
    
    def _cleanup_oldest_cache(self):
        """清理最旧的缓存条目"""
        if not self.cache_access_times:
            return
        
        # 找到最旧的条目
        oldest_key = min(self.cache_access_times.items(), key=lambda x: x[1])[0]
        
        # 删除条目
        if oldest_key in self.image_cache:
            del self.image_cache[oldest_key]
        if oldest_key in self.cache_access_times:
            del self.cache_access_times[oldest_key]
        
        logger.debug(f"已清理旧缓存条目: {oldest_key}")
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """
        强制进行垃圾回收
        
        Returns:
            Dict[str, int]: 垃圾回收统计信息
        """
        logger.info("执行强制垃圾回收")
        
        # 获取回收前的统计
        before_counts = gc.get_count()
        
        # 执行所有代的垃圾回收
        collected = []
        for generation in range(3):
            collected.append(gc.collect(generation))
        
        # 获取回收后的统计
        after_counts = gc.get_count()
        
        stats = {
            "generation_0_collected": collected[0],
            "generation_1_collected": collected[1] if len(collected) > 1 else 0,
            "generation_2_collected": collected[2] if len(collected) > 2 else 0,
            "before_counts": before_counts,
            "after_counts": after_counts
        }
        
        logger.info(f"垃圾回收完成: {stats}")
        return stats
    
    def optimize_memory(self) -> Dict[str, Any]:
        """
        优化内存使用
        
        Returns:
            Dict[str, Any]: 优化结果统计
        """
        logger.info("开始内存优化")
        
        # 记录优化前状态
        before_memory = psutil.Process().memory_info().rss / 1024 / 1024
        before_objects = len(gc.get_objects())
        
        # 1. 清理图像缓存中的过期条目
        expired_keys = []
        current_time = time.time()
        expire_time = PerformanceConstants.CACHE_EXPIRE_MINUTES * 60
        
        for key, access_time in self.cache_access_times.items():
            if current_time - access_time > expire_time:
                expired_keys.append(key)
        
        for key in expired_keys:
            if key in self.image_cache:
                del self.image_cache[key]
            if key in self.cache_access_times:
                del self.cache_access_times[key]
        
        # 2. 强制垃圾回收
        gc_stats = self.force_garbage_collection()
        
        # 3. 记录优化后状态
        after_memory = psutil.Process().memory_info().rss / 1024 / 1024
        after_objects = len(gc.get_objects())
        
        optimization_result = {
            "expired_cache_entries": len(expired_keys),
            "memory_before_mb": before_memory,
            "memory_after_mb": after_memory,
            "memory_saved_mb": before_memory - after_memory,
            "objects_before": before_objects,
            "objects_after": after_objects,
            "objects_collected": before_objects - after_objects,
            "gc_stats": gc_stats
        }
        
        logger.info(f"内存优化完成: 释放了 {optimization_result['memory_saved_mb']:.1f}MB 内存")
        return optimization_result
    
    def start_auto_cleanup(self, interval_minutes: int = 30):
        """
        启动自动清理
        
        Args:
            interval_minutes: 清理间隔（分钟）
        """
        if self.cleanup_timer is not None:
            self.cleanup_timer.stop()
        
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.optimize_memory)
        self.cleanup_timer.start(interval_minutes * 60 * 1000)  # 转换为毫秒
        
        logger.info(f"自动内存清理已启动，间隔: {interval_minutes}分钟")
    
    def stop_auto_cleanup(self):
        """停止自动清理"""
        if self.cleanup_timer is not None:
            self.cleanup_timer.stop()
            self.cleanup_timer = None
            logger.info("自动内存清理已停止")
    
    def get_cache_status(self) -> Dict[str, Any]:
        """
        获取缓存状态
        
        Returns:
            Dict[str, Any]: 缓存状态信息
        """
        total_memory_mb = 0
        for image in self.image_cache.values():
            if hasattr(image, 'nbytes'):
                total_memory_mb += image.nbytes / 1024 / 1024
        
        return {
            "cache_count": len(self.image_cache),
            "max_cache_size": self.max_cache_size,
            "total_memory_mb": total_memory_mb,
            "cache_keys": list(self.image_cache.keys())
        }


# 全局实例
performance_monitor = PerformanceMonitor()
memory_manager = MemoryManager()


def initialize_memory_management():
    """初始化内存管理"""
    # 启动性能监控
    performance_monitor.start_monitoring()
    
    # 启动自动清理
    memory_manager.start_auto_cleanup()
    
    # 添加告警回调
    def memory_warning_handler(snapshot: MemorySnapshot):
        logger.warning(f"内存使用率高: {snapshot.system_memory_percent:.1f}%, "
                      f"建议执行内存清理")
        # 自动执行内存优化
        memory_manager.optimize_memory()
    
    def memory_critical_handler(snapshot: MemorySnapshot):
        logger.critical(f"内存使用率过高: {snapshot.system_memory_percent:.1f}%, "
                       f"强制执行内存清理")
        # 强制清理所有缓存
        memory_manager.clear_cache()
        memory_manager.force_garbage_collection()
    
    performance_monitor.add_warning_callback(memory_warning_handler)
    performance_monitor.add_critical_callback(memory_critical_handler)
    
    logger.info("内存管理系统已初始化")


def cleanup_memory_management():
    """清理内存管理"""
    performance_monitor.stop_monitoring()
    memory_manager.stop_auto_cleanup()
    memory_manager.clear_cache()
    logger.info("内存管理系统已清理") 