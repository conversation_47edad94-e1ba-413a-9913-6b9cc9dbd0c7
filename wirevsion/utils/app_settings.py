#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用程序设置模块

用于管理应用程序的配置和设置
"""

import os
import json
from pathlib import Path
from loguru import logger
from PyQt6.QtCore import QSettings

class AppSettings:
    """应用程序设置管理类"""
    
    # 默认设置
    DEFAULT_SETTINGS = {
        "general": {
            "language": "zh_CN",
            "theme": "dark",
            "auto_save": True,
            "auto_save_interval": 5,  # 分钟
        },
        "camera": {
            "default_resolution": "640x480",
            "default_fps": 30,
            "frame_cache_time": 0.5,  # 秒
        },
        "workflow": {
            "auto_run": False,
            "auto_run_interval": 1.0,  # 秒
            "show_debug_info": True,
        },
        "display": {
            "default_zoom": 1.0,
            "show_grid": True,
            "grid_size": 50,
        },
        "paths": {
            "default_project_dir": "",
            "export_dir": "",
            "recent_projects": [],
        }
    }
    
    @classmethod
    def init_settings(cls):
        """初始化应用程序设置"""
        settings = QSettings("WireVsion", "App")
        
        # 检查设置是否已存在
        if settings.contains("initialized"):
            logger.info("加载现有应用设置")
            return
        
        # 创建默认设置
        logger.info("创建默认应用设置")
        
        # 设置默认值
        for section, section_settings in cls.DEFAULT_SETTINGS.items():
            for key, value in section_settings.items():
                settings.setValue(f"{section}/{key}", value)
        
        # 标记为已初始化
        settings.setValue("initialized", True)
        settings.sync()
    
    @classmethod
    def get_setting(cls, section, key, default=None):
        """获取设置值
        
        Args:
            section: 设置部分
            key: 设置键
            default: 默认值
            
        Returns:
            设置值
        """
        settings = QSettings("WireVsion", "App")
        value = settings.value(f"{section}/{key}", default)
        
        # 确保返回正确的数据类型
        if default is not None:
            if isinstance(default, bool):
                if isinstance(value, str):
                    return value.lower() in ("true", "1", "yes")
                return bool(value)
            elif isinstance(default, int):
                try:
                    return int(value)
                except (ValueError, TypeError):
                    return default
            elif isinstance(default, float):
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default
        
        return value
    
    @classmethod
    def set_setting(cls, section, key, value):
        """设置设置值
        
        Args:
            section: 设置部分
            key: 设置键
            value: 设置值
        """
        settings = QSettings("WireVsion", "App")
        settings.setValue(f"{section}/{key}", value)
        settings.sync()
    
    @classmethod
    def get_camera_settings(cls):
        """获取相机设置
        
        Returns:
            dict: 相机设置字典
        """
        settings = {}
        for key in cls.DEFAULT_SETTINGS["camera"].keys():
            settings[key] = cls.get_setting("camera", key, cls.DEFAULT_SETTINGS["camera"].get(key))
        return settings
    
    @classmethod
    def get_workflow_settings(cls):
        """获取工作流设置
        
        Returns:
            dict: 工作流设置字典
        """
        settings = {}
        for key in cls.DEFAULT_SETTINGS["workflow"].keys():
            settings[key] = cls.get_setting("workflow", key, cls.DEFAULT_SETTINGS["workflow"].get(key))
        return settings
    
    @classmethod
    def add_recent_project(cls, project_path):
        """添加最近项目
        
        Args:
            project_path: 项目路径
        """
        recent_projects = cls.get_setting("paths", "recent_projects", [])
        
        # 确保recent_projects是列表
        if not isinstance(recent_projects, list):
            recent_projects = []
        
        # 移除已存在的项目路径
        if project_path in recent_projects:
            recent_projects.remove(project_path)
        
        # 添加到列表开头
        recent_projects.insert(0, project_path)
        
        # 限制列表长度
        recent_projects = recent_projects[:10]
        
        # 保存更新后的列表
        cls.set_setting("paths", "recent_projects", recent_projects)
    
    @classmethod
    def get_recent_projects(cls):
        """获取最近项目列表
        
        Returns:
            list: 最近项目路径列表
        """
        recent_projects = cls.get_setting("paths", "recent_projects", [])
        
        # 确保返回列表
        if not isinstance(recent_projects, list):
            return []
        
        # 过滤不存在的项目
        return [p for p in recent_projects if os.path.exists(p)]
    
    @classmethod
    def clear_recent_projects(cls):
        """清除最近项目列表"""
        cls.set_setting("paths", "recent_projects", []) 