#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像处理工具模块

提供通用的图像处理功能，减少重复代码
"""

import cv2
import numpy as np
from PyQt6.QtGui import QImage, QPixmap
from typing import Dict, List, Tuple, Optional, Union
from loguru import logger


def cv_to_qimage(cv_img: np.ndarray) -> QImage:
    """
    将OpenCV图像转换为QImage
    
    Args:
        cv_img: OpenCV图像(numpy.ndarray)
        
    Returns:
        QImage: 转换后的QImage
    """
    if cv_img is None:
        return QImage()
        
    # 获取图像维度
    h, w = cv_img.shape[:2]
    
    # 根据图像通道数选择转换方式
    if len(cv_img.shape) == 2:  # 灰度图
        qimg = QImage(cv_img.data, w, h, w, QImage.Format_Grayscale8)
    else:  # 彩色图
        # OpenCV使用BGR顺序，需要转换为RGB
        rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
        bytes_per_line = 3 * w
        qimg = QImage(rgb_img.data, w, h, bytes_per_line, QImage.Format_RGB888)
    
    return qimg


def cv_to_qpixmap(cv_img: np.ndarray) -> QPixmap:
    """
    将OpenCV图像转换为QPixmap
    
    Args:
        cv_img: OpenCV图像(numpy.ndarray)
        
    Returns:
        QPixmap: 转换后的QPixmap
    """
    return QPixmap.fromImage(cv_to_qimage(cv_img))


def resize_image(image: np.ndarray, width: int = None, height: int = None) -> np.ndarray:
    """
    调整图像大小，保持宽高比
    
    Args:
        image: 输入图像
        width: 目标宽度，如果为None则根据高度计算
        height: 目标高度，如果为None则根据宽度计算
        
    Returns:
        np.ndarray: 调整大小后的图像
    """
    if image is None:
        return None
        
    # 获取原始尺寸
    h, w = image.shape[:2]
    
    # 如果宽度和高度都未指定，直接返回原图
    if width is None and height is None:
        return image
    
    # 计算调整后的尺寸
    if width is None:
        # 根据高度计算宽度
        r = height / float(h)
        dim = (int(w * r), height)
    elif height is None:
        # 根据宽度计算高度
        r = width / float(w)
        dim = (width, int(h * r))
    else:
        # 宽度和高度都指定，不保持宽高比
        dim = (width, height)
    
    # 调整图像大小
    resized = cv2.resize(image, dim, interpolation=cv2.INTER_AREA)
    
    return resized


def draw_text(image: np.ndarray, text: str, position: Tuple[int, int], font_scale: float = 1.0, color: Tuple[int, int, int] = (255, 255, 255), thickness: int = 1) -> np.ndarray:
    """
    在图像上绘制文本
    
    Args:
        image: 输入图像
        text: 要绘制的文本
        position: 文本位置(x, y)
        font_scale: 字体大小
        color: 文本颜色
        thickness: 线条粗细
        
    Returns:
        np.ndarray: 绘制文本后的图像
    """
    if image is None:
        return None
        
    # 复制图像
    result = image.copy()
    
    # 绘制文本
    cv2.putText(
        result, text, position,
        cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness
    )
    
    return result


class ImageUtils:
    """图像处理工具类"""
    
    @staticmethod
    def validate_coordinates(x: int, y: int, w: int, h: int, 
                           image_shape: Tuple[int, int]) -> Tuple[int, int, int, int]:
        """
        验证并修正坐标范围，确保在图像范围内
        
        Args:
            x: X坐标
            y: Y坐标
            w: 宽度
            h: 高度
            image_shape: 图像形状 (height, width)
            
        Returns:
            修正后的坐标 (x, y, w, h)
        """
        height, width = image_shape[:2]
        
        # 修正坐标范围
        x = max(0, min(x, width - 1))
        y = max(0, min(y, height - 1))
        w = min(w, width - x)
        h = min(h, height - y)
        
        # 确保宽度和高度为正数
        w = max(1, w)
        h = max(1, h)
        
        return x, y, w, h
    
    @staticmethod
    def create_interpolation_flags() -> Dict[str, int]:
        """
        创建插值方法映射
        
        Returns:
            插值方法字典
        """
        return {
            "linear": cv2.INTER_LINEAR,
            "cubic": cv2.INTER_CUBIC,
            "nearest": cv2.INTER_NEAREST,
            "area": cv2.INTER_AREA,
            "lanczos4": cv2.INTER_LANCZOS4
        }
    
    @staticmethod
    def create_border_flags() -> Dict[str, int]:
        """
        创建边界模式映射
        
        Returns:
            边界模式字典
        """
        return {
            "constant": cv2.BORDER_CONSTANT,
            "reflect": cv2.BORDER_REFLECT,
            "wrap": cv2.BORDER_WRAP,
            "replicate": cv2.BORDER_REPLICATE,
            "mirror": cv2.BORDER_REFLECT_101
        }
    
    @staticmethod
    def create_morphology_kernels() -> Dict[str, int]:
        """
        创建形态学内核映射
        
        Returns:
            形态学内核字典
        """
        return {
            "rectangle": cv2.MORPH_RECT,
            "ellipse": cv2.MORPH_ELLIPSE,
            "cross": cv2.MORPH_CROSS
        }
    
    @staticmethod
    def create_morphology_operations() -> Dict[str, int]:
        """
        创建形态学操作映射
        
        Returns:
            形态学操作字典
        """
        return {
            "erode": cv2.MORPH_ERODE,
            "dilate": cv2.MORPH_DILATE,
            "open": cv2.MORPH_OPEN,
            "close": cv2.MORPH_CLOSE,
            "gradient": cv2.MORPH_GRADIENT,
            "tophat": cv2.MORPH_TOPHAT,
            "blackhat": cv2.MORPH_BLACKHAT
        }
    
    @staticmethod
    def resize_image_keep_aspect(image: np.ndarray, target_width: int, target_height: int,
                               background_color: Tuple[int, int, int] = (0, 0, 0)) -> np.ndarray:
        """
        保持宽高比缩放图像
        
        Args:
            image: 输入图像
            target_width: 目标宽度
            target_height: 目标高度
            background_color: 背景颜色
            
        Returns:
            缩放后的图像
        """
        h, w = image.shape[:2]
        aspect_ratio = w / h
        
        # 计算新的尺寸
        if target_width / target_height > aspect_ratio:
            # 高度优先
            new_height = target_height
            new_width = int(target_height * aspect_ratio)
        else:
            # 宽度优先
            new_width = target_width
            new_height = int(target_width / aspect_ratio)
        
        # 缩放图像
        resized = cv2.resize(image, (new_width, new_height))
        
        # 创建目标尺寸的背景
        if len(image.shape) == 3:
            result = np.full((target_height, target_width, 3), background_color, dtype=image.dtype)
        else:
            result = np.full((target_height, target_width), background_color[0], dtype=image.dtype)
        
        # 计算居中位置
        y_offset = (target_height - new_height) // 2
        x_offset = (target_width - new_width) // 2
        
        # 将缩放后的图像放到中心
        result[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
        
        return result
    
    @staticmethod
    def convert_color_space(image: np.ndarray, color_space: str) -> np.ndarray:
        """
        转换颜色空间
        
        Args:
            image: 输入图像 (BGR格式)
            color_space: 目标颜色空间
            
        Returns:
            转换后的图像
        """
        color_space_map = {
            "BGR": image,  # 原始格式
            "RGB": cv2.cvtColor(image, cv2.COLOR_BGR2RGB),
            "GRAY": cv2.cvtColor(image, cv2.COLOR_BGR2GRAY),
            "HSV": cv2.cvtColor(image, cv2.COLOR_BGR2HSV),
            "LAB": cv2.cvtColor(image, cv2.COLOR_BGR2LAB),
            "HLS": cv2.cvtColor(image, cv2.COLOR_BGR2HLS),
            "YUV": cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
        }
        
        if color_space not in color_space_map:
            logger.warning(f"不支持的颜色空间: {color_space}，使用原始格式")
            return image
        
        return color_space_map[color_space]
    
    @staticmethod
    def apply_preprocessing(image: np.ndarray, preprocessing_config: Dict) -> np.ndarray:
        """
        应用图像预处理
        
        Args:
            image: 输入图像
            preprocessing_config: 预处理配置
            
        Returns:
            预处理后的图像
        """
        result = image.copy()
        
        # 高斯模糊
        if preprocessing_config.get("gaussian_blur", False):
            kernel_size = preprocessing_config.get("kernel_size", 5)
            if kernel_size % 2 == 0:
                kernel_size += 1
            result = cv2.GaussianBlur(result, (kernel_size, kernel_size), 0)
        
        # 中值滤波
        if preprocessing_config.get("median_blur", False):
            kernel_size = preprocessing_config.get("median_kernel_size", 5)
            if kernel_size % 2 == 0:
                kernel_size += 1
            result = cv2.medianBlur(result, kernel_size)
        
        # 双边滤波
        if preprocessing_config.get("bilateral_filter", False):
            d = preprocessing_config.get("bilateral_d", 9)
            sigma_color = preprocessing_config.get("bilateral_sigma_color", 75)
            sigma_space = preprocessing_config.get("bilateral_sigma_space", 75)
            result = cv2.bilateralFilter(result, d, sigma_color, sigma_space)
        
        # 直方图均衡化
        if preprocessing_config.get("equalize_hist", False):
            if len(result.shape) == 3:
                # 彩色图像，转换到YUV空间进行均衡化
                yuv = cv2.cvtColor(result, cv2.COLOR_BGR2YUV)
                yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
                result = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
            else:
                result = cv2.equalizeHist(result)
        
        # 对比度增强
        if preprocessing_config.get("enhance_contrast", False):
            alpha = preprocessing_config.get("contrast_alpha", 1.2)
            beta = preprocessing_config.get("contrast_beta", 0)
            result = cv2.convertScaleAbs(result, alpha=alpha, beta=beta)
        
        return result
    
    @staticmethod
    def create_mask_from_contours(image_shape: Tuple[int, int], 
                                contours: List[np.ndarray],
                                fill_value: int = 255) -> np.ndarray:
        """
        从轮廓创建掩码
        
        Args:
            image_shape: 图像形状 (height, width)
            contours: 轮廓列表
            fill_value: 填充值
            
        Returns:
            掩码图像
        """
        mask = np.zeros(image_shape, dtype=np.uint8)
        cv2.fillPoly(mask, contours, fill_value)
        return mask
    
    @staticmethod
    def find_largest_contour(contours: List[np.ndarray]) -> Optional[np.ndarray]:
        """
        找到最大的轮廓
        
        Args:
            contours: 轮廓列表
            
        Returns:
            最大轮廓，如果没有轮廓则返回None
        """
        if not contours:
            return None
        
        # 计算面积并找到最大的
        areas = [cv2.contourArea(contour) for contour in contours]
        max_area_index = np.argmax(areas)
        return contours[max_area_index]
    
    @staticmethod
    def filter_contours_by_area(contours: List[np.ndarray], 
                              min_area: float = 0, 
                              max_area: float = float('inf')) -> List[np.ndarray]:
        """
        根据面积过滤轮廓
        
        Args:
            contours: 轮廓列表
            min_area: 最小面积
            max_area: 最大面积
            
        Returns:
            过滤后的轮廓列表
        """
        filtered_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area <= area <= max_area:
                filtered_contours.append(contour)
        return filtered_contours
    
    @staticmethod
    def get_contour_features(contour: np.ndarray) -> Dict:
        """
        获取轮廓特征
        
        Args:
            contour: 输入轮廓
            
        Returns:
            轮廓特征字典
        """
        # 基本特征
        area = cv2.contourArea(contour)
        perimeter = cv2.arcLength(contour, True)
        
        # 边界矩形
        x, y, w, h = cv2.boundingRect(contour)
        
        # 最小外接矩形
        rect = cv2.minAreaRect(contour)
        box = cv2.boxPoints(rect)
        box = np.int0(box)
        
        # 最小外接圆
        (center_x, center_y), radius = cv2.minEnclosingCircle(contour)
        
        # 椭圆拟合
        ellipse = None
        if len(contour) >= 5:
            ellipse = cv2.fitEllipse(contour)
        
        # 形状描述符
        aspect_ratio = float(w) / h if h > 0 else 0
        extent = float(area) / (w * h) if w * h > 0 else 0
        solidity = float(area) / cv2.contourArea(cv2.convexHull(contour)) if area > 0 else 0
        
        return {
            'area': area,
            'perimeter': perimeter,
            'bounding_rect': (x, y, w, h),
            'min_area_rect': rect,
            'min_area_rect_box': box,
            'min_enclosing_circle': ((center_x, center_y), radius),
            'ellipse': ellipse,
            'aspect_ratio': aspect_ratio,
            'extent': extent,
            'solidity': solidity
        }
    
    @staticmethod
    def draw_contours_with_info(image: np.ndarray, contours: List[np.ndarray], 
                              color: Tuple[int, int, int] = (0, 255, 0),
                              thickness: int = 2,
                              show_area: bool = True) -> np.ndarray:
        """
        绘制轮廓及其信息
        
        Args:
            image: 输入图像
            contours: 轮廓列表
            color: 绘制颜色
            thickness: 线条粗细
            show_area: 是否显示面积信息
            
        Returns:
            绘制后的图像
        """
        result = image.copy()
        
        for i, contour in enumerate(contours):
            # 绘制轮廓
            cv2.drawContours(result, [contour], -1, color, thickness)
            
            if show_area:
                # 计算并显示面积
                area = cv2.contourArea(contour)
                
                # 找到轮廓的质心
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 绘制面积文本
                    text = f"Area: {area:.0f}"
                    cv2.putText(result, text, (cx - 50, cy), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return result
    
    @staticmethod
    def rotate_image(image: np.ndarray, angle: float, 
                   center: Optional[Tuple[int, int]] = None,
                   scale: float = 1.0,
                   border_mode: str = "constant",
                   border_value: Union[int, Tuple[int, int, int]] = 0) -> np.ndarray:
        """
        旋转图像
        
        Args:
            image: 输入图像
            angle: 旋转角度（度）
            center: 旋转中心，None表示图像中心
            scale: 缩放因子
            border_mode: 边界模式
            border_value: 边界值
            
        Returns:
            旋转后的图像
        """
        (h, w) = image.shape[:2]
        
        if center is None:
            center = (w // 2, h // 2)
        
        # 计算旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, scale)
        
        # 获取边界模式
        border_flags = ImageUtils.create_border_flags()
        border_mode_flag = border_flags.get(border_mode, cv2.BORDER_CONSTANT)
        
        # 执行旋转
        rotated = cv2.warpAffine(
            image, rotation_matrix, (w, h),
            borderMode=border_mode_flag,
            borderValue=border_value
        )
        
        return rotated
    
    @staticmethod
    def calculate_image_statistics(image: np.ndarray, mask: Optional[np.ndarray] = None) -> Dict:
        """
        计算图像统计信息
        
        Args:
            image: 输入图像
            mask: 可选的掩码
            
        Returns:
            统计信息字典
        """
        stats = {}
        
        if len(image.shape) == 3:
            # 彩色图像
            for i, channel_name in enumerate(['B', 'G', 'R']):
                channel = image[:, :, i]
                stats[f'channel_{channel_name}'] = {
                    'mean': cv2.mean(channel, mask)[0],
                    'std': np.std(channel[mask > 0] if mask is not None else channel),
                    'min': np.min(channel[mask > 0] if mask is not None else channel),
                    'max': np.max(channel[mask > 0] if mask is not None else channel)
                }
        else:
            # 灰度图像
            stats['grayscale'] = {
                'mean': cv2.mean(image, mask)[0],
                'std': np.std(image[mask > 0] if mask is not None else image),
                'min': np.min(image[mask > 0] if mask is not None else image),
                'max': np.max(image[mask > 0] if mask is not None else image)
            }
        
        return stats


class TemplateMatchingUtils:
    """模板匹配工具类"""
    
    @staticmethod
    def get_match_method(method_name: str) -> int:
        """
        获取模板匹配方法常量
        
        Args:
            method_name: 方法名称
            
        Returns:
            OpenCV匹配方法常量
        """
        method_map = {
            "TM_CCOEFF": cv2.TM_CCOEFF,
            "TM_CCOEFF_NORMED": cv2.TM_CCOEFF_NORMED,
            "TM_CCORR": cv2.TM_CCORR,
            "TM_CCORR_NORMED": cv2.TM_CCORR_NORMED,
            "TM_SQDIFF": cv2.TM_SQDIFF,
            "TM_SQDIFF_NORMED": cv2.TM_SQDIFF_NORMED,
            # 简化版本
            "CCOEFF": cv2.TM_CCOEFF,
            "CCOEFF_NORMED": cv2.TM_CCOEFF_NORMED,
            "CCORR": cv2.TM_CCORR,
            "CCORR_NORMED": cv2.TM_CCORR_NORMED,
            "SQDIFF": cv2.TM_SQDIFF,
            "SQDIFF_NORMED": cv2.TM_SQDIFF_NORMED
        }
        return method_map.get(method_name, cv2.TM_CCOEFF_NORMED)
    
    @staticmethod
    def non_max_suppression(boxes: List[Tuple[int, int, int, int]], 
                          scores: List[float], 
                          threshold: float = 0.3) -> List[int]:
        """
        非极大值抑制
        
        Args:
            boxes: 边界框列表 [(x, y, w, h), ...]
            scores: 对应的分数列表
            threshold: 重叠阈值
            
        Returns:
            保留的索引列表
        """
        if not boxes:
            return []
        
        # 转换为numpy数组
        boxes = np.array(boxes, dtype=np.float32)
        scores = np.array(scores, dtype=np.float32)
        
        # 计算面积
        areas = boxes[:, 2] * boxes[:, 3]
        
        # 按分数排序
        order = scores.argsort()[::-1]
        
        keep = []
        while order.size > 0:
            # 保留得分最高的框
            i = order[0]
            keep.append(i)
            
            if order.size == 1:
                break
            
            # 计算IoU
            xx1 = np.maximum(boxes[i, 0], boxes[order[1:], 0])
            yy1 = np.maximum(boxes[i, 1], boxes[order[1:], 1])
            xx2 = np.minimum(boxes[i, 0] + boxes[i, 2], boxes[order[1:], 0] + boxes[order[1:], 2])
            yy2 = np.minimum(boxes[i, 1] + boxes[i, 3], boxes[order[1:], 1] + boxes[order[1:], 3])
            
            w = np.maximum(0, xx2 - xx1)
            h = np.maximum(0, yy2 - yy1)
            intersection = w * h
            
            union = areas[i] + areas[order[1:]] - intersection
            iou = intersection / union
            
            # 保留IoU小于阈值的框
            indices = np.where(iou <= threshold)[0]
            order = order[indices + 1]
        
        return keep


class ColorDetectionUtils:
    """颜色检测工具类"""
    
    @staticmethod
    def create_color_range_hsv(hue_center: int, hue_range: int = 10,
                             saturation_min: int = 50, saturation_max: int = 255,
                             value_min: int = 50, value_max: int = 255) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建HSV颜色范围
        
        Args:
            hue_center: 色调中心值
            hue_range: 色调范围
            saturation_min: 饱和度最小值
            saturation_max: 饱和度最大值
            value_min: 明度最小值
            value_max: 明度最大值
            
        Returns:
            (lower_bound, upper_bound)
        """
        lower_bound = np.array([
            max(0, hue_center - hue_range),
            saturation_min,
            value_min
        ])
        
        upper_bound = np.array([
            min(179, hue_center + hue_range),
            saturation_max,
            value_max
        ])
        
        return lower_bound, upper_bound
    
    @staticmethod
    def get_dominant_colors(image: np.ndarray, k: int = 5) -> List[Tuple[int, int, int]]:
        """
        获取图像中的主要颜色
        
        Args:
            image: 输入图像
            k: 颜色聚类数量
            
        Returns:
            主要颜色列表
        """
        from sklearn.cluster import KMeans
        
        # 重塑图像数据
        data = image.reshape((-1, 3))
        data = np.float32(data)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=k, random_state=42)
        kmeans.fit(data)
        
        # 获取聚类中心（主要颜色）
        colors = kmeans.cluster_centers_
        colors = np.uint8(colors)
        
        return [tuple(color) for color in colors]
