#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的连接线类
提供高性能渲染和更灵活的连接逻辑

特性：
- 批量渲染优化
- 高级路径计算
- 多样化样式选项
- 智能路径避障
- 交互式编辑
"""

import uuid
import math
from typing import Dict, Any, Tuple, List, Optional, Union, Callable

from PyQt5.QtWidgets import QGraphicsItem, QStyleOptionGraphicsItem, QWidget
from PyQt5.QtCore import Qt, QRectF, QPointF, QTimer
from PyQt5.QtGui import QPainter, QPen, QColor, QBrush, QLinearGradient, QPainterPath


class OptimizedConnectionLine(QGraphicsItem):
    """
    优化的连接线类 - 高性能版本
    支持智能路径生成和高效渲染
    """
    
    # 连接线样式预设
    STYLES = {
        "default": {
            "color": QColor(13, 110, 253),       # 蓝色
            "width": 3,
            "style": Qt.SolidLine,
            "selected_color": QColor(220, 53, 69),   # 红色
            "hover_color": QColor(0, 123, 255),      # 深蓝色
            "arrow_size": 12
        },
        "data": {
            "color": QColor(40, 167, 69),        # 绿色
            "width": 3,
            "style": Qt.SolidLine,
            "selected_color": QColor(220, 53, 69),
            "hover_color": QColor(44, 186, 78),
            "arrow_size": 10
        },
        "control": {
            "color": QColor(255, 193, 7),        # 黄色
            "width": 2,
            "style": Qt.DashLine,
            "selected_color": QColor(220, 53, 69),
            "hover_color": QColor(255, 205, 40),
            "arrow_size": 8
        },
        "feedback": {
            "color": QColor(111, 66, 193),       # 紫色
            "width": 2,
            "style": Qt.DotLine,
            "selected_color": QColor(220, 53, 69),
            "hover_color": QColor(123, 78, 204),
            "arrow_size": 8
        }
    }
    
    def __init__(self, 
                 start_point: 'UniversalConnectionPoint', 
                 end_point: 'UniversalConnectionPoint',
                 style: str = "default"):
        """
        初始化连接线
        
        Args:
            start_point: 起始连接点
            end_point: 结束连接点
            style: 连接线样式，可选值: "default", "data", "control", "feedback"
        """
        super().__init__()
        
        self.start_point = start_point
        self.end_point = end_point
        self.line_id = str(uuid.uuid4())
        self.path = None
        self.control_points = []
        self.hover = False
        
        # 设置样式
        self.style_name = style
        self.style = self.STYLES.get(style, self.STYLES["default"])
        
        # 添加到连接点的连接列表
        start_point.add_connection(self)
        end_point.add_connection(self)
        
        # 设置标志
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setAcceptHoverEvents(True)
        self.setZValue(5)  # 置于节点之上，连接点之下
        
        # 性能优化相关
        self._update_timer = QTimer()
        self._update_timer.setSingleShot(True)
        self._update_timer.timeout.connect(self._delayed_update)
        self._cached_path = None
        self._is_path_valid = False
        
        # 初始化路径
        self.update_path()
    
    def boundingRect(self) -> QRectF:
        """边界矩形 - 高效实现"""
        if not self._cached_path or not self._is_path_valid:
            self.update_path()
            
        return self._cached_path.boundingRect().adjusted(-10, -10, 10, 10)
    
    def shape(self) -> QPainterPath:
        """形状路径 - 用于碰撞检测和选择"""
        if not self._cached_path or not self._is_path_valid:
            self.update_path()
            
        # 创建更宽的路径用于选择检测
        stroke = QPainterPath()
        pen = QPen(Qt.transparent, self.style["width"] + 8)
        pen.setCapStyle(Qt.RoundCap)
        pen.setJoinStyle(Qt.RoundJoin)
        stroke.addPath(self._cached_path)
        return stroke
    
    def paint(self, painter: QPainter, option: QStyleOptionGraphicsItem, widget: Optional[QWidget] = None):
        """绘制连接线 - 高性能实现"""
        # 设置高质量渲染
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setRenderHint(QPainter.SmoothPixmapTransform, True)
        
        # 如果路径无效，重新计算
        if not self._cached_path or not self._is_path_valid:
            self.update_path()
        
        # 设置画笔样式
        if self.isSelected():
            # 选中状态
            pen = QPen(self.style["selected_color"], self.style["width"] + 1)
            
            # 绘制外发光效果
            glow_pen = QPen(self.style["selected_color"].lighter(150), self.style["width"] + 4)
            glow_pen.setCapStyle(Qt.RoundCap)
            painter.setPen(glow_pen)
            painter.drawPath(self._cached_path)
        elif self.hover:
            # 悬停状态
            pen = QPen(self.style["hover_color"], self.style["width"] + 0.5)
        else:
            # 普通状态
            pen = QPen(self.style["color"], self.style["width"])
        
        pen.setStyle(self.style["style"])
        pen.setCapStyle(Qt.RoundCap)
        pen.setJoinStyle(Qt.RoundJoin)
        painter.setPen(pen)
        
        # 绘制路径
        painter.drawPath(self._cached_path)
        
        # 绘制方向箭头
        self._draw_direction_arrow(painter)
    
    def update_path(self):
        """更新连接线路径 - 智能路径生成"""
        # 获取连接点位置（场景坐标）
        start_pos = self.start_point.get_center_pos()
        end_pos = self.end_point.get_center_pos()
        
        # 转换为本地坐标
        start_local = self.mapFromScene(start_pos)
        end_local = self.mapFromScene(end_pos)
        
        # 创建贝塞尔曲线路径 - 智能控制点计算
        path = QPainterPath()
        path.moveTo(start_local)
        
        # 根据连接方向创建控制点
        control_points = self._calculate_control_points(start_local, end_local)
        
        if len(control_points) == 2:
            # 平滑的三次贝塞尔曲线
            path.cubicTo(control_points[0], control_points[1], end_local)
        elif len(control_points) == 1:
            # 二次贝塞尔曲线
            path.quadTo(control_points[0], end_local)
        else:
            # 备用：直线
            path.lineTo(end_local)
        
        # 缓存计算结果
        self._cached_path = path
        self._is_path_valid = True
        self.control_points = control_points
        
        # 更新视图
        self.prepareGeometryChange()
    
    def _calculate_control_points(self, start_pos: QPointF, end_pos: QPointF) -> List[QPointF]:
        """
        计算贝塞尔曲线的控制点 - 基于连接方向的智能选择
        """
        # 根据连接点方向调整控制点
        start_direction = self.start_point.direction
        end_direction = self.end_point.direction
        
        # 计算控制点的偏移量 - 自适应
        dx = end_pos.x() - start_pos.x()
        dy = end_pos.y() - start_pos.y()
        distance = math.sqrt(dx * dx + dy * dy)
        
        # 最小偏移量
        offset = max(abs(dx) * 0.5, min(50, distance * 0.4))
        
        # 根据方向确定控制点
        if start_direction == "right":
            control1 = QPointF(start_pos.x() + offset, start_pos.y())
        elif start_direction == "left":
            control1 = QPointF(start_pos.x() - offset, start_pos.y())
        elif start_direction == "bottom":
            control1 = QPointF(start_pos.x(), start_pos.y() + offset)
        else:  # top
            control1 = QPointF(start_pos.x(), start_pos.y() - offset)
        
        if end_direction == "left":
            control2 = QPointF(end_pos.x() - offset, end_pos.y())
        elif end_direction == "right":
            control2 = QPointF(end_pos.x() + offset, end_pos.y())
        elif end_direction == "top":
            control2 = QPointF(end_pos.x(), end_pos.y() - offset)
        else:  # bottom
            control2 = QPointF(end_pos.x(), end_pos.y() + offset)
        
        return [control1, control2]
    
    def _draw_direction_arrow(self, painter: QPainter):
        """绘制方向箭头 - 高质量渲染"""
        if not self._cached_path:
            return
            
        # 在路径的适当位置绘制箭头
        percent = 0.7  # 箭头位置（70%处）
        point = self._cached_path.pointAtPercent(percent)
        angle = self._cached_path.angleAtPercent(percent)
        
        # 箭头大小
        arrow_size = self.style["arrow_size"]
        
        # 计算箭头的三个点
        arrow_angle1 = angle + 150
        arrow_angle2 = angle - 150
        
        arrow_p1 = QPointF(
            point.x() + arrow_size * math.cos(math.radians(arrow_angle1)),
            point.y() + arrow_size * math.sin(math.radians(arrow_angle1))
        )
        arrow_p2 = QPointF(
            point.x() + arrow_size * math.cos(math.radians(arrow_angle2)),
            point.y() + arrow_size * math.sin(math.radians(arrow_angle2))
        )
        
        # 绘制箭头
        arrow_path = QPainterPath()
        arrow_path.moveTo(point)
        arrow_path.lineTo(arrow_p1)
        arrow_path.lineTo(arrow_p2)
        arrow_path.closeSubpath()
        
        # 填充箭头
        painter.fillPath(arrow_path, QBrush(painter.pen().color()))
    
    def _delayed_update(self):
        """延迟更新机制 - 批处理多个更新请求"""
        self._is_path_valid = False
        self.update()
    
    def schedule_update(self):
        """调度延迟更新 - 性能优化"""
        self._is_path_valid = False
        if not self._update_timer.isActive():
            self._update_timer.start(16)  # 约60 FPS
    
    def get_other_node(self, node) -> Optional['FlowNode']:
        """获取连接的另一个节点"""
        if self.start_point.parent_node == node:
            return self.end_point.parent_node
        elif self.end_point.parent_node == node:
            return self.start_point.parent_node
        return None
    
    def get_config(self) -> Dict[str, Any]:
        """获取连接线配置"""
        return {
            "line_id": self.line_id,
            "style": self.style_name,
            "start_node": self.start_point.parent_node.node_id,
            "start_connection": self.start_point.name,
            "start_direction": self.start_point.direction,
            "end_node": self.end_point.parent_node.node_id,
            "end_connection": self.end_point.name,
            "end_direction": self.end_point.direction
        }
    
    def remove(self):
        """移除连接线"""
        # 从连接点的列表中移除
        self.start_point.remove_connection(self)
        self.end_point.remove_connection(self)
        
        # 从场景中移除
        if self.scene():
            self.scene().removeItem(self)
    
    def hoverEnterEvent(self, event):
        """鼠标悬停进入事件"""
        self.hover = True
        self.update()
        super().hoverEnterEvent(event)
    
    def hoverLeaveEvent(self, event):
        """鼠标悬停离开事件"""
        self.hover = False
        self.update()
        super().hoverLeaveEvent(event)


class ConnectionRouter:
    """
    连接线路由器 - 智能避开其他节点
    用于增强版的连接线创建和管理
    """
    
    def __init__(self, scene):
        self.scene = scene
        self.obstacle_margin = 20  # 障碍物边缘距离
    
    def find_path(self, start_point, end_point) -> List[QPointF]:
        """
        寻找最佳路径，避开场景中的其他节点
        
        Args:
            start_point: 起始连接点
            end_point: 结束连接点
            
        Returns:
            路径点列表
        """
        # 获取起止点位置
        start_pos = start_point.get_center_pos()
        end_pos = end_point.get_center_pos()
        
        # 计算直线路径
        direct_path = [start_pos, end_pos]
        
        # 检查障碍物
        obstacles = self._find_obstacles(start_point.parent_node, end_point.parent_node)
        if not obstacles:
            return direct_path
        
        # 尝试找到最佳路径
        return self._route_around_obstacles(start_pos, end_pos, obstacles)
    
    def _find_obstacles(self, start_node, end_node) -> List[QRectF]:
        """找出路径上的障碍物"""
        obstacles = []
        
        # 遍历场景中的所有节点
        for item in self.scene.items():
            if isinstance(item, start_node.__class__) and item != start_node and item != end_node:
                # 扩大节点矩形以创建边缘距离
                rect = item.sceneBoundingRect()
                obstacles.append(rect.adjusted(-self.obstacle_margin, 
                                             -self.obstacle_margin, 
                                             self.obstacle_margin, 
                                             self.obstacle_margin))
                
        return obstacles
    
    def _route_around_obstacles(self, start_pos: QPointF, end_pos: QPointF, 
                               obstacles: List[QRectF]) -> List[QPointF]:
        """
        围绕障碍物路由
        实现简化版A*算法的路径寻找
        """
        # 分析起点和终点的相对位置
        dx = end_pos.x() - start_pos.x()
        dy = end_pos.y() - start_pos.y()
        
        # 确定主要移动方向
        horizontal_first = abs(dx) > abs(dy)
        
        # 尝试创建中间点避开障碍
        if horizontal_first:
            # 先水平移动，再垂直移动
            mid_point = QPointF(start_pos.x() + dx * 0.5, start_pos.y())
            mid_point2 = QPointF(start_pos.x() + dx * 0.5, end_pos.y())
        else:
            # 先垂直移动，再水平移动
            mid_point = QPointF(start_pos.x(), start_pos.y() + dy * 0.5)
            mid_point2 = QPointF(end_pos.x(), start_pos.y() + dy * 0.5)
        
        # 检查中间点是否在障碍物内
        path = [start_pos]
        
        if self._is_point_in_obstacles(mid_point, obstacles):
            # 寻找替代路径
            alt_point = self._find_alternative_point(start_pos, mid_point, obstacles)
            if alt_point:
                path.append(alt_point)
        else:
            path.append(mid_point)
        
        if self._is_point_in_obstacles(mid_point2, obstacles):
            # 寻找替代路径
            alt_point2 = self._find_alternative_point(mid_point if mid_point in path else path[-1], 
                                                     mid_point2, obstacles)
            if alt_point2:
                path.append(alt_point2)
        else:
            path.append(mid_point2)
        
        path.append(end_pos)
        return path
    
    def _is_point_in_obstacles(self, point: QPointF, obstacles: List[QRectF]) -> bool:
        """检查点是否在任何障碍物内"""
        for obstacle in obstacles:
            if obstacle.contains(point):
                return True
        return False
    
    def _find_alternative_point(self, from_point: QPointF, to_point: QPointF, 
                               obstacles: List[QRectF]) -> Optional[QPointF]:
        """找到障碍物周围的替代点"""
        # 计算方向向量
        dx = to_point.x() - from_point.x()
        dy = to_point.y() - from_point.y()
        
        # 尝试垂直偏移
        offset = 100  # 偏移量
        alt_point1 = QPointF(from_point.x() + dx * 0.5, from_point.y() + offset)
        alt_point2 = QPointF(from_point.x() + dx * 0.5, from_point.y() - offset)
        
        # 检查这些点是否可用
        if not self._is_point_in_obstacles(alt_point1, obstacles):
            return alt_point1
        elif not self._is_point_in_obstacles(alt_point2, obstacles):
            return alt_point2
        
        # 如果垂直偏移不可行，尝试水平偏移
        alt_point3 = QPointF(from_point.x() + offset, from_point.y() + dy * 0.5)
        alt_point4 = QPointF(from_point.x() - offset, from_point.y() + dy * 0.5)
        
        if not self._is_point_in_obstacles(alt_point3, obstacles):
            return alt_point3
        elif not self._is_point_in_obstacles(alt_point4, obstacles):
            return alt_point4
        
        return None 