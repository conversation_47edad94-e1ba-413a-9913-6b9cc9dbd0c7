#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像源配置界面模块
提供本地图像加载和相机捕获两种图像源配置
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
    QPushButton, QLabel, QFileDialog, QFormLayout,
    QGroupBox, QComboBox, QSpinBox, QSlider, QCheckBox,
    QMessageBox, QRadioButton, QButtonGroup
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QPixmap, QImage

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple
from loguru import logger

from wirevsion.config.config_manager import ConfigManager
from wirevsion.camera.camera_manager import CameraManager
from wirevsion.utils.image_utils import cv_to_qpixmap, resize_image


class ImageSourceWidget(QWidget):
    """
    图像源配置界面
    提供本地图像加载和相机捕获两种图像源配置
    """
    
    # 信号：图像源已更改
    image_source_changed = pyqtSignal(dict)
    # 信号：图像已获取
    image_captured = pyqtSignal(np.ndarray)
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化图像源配置界面
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_manager = config_manager
        
        # 创建相机管理器
        self.camera_manager = CameraManager()
        
        # 当前图像
        self.current_image = None
        
        # 图像源配置
        self.image_source_config = {
            "type": "local",  # 本地图像或相机
            "local": {
                "path": ""
            },
            "camera": {
                "id": "",
                "format": "MONO8",
                "interval": 0,
                "auto_capture": True,
                "save_images": False
            }
        }
        
        # 创建UI组件
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI组件"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建图像源选择分组
        source_group = QGroupBox("图像源")
        source_layout = QVBoxLayout()
        
        # 图像源选择单选按钮
        source_option_layout = QHBoxLayout()
        
        self.local_radio = QRadioButton("本地图像")
        self.camera_radio = QRadioButton("相机采集")
        
        self.source_group = QButtonGroup()
        self.source_group.addButton(self.local_radio, 0)
        self.source_group.addButton(self.camera_radio, 1)
        self.source_group.buttonClicked.connect(self._handle_source_changed)
        
        # 默认选择本地图像
        self.local_radio.setChecked(True)
        
        source_option_layout.addWidget(self.local_radio)
        source_option_layout.addWidget(self.camera_radio)
        source_option_layout.addStretch()
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 本地图像选项卡
        local_tab = QWidget()
        local_layout = QVBoxLayout(local_tab)
        
        # 文件选择
        file_layout = QHBoxLayout()
        
        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("background-color: #444; padding: 5px;")
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self._browse_image)
        
        file_layout.addWidget(self.file_path_label, 1)
        file_layout.addWidget(browse_btn)
        
        # 图像预览
        preview_group = QGroupBox("图像预览")
        preview_layout = QVBoxLayout()
        
        self.image_preview = QLabel("无图像")
        self.image_preview.setAlignment(Qt.AlignCenter)
        self.image_preview.setMinimumSize(400, 300)
        self.image_preview.setStyleSheet("border: 1px solid #CCCCCC; background-color: #444444;")
        
        preview_layout.addWidget(self.image_preview)
        
        preview_group.setLayout(preview_layout)
        
        # 添加到本地图像布局
        local_layout.addLayout(file_layout)
        local_layout.addWidget(preview_group)
        local_layout.addStretch()
        
        # 相机选项卡
        camera_tab = QWidget()
        camera_layout = QVBoxLayout(camera_tab)
        
        # 相机设置
        settings_group = QGroupBox("相机设置")
        settings_layout = QFormLayout()
        
        # 相机选择
        self.camera_combo = QComboBox()
        self.camera_combo.setMinimumWidth(300)
        self._refresh_camera_list()
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self._refresh_camera_list)
        
        camera_select_layout = QHBoxLayout()
        camera_select_layout.addWidget(self.camera_combo)
        camera_select_layout.addWidget(refresh_btn)
        
        # 图像格式
        self.format_combo = QComboBox()
        self.format_combo.addItem("MONO8")
        self.format_combo.addItem("MONO12")
        self.format_combo.addItem("RGB8")
        self.format_combo.addItem("BGR8")
        
        # 取图间隔
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(0, 1000)
        self.interval_spin.setValue(0)
        self.interval_spin.setSuffix(" ms")
        
        # 自动取图
        self.auto_capture_check = QCheckBox("启用")
        self.auto_capture_check.setChecked(True)
        
        # 方案存图
        self.save_images_check = QCheckBox("启用")
        
        # 添加表单项
        settings_layout.addRow(QLabel("相机:"), camera_select_layout)
        settings_layout.addRow(QLabel("像素格式:"), self.format_combo)
        settings_layout.addRow(QLabel("取图间隔:"), self.interval_spin)
        settings_layout.addRow(QLabel("自动切换:"), self.auto_capture_check)
        settings_layout.addRow(QLabel("方案存图:"), self.save_images_check)
        
        settings_group.setLayout(settings_layout)
        
        # 相机控制
        control_layout = QHBoxLayout()
        
        self.capture_btn = QPushButton("单次采集")
        self.capture_btn.clicked.connect(self._capture_image)
        
        self.start_btn = QPushButton("开始采集")
        self.start_btn.clicked.connect(self._start_capture)
        
        self.stop_btn = QPushButton("停止采集")
        self.stop_btn.clicked.connect(self._stop_capture)
        self.stop_btn.setEnabled(False)
        
        control_layout.addWidget(self.capture_btn)
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addStretch()
        
        # 相机预览
        camera_preview_group = QGroupBox("相机预览")
        camera_preview_layout = QVBoxLayout()
        
        self.camera_preview = QLabel("未启动相机")
        self.camera_preview.setAlignment(Qt.AlignCenter)
        self.camera_preview.setMinimumSize(400, 300)
        self.camera_preview.setStyleSheet("border: 1px solid #CCCCCC; background-color: #444444;")
        
        camera_preview_layout.addWidget(self.camera_preview)
        
        camera_preview_group.setLayout(camera_preview_layout)
        
        # 添加到相机选项卡布局
        camera_layout.addWidget(settings_group)
        camera_layout.addLayout(control_layout)
        camera_layout.addWidget(camera_preview_group)
        camera_layout.addStretch()
        
        # 添加选项卡到选项卡组件
        self.tab_widget.addTab(local_tab, "本地图像")
        self.tab_widget.addTab(camera_tab, "相机")
        
        # 添加到主布局
        source_layout.addLayout(source_option_layout)
        source_layout.addWidget(self.tab_widget)
        
        source_group.setLayout(source_layout)
        main_layout.addWidget(source_group)
    
    def _handle_source_changed(self, button):
        """处理图像源类型变更"""
        if button == self.local_radio:
            self.image_source_config["type"] = "local"
            self.tab_widget.setCurrentIndex(0)
        else:
            self.image_source_config["type"] = "camera"
            self.tab_widget.setCurrentIndex(1)
        
        # 发送图像源变更信号
        self.image_source_changed.emit(self.image_source_config)
    
    def _refresh_camera_list(self):
        """刷新相机列表"""
        self.camera_combo.clear()
        
        cameras = self.camera_manager.get_available_cameras()
        for camera in cameras:
            self.camera_combo.addItem(f"{camera['name']} ({camera['id']})", camera['id'])
    
    def _browse_image(self):
        """浏览并加载本地图像"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        
        if file_path:
            try:
                # 加载图像
                self.current_image = cv2.imread(file_path)
                
                if self.current_image is None:
                    QMessageBox.warning(self, "错误", "无法加载图像文件")
                    return
                
                print(f"已加载图像: {file_path}, 尺寸: {self.current_image.shape}")
                
                # 显示图像
                self._display_image(self.current_image, self.image_preview)
                
                # 更新文件路径标签
                self.file_path_label.setText(file_path)
                
                # 更新配置
                self.image_source_config["type"] = "local"
                self.image_source_config["local"]["path"] = file_path
                
                # 触发更改信号
                self.image_source_changed.emit(self.image_source_config)
                
                # 发送图像捕获信号，更新全局视图
                print("准备发送图像捕获信号以更新全局视图")
                self.image_captured.emit(self.current_image)
                print("图像捕获信号已发送")
            
            except Exception as e:
                print(f"加载图像时出错: {str(e)}")
                QMessageBox.warning(self, "错误", f"加载图像时出错: {str(e)}")
    
    def _capture_image(self):
        """从相机捕获单帧图像"""
        logger.info("🔥🔥🔥 _capture_image 方法被调用！🔥🔥🔥")
        print("🔥🔥🔥 _capture_image 方法被调用！🔥🔥🔥")
        
        if not self.camera_combo.currentText():
            QMessageBox.warning(self, "错误", "请先选择相机")
            return
        
        try:
            # 获取相机ID - 从currentData()获取，这是真正的相机ID
            camera_id = self.camera_combo.currentData()
            if camera_id is None:
                QMessageBox.warning(self, "错误", "请先选择有效的相机")
                return
            
            # 获取像素格式
            pixel_format = self.format_combo.currentText()
            
            logger.info(f"开始捕获图像: 相机ID={camera_id}, 格式={pixel_format}")
            
            # 打开相机
            if not self.camera_manager.open_camera(camera_id):
                QMessageBox.warning(self, "错误", f"无法打开相机 {camera_id}")
                return
            
            # 设置像素格式
            if not self.camera_manager.set_format(pixel_format):
                QMessageBox.warning(self, "错误", f"无法设置像素格式 {pixel_format}")
                self.camera_manager.close_camera()
                return
            
            logger.info("🔥 开始相机预热...")
            # 相机预热：连续读取几帧让相机稳定（解决黑屏问题）
            for i in range(5):
                success, warmup_frame = self.camera_manager.capture()
                if success and warmup_frame is not None:
                    mean_val = np.mean(warmup_frame)
                    logger.debug(f"预热帧 {i+1}/5: 均值={mean_val:.1f}")
                    if mean_val > 10:  # 如果找到正常图像，提前结束预热
                        logger.info(f"预热完成，在第{i+1}帧找到正常图像")
                        break
                else:
                    logger.warning(f"预热帧 {i+1}/5 捕获失败")
            
            # 从相机获取最终图像
            success, image = self.camera_manager.capture()
            
            # 关闭相机
            self.camera_manager.close_camera()
            
            if not success or image is None:
                QMessageBox.warning(self, "错误", "捕获图像失败")
                return
            
            logger.info(f"成功捕获图像: 尺寸={image.shape}, 数据类型={image.dtype}")
            
            # 检查图像数据
            if image.size == 0:
                QMessageBox.warning(self, "错误", "捕获的图像为空")
                return
            
            # 验证图像数据有效性
            mean_val = np.mean(image)
            std_val = np.std(image)
            
            if np.all(image == 0):
                logger.warning("捕获的图像全为黑色，相机可能需要更多预热时间")
                QMessageBox.warning(self, "提示", "捕获的图像为黑色，请确保相机前有足够光线，或稍后重试")
            elif mean_val < 10:
                logger.warning(f"捕获的图像较暗，均值={mean_val:.1f}")
                QMessageBox.information(self, "提示", f"图像较暗（均值={mean_val:.1f}），请检查光线条件")
            else:
                logger.info(f"图像正常: 均值={mean_val:.2f}, 标准差={std_val:.2f}")
            
            # 根据像素格式进行必要的处理
            processed_image = image.copy()
            
            # 如果是单色格式但图像是3通道，转换为灰度
            if pixel_format in ["MONO8", "MONO12"] and len(image.shape) == 3:
                processed_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                # 转回3通道以保持一致性
                processed_image = cv2.cvtColor(processed_image, cv2.COLOR_GRAY2BGR)
                logger.debug("已将彩色图像转换为灰度格式")
            
            # 更新UI - 在相机预览中显示
            logger.debug("开始更新相机预览显示...")
            self._display_image(processed_image, self.camera_preview)
            logger.debug("相机预览显示更新完成")
                
            # 更新当前图像
            self.current_image = processed_image
            
            # 更新图像源配置
            self.image_source_config["type"] = "camera"
                self.image_source_config["camera"]["id"] = camera_id
            self.image_source_config["camera"]["format"] = pixel_format
                self.image_source_config["camera"]["interval"] = self.interval_spin.value()
                self.image_source_config["camera"]["auto_capture"] = self.auto_capture_check.isChecked()
                self.image_source_config["camera"]["save_images"] = self.save_images_check.isChecked()
                
                # 发送图像源变更信号
                self.image_source_changed.emit(self.image_source_config)
                
            # 发送图像捕获信号，更新全局视图
            logger.info("发送图像捕获信号到全局显示...")
            self.image_captured.emit(self.current_image)
            logger.info("图像捕获和显示处理完成")
        
        except Exception as e:
            error_msg = f"捕获图像出错: {str(e)}"
            logger.error(error_msg)
            QMessageBox.warning(self, "错误", error_msg)
            if self.camera_manager.is_opened():
                self.camera_manager.close_camera()
    
    def _start_capture(self):
        """开始连续捕获相机图像"""
        # TODO: 实现连续捕获功能
        self.capture_btn.setEnabled(False)
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
    
    def _stop_capture(self):
        """停止连续捕获相机图像"""
        # TODO: 实现停止捕获功能
        self.capture_btn.setEnabled(True)
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
    
    def _display_image(self, image: np.ndarray, label: QLabel):
        """
        在标签上显示图像
        
        Args:
            image: 图像数据
            label: 显示标签
        """
        if image is None:
            label.setText("无图像")
            return
        
        try:
            # 获取标签的实际大小，如果标签还没有显示，使用最小尺寸
            label_width = max(label.width(), 400)
            label_height = max(label.height(), 300)
            
            # 计算适合的显示尺寸，保持图像宽高比
            image_height, image_width = image.shape[:2]
            
            # 计算缩放比例
            width_ratio = label_width / image_width
            height_ratio = label_height / image_height
            scale_ratio = min(width_ratio, height_ratio, 1.0)  # 不放大，只缩小
            
            # 计算新尺寸
            new_width = int(image_width * scale_ratio)
            new_height = int(image_height * scale_ratio)
            
            # 调整图像大小
            if scale_ratio < 1.0:
                resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            else:
                resized_image = image
        
        # 转换为QPixmap并显示
            pixmap = cv_to_qpixmap(resized_image)
            
            if pixmap.isNull():
                label.setText("图像转换失败")
                logger.error("图像转换为QPixmap失败")
                return
            
            # 设置图像到标签
            label.clear()
        label.setPixmap(pixmap)
            
            logger.debug(f"图像已显示: 原始尺寸={image_width}x{image_height}, "
                        f"显示尺寸={new_width}x{new_height}, "
                        f"缩放比例={scale_ratio:.2f}")
            
        except Exception as e:
            error_msg = f"显示图像失败: {str(e)}"
            logger.error(error_msg)
            label.setText(error_msg)
    
    def get_current_image(self) -> Optional[np.ndarray]:
        """
        获取当前图像
        
        Returns:
            np.ndarray: 当前图像，如果没有则返回None
        """
        return self.current_image
    
    def get_image_source_config(self) -> dict:
        """
        获取图像源配置
        
        Returns:
            dict: 图像源配置
        """
        return self.image_source_config 