"""
工作流模板管理器 - 预定义的工作流模板库

功能：
- 内置模板库
- 模板创建和保存
- 模板分类和搜索
- 模板预览
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path
import json
import time

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QLineEdit, QTextEdit, QComboBox,
    QDialog, QDialogButtonBox, QMessageBox, QSplitter,
    QGroupBox, QGridLayout
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QObject
from PyQt5.QtGui import QIcon, QPixmap

from loguru import logger


@dataclass
class WorkflowTemplate:
    """工作流模板"""
    id: str
    name: str
    category: str
    description: str
    tags: List[str]
    workflow_data: Dict[str, Any]
    preview_image: Optional[str] = None
    author: str = "System"
    created_at: float = field(default_factory=time.time)
    updated_at: float = field(default_factory=time.time)
    usage_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "category": self.category,
            "description": self.description,
            "tags": self.tags,
            "workflow_data": self.workflow_data,
            "preview_image": self.preview_image,
            "author": self.author,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "usage_count": self.usage_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowTemplate':
        """从字典创建"""
        return cls(**data)


# 预定义模板
BUILTIN_TEMPLATES = [
    WorkflowTemplate(
        id="basic_edge_detection",
        name="基础边缘检测",
        category="图像处理",
        description="使用高斯模糊和Canny边缘检测的基础工作流",
        tags=["边缘检测", "基础", "图像处理"],
        workflow_data={
            "nodes": [
                {
                    "node_id": "node_1",
                    "category": "image_source",
                    "algorithm": "camera",
                    "position": {"x": 100, "y": 200},
                    "parameters": {"camera_index": 0}
                },
                {
                    "node_id": "node_2",
                    "category": "image_processing",
                    "algorithm": "gaussian_blur",
                    "position": {"x": 300, "y": 200},
                    "parameters": {"kernel_size_x": 5, "kernel_size_y": 5}
                },
                {
                    "node_id": "node_3",
                    "category": "image_processing",
                    "algorithm": "edge_detection",
                    "position": {"x": 500, "y": 200},
                    "parameters": {"low_threshold": 50, "high_threshold": 150}
                }
            ],
            "connections": [
                {"source": "node_1", "target": "node_2"},
                {"source": "node_2", "target": "node_3"}
            ]
        }
    ),
    
    WorkflowTemplate(
        id="color_object_detection",
        name="颜色目标检测",
        category="目标检测",
        description="基于HSV颜色空间的目标检测工作流",
        tags=["颜色检测", "目标检测", "HSV"],
        workflow_data={
            "nodes": [
                {
                    "node_id": "node_1",
                    "category": "image_source",
                    "algorithm": "camera",
                    "position": {"x": 100, "y": 200},
                    "parameters": {"camera_index": 0}
                },
                {
                    "node_id": "node_2",
                    "category": "image_processing",
                    "algorithm": "color_space_conversion",
                    "position": {"x": 300, "y": 200},
                    "parameters": {"conversion": "BGR2HSV"}
                },
                {
                    "node_id": "node_3",
                    "category": "object_detection",
                    "algorithm": "color_detection",
                    "position": {"x": 500, "y": 200},
                    "parameters": {
                        "color_lower": [0, 100, 100],
                        "color_upper": [10, 255, 255],
                        "min_area": 100
                    }
                }
            ],
            "connections": [
                {"source": "node_1", "target": "node_2"},
                {"source": "node_2", "target": "node_3"}
            ]
        }
    ),
    
    WorkflowTemplate(
        id="template_matching_flow",
        name="模板匹配流程",
        category="特征检测",
        description="使用模板匹配进行目标定位",
        tags=["模板匹配", "特征检测", "定位"],
        workflow_data={
            "nodes": [
                {
                    "node_id": "node_1",
                    "category": "image_source",
                    "algorithm": "camera",
                    "position": {"x": 100, "y": 200},
                    "parameters": {"camera_index": 0}
                },
                {
                    "node_id": "node_2",
                    "category": "image_processing",
                    "algorithm": "grayscale",
                    "position": {"x": 300, "y": 200},
                    "parameters": {}
                },
                {
                    "node_id": "node_3",
                    "category": "feature_detection",
                    "algorithm": "template_matching",
                    "position": {"x": 500, "y": 200},
                    "parameters": {
                        "match_threshold": 0.8,
                        "match_method": "TM_CCOEFF_NORMED"
                    }
                }
            ],
            "connections": [
                {"source": "node_1", "target": "node_2"},
                {"source": "node_2", "target": "node_3"}
            ]
        }
    ),
    
    WorkflowTemplate(
        id="yolo_detection_flow",
        name="YOLO目标检测",
        category="深度学习",
        description="使用YOLOv8进行实时目标检测",
        tags=["YOLO", "深度学习", "目标检测"],
        workflow_data={
            "nodes": [
                {
                    "node_id": "node_1",
                    "category": "image_source",
                    "algorithm": "camera",
                    "position": {"x": 100, "y": 200},
                    "parameters": {
                        "camera_index": 0,
                        "resolution_width": 640,
                        "resolution_height": 480
                    }
                },
                {
                    "node_id": "node_2",
                    "category": "deep_learning",
                    "algorithm": "yolo_detection",
                    "position": {"x": 350, "y": 200},
                    "parameters": {
                        "model_path": "yolov8n.pt",
                        "confidence_threshold": 0.5,
                        "nms_threshold": 0.4,
                        "device": "auto"
                    }
                }
            ],
            "connections": [
                {"source": "node_1", "target": "node_2"}
            ]
        }
    ),
    
    WorkflowTemplate(
        id="measurement_workflow",
        name="尺寸测量流程",
        category="测量",
        description="边缘检测后进行尺寸测量",
        tags=["测量", "尺寸", "边缘检测"],
        workflow_data={
            "nodes": [
                {
                    "node_id": "node_1",
                    "category": "image_source",
                    "algorithm": "camera",
                    "position": {"x": 100, "y": 200},
                    "parameters": {"camera_index": 0}
                },
                {
                    "node_id": "node_2",
                    "category": "image_processing",
                    "algorithm": "edge_detection",
                    "position": {"x": 300, "y": 200},
                    "parameters": {"low_threshold": 50, "high_threshold": 150}
                },
                {
                    "node_id": "node_3",
                    "category": "measurement",
                    "algorithm": "distance_measurement",
                    "position": {"x": 500, "y": 200},
                    "parameters": {
                        "unit": "pixel",
                        "scale_factor": 1.0
                    }
                }
            ],
            "connections": [
                {"source": "node_1", "target": "node_2"},
                {"source": "node_2", "target": "node_3"}
            ]
        }
    )
]


class WorkflowTemplateManager(QObject):
    """工作流模板管理器"""
    
    template_loaded = pyqtSignal(WorkflowTemplate)
    
    def __init__(self, template_dir: Optional[Path] = None):
        super().__init__()
        
        self.template_dir = template_dir or Path.home() / ".wirevision" / "templates"
        self.template_dir.mkdir(parents=True, exist_ok=True)
        
        self.templates: Dict[str, WorkflowTemplate] = {}
        self.categories: List[str] = ["全部", "图像处理", "目标检测", "特征检测", 
                                     "深度学习", "测量", "自定义"]
        
        # 加载模板
        self._load_builtin_templates()
        self._load_custom_templates()
    
    def _load_builtin_templates(self):
        """加载内置模板"""
        for template in BUILTIN_TEMPLATES:
            self.templates[template.id] = template
        
        logger.info(f"加载了 {len(BUILTIN_TEMPLATES)} 个内置模板")
    
    def _load_custom_templates(self):
        """加载自定义模板"""
        template_files = self.template_dir.glob("*.json")
        
        for file_path in template_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                template = WorkflowTemplate.from_dict(data)
                self.templates[template.id] = template
                
                logger.debug(f"加载自定义模板: {template.name}")
                
            except Exception as e:
                logger.error(f"加载模板失败 {file_path}: {e}")
    
    def save_template(self, template: WorkflowTemplate):
        """保存模板"""
        # 添加到内存
        self.templates[template.id] = template
        
        # 保存到文件
        file_path = self.template_dir / f"{template.id}.json"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, ensure_ascii=False, indent=2)
            
            logger.info(f"保存模板: {template.name}")
            
        except Exception as e:
            logger.error(f"保存模板失败: {e}")
            raise
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id not in self.templates:
            return False
        
        template = self.templates[template_id]
        
        # 不能删除内置模板
        if template.author == "System":
            logger.warning("不能删除内置模板")
            return False
        
        # 从内存删除
        del self.templates[template_id]
        
        # 删除文件
        file_path = self.template_dir / f"{template_id}.json"
        if file_path.exists():
            file_path.unlink()
        
        logger.info(f"删除模板: {template.name}")
        return True
    
    def get_templates(self, category: Optional[str] = None, 
                     search_text: Optional[str] = None) -> List[WorkflowTemplate]:
        """获取模板列表"""
        templates = list(self.templates.values())
        
        # 按类别筛选
        if category and category != "全部":
            templates = [t for t in templates if t.category == category]
        
        # 按搜索文本筛选
        if search_text:
            search_lower = search_text.lower()
            templates = [
                t for t in templates
                if search_lower in t.name.lower() or
                   search_lower in t.description.lower() or
                   any(search_lower in tag.lower() for tag in t.tags)
            ]
        
        # 按使用次数和更新时间排序
        templates.sort(key=lambda t: (-t.usage_count, -t.updated_at))
        
        return templates
    
    def use_template(self, template_id: str):
        """使用模板（增加使用计数）"""
        if template_id in self.templates:
            template = self.templates[template_id]
            template.usage_count += 1
            template.updated_at = time.time()
            
            # 如果是自定义模板，保存更新
            if template.author != "System":
                self.save_template(template)


class WorkflowTemplateDialog(QDialog):
    """工作流模板对话框"""
    
    template_selected = pyqtSignal(WorkflowTemplate)
    
    def __init__(self, manager: WorkflowTemplateManager, parent=None):
        super().__init__(parent)
        self.manager = manager
        self.selected_template = None
        
        self.setWindowTitle("工作流模板库")
        self.setModal(True)
        self.resize(900, 600)
        
        self.init_ui()
        self.load_templates()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        
        # 类别选择
        self.category_combo = QComboBox()
        self.category_combo.addItems(self.manager.categories)
        self.category_combo.currentTextChanged.connect(self.filter_templates)
        toolbar_layout.addWidget(QLabel("类别:"))
        toolbar_layout.addWidget(self.category_combo)
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索模板...")
        self.search_edit.textChanged.connect(self.filter_templates)
        toolbar_layout.addWidget(self.search_edit)
        
        # 创建按钮
        create_btn = QPushButton("创建新模板")
        create_btn.clicked.connect(self.create_template)
        toolbar_layout.addWidget(create_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 主分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：模板列表
        self.template_list = QListWidget()
        self.template_list.setMinimumWidth(300)
        self.template_list.itemSelectionChanged.connect(self.on_template_selected)
        splitter.addWidget(self.template_list)
        
        # 右侧：模板详情
        detail_widget = self.create_detail_widget()
        splitter.addWidget(detail_widget)
        
        splitter.setSizes([350, 550])
        layout.addWidget(splitter)
        
        # 底部按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        self.ok_button = button_box.button(QDialogButtonBox.Ok)
        self.ok_button.setText("使用模板")
        self.ok_button.setEnabled(False)
        
        layout.addWidget(button_box)
    
    def create_detail_widget(self) -> QWidget:
        """创建详情面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模板信息
        info_group = QGroupBox("模板信息")
        info_layout = QGridLayout()
        
        # 名称
        info_layout.addWidget(QLabel("名称:"), 0, 0)
        self.name_label = QLabel("-")
        self.name_label.setStyleSheet("font-weight: bold;")
        info_layout.addWidget(self.name_label, 0, 1)
        
        # 类别
        info_layout.addWidget(QLabel("类别:"), 1, 0)
        self.category_label = QLabel("-")
        info_layout.addWidget(self.category_label, 1, 1)
        
        # 作者
        info_layout.addWidget(QLabel("作者:"), 2, 0)
        self.author_label = QLabel("-")
        info_layout.addWidget(self.author_label, 2, 1)
        
        # 使用次数
        info_layout.addWidget(QLabel("使用次数:"), 3, 0)
        self.usage_label = QLabel("-")
        info_layout.addWidget(self.usage_label, 3, 1)
        
        # 标签
        info_layout.addWidget(QLabel("标签:"), 4, 0)
        self.tags_label = QLabel("-")
        self.tags_label.setWordWrap(True)
        info_layout.addWidget(self.tags_label, 4, 1)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 描述
        desc_group = QGroupBox("描述")
        desc_layout = QVBoxLayout()
        
        self.desc_text = QTextEdit()
        self.desc_text.setReadOnly(True)
        self.desc_text.setMaximumHeight(100)
        desc_layout.addWidget(self.desc_text)
        
        desc_group.setLayout(desc_layout)
        layout.addWidget(desc_group)
        
        # 预览
        preview_group = QGroupBox("工作流预览")
        preview_layout = QVBoxLayout()
        
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)
        
        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)
        
        # 操作按钮
        action_layout = QHBoxLayout()
        
        self.edit_btn = QPushButton("编辑")
        self.edit_btn.setEnabled(False)
        self.edit_btn.clicked.connect(self.edit_template)
        action_layout.addWidget(self.edit_btn)
        
        self.delete_btn = QPushButton("删除")
        self.delete_btn.setEnabled(False)
        self.delete_btn.clicked.connect(self.delete_template)
        action_layout.addWidget(self.delete_btn)
        
        self.export_btn = QPushButton("导出")
        self.export_btn.setEnabled(False)
        self.export_btn.clicked.connect(self.export_template)
        action_layout.addWidget(self.export_btn)
        
        action_layout.addStretch()
        
        layout.addLayout(action_layout)
        
        return widget
    
    def load_templates(self):
        """加载模板列表"""
        self.template_list.clear()
        
        templates = self.manager.get_templates()
        
        for template in templates:
            item = QListWidgetItem(f"{template.name}")
            item.setData(Qt.UserRole, template.id)
            
            # 设置图标
            if template.author == "System":
                item.setIcon(QIcon.fromTheme("emblem-system"))
            else:
                item.setIcon(QIcon.fromTheme("emblem-favorite"))
            
            self.template_list.addItem(item)
    
    def filter_templates(self):
        """筛选模板"""
        category = self.category_combo.currentText()
        search_text = self.search_edit.text()
        
        self.template_list.clear()
        
        templates = self.manager.get_templates(
            category if category != "全部" else None,
            search_text if search_text else None
        )
        
        for template in templates:
            item = QListWidgetItem(template.name)
            item.setData(Qt.UserRole, template.id)
            
            if template.author == "System":
                item.setIcon(QIcon.fromTheme("emblem-system"))
            else:
                item.setIcon(QIcon.fromTheme("emblem-favorite"))
            
            self.template_list.addItem(item)
    
    def on_template_selected(self):
        """模板选择事件"""
        current_item = self.template_list.currentItem()
        
        if not current_item:
            self.clear_detail()
            return
        
        template_id = current_item.data(Qt.UserRole)
        template = self.manager.templates.get(template_id)
        
        if template:
            self.show_template_detail(template)
            self.selected_template = template
            self.ok_button.setEnabled(True)
        else:
            self.clear_detail()
    
    def show_template_detail(self, template: WorkflowTemplate):
        """显示模板详情"""
        self.name_label.setText(template.name)
        self.category_label.setText(template.category)
        self.author_label.setText(template.author)
        self.usage_label.setText(str(template.usage_count))
        self.tags_label.setText(", ".join(template.tags))
        
        self.desc_text.setPlainText(template.description)
        
        # 显示工作流结构
        workflow_info = f"节点数: {len(template.workflow_data.get('nodes', []))}\n"
        workflow_info += f"连接数: {len(template.workflow_data.get('connections', []))}\n\n"
        
        # 节点列表
        workflow_info += "节点列表:\n"
        for node in template.workflow_data.get('nodes', []):
            workflow_info += f"- {node.get('algorithm', 'Unknown')} "
            workflow_info += f"({node.get('category', 'Unknown')})\n"
        
        self.preview_text.setPlainText(workflow_info)
        
        # 更新按钮状态
        is_custom = template.author != "System"
        self.edit_btn.setEnabled(is_custom)
        self.delete_btn.setEnabled(is_custom)
        self.export_btn.setEnabled(True)
    
    def clear_detail(self):
        """清空详情"""
        self.name_label.setText("-")
        self.category_label.setText("-")
        self.author_label.setText("-")
        self.usage_label.setText("-")
        self.tags_label.setText("-")
        self.desc_text.clear()
        self.preview_text.clear()
        
        self.selected_template = None
        self.ok_button.setEnabled(False)
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
    
    def create_template(self):
        """创建新模板"""
        # TODO: 实现模板创建对话框
        QMessageBox.information(self, "提示", "模板创建功能待实现")
    
    def edit_template(self):
        """编辑模板"""
        if self.selected_template:
            # TODO: 实现模板编辑对话框
            QMessageBox.information(self, "提示", "模板编辑功能待实现")
    
    def delete_template(self):
        """删除模板"""
        if self.selected_template:
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除模板 '{self.selected_template.name}' 吗？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if self.manager.delete_template(self.selected_template.id):
                    self.load_templates()
                    self.clear_detail()
                    QMessageBox.information(self, "成功", "模板已删除")
                else:
                    QMessageBox.warning(self, "失败", "无法删除该模板")
    
    def export_template(self):
        """导出模板"""
        if self.selected_template:
            from PyQt5.QtWidgets import QFileDialog
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出模板",
                f"{self.selected_template.name}.json",
                "JSON文件 (*.json)"
            )
            
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(
                            self.selected_template.to_dict(),
                            f, ensure_ascii=False, indent=2
                        )
                    
                    QMessageBox.information(self, "成功", "模板导出成功")
                    
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"导出失败: {e}")
    
    def accept(self):
        """确认选择"""
        if self.selected_template:
            self.manager.use_template(self.selected_template.id)
            self.template_selected.emit(self.selected_template)
        
        super().accept()


def create_template_from_workflow(workflow_data: Dict[str, Any],
                                 name: str, category: str,
                                 description: str, tags: List[str]) -> WorkflowTemplate:
    """从工作流数据创建模板"""
    import uuid
    
    template = WorkflowTemplate(
        id=str(uuid.uuid4()),
        name=name,
        category=category,
        description=description,
        tags=tags,
        workflow_data=workflow_data,
        author="User"
    )
    
    return template 