#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主题管理器

统一管理应用程序的主题和配色方案
"""

from PyQt5.QtGui import QColor
from PyQt5.QtWidgets import QWidget
from typing import Dict, Any
from wirevsion.ui.modern_components import THEME_COLORS
from loguru import logger

class ThemeManager:
    """主题管理器"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.current_theme = "dark"
            self.theme_colors = THEME_COLORS.copy()
            self.initialized = True
            logger.info("主题管理器初始化完成")

    # 当前主题
    _current_theme = THEME_COLORS

    def get_color(self, key: str) -> str:
        """获取颜色值"""
        return self.theme_colors.get(key, "#FFFFFF")

    def get_qcolor(self, key: str) -> QColor:
        """获取QColor对象"""
        return QColor(self.get_color(key))

    def get_input_style(self, widget_type: str = "QLineEdit") -> str:
        """获取输入控件的统一样式"""
        return f"""
            {widget_type} {{
                background-color: {self.get_color("dark_bg_input")};
                color: {self.get_color("text_primary")};
                border: 2px solid {self.get_color("dark_border_primary")};
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
            }}
            {widget_type}:focus {{
                border-color: {self.get_color("primary")};
            }}
            {widget_type}:hover {{
                border-color: {self.get_color("primary")};
                background-color: {self.get_color("dark_surface_hover")};
            }}
        """

    def get_button_style(self, button_type: str = "primary") -> str:
        """获取按钮的统一样式"""
        if button_type == "primary":
            bg_color = self.get_color("primary")
            hover_color = self.get_color("primary_hover")
            pressed_color = self.get_color("primary_pressed")
            text_color = self.get_color("text_on_primary_bg")
        elif button_type == "secondary":
            bg_color = self.get_color("secondary")
            hover_color = self.get_color("secondary_hover")
            pressed_color = self.get_color("secondary_pressed")
            text_color = self.get_color("text_on_primary_bg")
        elif button_type == "success":
            bg_color = self.get_color("success")
            hover_color = self.get_color("success_hover")
            pressed_color = self.get_color("success_pressed")
            text_color = self.get_color("text_on_primary_bg")
        elif button_type == "warning":
            bg_color = self.get_color("warning")
            hover_color = self.get_color("warning_hover")
            pressed_color = self.get_color("warning_pressed")
            text_color = self.get_color("text_on_warning_bg")
        elif button_type == "danger":
            bg_color = self.get_color("danger")
            hover_color = self.get_color("danger_hover")
            pressed_color = self.get_color("danger_pressed")
            text_color = self.get_color("text_on_primary_bg")
        elif button_type == "info":
            bg_color = self.get_color("info")
            hover_color = QColor(self.get_color("info")).darker(110).name()
            pressed_color = QColor(self.get_color("info")).darker(120).name()
            text_color = self.get_color("text_on_primary_bg")
        else:
            # 默认样式
            bg_color = self.get_color("dark_bg_input")
            hover_color = self.get_color("dark_surface_hover")
            pressed_color = self.get_color("dark_surface_pressed")
            text_color = self.get_color("text_primary")

        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: 2px solid {self.get_color("dark_border_primary")};
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
                border-color: {self.get_color("primary")};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
            QPushButton:disabled {{
                background-color: {self.get_color("dark_bg_input")};
                color: {self.get_color("text_disabled")};
                border-color: {self.get_color("dark_border_secondary")};
            }}
        """

    @classmethod
    def get_node_style(cls, node_type: str) -> Dict[str, str]:
        """获取节点样式"""
        styles = {
            "input": {
                "bg": cls.get_color("node_input"),
                "border": cls.get_qcolor("node_input").darker(120).name(),
                "text": cls.get_color("text_on_primary")
            },
            "output": {
                "bg": cls.get_color("node_output"),
                "border": cls.get_qcolor("node_output").darker(120).name(),
                "text": cls.get_color("text_on_primary")
            },
            "process": {
                "bg": cls.get_color("node_process"),
                "border": cls.get_qcolor("node_process").darker(130).name(),
                "text": cls.get_color("text_on_warning")
            }
        }
        return styles.get(node_type, styles["process"])

    def get_global_stylesheet(self) -> str:
        """获取全局样式表"""
        return f"""
        QWidget {{
            color: {self.get_color("text_primary")};
            font-family: "Segoe UI", "Arial", sans-serif;
        }}

        QMainWindow, QDialog {{
            background-color: {self.get_color("dark_bg_app")};
        }}

        QToolTip {{
            background-color: {self.get_color("dark_bg_input")};
            color: {self.get_color("text_primary")};
            border: 1px solid {self.get_color("dark_border_primary")};
            padding: 5px;
            border-radius: 4px;
        }}

        QMenu {{
            background-color: {self.get_color("dark_bg_content")};
            color: {self.get_color("text_primary")};
            border: 1px solid {self.get_color("dark_border_primary")};
            padding: 5px;
            border-radius: 6px;
        }}

        QMenu::item {{
            padding: 8px 24px;
            border-radius: 4px;
        }}

        QMenu::item:selected {{
            background-color: {self.get_color("primary")};
            color: {self.get_color("text_on_primary_bg")};
        }}

        QScrollBar:vertical {{
            background: {self.get_color("dark_bg_sidebar")};
            width: 10px;
            border-radius: 5px;
        }}

        QScrollBar::handle:vertical {{
            background: {self.get_color("dark_border_primary")};
            min-height: 25px;
            border-radius: 5px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {self.get_color("primary")};
        }}
        """

    def get_list_style(self) -> str:
        """获取列表控件的统一样式"""
        return f"""
            QListWidget {{
                background-color: {self.get_color("dark_bg_input")};
                color: {self.get_color("text_primary")};
                border: 1px solid {self.get_color("dark_border_primary")};
                border-radius: 4px;
                padding: 2px;
                font-size: 12px;
            }}
            QListWidget::item {{
                padding: 6px 8px;
                border-bottom: 1px solid {self.get_color("dark_border_secondary")};
                border-radius: 3px;
                margin: 1px;
            }}
            QListWidget::item:selected {{
                background-color: {self.get_color("primary")};
                color: {self.get_color("text_on_primary_bg")};
            }}
            QListWidget::item:hover {{
                background-color: {self.get_color("dark_surface_hover")};
            }}
        """

    def get_spinbox_style(self) -> str:
        """获取数字输入框的统一样式"""
        return f"""
            QSpinBox, QDoubleSpinBox {{
                background-color: {self.get_color("dark_bg_input")};
                color: {self.get_color("text_primary")};
                border: 1px solid {self.get_color("dark_border_primary")};
                border-radius: 3px;
                padding: 4px 6px;
                font-size: 11px;
            }}
            QSpinBox:focus, QDoubleSpinBox:focus {{
                border-color: {self.get_color("primary")};
            }}
            QSpinBox:hover, QDoubleSpinBox:hover {{
                border-color: {self.get_color("primary")};
                background-color: {self.get_color("dark_surface_hover")};
            }}
            QSpinBox::up-button, QDoubleSpinBox::up-button {{
                background-color: {self.get_color("dark_bg_sidebar")};
                border: none;
                border-radius: 2px;
            }}
            QSpinBox::down-button, QDoubleSpinBox::down-button {{
                background-color: {self.get_color("dark_bg_sidebar")};
                border: none;
                border-radius: 2px;
            }}
        """

    def get_combobox_style(self) -> str:
        """获取下拉框的统一样式"""
        return f"""
            QComboBox {{
                background-color: {self.get_color("dark_bg_input")};
                color: {self.get_color("text_primary")};
                border: 2px solid {self.get_color("dark_border_primary")};
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                min-height: 20px;
            }}
            QComboBox:focus {{
                border-color: {self.get_color("primary")};
            }}
            QComboBox:hover {{
                border-color: {self.get_color("primary")};
                background-color: {self.get_color("dark_surface_hover")};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid {self.get_color("text_secondary")};
            }}
            QComboBox QAbstractItemView {{
                background-color: {self.get_color("dark_bg_input")};
                color: {self.get_color("text_primary")};
                border: 1px solid {self.get_color("dark_border_primary")};
                selection-background-color: {self.get_color("primary")};
                selection-color: {self.get_color("text_on_primary_bg")};
            }}
        """

    def get_card_style(self) -> str:
        """获取卡片样式"""
        return f"""
            QWidget {{
                background-color: {self.get_color("dark_bg_content")};
                border: 1px solid {self.get_color("dark_border_secondary")};
                border-radius: 8px;
                margin: 5px;
            }}
        """

    def get_input_container_style(self) -> str:
        """获取输入容器样式"""
        return f"""
            QWidget {{
                background-color: {self.get_color("dark_bg_input")};
                border: 1px solid {self.get_color("dark_border_secondary")};
                border-radius: 6px;
                padding: 8px;
            }}
        """

    def get_label_style(self, label_type: str = "primary") -> str:
        """获取标签样式"""
        if label_type == "secondary":
            color = self.get_color("text_secondary")
            font_weight = "bold"
        elif label_type == "title":
            color = self.get_color("text_title")
            font_weight = "600"
        else:
            color = self.get_color("text_primary")
            font_weight = "normal"

        return f"""
            QLabel {{
                color: {color};
                font-size: 12px;
                font-weight: {font_weight};
                background: transparent;
                border: none;
                padding: 0;
            }}
        """

    def get_checkbox_style(self) -> str:
        """获取复选框样式"""
        return f"""
            QCheckBox {{
                color: {self.get_color("text_primary")};
                font-size: 13px;
                background: transparent;
                border: none;
            }}
            QCheckBox::indicator {{
                width: 16px;
                height: 16px;
                border: 2px solid {self.get_color("dark_border_primary")};
                border-radius: 3px;
                background-color: {self.get_color("dark_bg_sidebar")};
            }}
            QCheckBox::indicator:checked {{
                background-color: {self.get_color("primary")};
                border-color: {self.get_color("primary")};
            }}
            QCheckBox::indicator:hover {{
                border-color: {self.get_color("primary")};
            }}
        """


# 全局主题管理器实例
theme_manager = ThemeManager()