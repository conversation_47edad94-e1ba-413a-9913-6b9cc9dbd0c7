#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动屏幕模块

用于在应用程序启动时显示启动屏幕
"""

from PyQt5.QtWidgets import QSplashScreen
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QPainter, QFont

class SplashScreen(QSplashScreen):
    """应用程序启动屏幕类"""
    
    def __init__(self, pixmap):
        """初始化启动屏幕
        
        Args:
            pixmap: 要显示的启动图像
        """
        super().__init__(pixmap)
        
        # 设置启动屏幕样式
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # 添加版本号和版权信息
        self._draw_version_info()
    
    def _draw_version_info(self):
        """在启动图像上绘制版本和版权信息"""
        painter = QPainter(self.pixmap())
        
        # 设置字体
        font = QFont("Arial", 10)
        painter.setFont(font)
        
        # 设置颜色
        painter.setPen(QColor(220, 220, 220))
        
        # 添加版本信息
        painter.drawText(
            self.pixmap().rect().adjusted(10, 10, -10, -30),
            Qt.AlignBottom | Qt.AlignRight,
            "v1.0.0"
        )
        
        # 添加版权信息
        painter.drawText(
            self.pixmap().rect().adjusted(10, 10, -10, -10),
            Qt.AlignBottom | Qt.AlignRight,
            "© 2025 WireVsion Team"
        )
        
        # 完成绘制
        painter.end()
    
    def showMessage(self, message, alignment=Qt.AlignLeft, color=Qt.black):
        """显示启动消息
        
        Args:
            message: 要显示的消息
            alignment: 消息对齐方式
            color: 消息颜色
        """
        super().showMessage(message, alignment, color)
    
    def clearMessage(self):
        """清除启动消息"""
        super().clearMessage() 