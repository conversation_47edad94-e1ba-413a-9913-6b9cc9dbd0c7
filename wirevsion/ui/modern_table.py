#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化表格组件

提供功能丰富的表格组件：
- 排序功能
- 筛选功能
- 分页支持
- 自定义列
- 行选择
- 数据导出
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QHeaderView, QPushButton, QLabel,
    QLineEdit, QComboBox, QCheckBox, QMenu, QAction,
    QFileDialog, QMessageBox
)
from PyQt6.QtCore import (
    Qt, pyqtSignal, QTimer, QPropertyAnimation,
    QEasingCurve, QSize
)
from PyQt6.QtGui import (
    QPainter, QColor, QFont, QIcon, QPixmap,
    QBrush, QPen
)
from typing import List, Dict, Any, Optional, Callable
from loguru import logger
import csv
import json

from wirevsion.ui.modern_components import ModernButton, ModernInput


class ModernTableHeader(QHeaderView):
    """现代化表格头部"""
    
    sort_changed = pyqtSignal(int, bool)  # 列索引, 是否升序
    filter_changed = pyqtSignal(int, str)  # 列索引, 筛选文本
    
    def __init__(self, orientation, parent=None):
        super().__init__(orientation, parent)
        
        self.setSectionsClickable(True)
        self.setSortIndicatorShown(True)
        
        # 样式设置
        self.setStyleSheet("""
            QHeaderView::section {
                background-color: #2b2b2b;
                color: #ffffff;
                padding: 8px;
                border: none;
                border-right: 1px solid #3d3d3d;
                border-bottom: 2px solid #0d6efd;
                font-weight: bold;
            }
            QHeaderView::section:hover {
                background-color: #3d3d3d;
            }
            QHeaderView::section:pressed {
                background-color: #4d4d4d;
            }
        """)
        
        # 连接信号
        self.sortIndicatorChanged.connect(self._on_sort_changed)
    
    def _on_sort_changed(self, logical_index: int, order: Qt.SortOrder):
        """排序变化处理"""
        is_ascending = (order == Qt.AscendingOrder)
        self.sort_changed.emit(logical_index, is_ascending)


class ModernTablePagination(QWidget):
    """表格分页组件"""
    
    page_changed = pyqtSignal(int)  # 页码
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.total_items = 0
        self.items_per_page = 20
        self.current_page = 1
        self.total_pages = 1
        
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 8, 0, 8)
        
        # 每页显示数量
        layout.addWidget(QLabel("每页显示:"))
        
        self.per_page_combo = QComboBox()
        self.per_page_combo.addItems(["10", "20", "50", "100"])
        self.per_page_combo.setCurrentText("20")
        self.per_page_combo.currentTextChanged.connect(self._on_per_page_changed)
        self.per_page_combo.setStyleSheet("""
            QComboBox {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #3d3d3d;
                padding: 4px 8px;
                border-radius: 4px;
                min-width: 80px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #ffffff;
                margin-right: 4px;
            }
        """)
        layout.addWidget(self.per_page_combo)
        
        layout.addStretch()
        
        # 页码信息
        self.info_label = QLabel("第 1 页，共 1 页")
        self.info_label.setStyleSheet("color: #b0b0b0;")
        layout.addWidget(self.info_label)
        
        # 翻页按钮
        self.first_btn = ModernButton("⏮", ModernButton.SECONDARY)
        self.first_btn.setFixedSize(32, 32)
        self.first_btn.clicked.connect(self.go_first_page)
        layout.addWidget(self.first_btn)
        
        self.prev_btn = ModernButton("◀", ModernButton.SECONDARY)
        self.prev_btn.setFixedSize(32, 32)
        self.prev_btn.clicked.connect(self.go_prev_page)
        layout.addWidget(self.prev_btn)
        
        # 页码输入
        self.page_input = QLineEdit()
        self.page_input.setFixedWidth(50)
        self.page_input.setText("1")
        self.page_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.page_input.setStyleSheet("""
            QLineEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #3d3d3d;
                padding: 4px;
                border-radius: 4px;
            }
        """)
        self.page_input.returnPressed.connect(self._on_page_input)
        layout.addWidget(self.page_input)
        
        self.next_btn = ModernButton("▶", ModernButton.SECONDARY)
        self.next_btn.setFixedSize(32, 32)
        self.next_btn.clicked.connect(self.go_next_page)
        layout.addWidget(self.next_btn)
        
        self.last_btn = ModernButton("⏭", ModernButton.SECONDARY)
        self.last_btn.setFixedSize(32, 32)
        self.last_btn.clicked.connect(self.go_last_page)
        layout.addWidget(self.last_btn)
    
    def set_total_items(self, total: int):
        """设置总条目数"""
        self.total_items = total
        self._update_pages()
    
    def _update_pages(self):
        """更新页码信息"""
        self.total_pages = max(1, (self.total_items + self.items_per_page - 1) // self.items_per_page)
        self.current_page = min(self.current_page, self.total_pages)
        
        # 更新显示
        self.info_label.setText(f"第 {self.current_page} 页，共 {self.total_pages} 页")
        self.page_input.setText(str(self.current_page))
        
        # 更新按钮状态
        self.first_btn.setEnabled(self.current_page > 1)
        self.prev_btn.setEnabled(self.current_page > 1)
        self.next_btn.setEnabled(self.current_page < self.total_pages)
        self.last_btn.setEnabled(self.current_page < self.total_pages)
    
    def _on_per_page_changed(self, text: str):
        """每页显示数量变化"""
        try:
            self.items_per_page = int(text)
            self.current_page = 1
            self._update_pages()
            self.page_changed.emit(self.current_page)
        except ValueError:
            pass
    
    def _on_page_input(self):
        """页码输入"""
        try:
            page = int(self.page_input.text())
            if 1 <= page <= self.total_pages:
                self.current_page = page
                self._update_pages()
                self.page_changed.emit(self.current_page)
        except ValueError:
            self.page_input.setText(str(self.current_page))
    
    def go_first_page(self):
        """第一页"""
        self.current_page = 1
        self._update_pages()
        self.page_changed.emit(self.current_page)
    
    def go_prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self._update_pages()
            self.page_changed.emit(self.current_page)
    
    def go_next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self._update_pages()
            self.page_changed.emit(self.current_page)
    
    def go_last_page(self):
        """最后一页"""
        self.current_page = self.total_pages
        self._update_pages()
        self.page_changed.emit(self.current_page)
    
    def get_page_range(self) -> tuple:
        """获取当前页的数据范围"""
        start = (self.current_page - 1) * self.items_per_page
        end = start + self.items_per_page
        return start, end


class ModernTable(QWidget):
    """现代化表格组件"""
    
    # 信号
    row_selected = pyqtSignal(int)  # 行索引
    row_double_clicked = pyqtSignal(int)  # 行索引
    data_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.columns: List[Dict[str, Any]] = []
        self.data: List[Dict[str, Any]] = []
        self.filtered_data: List[Dict[str, Any]] = []
        self.sort_column = -1
        self.sort_ascending = True
        self.filter_text = ""
        
        self._setup_ui()
        self._setup_style()
        
        logger.debug("现代化表格组件初始化完成")
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # 表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.verticalHeader().setVisible(False)
        
        # 设置自定义头部
        header = ModernTableHeader(Qt.Orientation.Horizontal, self.table)
        self.table.setHorizontalHeader(header)
        header.sort_changed.connect(self._on_sort_changed)
        
        # 连接信号
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        self.table.itemDoubleClicked.connect(self._on_item_double_clicked)
        
        layout.addWidget(self.table)
        
        # 分页
        self.pagination = ModernTablePagination()
        self.pagination.page_changed.connect(self._on_page_changed)
        layout.addWidget(self.pagination)
    
    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        toolbar.setFixedHeight(48)
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(16, 0, 16, 0)
        
        # 搜索框
        self.search_input = ModernInput("搜索...", "")
        self.search_input.setFixedWidth(300)
        self.search_input.textChanged.connect(self._on_search_changed)
        layout.addWidget(self.search_input)
        
        layout.addStretch()
        
        # 导出按钮
        export_btn = ModernButton("导出", ModernButton.SECONDARY)
        export_btn.clicked.connect(self._show_export_menu)
        layout.addWidget(export_btn)
        
        # 刷新按钮
        refresh_btn = ModernButton("刷新", ModernButton.SECONDARY)
        refresh_btn.clicked.connect(self.refresh_data)
        layout.addWidget(refresh_btn)
        
        return toolbar
    
    def _setup_style(self):
        """设置样式"""
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #1a1a1a;
                color: #ffffff;
                border: none;
                gridline-color: #3d3d3d;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #3d3d3d;
            }
            QTableWidget::item:selected {
                background-color: #0d6efd;
            }
            QTableWidget::item:hover {
                background-color: rgba(13, 110, 253, 0.2);
            }
            QTableWidget::item:alternate {
                background-color: #242424;
            }
        """)
    
    def set_columns(self, columns: List[Dict[str, Any]]):
        """设置列配置
        
        Args:
            columns: 列配置列表，每个配置包含:
                - key: 数据键名
                - title: 显示标题
                - width: 列宽度（可选）
                - sortable: 是否可排序（可选）
                - formatter: 格式化函数（可选）
        """
        self.columns = columns
        self.table.setColumnCount(len(columns))
        
        # 设置列标题
        headers = [col.get("title", col["key"]) for col in columns]
        self.table.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        for i, col in enumerate(columns):
            if "width" in col:
                self.table.setColumnWidth(i, col["width"])
    
    def set_data(self, data: List[Dict[str, Any]]):
        """设置数据"""
        self.data = data
        self.filtered_data = data.copy()
        self.pagination.set_total_items(len(self.filtered_data))
        self._update_table()
    
    def add_row(self, row_data: Dict[str, Any]):
        """添加行"""
        self.data.append(row_data)
        self._apply_filter()
        self.data_changed.emit()
    
    def remove_row(self, row_index: int):
        """删除行"""
        if 0 <= row_index < len(self.data):
            del self.data[row_index]
            self._apply_filter()
            self.data_changed.emit()
    
    def get_selected_row(self) -> Optional[Dict[str, Any]]:
        """获取选中的行数据"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            start, _ = self.pagination.get_page_range()
            data_index = start + current_row
            if 0 <= data_index < len(self.filtered_data):
                return self.filtered_data[data_index]
        return None
    
    def _update_table(self):
        """更新表格显示"""
        # 获取当前页数据范围
        start, end = self.pagination.get_page_range()
        page_data = self.filtered_data[start:end]
        
        # 设置行数
        self.table.setRowCount(len(page_data))
        
        # 填充数据
        for row, data in enumerate(page_data):
            for col, col_config in enumerate(self.columns):
                key = col_config["key"]
                value = data.get(key, "")
                
                # 格式化值
                if "formatter" in col_config:
                    value = col_config["formatter"](value)
                
                # 创建单元格
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
                self.table.setItem(row, col, item)
    
    def _on_sort_changed(self, column: int, ascending: bool):
        """排序变化处理"""
        if 0 <= column < len(self.columns):
            col_config = self.columns[column]
            if col_config.get("sortable", True):
                self.sort_column = column
                self.sort_ascending = ascending
                self._apply_sort()
    
    def _apply_sort(self):
        """应用排序"""
        if self.sort_column >= 0:
            key = self.columns[self.sort_column]["key"]
            self.filtered_data.sort(
                key=lambda x: x.get(key, ""),
                reverse=not self.sort_ascending
            )
            self._update_table()
    
    def _on_search_changed(self, text: str):
        """搜索文本变化"""
        self.filter_text = text.lower()
        self._apply_filter()
    
    def _apply_filter(self):
        """应用筛选"""
        if self.filter_text:
            self.filtered_data = []
            for row in self.data:
                # 在所有列中搜索
                for col in self.columns:
                    value = str(row.get(col["key"], "")).lower()
                    if self.filter_text in value:
                        self.filtered_data.append(row)
                        break
        else:
            self.filtered_data = self.data.copy()
        
        # 重新排序
        if self.sort_column >= 0:
            self._apply_sort()
        
        # 更新分页
        self.pagination.set_total_items(len(self.filtered_data))
        self.pagination.go_first_page()
        self._update_table()
    
    def _on_page_changed(self, page: int):
        """页码变化"""
        self._update_table()
    
    def _on_selection_changed(self):
        """选择变化"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            start, _ = self.pagination.get_page_range()
            data_index = start + current_row
            self.row_selected.emit(data_index)
    
    def _on_item_double_clicked(self, item: QTableWidgetItem):
        """双击事件"""
        row = item.row()
        start, _ = self.pagination.get_page_range()
        data_index = start + row
        self.row_double_clicked.emit(data_index)
    
    def _show_export_menu(self):
        """显示导出菜单"""
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #3d3d3d;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 24px;
            }
            QMenu::item:selected {
                background-color: #0d6efd;
            }
        """)
        
        # CSV导出
        csv_action = QAction("导出为CSV", self)
        csv_action.triggered.connect(self._export_csv)
        menu.addAction(csv_action)
        
        # JSON导出
        json_action = QAction("导出为JSON", self)
        json_action.triggered.connect(self._export_json)
        menu.addAction(json_action)
        
        # 显示菜单
        menu.exec_(self.cursor().pos())
    
    def _export_csv(self):
        """导出CSV"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出CSV", "", "CSV文件 (*.csv)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(
                        f, 
                        fieldnames=[col["key"] for col in self.columns]
                    )
                    
                    # 写入标题
                    writer.writerow({
                        col["key"]: col["title"] 
                        for col in self.columns
                    })
                    
                    # 写入数据
                    writer.writerows(self.filtered_data)
                
                QMessageBox.information(self, "成功", "数据已导出到CSV文件")
                logger.info(f"数据导出到CSV: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
                logger.error(f"CSV导出失败: {e}")
    
    def _export_json(self):
        """导出JSON"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出JSON", "", "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.filtered_data, f, ensure_ascii=False, indent=2)
                
                QMessageBox.information(self, "成功", "数据已导出到JSON文件")
                logger.info(f"数据导出到JSON: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
                logger.error(f"JSON导出失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        self._update_table()
        self.data_changed.emit()
        logger.debug("表格数据已刷新") 