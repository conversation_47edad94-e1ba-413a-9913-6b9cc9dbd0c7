#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVsion现代化主应用程序

整合了侧边栏和工作流编辑器的主应用程序
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QStackedWidget, QLabel, QFrame, QSplitter
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont
from loguru import logger

from wirevsion.ui.modern_sidebar import ModernSidebar
from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
from wirevsion.ui.modern_components import ModernCard, THEME_COLORS
from wirevsion.ui.algorithm_ui_manager import AlgorithmUIManager


class ModernMainApplication(QMainWindow):
    """现代化主应用程序"""

    def __init__(self):
        super().__init__()

        # 设置窗口属性
        self.setWindowTitle("WireVsion - 智能视觉检测系统")
        self.resize(1280, 800)
        self.setMinimumSize(800, 600)

        # 初始化算法UI管理器
        self.algorithm_ui_manager = AlgorithmUIManager()

        # 应用全局样式
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {THEME_COLORS["dark_bg_app"]};
            }}
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
            }}
        """)

        # 初始化UI
        self._setup_ui()

        logger.info("现代化主应用程序已创建")

    def _setup_ui(self):
        """设置UI"""
        # 创建中央部件
        central_widget = QWidget()
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建侧边栏
        self.sidebar = ModernSidebar()

        # 添加Logo
        self.sidebar.logo_label.setPixmap(QIcon("resources/icons/logo.svg").pixmap(26, 26))

        # 添加导航组和项目
        self._setup_navigation()

        # 创建内容区域
        self.content_stack = QStackedWidget()
        self.content_stack.setStyleSheet(f"background-color: {THEME_COLORS['dark_bg_app']};")

        # 创建页面
        self._create_dashboard_page()
        self._create_workflow_page()
        self._create_tools_page()
        self._create_algorithm_page()
        self._create_results_page()
        self._create_settings_page()

        # 连接侧边栏导航信号
        self.sidebar.navigation_changed.connect(self._on_navigation_changed)

        # 设置默认页面
        self.sidebar.set_current_item("dashboard")

        # 添加组件到主布局
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.content_stack, 1)

        self.setCentralWidget(central_widget)

    def _setup_navigation(self):
        """设置导航菜单"""
        # 主要功能组
        main_group = self.sidebar.add_navigation_group("main", "主要功能")
        main_group.add_item(self.sidebar.add_navigation_item("main", "dashboard", "仪表板", "resources/icons/dashboard.svg"))
        main_group.add_item(self.sidebar.add_navigation_item("main", "workflow", "工作流编辑", "resources/icons/workflow.svg"))

        # 工具组
        tools_group = self.sidebar.add_navigation_group("tools", "工具")
        tools_group.add_item(self.sidebar.add_navigation_item("tools", "tools", "工具", "resources/icons/tools.svg"))

        # 算法库组
        library_group = self.sidebar.add_navigation_group("library", "算法库")
        library_group.add_item(self.sidebar.add_navigation_item("library", "algorithm", "算法库", "resources/icons/algorithm.svg"))

        # 结果分析组
        analysis_group = self.sidebar.add_navigation_group("analysis", "结果分析")
        analysis_group.add_item(self.sidebar.add_navigation_item("analysis", "results", "结果分析", "resources/icons/results.svg"))

        # 设置设置按钮图标
        self.sidebar.settings_btn.setIcon(QIcon("resources/icons/settings.svg"))

    def _on_navigation_changed(self, item_id: str):
        """处理导航项点击"""
        page_map = {
            "dashboard": 0,
            "workflow": 1,
            "tools": 2,
            "algorithm": 3,
            "results": 4,
            "settings": 5
        }

        if item_id in page_map:
            self.content_stack.setCurrentIndex(page_map[item_id])

        logger.debug(f"切换到页面: {item_id}")

    def _create_dashboard_page(self):
        """创建仪表盘页面"""
        dashboard_widget = QWidget()
        layout = QVBoxLayout(dashboard_widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # 创建欢迎卡片
        welcome_card = ModernCard("欢迎使用 WireVsion", "智能视觉检测系统")
        welcome_label = QLabel("WireVsion 是一个用于工业视觉检测的系统，可以通过工作流编辑器创建和管理检测流程。")
        welcome_label.setWordWrap(True)
        welcome_label.setStyleSheet(f"color: {THEME_COLORS['text_secondary']}; font-size: 14px;")
        welcome_card.add_content(welcome_label)

        # 添加统计卡片
        stats_layout = QHBoxLayout()

        # 创建几个统计卡片
        stats_cards = [
            ("工作流数量", "5"),
            ("运行中工作流", "2"),
            ("总处理图像", "12,453"),
            ("检测准确率", "98.7%")
        ]

        for title, value in stats_cards:
            card = ModernCard(title)
            value_label = QLabel(value)
            value_label.setAlignment(Qt.AlignCenter)
            value_label.setStyleSheet(f"color: {THEME_COLORS['primary']}; font-size: 24px; font-weight: bold;")
            card.add_content(value_label)
            stats_layout.addWidget(card)

        layout.addWidget(welcome_card)
        layout.addLayout(stats_layout)
        layout.addStretch()

        self.content_stack.addWidget(dashboard_widget)

    def _create_workflow_page(self):
        """创建工作流页面"""
        workflow_widget = QWidget()
        layout = QVBoxLayout(workflow_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建工作流编辑器
        workflow_editor = ModernWorkflowEditor()
        layout.addWidget(workflow_editor)

        self.content_stack.addWidget(workflow_widget)

    def _create_tools_page(self):
        """创建工具页面"""
        tools_widget = QWidget()
        layout = QVBoxLayout(tools_widget)
        layout.setContentsMargins(20, 20, 20, 20)

        title_label = QLabel("工具")
        title_label.setStyleSheet(f"color: {THEME_COLORS['text_title']}; font-size: 24px; font-weight: bold;")

        # 创建工具卡片
        tools_layout = QHBoxLayout()
        tools = [
            ("图像查看器", "查看和分析图像"),
            ("数据导出", "导出检测结果为CSV或JSON"),
            ("批量处理", "批量处理多个图像"),
            ("标注工具", "手动标注图像")
        ]

        for title, subtitle in tools:
            card = ModernCard(title, subtitle)
            tools_layout.addWidget(card)

        layout.addWidget(title_label)
        layout.addLayout(tools_layout)
        layout.addStretch()

        self.content_stack.addWidget(tools_widget)

    def _create_algorithm_page(self):
        """创建算法库页面 - 使用新的算法UI管理器"""
        from wirevsion.ui.modern_algorithm_library import ModernAlgorithmLibrary

        # 创建现代化算法库界面
        algorithm_library = ModernAlgorithmLibrary(self.algorithm_ui_manager)

        self.content_stack.addWidget(algorithm_library)

    def _create_results_page(self):
        """创建结果分析页面"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)
        layout.setContentsMargins(20, 20, 20, 20)

        title_label = QLabel("结果分析")
        title_label.setStyleSheet(f"color: {THEME_COLORS['text_title']}; font-size: 24px; font-weight: bold;")

        placeholder_label = QLabel("这里将显示检测结果分析")
        placeholder_label.setAlignment(Qt.AlignCenter)
        placeholder_label.setStyleSheet(f"color: {THEME_COLORS['text_secondary']}; font-size: 18px;")

        layout.addWidget(title_label)
        layout.addWidget(placeholder_label)

        self.content_stack.addWidget(results_widget)

    def _create_settings_page(self):
        """创建设置页面"""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        layout.setContentsMargins(20, 20, 20, 20)

        title_label = QLabel("设置")
        title_label.setStyleSheet(f"color: {THEME_COLORS['text_title']}; font-size: 24px; font-weight: bold;")

        settings_card = ModernCard("应用设置")
        settings_layout = QVBoxLayout()

        settings = [
            "通用设置",
            "外观设置",
            "性能设置",
            "高级设置",
            "关于"
        ]

        for setting in settings:
            setting_label = QLabel(setting)
            setting_label.setStyleSheet(f"color: {THEME_COLORS['text_secondary']}; padding: 8px;")
            settings_layout.addWidget(setting_label)

        settings_card.add_content(QWidget())  # 占位符
        settings_card.content_layout.addLayout(settings_layout)

        layout.addWidget(title_label)
        layout.addWidget(settings_card)
        layout.addStretch()

        self.content_stack.addWidget(settings_widget)