#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化算法管理界面

第二阶段功能实现：
- 算法模块全覆盖管理
- 高级特性开发工具
- 开发者友好界面
- 智能算法推荐
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTreeWidget, QTreeWidgetItem, QLabel, QTextEdit,
    QSplitter, QScrollArea, QGroupBox, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, QMutex
from PyQt6.QtGui import QPixmap, QIcon, QFont, QColor

from .modern_components import (
    ModernCard, ModernButton, ModernInput, ModernComboBox,
    ModernTabs, ModernStatusBar, ModernDialog, ModernProgressBar
)
from ..algorithms import algorithm_registry
from ..algorithms.base_algorithm import BaseAlgorithm
from ..utils.memory_manager import performance_monitor
from loguru import logger


class AlgorithmCard(ModernCard):
    """算法卡片组件"""
    
    algorithm_selected = pyqtSignal(str, str)  # category, algorithm_name
    algorithm_configured = pyqtSignal(str, str, dict)  # category, algorithm_name, config
    
    def __init__(self, category: str, algorithm_name: str, metadata: Dict[str, Any], parent=None):
        super().__init__(
            title=metadata.get('display_name', algorithm_name),
            subtitle=metadata.get('description', ''),
            parent=parent
        )
        
        self.category = category
        self.algorithm_name = algorithm_name
        self.metadata = metadata
        self.algorithm_instance = None
        
        # 设置算法卡片内容
        self._setup_algorithm_content()
        
        # 连接信号
        self._connect_signals()
        
        logger.debug(f"算法卡片创建: {category}.{algorithm_name}")
    
    def _setup_algorithm_content(self):
        """设置算法卡片内容"""
        content_layout = QVBoxLayout()
        
        # 算法状态和统计
        status_layout = QHBoxLayout()
        
        # 状态指示器
        self.status_label = QLabel("未初始化")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #ffa726;
                color: white;
                border-radius: 10px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 性能指标
        self.performance_label = QLabel("性能: --")
        self.performance_label.setStyleSheet("font-size: 11px; color: #757575;")
        status_layout.addWidget(self.performance_label)
        
        content_layout.addLayout(status_layout)
        
        # 算法参数预览
        params_info = self.metadata.get('default_parameters', {})
        if params_info:
            params_text = f"参数: {len(params_info)} 个"
            params_label = QLabel(params_text)
            params_label.setStyleSheet("font-size: 12px; color: #616161; margin-top: 4px;")
            content_layout.addWidget(params_label)
        
        # 连接信息
        input_count = len(self.metadata.get('input_connections', []))
        output_count = len(self.metadata.get('output_connections', []))
        connections_text = f"输入: {input_count} | 输出: {output_count}"
        connections_label = QLabel(connections_text)
        connections_label.setStyleSheet("font-size: 11px; color: #9e9e9e; margin-top: 2px;")
        content_layout.addWidget(connections_label)
        
        # 操作按钮
        self._create_action_buttons(content_layout)
        
        # 设置内容
        self.set_content_layout(content_layout)
    
    def _create_action_buttons(self, layout: QVBoxLayout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        # 测试按钮
        self.test_button = ModernButton("测试", ModernButton.PRIMARY)
        self.test_button.clicked.connect(self._test_algorithm)
        button_layout.addWidget(self.test_button)
        
        # 配置按钮
        self.config_button = ModernButton("配置", ModernButton.SECONDARY)
        self.config_button.clicked.connect(self._configure_algorithm)
        button_layout.addWidget(self.config_button)
        
        layout.addLayout(button_layout)
    
    def _connect_signals(self):
        """连接信号"""
        # 卡片点击选择
        self.mousePressEvent = self._on_card_clicked
    
    def _on_card_clicked(self, event):
        """卡片点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.algorithm_selected.emit(self.category, self.algorithm_name)
            self._update_selection_style(True)
    
    def _update_selection_style(self, selected: bool):
        """更新选择样式"""
        if selected:
            self.setStyleSheet("""
                ModernCard {
                    background-color: #e3f2fd;
                    border: 2px solid #2196f3;
                    border-radius: 12px;
                }
            """)
        else:
            self._setup_style()  # 恢复默认样式
    
    def _test_algorithm(self):
        """测试算法"""
        try:
            # 更新状态
            self.status_label.setText("测试中...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #ffa726;
                    color: white;
                    border-radius: 10px;
                    padding: 4px 8px;
                    font-size: 11px;
                    font-weight: bold;
                }
            """)
            
            # 创建算法实例进行测试
            if not self.algorithm_instance:
                algorithm_class = algorithm_registry.get_algorithm_class(self.category, self.algorithm_name)
                if algorithm_class:
                    self.algorithm_instance = algorithm_class()
            
            if self.algorithm_instance:
                # 模拟测试执行
                QTimer.singleShot(1000, self._on_test_completed)
                logger.info(f"开始测试算法: {self.category}.{self.algorithm_name}")
            else:
                self._on_test_failed("算法类未找到")
                
        except Exception as e:
            self._on_test_failed(str(e))
    
    def _on_test_completed(self):
        """测试完成"""
        self.status_label.setText("测试通过")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #4caf50;
                color: white;
                border-radius: 10px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        
        # 更新性能信息
        self.performance_label.setText("性能: 优秀")
        logger.info(f"算法测试完成: {self.category}.{self.algorithm_name}")
    
    def _on_test_failed(self, error: str):
        """测试失败"""
        self.status_label.setText("测试失败")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f44336;
                color: white;
                border-radius: 10px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: bold;
            }
        """)
        logger.error(f"算法测试失败: {self.category}.{self.algorithm_name} - {error}")
    
    def _configure_algorithm(self):
        """配置算法"""
        self.algorithm_selected.emit(self.category, self.algorithm_name)
        
        # 打开配置对话框
        config_dialog = AlgorithmConfigDialog(
            self.category, 
            self.algorithm_name, 
            self.metadata, 
            parent=self
        )
        
        config_dialog.configuration_saved.connect(
            lambda config: self.algorithm_configured.emit(self.category, self.algorithm_name, config)
        )
        
        config_dialog.show()
        logger.info(f"打开算法配置: {self.category}.{self.algorithm_name}")


class AlgorithmConfigDialog(ModernDialog):
    """算法配置对话框"""
    
    configuration_saved = pyqtSignal(dict)
    
    def __init__(self, category: str, algorithm_name: str, metadata: Dict[str, Any], parent=None):
        super().__init__(f"配置算法 - {metadata.get('display_name', algorithm_name)}", parent)
        
        self.category = category
        self.algorithm_name = algorithm_name
        self.metadata = metadata
        self.parameter_widgets = {}
        
        self.setFixedSize(800, 600)
        
        # 创建配置界面
        self._setup_config_interface()
        
        logger.debug(f"算法配置对话框创建: {category}.{algorithm_name}")
    
    def _setup_config_interface(self):
        """设置配置界面"""
        config_tabs = ModernTabs()
        
        # 基本配置选项卡
        basic_config = self._create_basic_config_tab()
        config_tabs.add_tab(basic_config, "基本配置")
        
        # 高级配置选项卡
        advanced_config = self._create_advanced_config_tab()
        config_tabs.add_tab(advanced_config, "高级配置")
        
        # 性能优化选项卡
        performance_config = self._create_performance_config_tab()
        config_tabs.add_tab(performance_config, "性能优化")
        
        self.add_content(config_tabs)
    
    def _create_basic_config_tab(self) -> QWidget:
        """创建基本配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 算法信息卡片
        info_card = ModernCard("算法信息", "查看算法的基本信息和元数据")
        info_layout = QVBoxLayout()
        
        # 算法描述
        desc_label = QLabel(self.metadata.get('description', '暂无描述'))
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #616161; font-size: 13px; line-height: 1.4;")
        info_layout.addWidget(desc_label)
        
        # 算法类型
        type_label = QLabel(f"类型: {self.category}")
        type_label.setStyleSheet("color: #424242; font-weight: bold; margin-top: 8px;")
        info_layout.addWidget(type_label)
        
        info_card.set_content_layout(info_layout)
        layout.addWidget(info_card)
        
        # 参数配置卡片
        params_card = ModernCard("参数配置", "配置算法的运行参数")
        params_layout = self._create_parameter_controls()
        params_card.set_content_layout(params_layout)
        layout.addWidget(params_card)
        
        layout.addStretch()
        return widget
    
    def _create_parameter_controls(self) -> QVBoxLayout:
        """创建参数控件"""
        layout = QVBoxLayout()
        
        # 获取参数模式和默认值
        schema = self.metadata.get('parameter_schema', {})
        defaults = self.metadata.get('default_parameters', {})
        
        if not schema:
            no_params_label = QLabel("此算法无可配置参数")
            no_params_label.setStyleSheet("color: #9e9e9e; font-style: italic; padding: 20px;")
            layout.addWidget(no_params_label)
            return layout
        
        # 为每个参数创建控件
        for param_name, param_schema in schema.items():
            param_layout = QVBoxLayout()
            
            # 参数标签
            param_label = QLabel(param_name)
            param_label.setStyleSheet("font-weight: bold; color: #424242; margin-bottom: 4px;")
            param_layout.addWidget(param_label)
            
            # 参数描述
            description = param_schema.get('description', '')
            if description:
                desc_label = QLabel(description)
                desc_label.setStyleSheet("color: #757575; font-size: 11px; margin-bottom: 8px;")
                desc_label.setWordWrap(True)
                param_layout.addWidget(desc_label)
            
            # 参数控件
            control_widget = self._create_parameter_control(param_name, param_schema, defaults)
            if control_widget:
                param_layout.addWidget(control_widget)
                self.parameter_widgets[param_name] = control_widget
            
            layout.addLayout(param_layout)
        
        return layout
    
    def _create_parameter_control(self, param_name: str, schema: Dict[str, Any], 
                                defaults: Dict[str, Any]) -> Optional[QWidget]:
        """创建单个参数控件"""
        param_type = schema.get('type', 'string')
        default_value = defaults.get(param_name)
        
        if param_type == 'boolean':
            from .modern_components import ModernSwitch
            widget = ModernSwitch()
            if default_value is not None:
                widget.setChecked(default_value)
            return widget
        
        elif param_type == 'string':
            if 'enum' in schema:
                widget = ModernComboBox(schema['enum'])
                if default_value and default_value in schema['enum']:
                    widget.setCurrentText(default_value)
                return widget
            else:
                widget = ModernInput(placeholder=f"输入{param_name}")
                if default_value is not None:
                    widget.setText(str(default_value))
                return widget
        
        elif param_type in ['integer', 'number']:
            widget = ModernInput(placeholder=f"输入{param_name}值")
            if default_value is not None:
                widget.setText(str(default_value))
            
            # 添加范围验证提示
            if 'minimum' in schema or 'maximum' in schema:
                min_val = schema.get('minimum', '∞')
                max_val = schema.get('maximum', '∞')
                widget.setToolTip(f"范围: {min_val} ~ {max_val}")
            
            return widget
        
        return None
    
    def _create_advanced_config_tab(self) -> QWidget:
        """创建高级配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # ROI处理配置
        roi_card = ModernCard("ROI处理", "配置感兴趣区域处理选项")
        roi_layout = QVBoxLayout()
        
        self.enable_roi_switch = ModernButton("启用ROI处理", ModernButton.SECONDARY)
        self.enable_roi_switch.setCheckable(True)
        roi_layout.addWidget(self.enable_roi_switch)
        
        roi_card.set_content_layout(roi_layout)
        layout.addWidget(roi_card)
        
        # 缓存配置
        cache_card = ModernCard("缓存设置", "配置算法结果缓存选项")
        cache_layout = QVBoxLayout()
        
        self.enable_cache_switch = ModernButton("启用结果缓存", ModernButton.SECONDARY)
        self.enable_cache_switch.setCheckable(True)
        cache_layout.addWidget(self.enable_cache_switch)
        
        cache_card.set_content_layout(cache_layout)
        layout.addWidget(cache_card)
        
        layout.addStretch()
        return widget
    
    def _create_performance_config_tab(self) -> QWidget:
        """创建性能优化选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 性能监控配置
        monitor_card = ModernCard("性能监控", "配置性能监控和分析选项")
        monitor_layout = QVBoxLayout()
        
        self.enable_monitor_switch = ModernButton("启用性能监控", ModernButton.SECONDARY)
        self.enable_monitor_switch.setCheckable(True)
        self.enable_monitor_switch.setChecked(True)
        monitor_layout.addWidget(self.enable_monitor_switch)
        
        monitor_card.set_content_layout(monitor_layout)
        layout.addWidget(monitor_card)
        
        # 优化配置
        optimization_card = ModernCard("算法优化", "配置算法执行优化选项")
        optimization_layout = QVBoxLayout()
        
        self.parallel_processing_switch = ModernButton("并行处理", ModernButton.SECONDARY)
        self.parallel_processing_switch.setCheckable(True)
        optimization_layout.addWidget(self.parallel_processing_switch)
        
        optimization_card.set_content_layout(optimization_layout)
        layout.addWidget(optimization_card)
        
        layout.addStretch()
        return widget
    
    def accept(self):
        """保存配置"""
        config = self._collect_configuration()
        self.configuration_saved.emit(config)
        super().accept()
        logger.info(f"算法配置已保存: {self.category}.{self.algorithm_name}")
    
    def _collect_configuration(self) -> Dict[str, Any]:
        """收集配置信息"""
        config = {
            'parameters': {},
            'advanced': {
                'enable_roi': getattr(self.enable_roi_switch, 'isChecked', lambda: False)(),
                'enable_cache': getattr(self.enable_cache_switch, 'isChecked', lambda: False)(),
                'enable_monitor': getattr(self.enable_monitor_switch, 'isChecked', lambda: True)(),
                'parallel_processing': getattr(self.parallel_processing_switch, 'isChecked', lambda: False)()
            }
        }
        
        # 收集参数值
        for param_name, widget in self.parameter_widgets.items():
            if hasattr(widget, 'isChecked'):
                config['parameters'][param_name] = widget.isChecked()
            elif hasattr(widget, 'text'):
                text = widget.text()
                # 尝试转换为适当的类型
                try:
                    if '.' in text:
                        config['parameters'][param_name] = float(text)
                    else:
                        config['parameters'][param_name] = int(text)
                except ValueError:
                    config['parameters'][param_name] = text
            elif hasattr(widget, 'currentText'):
                config['parameters'][param_name] = widget.currentText()
        
        return config


class ModernAlgorithmManager(QWidget):
    """现代化算法管理器主界面"""
    
    algorithm_selected = pyqtSignal(str, str)  # category, algorithm_name
    algorithm_executed = pyqtSignal(str, str, dict)  # category, algorithm_name, result
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 算法数据
        self.algorithm_cards = {}  # {category: {algorithm_name: AlgorithmCard}}
        self.selected_algorithm = None
        self.execution_history = []
        
        # 创建界面
        self._setup_ui()
        
        # 加载算法
        self._load_algorithms()
        
        # 启动性能监控
        self._start_performance_monitoring()
        
        logger.info("现代化算法管理器初始化完成")
    
    def _setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)
        
        # 标题和工具栏
        self._create_header(layout)
        
        # 主要内容区域
        self._create_main_content(layout)
        
        # 状态栏
        self.status_bar = ModernStatusBar()
        layout.addWidget(self.status_bar)
    
    def _create_header(self, layout: QVBoxLayout):
        """创建标题和工具栏"""
        header_layout = QHBoxLayout()
        
        # 标题
        title_label = QLabel("算法管理中心")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #212121;
                margin-bottom: 8px;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # 工具按钮
        self.refresh_button = ModernButton("刷新算法", ModernButton.SECONDARY)
        self.refresh_button.clicked.connect(self._reload_algorithms)
        header_layout.addWidget(self.refresh_button)
        
        self.batch_test_button = ModernButton("批量测试", ModernButton.PRIMARY)
        self.batch_test_button.clicked.connect(self._batch_test_algorithms)
        header_layout.addWidget(self.batch_test_button)
        
        layout.addLayout(header_layout)
        
        # 副标题
        subtitle_label = QLabel("管理和配置机器视觉算法模块")
        subtitle_label.setStyleSheet("color: #757575; font-size: 14px; margin-bottom: 16px;")
        layout.addWidget(subtitle_label)
    
    def _create_main_content(self, layout: QVBoxLayout):
        """创建主要内容区域"""
        # 创建选项卡
        self.main_tabs = ModernTabs()
        
        # 算法浏览选项卡
        algorithm_browser = self._create_algorithm_browser()
        self.main_tabs.add_tab(algorithm_browser, "算法浏览")
        
        # 性能分析选项卡
        performance_analyzer = self._create_performance_analyzer()
        self.main_tabs.add_tab(performance_analyzer, "性能分析")
        
        # 开发工具选项卡
        developer_tools = self._create_developer_tools()
        self.main_tabs.add_tab(developer_tools, "开发工具")
        
        layout.addWidget(self.main_tabs)
    
    def _create_algorithm_browser(self) -> QWidget:
        """创建算法浏览器"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 16, 0, 0)
        
        # 搜索和筛选
        filter_layout = QHBoxLayout()
        
        self.search_input = ModernInput(placeholder="搜索算法...")
        self.search_input.textChanged.connect(self._filter_algorithms)
        filter_layout.addWidget(self.search_input)
        
        self.category_filter = ModernComboBox()
        self.category_filter.addItem("所有类别")
        self.category_filter.currentTextChanged.connect(self._filter_algorithms)
        filter_layout.addWidget(self.category_filter)
        
        layout.addLayout(filter_layout)
        
        # 算法卡片网格
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.algorithm_grid_widget = QWidget()
        self.algorithm_grid_layout = QGridLayout(self.algorithm_grid_widget)
        self.algorithm_grid_layout.setSpacing(16)
        
        scroll_area.setWidget(self.algorithm_grid_widget)
        layout.addWidget(scroll_area)
        
        return widget
    
    def _create_performance_analyzer(self) -> QWidget:
        """创建性能分析器"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 16, 0, 0)
        
        # 性能概览卡片
        overview_card = ModernCard("性能概览", "系统整体性能监控")
        overview_layout = QVBoxLayout()
        
        # 实时性能指标
        metrics_layout = QGridLayout()
        
        self.cpu_usage_label = QLabel("CPU使用率: --%")
        self.memory_usage_label = QLabel("内存使用: -- MB")
        self.algorithm_count_label = QLabel("已加载算法: 0")
        self.execution_count_label = QLabel("执行次数: 0")
        
        metrics_layout.addWidget(self.cpu_usage_label, 0, 0)
        metrics_layout.addWidget(self.memory_usage_label, 0, 1)
        metrics_layout.addWidget(self.algorithm_count_label, 1, 0)
        metrics_layout.addWidget(self.execution_count_label, 1, 1)
        
        overview_layout.addLayout(metrics_layout)
        overview_card.set_content_layout(overview_layout)
        layout.addWidget(overview_card)
        
        # 执行历史
        history_card = ModernCard("执行历史", "最近的算法执行记录")
        history_layout = QVBoxLayout()
        
        self.history_text = QTextEdit()
        self.history_text.setMaximumHeight(200)
        self.history_text.setReadOnly(True)
        self.history_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                padding: 8px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        history_layout.addWidget(self.history_text)
        
        history_card.set_content_layout(history_layout)
        layout.addWidget(history_card)
        
        layout.addStretch()
        return widget
    
    def _create_developer_tools(self) -> QWidget:
        """创建开发工具"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 16, 0, 0)
        
        # 算法开发工具
        dev_card = ModernCard("算法开发工具", "快速开发和调试算法")
        dev_layout = QVBoxLayout()
        
        # 工具按钮
        tools_layout = QGridLayout()
        
        create_algorithm_btn = ModernButton("创建新算法", ModernButton.PRIMARY)
        create_algorithm_btn.clicked.connect(self._create_new_algorithm)
        tools_layout.addWidget(create_algorithm_btn, 0, 0)
        
        debug_algorithm_btn = ModernButton("调试算法", ModernButton.SECONDARY)
        debug_algorithm_btn.clicked.connect(self._debug_algorithm)
        tools_layout.addWidget(debug_algorithm_btn, 0, 1)
        
        benchmark_btn = ModernButton("性能基准测试", ModernButton.WARNING)
        benchmark_btn.clicked.connect(self._run_benchmark)
        tools_layout.addWidget(benchmark_btn, 1, 0)
        
        export_config_btn = ModernButton("导出配置", ModernButton.SECONDARY)
        export_config_btn.clicked.connect(self._export_configurations)
        tools_layout.addWidget(export_config_btn, 1, 1)
        
        dev_layout.addLayout(tools_layout)
        dev_card.set_content_layout(dev_layout)
        layout.addWidget(dev_card)
        
        # 代码生成器
        codegen_card = ModernCard("代码生成器", "自动生成算法模板代码")
        codegen_layout = QVBoxLayout()
        
        self.algorithm_template_combo = ModernComboBox([
            "基础图像处理算法",
            "特征检测算法",
            "测量算法",
            "深度学习算法",
            "位置修正算法"
        ])
        codegen_layout.addWidget(self.algorithm_template_combo)
        
        generate_code_btn = ModernButton("生成代码模板", ModernButton.SUCCESS)
        generate_code_btn.clicked.connect(self._generate_algorithm_template)
        codegen_layout.addWidget(generate_code_btn)
        
        codegen_card.set_content_layout(codegen_layout)
        layout.addWidget(codegen_card)
        
        layout.addStretch()
        return widget
    
    def _load_algorithms(self):
        """加载算法"""
        if not algorithm_registry:
            logger.warning("算法注册表不可用")
            return
        
        # 清除现有卡片
        self.algorithm_cards.clear()
        self._clear_algorithm_grid()
        
        # 获取所有算法类别
        categories = algorithm_registry.get_all_categories()
        
        # 更新类别筛选器
        self.category_filter.clear()
        self.category_filter.addItem("所有类别")
        for category in categories:
            display_name = self._get_category_display_name(category)
            self.category_filter.addItem(display_name)
        
        # 创建算法卡片
        row, col = 0, 0
        for category in categories:
            self.algorithm_cards[category] = {}
            algorithms = algorithm_registry.get_category_metadata(category)
            
            for algorithm_name, metadata in algorithms.items():
                # 创建算法卡片
                card = AlgorithmCard(category, algorithm_name, metadata)
                card.algorithm_selected.connect(self._on_algorithm_selected)
                card.algorithm_configured.connect(self._on_algorithm_configured)
                
                # 添加到网格
                self.algorithm_grid_layout.addWidget(card, row, col)
                self.algorithm_cards[category][algorithm_name] = card
                
                # 更新网格位置
                col += 1
                if col >= 3:  # 每行3个卡片
                    col = 0
                    row += 1
        
        # 更新统计信息
        total_algorithms = sum(len(algs) for algs in self.algorithm_cards.values())
        self.algorithm_count_label.setText(f"已加载算法: {total_algorithms}")
        
        logger.info(f"加载了 {total_algorithms} 个算法")
    
    def _clear_algorithm_grid(self):
        """清除算法网格"""
        while self.algorithm_grid_layout.count():
            item = self.algorithm_grid_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
    
    def _get_category_display_name(self, category: str) -> str:
        """获取类别显示名称"""
        display_names = {
            "image_source": "图像源",
            "image_processing": "图像处理", 
            "feature_detection": "特征检测",
            "object_detection": "目标检测",
            "measurement": "测量算法",
            "deep_learning": "深度学习",
            "position_correction": "位置修正"
        }
        return display_names.get(category, category)
    
    def _filter_algorithms(self):
        """过滤算法显示"""
        search_text = self.search_input.text().lower()
        selected_category = self.category_filter.currentText()
        
        # 显示/隐藏算法卡片
        for category, algorithms in self.algorithm_cards.items():
            category_display = self._get_category_display_name(category)
            
            for algorithm_name, card in algorithms.items():
                # 检查类别筛选
                if selected_category != "所有类别" and category_display != selected_category:
                    card.hide()
                    continue
                
                # 检查搜索文本
                if search_text:
                    algorithm_display = card.metadata.get('display_name', algorithm_name)
                    description = card.metadata.get('description', '')
                    
                    if (search_text not in algorithm_display.lower() and 
                        search_text not in description.lower() and
                        search_text not in algorithm_name.lower()):
                        card.hide()
                        continue
                
                card.show()
    
    def _start_performance_monitoring(self):
        """启动性能监控"""
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self._update_performance_metrics)
        self.performance_timer.start(2000)  # 每2秒更新一次
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        try:
            status = performance_monitor.get_current_status()
            
            # 更新内存使用
            memory_mb = status.get('process_memory_mb', 0)
            self.memory_usage_label.setText(f"内存使用: {memory_mb:.1f} MB")
            self.status_bar.update_memory(memory_mb)
            
            # 更新执行次数
            self.execution_count_label.setText(f"执行次数: {len(self.execution_history)}")
            
        except Exception as e:
            logger.debug(f"性能监控更新失败: {e}")
    
    def _on_algorithm_selected(self, category: str, algorithm_name: str):
        """算法选择事件"""
        self.selected_algorithm = (category, algorithm_name)
        self.algorithm_selected.emit(category, algorithm_name)
        
        # 更新其他卡片的选择状态
        for cat_algorithms in self.algorithm_cards.values():
            for card in cat_algorithms.values():
                if card.category == category and card.algorithm_name == algorithm_name:
                    card._update_selection_style(True)
                else:
                    card._update_selection_style(False)
        
        logger.info(f"选择算法: {category}.{algorithm_name}")
    
    def _on_algorithm_configured(self, category: str, algorithm_name: str, config: Dict[str, Any]):
        """算法配置事件"""
        logger.info(f"算法配置完成: {category}.{algorithm_name}")
        
        # 记录配置历史
        history_entry = f"[{time.strftime('%H:%M:%S')}] 配置算法 {category}.{algorithm_name}"
        self.execution_history.append(history_entry)
        self._update_history_display()
    
    def _update_history_display(self):
        """更新历史显示"""
        # 只显示最近10条记录
        recent_history = self.execution_history[-10:]
        self.history_text.setPlainText('\n'.join(recent_history))
        
        # 滚动到底部
        cursor = self.history_text.textCursor()
        cursor.movePosition(cursor.End)
        self.history_text.setTextCursor(cursor)
    
    def _reload_algorithms(self):
        """重新加载算法"""
        self.status_bar.update_status("正在重新加载算法...")
        self._load_algorithms()
        self.status_bar.update_status("算法重新加载完成")
        logger.info("算法重新加载完成")
    
    def _batch_test_algorithms(self):
        """批量测试算法"""
        self.status_bar.update_status("开始批量测试算法...")
        self.status_bar.show_progress(0, 100)
        
        # 模拟批量测试
        QTimer.singleShot(3000, self._on_batch_test_completed)
        logger.info("开始批量测试算法")
    
    def _on_batch_test_completed(self):
        """批量测试完成"""
        self.status_bar.hide_progress()
        self.status_bar.update_status("批量测试完成")
        logger.info("批量测试完成")
    
    def _create_new_algorithm(self):
        """创建新算法"""
        self.status_bar.update_status("新算法创建功能开发中...")
        logger.info("创建新算法功能触发")
    
    def _debug_algorithm(self):
        """调试算法"""
        if self.selected_algorithm:
            category, algorithm_name = self.selected_algorithm
            self.status_bar.update_status(f"调试算法: {category}.{algorithm_name}")
            logger.info(f"调试算法: {category}.{algorithm_name}")
        else:
            self.status_bar.update_status("请先选择要调试的算法")
    
    def _run_benchmark(self):
        """运行性能基准测试"""
        self.status_bar.update_status("性能基准测试功能开发中...")
        logger.info("性能基准测试功能触发")
    
    def _export_configurations(self):
        """导出配置"""
        self.status_bar.update_status("配置导出功能开发中...")
        logger.info("配置导出功能触发")
    
    def _generate_algorithm_template(self):
        """生成算法模板"""
        template_type = self.algorithm_template_combo.currentText()
        self.status_bar.update_status(f"生成算法模板: {template_type}")
        logger.info(f"生成算法模板: {template_type}")
    
    def get_selected_algorithm(self) -> Optional[Tuple[str, str]]:
        """获取当前选择的算法"""
        return self.selected_algorithm
    
    def refresh_algorithms(self):
        """刷新算法列表"""
        self._reload_algorithms() 