#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化侧边栏导航组件

提供美观的侧边栏导航功能：
- 图标和文字导航
- 折叠/展开动画
- 分组功能
- 活动状态指示
- 工具提示
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QScrollArea, QFrame, QToolTip
)
from PyQt6.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve,
    QSize, QTimer, QPoint, pyqtProperty
)
from PyQt6.QtGui import (
    QPainter, QColor, QFont, QIcon, QPixmap,
    QPainterPath, QBrush, QPen, QLinearGradient
)
from typing import List, Dict, Optional, Any
from loguru import logger
from wirevsion.ui.modern_components import THEME_COLORS # 确保导入了THEME_COLORS
from pathlib import Path


class NavigationItem(QPushButton):
    """导航项组件"""
    
    # 点击信号
    item_clicked = pyqtSignal(str)
    
    def __init__(self, item_id: str, text: str, icon: Optional[QIcon] = None, parent=None):
        super().__init__(parent)
        
        self.item_id = item_id
        self.text = text
        self.icon = icon
        self.is_active = False
        self.is_expanded = True
        
        # 设置属性
        self.setCheckable(True)
        self.setCursor(Qt.PointingHandCursor)
        
        # 初始化UI
        self._setup_ui()
        
        # 连接信号
        self.clicked.connect(lambda: self.item_clicked.emit(self.item_id))
        
        logger.debug(f"导航项创建: {text}")
    
    def _setup_ui(self):
        """设置UI"""
        self.setFixedHeight(42)  # 调整高度更合适
        self.setStyleSheet(self._get_style())
        
        # 更新显示
        self._update_display()
    
    def _get_style(self) -> str:
        """获取样式表 (使用调整后的颜色)"""
        text_color_normal = THEME_COLORS["text_secondary"]
        text_color_hover = THEME_COLORS["text_primary"]
        text_color_checked = "#FFFFFF"  # 选中时使用白色文本
        bg_hover = THEME_COLORS["dark_surface_hover"]
        
        # 使用主色的渐变作为选中背景
        primary_color = QColor(THEME_COLORS["primary"])
        primary_darker = primary_color.darker(125).name()
        
        return f"""
            NavigationItem {{
                background-color: transparent;
                border: none; 
                border-radius: 6px;
                text-align: left; 
                padding: 9px 14px;
                color: {text_color_normal}; 
                font-size: 13px;
                font-weight: 400;
                margin: 3px 6px;
            }}
            NavigationItem:hover {{
                background-color: {bg_hover};
                color: {text_color_hover};
            }}
            NavigationItem:checked {{
                background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                                               stop:0 {THEME_COLORS["primary"]}, 
                                               stop:1 {primary_darker});
                color: {text_color_checked};
                font-weight: 500;
            }}
        """
    
    def _update_display(self):
        """更新显示内容"""
        if self.icon and self.is_expanded:
            self.setIcon(self.icon)
            self.setIconSize(QSize(18, 18))  # 图标稍小更精致
            self.setText(f"  {self.text}")
        elif self.icon and not self.is_expanded:
            self.setIcon(self.icon)
            self.setIconSize(QSize(20, 20))
            self.setText("")
            self.setToolTip(self.text)
        else:
            self.setText(self.text)
    
    def set_active(self, active: bool):
        """设置活动状态"""
        self.is_active = active
        self.setChecked(active)
    
    def set_expanded(self, expanded: bool):
        """设置展开状态"""
        self.is_expanded = expanded
        self._update_display()


class NavigationGroup(QWidget):
    """导航分组组件"""
    
    def __init__(self, title: str, parent=None):
        super().__init__(parent)
        
        self.title = title
        self.items: List[NavigationItem] = []
        
        # 初始化UI
        self._setup_ui()
        
        logger.debug(f"导航分组创建: {title}")
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)  # 减少间距，更紧凑
        
        # 分组标题
        self.title_label = QLabel(self.title.upper())
        title_color = THEME_COLORS["text_secondary"]
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {title_color};
                font-size: 11px;
                font-weight: 600;
                padding: 20px 14px 10px 14px;  /* 增加内边距 */
                text-transform: uppercase;
                letter-spacing: 1.5px;  /* 增加字符间距 */
                opacity: 0.85;  /* 稍微增加透明度 */
            }}
        """)
        layout.addWidget(self.title_label)
        
        # 项目容器
        self.items_widget = QWidget()
        self.items_layout = QVBoxLayout(self.items_widget)
        self.items_layout.setContentsMargins(4, 0, 4, 0)  # 增加左右边距
        self.items_layout.setSpacing(1)  # 项目间距更小
        
        layout.addWidget(self.items_widget)
    
    def add_item(self, item: NavigationItem):
        """添加导航项"""
        self.items.append(item)
        self.items_layout.addWidget(item)
    
    def set_expanded(self, expanded: bool):
        """设置展开状态"""
        self.title_label.setVisible(expanded)
        for item in self.items:
            item.set_expanded(expanded)


class ModernSidebar(QWidget):
    """现代化侧边栏组件"""
    
    # 导航项点击信号
    navigation_changed = pyqtSignal(str)
    # 展开/折叠信号
    expanded_changed = pyqtSignal(bool)
    
    # 宽度常量
    EXPANDED_WIDTH = 210  # 稍微减小宽度
    COLLAPSED_WIDTH = 50  # 稍微减小宽度
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.is_expanded = True
        self.current_item_id = None
        self.groups: Dict[str, NavigationGroup] = {}
        self.all_items: Dict[str, NavigationItem] = {}
        
        # 动画
        self.width_animation = None
        
        # 初始化UI
        self._setup_ui()
        
        # 设置初始宽度
        self.setFixedWidth(self.EXPANDED_WIDTH)
        
        logger.info("现代化侧边栏初始化完成")
    
    def _setup_ui(self):
        """设置UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 设置背景样式 (使用调整后的颜色)
        sidebar_bg = THEME_COLORS["dark_bg_sidebar"]
        border_color = THEME_COLORS["dark_border_primary"]
        
        # 使用QSS而不是paintEvent来设置背景，以确保所有组件都继承这个背景
        self.setStyleSheet(f"""
            ModernSidebar {{
                background-color: {sidebar_bg};
                border-right: 1px solid {border_color};
            }}
        """)
        
        # 头部区域
        self._create_header(main_layout)
        
        # 导航区域
        self._create_navigation_area(main_layout)
        
        # 底部区域
        self._create_footer(main_layout)
    
    def _create_header(self, layout: QVBoxLayout):
        """创建头部区域"""
        header = QWidget()
        header.setFixedHeight(52)  # 增加高度
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(16, 0, 10, 0)  # 调整边距
        header_layout.setSpacing(10)  # 增加间距
        
        # Logo
        self.logo_label = QLabel()
        self.logo_label.setFixedSize(26, 26)  # 调整大小
        logo_bg_color = THEME_COLORS["primary"]
        self.logo_label.setStyleSheet(f"""
            QLabel {{
                background-color: {logo_bg_color};
                border-radius: 6px;
            }}
        """)
        
        # 加载Logo SVG
        if Path("resources/icons/logo.svg").exists():
            try:
                pixmap = QIcon("resources/icons/logo.svg").pixmap(26, 26)
                self.logo_label.setPixmap(pixmap)
                self.logo_label.setStyleSheet("")  # 清除背景色样式
            except Exception as e:
                logger.error(f"无法加载Logo: {e}")
        
        header_layout.addWidget(self.logo_label)
        
        # 标题
        self.sidebar_title = QLabel("WireVision")
        title_color = THEME_COLORS["text_title"]
        self.sidebar_title.setStyleSheet(f"""
            QLabel {{
                color: {title_color};
                font-size: 16px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }}
        """)
        header_layout.addWidget(self.sidebar_title)
        
        header_layout.addStretch()
        
        # 折叠按钮
        self.toggle_btn = QPushButton()
        self.toggle_btn.setFixedSize(28, 28)
        self.toggle_btn.setCursor(Qt.PointingHandCursor)
        toggle_text_color = THEME_COLORS["text_secondary"]
        toggle_hover_bg = THEME_COLORS["dark_surface_hover"]
        toggle_hover_text = THEME_COLORS["text_primary"]
        self.toggle_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: {toggle_text_color};
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: {toggle_hover_bg};
                color: {toggle_hover_text};
            }}
        """)
        self.toggle_btn.setText("≡")  # 使用更常见的汉堡菜单图标
        self.toggle_btn.clicked.connect(self.toggle_sidebar)
        header_layout.addWidget(self.toggle_btn)
        
        layout.addWidget(header)
        
        # 添加一个细微的分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator_color = QColor(THEME_COLORS["dark_border_secondary"])
        separator_color.setAlpha(120)  # 增加透明度
        separator.setStyleSheet(f"QFrame {{ background-color: {separator_color.name(QColor.HexArgb)}; height: 1px; }}")
        layout.addWidget(separator)
    
    def _create_navigation_area(self, layout: QVBoxLayout):
        """创建导航区域"""
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        scrollbar_bg = THEME_COLORS["dark_bg_sidebar"]
        handle_bg = QColor(THEME_COLORS["dark_border_primary"])
        handle_bg.setAlpha(150)  # 半透明
        handle_hover_bg = THEME_COLORS["primary"]
        
        # 优化滚动条样式
        scroll_area.setStyleSheet(f"""
            QScrollArea {{ 
                background-color: transparent; 
                border: none; 
            }}
            QScrollBar:vertical {{ 
                background-color: {scrollbar_bg}; 
                width: 4px; 
                border-radius: 2px; 
                margin: 0px; 
            }}
            QScrollBar::handle:vertical {{ 
                background-color: {handle_bg.name(QColor.HexArgb)}; 
                border-radius: 2px; 
                min-height: 25px; 
            }}
            QScrollBar::handle:vertical:hover {{ 
                background-color: {handle_hover_bg}; 
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{ 
                border: none; 
                background: none; 
                height: 0px; 
            }}
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{ 
                background: none; 
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{ 
                background: none; 
            }}
        """)
        
        # 导航容器
        self.nav_container = QWidget()
        self.nav_layout = QVBoxLayout(self.nav_container)
        self.nav_layout.setContentsMargins(8, 4, 8, 8)  # 调整边距
        self.nav_layout.setSpacing(2)  # 更小的间距
        
        # 添加弹性空间在底部
        self.nav_layout.addStretch(1)
        
        scroll_area.setWidget(self.nav_container)
        layout.addWidget(scroll_area, 1)  # 添加拉伸系数
    
    def _create_footer(self, layout: QVBoxLayout):
        """创建底部区域"""
        footer_widget = QWidget()
        footer_widget.setFixedHeight(50)  # 增加高度
        
        # 添加顶部分隔线
        separator = QFrame(footer_widget)
        separator.setFrameShape(QFrame.HLine)
        separator_color = QColor(THEME_COLORS["dark_border_secondary"])
        separator_color.setAlpha(120)  # 增加透明度
        separator.setStyleSheet(f"QFrame {{ background-color: {separator_color.name(QColor.HexArgb)}; height: 1px; }}")
        separator.setFixedWidth(footer_widget.width())
        
        footer_layout = QVBoxLayout(footer_widget)
        footer_layout.setContentsMargins(8, 4, 8, 4)
        footer_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置按钮 - 使用更明显的样式
        self.settings_btn = NavigationItem("settings", "设置")
        # icon_path = "resources/icons/settings_light.svg" 
        # if Path(icon_path).exists(): self.settings_btn.setIcon(QIcon(icon_path))
        self.settings_btn.item_clicked.connect(self._on_item_clicked)
        footer_layout.addWidget(self.settings_btn)
        
        # 添加分隔线和设置按钮
        layout.addWidget(separator)
        layout.addWidget(footer_widget)
    
    def add_navigation_group(self, group_id: str, title: str) -> NavigationGroup:
        """添加导航分组"""
        group = NavigationGroup(title)
        self.groups[group_id] = group
        self.nav_layout.insertWidget(self.nav_layout.count() - 1, group)  # 插入到弹性空间之前
        return group
    
    def add_navigation_item(self, group_id: str, item_id: str, text: str, icon_path: Optional[str] = None) -> NavigationItem:
        """添加导航项"""
        if group_id not in self.groups:
            logger.error(f"导航分组不存在: {group_id}")
            return None
        
        icon = QIcon(icon_path) if icon_path and Path(icon_path).exists() else None
        item = NavigationItem(item_id, text, icon)
        item.item_clicked.connect(self._on_item_clicked)
        item.set_expanded(self.is_expanded)
        
        self.groups[group_id].add_item(item)
        self.all_items[item_id] = item
        
        return item
    
    def _on_item_clicked(self, item_id: str):
        """处理导航项点击"""
        # 更新活动状态
        for item in self.all_items.values():
            item.set_active(item.item_id == item_id)
        
        self.current_item_id = item_id
        self.navigation_changed.emit(item_id)
        
        logger.debug(f"导航项点击: {item_id}")
    
    def set_current_item(self, item_id: str):
        """设置当前导航项"""
        if item_id in self.all_items:
            self._on_item_clicked(item_id)
    
    def toggle_sidebar(self):
        """切换侧边栏展开/折叠状态"""
        self.is_expanded = not self.is_expanded
        
        # 更新header的边距以适应折叠状态下的logo居中
        header_layout = self.logo_label.parentWidget().layout()  # 获取header的布局
        if header_layout:
            header_layout.setContentsMargins(
                16 if self.is_expanded else (self.COLLAPSED_WIDTH - 26) // 2, 
                0, 
                10 if self.is_expanded else 0, 
                0
            )

        target_width = self.EXPANDED_WIDTH if self.is_expanded else self.COLLAPSED_WIDTH
        self.width_animation = QPropertyAnimation(self, b"minimumWidth")
        self.width_animation.setDuration(200)  # 稍微加快动画
        self.width_animation.setEasingCurve(QEasingCurve.InOutQuart)  # 更平滑的动画曲线
        self.width_animation.setStartValue(self.width())
        self.width_animation.setEndValue(target_width)
        self.width_animation.finished.connect(self._update_layout_post_animation)
        self.width_animation.start()
        
        # 立即更新标题可见性，避免延迟
        self.sidebar_title.setVisible(self.is_expanded)
        self.expanded_changed.emit(self.is_expanded)
        logger.debug(f"侧边栏{'展开' if self.is_expanded else '折叠'}")
    
    def _update_layout_post_animation(self):
        """动画结束后更新依赖于展开状态的布局元素"""
        for group in self.groups.values():
            group.set_expanded(self.is_expanded)
        self.settings_btn.set_expanded(self.is_expanded)
        
        # 更新页脚布局以适应展开/折叠状态
        footer_layout = self.settings_btn.parentWidget().layout()
        if footer_layout:
            footer_layout.setContentsMargins(
                8 if self.is_expanded else 4, 
                4, 
                8 if self.is_expanded else 4, 
                4
            )
    
    def paintEvent(self, event):
        """绘制事件 - 添加渐变背景和微妙的纹理"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建垂直渐变背景
        gradient = QLinearGradient(0, 0, 0, self.height())
        sidebar_bg_color = QColor(THEME_COLORS["dark_bg_sidebar"])
        gradient.setColorAt(0, sidebar_bg_color.lighter(105))  # 顶部稍亮
        gradient.setColorAt(1, sidebar_bg_color)  # 底部保持原色
        
        painter.fillRect(self.rect(), gradient)
        
        # 绘制细微的网格纹理
        texture_color = QColor(255, 255, 255, 3)  # 极其微妙的白色
        painter.setPen(QPen(texture_color, 0.5))
        
        # 只在展开状态下绘制纹理，以提高性能
        if self.is_expanded:
            grid_size = 15
            for x in range(0, self.width(), grid_size):
                painter.drawLine(x, 0, x, self.height())
            for y in range(0, self.height(), grid_size):
                painter.drawLine(0, y, self.width(), y) 