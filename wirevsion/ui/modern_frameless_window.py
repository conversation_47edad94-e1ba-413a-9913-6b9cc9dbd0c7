#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化无边框窗口实现

提供完整的无边框窗口功能，包括：
- 自定义标题栏
- 窗口拖动和调整大小
- 最小化、最大化、关闭按钮
- 阴影效果
- 圆角边框
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QGraphicsDropShadowEffect,
    QSizeGrip, QApplication
)
from PyQt6.QtCore import (
    Qt, QPoint, QRect, pyqtSignal, QPropertyAnimation,
    QEasingCurve, QTimer, QSize
)
from PyQt6.QtGui import (
    QPainter, QPainterPath, QColor, QFont, QIcon,
    QPixmap, QBrush, QPen, QLinearGradient, QMouseEvent
)
from typing import Optional
from loguru import logger
import sys


class ModernTitleBar(QWidget):
    """现代化自定义标题栏"""
    
    # 窗口控制信号
    minimize_clicked = pyqtSignal()
    maximize_clicked = pyqtSignal()
    close_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.is_maximized = False
        
        # 设置固定高度
        self.setFixedHeight(40)
        
        # 初始化UI
        self._setup_ui()
        
        # 设置样式
        self._setup_style()
        
        # 鼠标拖动相关
        self.mouse_pressed = False
        self.mouse_pos = QPoint()
        
        logger.debug("现代化标题栏初始化完成")
    
    def _setup_ui(self):
        """设置UI布局"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo和标题区域
        logo_title_widget = QWidget()
        logo_title_layout = QHBoxLayout(logo_title_widget)
        logo_title_layout.setContentsMargins(0, 0, 0, 0)
        logo_title_layout.setSpacing(12)
        
        # Logo
        self.logo_label = QLabel()
        self.logo_label.setFixedSize(24, 24)
        logo_title_layout.addWidget(self.logo_label)
        
        # 标题
        self.title_label = QLabel("WireVision")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        logo_title_layout.addWidget(self.title_label)
        
        layout.addWidget(logo_title_widget)
        
        # 弹性空间
        layout.addStretch()
        
        # 窗口控制按钮
        self._create_window_controls(layout)
    
    def _create_window_controls(self, layout):
        """创建窗口控制按钮"""
        # 按钮容器
        controls_widget = QWidget()
        controls_layout = QHBoxLayout(controls_widget)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(0)
        
        # 最小化按钮
        self.minimize_btn = self._create_control_button("─")
        self.minimize_btn.clicked.connect(self.minimize_clicked.emit)
        controls_layout.addWidget(self.minimize_btn)
        
        # 最大化/还原按钮
        self.maximize_btn = self._create_control_button("□")
        self.maximize_btn.clicked.connect(self._on_maximize_clicked)
        controls_layout.addWidget(self.maximize_btn)
        
        # 关闭按钮
        self.close_btn = self._create_control_button("✕")
        self.close_btn.setObjectName("closeButton")
        self.close_btn.clicked.connect(self.close_clicked.emit)
        controls_layout.addWidget(self.close_btn)
        
        layout.addWidget(controls_widget)
    
    def _create_control_button(self, text: str) -> QPushButton:
        """创建控制按钮"""
        btn = QPushButton(text)
        btn.setFixedSize(46, 40)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #ffffff;
                border: none;
                font-size: 16px;
                font-weight: 300;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.2);
            }
            QPushButton#closeButton:hover {
                background-color: #e81123;
            }
        """)
        return btn
    
    def _setup_style(self):
        """设置标题栏样式"""
        self.setStyleSheet("""
            ModernTitleBar {
                background-color: #1e1e1e;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
            }
        """)
    
    def _on_maximize_clicked(self):
        """处理最大化按钮点击"""
        self.is_maximized = not self.is_maximized
        self.maximize_btn.setText("❐" if self.is_maximized else "□")
        self.maximize_clicked.emit()
    
    def set_title(self, title: str):
        """设置标题"""
        self.title_label.setText(title)
    
    def set_logo(self, pixmap: QPixmap):
        """设置Logo"""
        scaled_pixmap = pixmap.scaled(24, 24, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.logo_label.setPixmap(scaled_pixmap)
    
    # 鼠标事件处理
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.mouse_pressed = True
            self.mouse_pos = event.globalPos() - self.parent_window.pos()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.mouse_pressed and event.buttons() == Qt.MouseButton.LeftButton:
            if self.parent_window:
                self.parent_window.move(event.globalPos() - self.mouse_pos)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        self.mouse_pressed = False
    
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self._on_maximize_clicked()


class ModernFramelessWindow(QMainWindow):
    """现代化无边框主窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置无边框
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.Window)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 窗口状态
        self.is_maximized = False
        self.normal_geometry = None
        
        # 调整大小相关
        self.resize_margin = 8
        self.resize_direction = None
        self.resize_start_pos = None
        self.resize_start_geometry = None
        
        # 初始化UI
        self._setup_ui()
        
        # 设置阴影效果
        self._setup_shadow()
        
        # 设置默认大小
        self.resize(1200, 800)
        
        logger.info("现代化无边框窗口初始化完成")
    
    def _setup_ui(self):
        """设置UI布局"""
        # 主容器
        self.main_container = QWidget()
        self.main_container.setObjectName("mainContainer")
        self.setCentralWidget(self.main_container)
        
        # 主布局
        main_layout = QVBoxLayout(self.main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 内容容器（带圆角）
        self.content_container = QWidget()
        self.content_container.setObjectName("contentContainer")
        
        content_layout = QVBoxLayout(self.content_container)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = ModernTitleBar(self)
        self.title_bar.minimize_clicked.connect(self.showMinimized)
        self.title_bar.maximize_clicked.connect(self._toggle_maximize)
        self.title_bar.close_clicked.connect(self.close)
        content_layout.addWidget(self.title_bar)
        
        # 内容区域
        self.content_widget = QWidget()
        self.content_widget.setObjectName("contentWidget")
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.addWidget(self.content_widget)
        
        main_layout.addWidget(self.content_container)
        
        # 设置样式
        self._setup_style()
    
    def _setup_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            #mainContainer {
                background-color: transparent;
            }
            
            #contentContainer {
                background-color: #2b2b2b;
                border-radius: 10px;
            }
            
            #contentWidget {
                background-color: #2b2b2b;
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
            }
        """)
    
    def _setup_shadow(self):
        """设置阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 0)
        self.content_container.setGraphicsEffect(shadow)
    
    def _toggle_maximize(self):
        """切换最大化状态"""
        if self.is_maximized:
            self._restore_window()
        else:
            self._maximize_window()
    
    def _maximize_window(self):
        """最大化窗口"""
        self.normal_geometry = self.geometry()
        self.is_maximized = True
        
        # 获取可用屏幕区域
        screen = QApplication.primaryScreen().availableGeometry(self)
        
        # 移除圆角和阴影
        self.content_container.setStyleSheet("""
            #contentContainer {
                background-color: #2b2b2b;
                border-radius: 0px;
            }
        """)
        self.content_widget.setStyleSheet("""
            #contentWidget {
                background-color: #2b2b2b;
                border-radius: 0px;
            }
        """)
        
        # 移除阴影
        self.content_container.setGraphicsEffect(None)
        
        # 设置最大化几何
        self.setGeometry(screen)
        
        # 更新标题栏按钮
        self.title_bar.is_maximized = True
        self.title_bar.maximize_btn.setText("❐")
    
    def _restore_window(self):
        """还原窗口"""
        self.is_maximized = False
        
        # 恢复圆角
        self.content_container.setStyleSheet("""
            #contentContainer {
                background-color: #2b2b2b;
                border-radius: 10px;
            }
        """)
        self.content_widget.setStyleSheet("""
            #contentWidget {
                background-color: #2b2b2b;
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
            }
        """)
        
        # 恢复阴影
        self._setup_shadow()
        
        # 恢复几何
        if self.normal_geometry:
            self.setGeometry(self.normal_geometry)
        
        # 更新标题栏按钮
        self.title_bar.is_maximized = False
        self.title_bar.maximize_btn.setText("□")
    
    def set_content_widget(self, widget: QWidget):
        """设置内容组件"""
        # 清除现有内容
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 添加新内容
        self.content_layout.addWidget(widget)
    
    def set_title(self, title: str):
        """设置窗口标题"""
        self.title_bar.set_title(title)
        self.setWindowTitle(title)
    
    def set_logo(self, pixmap: QPixmap):
        """设置窗口Logo"""
        self.title_bar.set_logo(pixmap)
    
    # 调整大小功能
    def _get_resize_direction(self, pos: QPoint) -> Optional[str]:
        """获取调整大小的方向"""
        if self.is_maximized:
            return None
        
        rect = self.rect()
        x, y = pos.x(), pos.y()
        
        # 边缘检测
        directions = []
        
        if x <= self.resize_margin:
            directions.append("left")
        elif x >= rect.width() - self.resize_margin:
            directions.append("right")
        
        if y <= self.resize_margin:
            directions.append("top")
        elif y >= rect.height() - self.resize_margin:
            directions.append("bottom")
        
        return "-".join(directions) if directions else None
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.resize_direction = self._get_resize_direction(event.pos())
            if self.resize_direction:
                self.resize_start_pos = event.globalPos()
                self.resize_start_geometry = self.geometry()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if not event.buttons() == Qt.MouseButton.LeftButton:
            # 更新鼠标光标
            direction = self._get_resize_direction(event.pos())
            if direction:
                cursor_map = {
                    "left": Qt.SizeHorCursor,
                    "right": Qt.SizeHorCursor,
                    "top": Qt.SizeVerCursor,
                    "bottom": Qt.SizeVerCursor,
                    "left-top": Qt.SizeFDiagCursor,
                    "right-bottom": Qt.SizeFDiagCursor,
                    "right-top": Qt.SizeBDiagCursor,
                    "left-bottom": Qt.SizeBDiagCursor,
                }
                self.setCursor(cursor_map.get(direction, Qt.ArrowCursor))
            else:
                self.setCursor(Qt.ArrowCursor)
        
        elif self.resize_direction and self.resize_start_pos:
            # 执行调整大小
            delta = event.globalPos() - self.resize_start_pos
            new_geometry = QRect(self.resize_start_geometry)
            
            if "left" in self.resize_direction:
                new_geometry.setLeft(new_geometry.left() + delta.x())
            if "right" in self.resize_direction:
                new_geometry.setRight(new_geometry.right() + delta.x())
            if "top" in self.resize_direction:
                new_geometry.setTop(new_geometry.top() + delta.y())
            if "bottom" in self.resize_direction:
                new_geometry.setBottom(new_geometry.bottom() + delta.y())
            
            # 限制最小尺寸
            if new_geometry.width() >= 800 and new_geometry.height() >= 600:
                self.setGeometry(new_geometry)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        self.resize_direction = None
        self.resize_start_pos = None
        self.resize_start_geometry = None
        self.setCursor(Qt.ArrowCursor) 