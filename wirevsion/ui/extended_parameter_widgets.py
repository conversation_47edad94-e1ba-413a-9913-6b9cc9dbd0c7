"""
扩展参数控件 - 支持更多高级参数类型

功能：
- 文件选择器
- ROI（感兴趣区域）选择器
- 曲线编辑器
- 颜色范围选择器
- 多选列表
- 键值对编辑器
"""

from typing import Dict, Any, List, Optional, Tuple
import json

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QFileDialog, QListWidget, QListWidgetItem, QTableWidget,
    QTableWidgetItem, QHeaderView, QMenu, QGraphicsView,
    QGraphicsScene, QGraphicsRectItem, QGraphicsEllipseItem,
    QGraphicsItem, QSlider, QSpinBox, QCheckBox, QGroupBox,
    QDialogButtonBox, QDialog, QGridLayout, QTextEdit
)
from PyQt6.QtCore import (
    Qt, QRectF, QPointF, pyqtSignal, QSizeF, 
    QPropertyAnimation, QEasingCurve
)
from PyQt6.QtGui import (
    QPen, QBrush, QColor, QPainter, QPixmap,
    QImage, QPolygonF, QPainterPath
)

from loguru import logger


class FileSelectWidget(QWidget):
    """文件选择控件"""
    
    file_selected = pyqtSignal(str)
    
    def __init__(self, file_filter="All Files (*.*)", 
                 select_directory=False, parent=None):
        super().__init__(parent)
        
        self.file_filter = file_filter
        self.select_directory = select_directory
        self.file_path = ""
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 文件路径显示
        self.path_label = QLabel("未选择")
        self.path_label.setStyleSheet("""
            QLabel {
                border: 1px solid #dee2e6;
                padding: 5px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.path_label, 1)
        
        # 浏览按钮
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_file)
        layout.addWidget(self.browse_btn)
    
    def browse_file(self):
        """浏览文件"""
        if self.select_directory:
            path = QFileDialog.getExistingDirectory(
                self, "选择目录", self.file_path
            )
        else:
            path, _ = QFileDialog.getOpenFileName(
                self, "选择文件", self.file_path, self.file_filter
            )
        
        if path:
            self.set_path(path)
    
    def set_path(self, path: str):
        """设置路径"""
        self.file_path = path
        
        # 显示文件名或最后的目录名
        if path:
            from pathlib import Path
            display_name = Path(path).name
            if not display_name:  # 根目录
                display_name = path
            self.path_label.setText(display_name)
            self.path_label.setToolTip(path)
        else:
            self.path_label.setText("未选择")
            self.path_label.setToolTip("")
        
        self.file_selected.emit(path)
    
    def get_path(self) -> str:
        """获取路径"""
        return self.file_path


class ROISelectorWidget(QWidget):
    """ROI选择控件"""
    
    roi_changed = pyqtSignal(dict)  # {x, y, width, height}
    
    def __init__(self, image_size=(640, 480), parent=None):
        super().__init__(parent)
        
        self.image_size = image_size
        self.roi = {"x": 0, "y": 0, "width": 100, "height": 100}
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 图形视图
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        self.graphics_view.setFixedHeight(200)
        
        # 设置场景大小
        self.graphics_scene.setSceneRect(0, 0, self.image_size[0], self.image_size[1])
        
        # 背景
        self.graphics_scene.addRect(
            0, 0, self.image_size[0], self.image_size[1],
            QPen(Qt.black), QBrush(QColor(240, 240, 240))
        )
        
        # ROI矩形
        self.roi_rect = ResizableRectItem(QRectF(0, 0, 100, 100))
        self.roi_rect.setPen(QPen(QColor(0, 120, 215), 2))
        self.roi_rect.setBrush(QBrush(QColor(0, 120, 215, 50)))
        self.roi_rect.roi_changed.connect(self._on_roi_changed)
        self.graphics_scene.addItem(self.roi_rect)
        
        layout.addWidget(self.graphics_view)
        
        # 数值输入
        input_layout = QGridLayout()
        
        # X坐标
        input_layout.addWidget(QLabel("X:"), 0, 0)
        self.x_spin = QSpinBox()
        self.x_spin.setRange(0, self.image_size[0])
        self.x_spin.valueChanged.connect(self._update_from_spinbox)
        input_layout.addWidget(self.x_spin, 0, 1)
        
        # Y坐标
        input_layout.addWidget(QLabel("Y:"), 0, 2)
        self.y_spin = QSpinBox()
        self.y_spin.setRange(0, self.image_size[1])
        self.y_spin.valueChanged.connect(self._update_from_spinbox)
        input_layout.addWidget(self.y_spin, 0, 3)
        
        # 宽度
        input_layout.addWidget(QLabel("宽度:"), 1, 0)
        self.width_spin = QSpinBox()
        self.width_spin.setRange(1, self.image_size[0])
        self.width_spin.setValue(100)
        self.width_spin.valueChanged.connect(self._update_from_spinbox)
        input_layout.addWidget(self.width_spin, 1, 1)
        
        # 高度
        input_layout.addWidget(QLabel("高度:"), 1, 2)
        self.height_spin = QSpinBox()
        self.height_spin.setRange(1, self.image_size[1])
        self.height_spin.setValue(100)
        self.height_spin.valueChanged.connect(self._update_from_spinbox)
        input_layout.addWidget(self.height_spin, 1, 3)
        
        layout.addLayout(input_layout)
        
        # 预设按钮
        preset_layout = QHBoxLayout()
        
        # 全选
        full_btn = QPushButton("全选")
        full_btn.clicked.connect(self._select_full)
        preset_layout.addWidget(full_btn)
        
        # 中心
        center_btn = QPushButton("中心区域")
        center_btn.clicked.connect(self._select_center)
        preset_layout.addWidget(center_btn)
        
        # 清除
        clear_btn = QPushButton("清除")
        clear_btn.clicked.connect(self._clear_roi)
        preset_layout.addWidget(clear_btn)
        
        layout.addLayout(preset_layout)
    
    def _on_roi_changed(self, rect: QRectF):
        """ROI改变事件"""
        self.roi = {
            "x": int(rect.x()),
            "y": int(rect.y()),
            "width": int(rect.width()),
            "height": int(rect.height())
        }
        
        # 更新数值框
        self.x_spin.blockSignals(True)
        self.y_spin.blockSignals(True)
        self.width_spin.blockSignals(True)
        self.height_spin.blockSignals(True)
        
        self.x_spin.setValue(self.roi["x"])
        self.y_spin.setValue(self.roi["y"])
        self.width_spin.setValue(self.roi["width"])
        self.height_spin.setValue(self.roi["height"])
        
        self.x_spin.blockSignals(False)
        self.y_spin.blockSignals(False)
        self.width_spin.blockSignals(False)
        self.height_spin.blockSignals(False)
        
        self.roi_changed.emit(self.roi)
    
    def _update_from_spinbox(self):
        """从数值框更新"""
        rect = QRectF(
            self.x_spin.value(),
            self.y_spin.value(),
            self.width_spin.value(),
            self.height_spin.value()
        )
        
        self.roi_rect.setRect(rect)
        
        self.roi = {
            "x": self.x_spin.value(),
            "y": self.y_spin.value(),
            "width": self.width_spin.value(),
            "height": self.height_spin.value()
        }
        
        self.roi_changed.emit(self.roi)
    
    def _select_full(self):
        """选择全部区域"""
        self.roi_rect.setRect(QRectF(0, 0, self.image_size[0], self.image_size[1]))
    
    def _select_center(self):
        """选择中心区域"""
        w = self.image_size[0] // 2
        h = self.image_size[1] // 2
        x = self.image_size[0] // 4
        y = self.image_size[1] // 4
        self.roi_rect.setRect(QRectF(x, y, w, h))
    
    def _clear_roi(self):
        """清除ROI"""
        self.roi_rect.setRect(QRectF(0, 0, 0, 0))
    
    def set_roi(self, roi: Dict[str, int]):
        """设置ROI"""
        self.roi = roi
        rect = QRectF(roi["x"], roi["y"], roi["width"], roi["height"])
        self.roi_rect.setRect(rect)
    
    def get_roi(self) -> Dict[str, int]:
        """获取ROI"""
        return self.roi


class ResizableRectItem(QGraphicsRectItem):
    """可调整大小的矩形项"""
    
    roi_changed = pyqtSignal(QRectF)
    
    def __init__(self, rect: QRectF):
        super().__init__(rect)
        
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.ItemSendsGeometryChanges, True)
        self.setCursor(Qt.SizeAllCursor)
        
        # 调整手柄
        self.handles = []
        self.handle_size = 8
        self.dragging_handle = None
        
        self._create_handles()
    
    def _create_handles(self):
        """创建调整手柄"""
        # 8个手柄：4角 + 4边中点
        positions = [
            "top_left", "top", "top_right",
            "left", "right",
            "bottom_left", "bottom", "bottom_right"
        ]
        
        for pos in positions:
            handle = QGraphicsRectItem(0, 0, self.handle_size, self.handle_size, self)
            handle.setPen(QPen(Qt.NoPen))
            handle.setBrush(QBrush(QColor(0, 120, 215)))
            handle.setData(0, pos)  # 存储位置标识
            self.handles.append(handle)
        
        self._update_handles()
    
    def _update_handles(self):
        """更新手柄位置"""
        rect = self.rect()
        hs = self.handle_size / 2
        
        positions = {
            "top_left": (rect.left() - hs, rect.top() - hs),
            "top": (rect.center().x() - hs, rect.top() - hs),
            "top_right": (rect.right() - hs, rect.top() - hs),
            "left": (rect.left() - hs, rect.center().y() - hs),
            "right": (rect.right() - hs, rect.center().y() - hs),
            "bottom_left": (rect.left() - hs, rect.bottom() - hs),
            "bottom": (rect.center().x() - hs, rect.bottom() - hs),
            "bottom_right": (rect.right() - hs, rect.bottom() - hs)
        }
        
        for handle in self.handles:
            pos_name = handle.data(0)
            if pos_name in positions:
                handle.setPos(*positions[pos_name])
    
    def setRect(self, rect: QRectF):
        """设置矩形"""
        super().setRect(rect)
        self._update_handles()
        self.roi_changed.emit(rect)
    
    def itemChange(self, change, value):
        """项目改变事件"""
        if change == QGraphicsItem.ItemPositionHasChanged:
            self.roi_changed.emit(self.rect())
        
        return super().itemChange(change, value)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        # 检查是否点击了手柄
        for handle in self.handles:
            if handle.contains(handle.mapFromScene(event.scenePos())):
                self.dragging_handle = handle
                return
        
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging_handle:
            # 调整矩形大小
            pos = event.scenePos()
            rect = self.rect()
            handle_type = self.dragging_handle.data(0)
            
            if "left" in handle_type:
                rect.setLeft(pos.x())
            elif "right" in handle_type:
                rect.setRight(pos.x())
            
            if "top" in handle_type:
                rect.setTop(pos.y())
            elif "bottom" in handle_type:
                rect.setBottom(pos.y())
            
            # 确保矩形有效
            if rect.width() > 0 and rect.height() > 0:
                self.setRect(rect)
        else:
            super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.dragging_handle = None
        super().mouseReleaseEvent(event)


class CurveEditorWidget(QWidget):
    """曲线编辑器控件"""
    
    curve_changed = pyqtSignal(list)  # [(x, y), ...]
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.curve_points = [(0, 0), (0.5, 0.5), (1, 1)]  # 默认线性曲线
        self.selected_point = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 图形视图
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        self.graphics_view.setFixedHeight(200)
        
        # 设置场景
        self.graphics_scene.setSceneRect(-10, -10, 220, 220)
        
        # 绘制网格
        self._draw_grid()
        
        # 绘制曲线
        self.curve_path = self.graphics_scene.addPath(
            QPainterPath(),
            QPen(QColor(0, 120, 215), 2)
        )
        
        # 控制点
        self.control_points = []
        self._create_control_points()
        
        layout.addWidget(self.graphics_view)
        
        # 预设按钮
        preset_layout = QHBoxLayout()
        
        presets = [
            ("线性", [(0, 0), (1, 1)]),
            ("S曲线", [(0, 0), (0.25, 0.1), (0.75, 0.9), (1, 1)]),
            ("反S曲线", [(0, 0), (0.25, 0.9), (0.75, 0.1), (1, 1)]),
            ("平方", [(0, 0), (0.5, 0.25), (1, 1)]),
            ("平方根", [(0, 0), (0.5, 0.7), (1, 1)])
        ]
        
        for name, points in presets:
            btn = QPushButton(name)
            btn.clicked.connect(lambda checked, p=points: self.set_curve(p))
            preset_layout.addWidget(btn)
        
        layout.addLayout(preset_layout)
    
    def _draw_grid(self):
        """绘制网格"""
        # 边框
        self.graphics_scene.addRect(
            0, 0, 200, 200,
            QPen(Qt.black, 1),
            QBrush(Qt.white)
        )
        
        # 网格线
        grid_pen = QPen(QColor(200, 200, 200), 1, Qt.DotLine)
        
        for i in range(1, 10):
            x = i * 20
            # 垂直线
            self.graphics_scene.addLine(x, 0, x, 200, grid_pen)
            # 水平线
            self.graphics_scene.addLine(0, x, 200, x, grid_pen)
    
    def _create_control_points(self):
        """创建控制点"""
        for x, y in self.curve_points:
            point = DraggablePointItem(x * 200, (1 - y) * 200, 6)
            point.setPen(QPen(Qt.black, 1))
            point.setBrush(QBrush(QColor(0, 120, 215)))
            point.point_moved.connect(self._on_point_moved)
            self.graphics_scene.addItem(point)
            self.control_points.append(point)
        
        self._update_curve()
    
    def _on_point_moved(self, point_item):
        """控制点移动事件"""
        # 更新曲线点
        for i, point in enumerate(self.control_points):
            if point == point_item:
                x = point.pos().x() / 200
                y = 1 - (point.pos().y() / 200)
                
                # 限制范围
                x = max(0, min(1, x))
                y = max(0, min(1, y))
                
                # 如果是第一个或最后一个点，固定X坐标
                if i == 0:
                    x = 0
                elif i == len(self.control_points) - 1:
                    x = 1
                
                self.curve_points[i] = (x, y)
                
                # 重新定位点
                point.setPos(x * 200, (1 - y) * 200)
                break
        
        # 按X坐标排序
        self.curve_points.sort(key=lambda p: p[0])
        
        self._update_curve()
        self.curve_changed.emit(self.curve_points)
    
    def _update_curve(self):
        """更新曲线"""
        if len(self.curve_points) < 2:
            return
        
        # 创建路径
        path = QPainterPath()
        
        # 移动到第一个点
        first_point = self.curve_points[0]
        path.moveTo(first_point[0] * 200, (1 - first_point[1]) * 200)
        
        # 使用贝塞尔曲线连接点
        for i in range(1, len(self.curve_points)):
            p1 = self.curve_points[i - 1]
            p2 = self.curve_points[i]
            
            # 控制点
            cx1 = p1[0] * 200 + (p2[0] - p1[0]) * 200 * 0.5
            cy1 = (1 - p1[1]) * 200
            cx2 = p2[0] * 200 - (p2[0] - p1[0]) * 200 * 0.5
            cy2 = (1 - p2[1]) * 200
            
            path.cubicTo(
                cx1, cy1,
                cx2, cy2,
                p2[0] * 200, (1 - p2[1]) * 200
            )
        
        self.curve_path.setPath(path)
    
    def set_curve(self, points: List[Tuple[float, float]]):
        """设置曲线"""
        self.curve_points = points
        
        # 清除旧的控制点
        for point in self.control_points:
            self.graphics_scene.removeItem(point)
        self.control_points.clear()
        
        # 创建新的控制点
        self._create_control_points()
        
        self.curve_changed.emit(self.curve_points)
    
    def get_curve(self) -> List[Tuple[float, float]]:
        """获取曲线"""
        return self.curve_points


class DraggablePointItem(QGraphicsEllipseItem):
    """可拖动的点项"""
    
    point_moved = pyqtSignal(object)  # self
    
    def __init__(self, x, y, radius):
        super().__init__(-radius, -radius, 2 * radius, 2 * radius)
        self.setPos(x, y)
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemSendsGeometryChanges, True)
        self.setCursor(Qt.SizeAllCursor)
    
    def itemChange(self, change, value):
        """项目改变事件"""
        if change == QGraphicsItem.ItemPositionChange:
            # 限制在场景内
            new_pos = value
            scene_rect = self.scene().sceneRect()
            
            if new_pos.x() < 0:
                new_pos.setX(0)
            elif new_pos.x() > 200:
                new_pos.setX(200)
            
            if new_pos.y() < 0:
                new_pos.setY(0)
            elif new_pos.y() > 200:
                new_pos.setY(200)
            
            return new_pos
        
        elif change == QGraphicsItem.ItemPositionHasChanged:
            self.point_moved.emit(self)
        
        return super().itemChange(change, value)


class ColorRangeWidget(QWidget):
    """颜色范围选择控件"""
    
    range_changed = pyqtSignal(dict)  # {lower: [h,s,v], upper: [h,s,v]}
    
    def __init__(self, color_space="HSV", parent=None):
        super().__init__(parent)
        
        self.color_space = color_space
        self.color_range = {
            "lower": [0, 0, 0],
            "upper": [180, 255, 255]
        }
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 颜色空间选择
        space_layout = QHBoxLayout()
        space_layout.addWidget(QLabel("颜色空间:"))
        
        self.space_buttons = []
        for space in ["HSV", "RGB", "LAB"]:
            btn = QPushButton(space)
            btn.setCheckable(True)
            btn.setChecked(space == self.color_space)
            btn.clicked.connect(lambda checked, s=space: self._change_color_space(s))
            space_layout.addWidget(btn)
            self.space_buttons.append(btn)
        
        layout.addLayout(space_layout)
        
        # 创建滑块
        self.sliders = {}
        
        # 根据颜色空间创建滑块
        self._create_sliders()
        
        # 预览
        preview_layout = QHBoxLayout()
        
        self.lower_preview = QLabel()
        self.lower_preview.setFixedSize(60, 60)
        self.lower_preview.setStyleSheet("border: 1px solid black;")
        preview_layout.addWidget(QLabel("下限:"))
        preview_layout.addWidget(self.lower_preview)
        
        preview_layout.addStretch()
        
        self.upper_preview = QLabel()
        self.upper_preview.setFixedSize(60, 60)
        self.upper_preview.setStyleSheet("border: 1px solid black;")
        preview_layout.addWidget(QLabel("上限:"))
        preview_layout.addWidget(self.upper_preview)
        
        layout.addLayout(preview_layout)
        
        self._update_preview()
    
    def _create_sliders(self):
        """创建滑块"""
        # 清除旧的滑块
        for slider_group in self.sliders.values():
            for widget in slider_group.values():
                widget.setParent(None)
        self.sliders.clear()
        
        # 获取通道信息
        if self.color_space == "HSV":
            channels = [
                ("H", 0, 180),
                ("S", 0, 255),
                ("V", 0, 255)
            ]
        elif self.color_space == "RGB":
            channels = [
                ("R", 0, 255),
                ("G", 0, 255),
                ("B", 0, 255)
            ]
        else:  # LAB
            channels = [
                ("L", 0, 255),
                ("A", 0, 255),
                ("B", 0, 255)
            ]
        
        # 创建下限和上限滑块
        for bound in ["lower", "upper"]:
            group = QGroupBox(f"{bound}限")
            group_layout = QVBoxLayout()
            
            self.sliders[bound] = {}
            
            for i, (channel, min_val, max_val) in enumerate(channels):
                channel_layout = QHBoxLayout()
                
                # 标签
                label = QLabel(f"{channel}:")
                label.setFixedWidth(20)
                channel_layout.addWidget(label)
                
                # 滑块
                slider = QSlider(Qt.Orientation.Horizontal)
                slider.setRange(min_val, max_val)
                slider.setValue(self.color_range[bound][i])
                slider.valueChanged.connect(self._on_slider_changed)
                channel_layout.addWidget(slider)
                
                # 数值标签
                value_label = QLabel(str(self.color_range[bound][i]))
                value_label.setFixedWidth(40)
                slider.valueChanged.connect(
                    lambda v, label=value_label: label.setText(str(v))
                )
                channel_layout.addWidget(value_label)
                
                self.sliders[bound][channel] = {
                    "slider": slider,
                    "label": value_label
                }
                
                group_layout.addLayout(channel_layout)
            
            group.setLayout(group_layout)
            self.layout().addWidget(group)
    
    def _change_color_space(self, space: str):
        """改变颜色空间"""
        self.color_space = space
        
        # 更新按钮状态
        for btn in self.space_buttons:
            btn.setChecked(btn.text() == space)
        
        # 重新创建滑块
        self._create_sliders()
        
        # 更新预览
        self._update_preview()
    
    def _on_slider_changed(self):
        """滑块改变事件"""
        # 更新颜色范围
        for bound in ["lower", "upper"]:
            values = []
            for channel in ["H", "S", "V"] if self.color_space == "HSV" else \
                         ["R", "G", "B"] if self.color_space == "RGB" else \
                         ["L", "A", "B"]:
                if channel in self.sliders[bound]:
                    slider = self.sliders[bound][channel]["slider"]
                    values.append(slider.value())
            
            self.color_range[bound] = values
        
        self._update_preview()
        self.range_changed.emit(self.color_range)
    
    def _update_preview(self):
        """更新预览"""
        # 将颜色值转换为RGB用于显示
        for bound, preview in [("lower", self.lower_preview), 
                              ("upper", self.upper_preview)]:
            
            values = self.color_range[bound]
            
            if self.color_space == "HSV":
                # HSV转RGB
                import colorsys
                h = values[0] / 180.0  # OpenCV的H范围是0-180
                s = values[1] / 255.0
                v = values[2] / 255.0
                r, g, b = colorsys.hsv_to_rgb(h, s, v)
                rgb = (int(r * 255), int(g * 255), int(b * 255))
            elif self.color_space == "RGB":
                rgb = tuple(values)
            else:  # LAB
                # 简化处理，仅用于预览
                rgb = tuple(values)
            
            # 创建颜色pixmap
            pixmap = QPixmap(60, 60)
            pixmap.fill(QColor(*rgb))
            preview.setPixmap(pixmap)
    
    def get_range(self) -> Dict[str, List[int]]:
        """获取颜色范围"""
        return self.color_range
    
    def set_range(self, color_range: Dict[str, List[int]]):
        """设置颜色范围"""
        self.color_range = color_range
        
        # 更新滑块
        for bound in ["lower", "upper"]:
            for i, channel in enumerate(["H", "S", "V"] if self.color_space == "HSV" else \
                                      ["R", "G", "B"] if self.color_space == "RGB" else \
                                      ["L", "A", "B"]):
                if channel in self.sliders[bound]:
                    slider = self.sliders[bound][channel]["slider"]
                    slider.setValue(color_range[bound][i])


class KeyValueEditorWidget(QWidget):
    """键值对编辑器控件"""
    
    data_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.data = {}
        
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        add_btn = QPushButton("添加")
        add_btn.clicked.connect(self._add_item)
        toolbar_layout.addWidget(add_btn)
        
        remove_btn = QPushButton("删除")
        remove_btn.clicked.connect(self._remove_item)
        toolbar_layout.addWidget(remove_btn)
        
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self._clear_all)
        toolbar_layout.addWidget(clear_btn)
        
        toolbar_layout.addStretch()
        
        import_btn = QPushButton("导入")
        import_btn.clicked.connect(self._import_data)
        toolbar_layout.addWidget(import_btn)
        
        export_btn = QPushButton("导出")
        export_btn.clicked.connect(self._export_data)
        toolbar_layout.addWidget(export_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["键", "值"])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.itemChanged.connect(self._on_item_changed)
        
        layout.addWidget(self.table)
    
    def _add_item(self):
        """添加项"""
        row = self.table.rowCount()
        self.table.insertRow(row)
        
        # 设置默认值
        self.table.setItem(row, 0, QTableWidgetItem(f"key_{row}"))
        self.table.setItem(row, 1, QTableWidgetItem("value"))
        
        # 选中新行
        self.table.selectRow(row)
    
    def _remove_item(self):
        """删除项"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            self.table.removeRow(current_row)
            self._update_data()
    
    def _clear_all(self):
        """清空所有"""
        self.table.setRowCount(0)
        self.data.clear()
        self.data_changed.emit(self.data)
    
    def _import_data(self):
        """导入数据"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入JSON", "", "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, dict):
                    self.set_data(data)
                else:
                    logger.error("导入的数据不是字典类型")
                    
            except Exception as e:
                logger.error(f"导入失败: {e}")
    
    def _export_data(self):
        """导出数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出JSON", "data.json", "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.data, f, ensure_ascii=False, indent=2)
                    
            except Exception as e:
                logger.error(f"导出失败: {e}")
    
    def _on_item_changed(self, item):
        """项改变事件"""
        self._update_data()
    
    def _update_data(self):
        """更新数据"""
        self.data.clear()
        
        for row in range(self.table.rowCount()):
            key_item = self.table.item(row, 0)
            value_item = self.table.item(row, 1)
            
            if key_item and value_item:
                key = key_item.text()
                value = value_item.text()
                
                # 尝试解析值
                try:
                    # 尝试作为JSON解析
                    value = json.loads(value)
                except:
                    # 保持为字符串
                    pass
                
                self.data[key] = value
        
        self.data_changed.emit(self.data)
    
    def set_data(self, data: Dict[str, Any]):
        """设置数据"""
        self.data = data
        
        # 清空表格
        self.table.setRowCount(0)
        
        # 添加数据
        for key, value in data.items():
            row = self.table.rowCount()
            self.table.insertRow(row)
            
            self.table.setItem(row, 0, QTableWidgetItem(str(key)))
            
            # 值转换为字符串
            if isinstance(value, str):
                value_str = value
            else:
                value_str = json.dumps(value, ensure_ascii=False)
            
            self.table.setItem(row, 1, QTableWidgetItem(value_str))
    
    def get_data(self) -> Dict[str, Any]:
        """获取数据"""
        return self.data 