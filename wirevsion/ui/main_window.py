#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVsion主窗口模块

包含应用程序的主窗口类，负责集成和管理各种UI组件
"""

import os
import sys
from loguru import logger

from PyQt5.QtWidgets import (
    QMainWindow, QDockWidget, QAction, QMenu, QToolBar, 
    QStatusBar, QMessageBox, QFileDialog, QWidget, QVBoxLayout
)
from PyQt5.QtCore import Qt, QSize, QSettings
from PyQt5.QtGui import QIcon

from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor

class MainWindow(QMainWindow):
    """应用程序主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 设置窗口基本属性
        self.setWindowTitle("WireVsion - 图像处理系统")
        self.setMinimumSize(1200, 800)
        
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建布局
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建工作流编辑器
        self.workflow_editor = ModernWorkflowEditor()
        self.main_layout.addWidget(self.workflow_editor)
        
        # 创建UI组件
        self._create_actions()
        self._create_menus()
        self._create_toolbars()
        self._create_statusbar()
        
        # 从设置恢复窗口状态
        self._restore_settings()
        
        logger.info("主窗口初始化完成")
    
    def _create_actions(self):
        """创建动作"""
        # 文件菜单动作
        self.new_action = QAction("新建工程", self)
        self.new_action.setShortcut("Ctrl+N")
        self.new_action.setStatusTip("创建新的工程")
        self.new_action.triggered.connect(self._on_new)
        
        self.open_action = QAction("打开工程", self)
        self.open_action.setShortcut("Ctrl+O")
        self.open_action.setStatusTip("打开现有工程")
        self.open_action.triggered.connect(self._on_open)
        
        self.save_action = QAction("保存工程", self)
        self.save_action.setShortcut("Ctrl+S")
        self.save_action.setStatusTip("保存当前工程")
        self.save_action.triggered.connect(self._on_save)
        
        self.exit_action = QAction("退出", self)
        self.exit_action.setShortcut("Ctrl+Q")
        self.exit_action.setStatusTip("退出应用程序")
        self.exit_action.triggered.connect(self.close)
        
        # 视图菜单动作
        self.fullscreen_action = QAction("全屏模式", self)
        self.fullscreen_action.setShortcut("F11")
        self.fullscreen_action.setStatusTip("切换全屏模式")
        self.fullscreen_action.setCheckable(True)
        self.fullscreen_action.triggered.connect(self._toggle_fullscreen)
        
        # 工具菜单动作
        self.settings_action = QAction("设置", self)
        self.settings_action.setStatusTip("打开应用设置")
        self.settings_action.triggered.connect(self._on_settings)
        
        # 帮助菜单动作
        self.about_action = QAction("关于", self)
        self.about_action.setStatusTip("显示关于信息")
        self.about_action.triggered.connect(self._on_about)
    
    def _create_menus(self):
        """创建菜单"""
        # 主菜单
        self.menu_bar = self.menuBar()
        
        # 文件菜单
        self.file_menu = self.menu_bar.addMenu("文件")
        self.file_menu.addAction(self.new_action)
        self.file_menu.addAction(self.open_action)
        self.file_menu.addAction(self.save_action)
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.exit_action)
        
        # 视图菜单
        self.view_menu = self.menu_bar.addMenu("视图")
        self.view_menu.addAction(self.fullscreen_action)
        
        # 工具菜单
        self.tools_menu = self.menu_bar.addMenu("工具")
        self.tools_menu.addAction(self.settings_action)
        
        # 帮助菜单
        self.help_menu = self.menu_bar.addMenu("帮助")
        self.help_menu.addAction(self.about_action)
    
    def _create_toolbars(self):
        """创建工具栏"""
        # 主工具栏
        self.main_toolbar = QToolBar("主工具栏", self)
        self.main_toolbar.setMovable(False)
        self.addToolBar(self.main_toolbar)
        
        # 添加工具栏按钮
        self.main_toolbar.addAction(self.new_action)
        self.main_toolbar.addAction(self.open_action)
        self.main_toolbar.addAction(self.save_action)
    
    def _create_statusbar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar(self)
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def _on_new(self):
        """新建工程处理"""
        # 询问是否保存当前工程
        if self._confirm_save():
            # 清空工作流编辑器
            self.workflow_editor.clear_workflow()
            self.status_bar.showMessage("已创建新工程")
    
    def _on_open(self):
        """打开工程处理"""
        # 询问是否保存当前工程
        if self._confirm_save():
            # 打开文件对话框
            filepath, _ = QFileDialog.getOpenFileName(
                self, 
                "打开工程",
                "",
                "WireVsion工程文件 (*.wvp);;所有文件 (*)"
            )
            
            if filepath:
                # TODO: 实现工程加载
                self.status_bar.showMessage(f"已打开工程: {os.path.basename(filepath)}")
    
    def _on_save(self):
        """保存工程处理"""
        # TODO: 实现工程保存
        self.status_bar.showMessage("工程已保存")
    
    def _on_settings(self):
        """打开设置对话框"""
        # TODO: 实现设置对话框
        self.status_bar.showMessage("设置对话框")
    
    def _on_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 WireVsion",
            "WireVsion 图像处理系统\n\n"
            "版本 1.0.0\n"
            "© 2025 WireVsion Team"
        )
    
    def _toggle_fullscreen(self, checked):
        """切换全屏模式"""
        if checked:
            self.showFullScreen()
        else:
            self.showNormal()
    
    def _confirm_save(self):
        """确认是否保存当前工程
        
        Returns:
            bool: 用户是否取消了操作
        """
        # TODO: 实现检查工程是否已修改
        return True
    
    def _restore_settings(self):
        """从设置恢复窗口状态"""
        settings = QSettings("WireVsion", "App")
        
        # 恢复窗口几何形状
        geometry = settings.value("mainwindow/geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        # 恢复窗口状态
        state = settings.value("mainwindow/state")
        if state:
            self.restoreState(state)
    
    def closeEvent(self, event):
        """关闭窗口事件处理"""
        # 询问是否保存当前工程
        if not self._confirm_save():
            event.ignore()
            return
        
        # 保存窗口状态
        settings = QSettings("WireVsion", "App")
        settings.setValue("mainwindow/geometry", self.saveGeometry())
        settings.setValue("mainwindow/state", self.saveState())
        
        # 关闭相机资源
        app = QApplication.instance()
        camera_manager = app.property("camera_manager")
        if camera_manager:
            camera_manager.release()
        
        # 接受关闭事件
        event.accept() 