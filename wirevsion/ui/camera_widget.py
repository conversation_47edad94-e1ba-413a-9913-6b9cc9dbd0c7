#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机配置组件
提供相机连接、参数设置和图像捕获功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QLabel, QComboBox, QSpinBox,
    QCheckBox, QGroupBox, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer

import cv2
import numpy as np
from typing import Optional, Dict, Any
from loguru import logger

from wirevsion.camera.camera_manager import CameraManager


class CameraWidget(QWidget):
    """
    相机配置组件
    
    提供相机连接和图像捕获功能
    """
    
    # 信号定义
    image_captured = pyqtSignal(np.ndarray)  # 图像捕获信号
    status_message = pyqtSignal(str)  # 状态消息信号
    
    def __init__(self, camera_manager: CameraManager):
        """
        初始化相机配置组件
        
        Args:
            camera_manager: 相机管理器
        """
        super().__init__()
        
        self.camera_manager = camera_manager
        self.is_connected = False
        self.is_capturing = False  # 连续拍照状态
        
        # 连续拍照定时器
        self.capture_timer = QTimer()
        self.capture_timer.timeout.connect(self._continuous_capture)
        
        # 拍照统计
        self.capture_count = 0
        self.successful_captures = 0
        
        # 创建UI
        self._create_ui()
        
        # 连接信号
        self._connect_signals()
        
        # 刷新相机列表
        self._refresh_cameras()
        
        logger.info("相机配置组件初始化完成")
    
    def _create_ui(self):
        """创建UI组件"""
        layout = QVBoxLayout(self)
        
        # 相机选择组
        camera_group = QGroupBox("相机选择")
        camera_layout = QFormLayout(camera_group)
        
        # 相机列表
        camera_select_layout = QHBoxLayout()
        
        self.camera_combo = QComboBox()
        self.camera_combo.setMinimumWidth(200)
        camera_select_layout.addWidget(self.camera_combo)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self._refresh_cameras)
        camera_select_layout.addWidget(self.refresh_btn)
        
        camera_layout.addRow("相机设备:", camera_select_layout)
        
        # 连接控制
        self.connect_btn = QPushButton("连接相机")
        self.connect_btn.clicked.connect(self._toggle_connection)
        camera_layout.addRow("", self.connect_btn)
        
        layout.addWidget(camera_group)
        
        # 相机参数组
        params_group = QGroupBox("相机参数")
        params_layout = QFormLayout(params_group)
        
        # 像素格式
        self.format_combo = QComboBox()
        self.format_combo.addItems(["MONO8", "MONO12", "RGB8", "BGR8"])
        params_layout.addRow("像素格式:", self.format_combo)
        
        # 取图间隔
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(100, 5000)  # 100ms到5秒
        self.interval_spin.setValue(1000)  # 默认1秒
        self.interval_spin.setSuffix(" ms")
        params_layout.addRow("取图间隔:", self.interval_spin)
        
        # 自动取图
        self.auto_capture_check = QCheckBox("启用")
        self.auto_capture_check.setChecked(True)
        params_layout.addRow("自动取图:", self.auto_capture_check)
        
        # 方案存图
        self.save_images_check = QCheckBox("启用")
        params_layout.addRow("方案存图:", self.save_images_check)
        
        layout.addWidget(params_group)
        
        # 图像捕获控制
        capture_group = QGroupBox("图像捕获")
        capture_layout = QVBoxLayout(capture_group)
        
        btn_layout = QHBoxLayout()
        
        self.capture_btn = QPushButton("单次捕获")
        self.capture_btn.clicked.connect(self.capture_image)
        self.capture_btn.setEnabled(False)
        btn_layout.addWidget(self.capture_btn)
        
        self.start_btn = QPushButton("开始连续")
        self.start_btn.clicked.connect(self._start_continuous_capture)
        self.start_btn.setEnabled(False)
        btn_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止连续")
        self.stop_btn.clicked.connect(self._stop_continuous_capture)
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_btn)
        
        capture_layout.addLayout(btn_layout)
        
        # 拍照统计显示
        self.stats_label = QLabel("拍照统计: 总计0次, 成功0次")
        capture_layout.addWidget(self.stats_label)
        
        layout.addWidget(capture_group)
        
        # 状态显示
        self.status_label = QLabel("状态: 未连接")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
    
    def _connect_signals(self):
        """连接信号"""
        # 连接取图间隔变化信号
        self.interval_spin.valueChanged.connect(self._update_capture_interval)
    
    def _update_capture_interval(self, value):
        """更新拍照间隔"""
        if self.is_capturing:
            # 如果正在连续拍照，更新定时器间隔
            self.capture_timer.setInterval(value)
            logger.info(f"更新拍照间隔为: {value}ms")
    
    def _refresh_cameras(self):
        """刷新相机列表"""
        try:
            self.camera_combo.clear()
            
            cameras = self.camera_manager.get_available_cameras()
            
            for camera in cameras:
                camera_text = f"{camera['name']} (ID: {camera['id']})"
                self.camera_combo.addItem(camera_text, camera['id'])
            
            if cameras:
                self.status_message.emit(f"找到 {len(cameras)} 个可用相机")
            else:
                self.status_message.emit("未找到可用相机")
                
            logger.info(f"刷新相机列表: 找到 {len(cameras)} 个相机")
            
        except Exception as e:
            error_msg = f"刷新相机列表失败: {str(e)}"
            logger.error(error_msg)
            self.status_message.emit(error_msg)
    
    def _toggle_connection(self):
        """切换相机连接状态"""
        if self.is_connected:
            self._disconnect_camera()
        else:
            self._connect_camera()
    
    def _connect_camera(self):
        """连接相机"""
        if self.camera_combo.count() == 0:
            QMessageBox.warning(self, "警告", "没有可用的相机设备")
            return
        
        try:
            camera_id = self.camera_combo.currentData()
            if camera_id is None:
                QMessageBox.warning(self, "警告", "请选择相机设备")
                return
            
            # 连接相机
            if self.camera_manager.open_camera(camera_id):
                self.is_connected = True
                
                # 更新UI状态
                self.connect_btn.setText("断开相机")
                self.capture_btn.setEnabled(True)
                self.start_btn.setEnabled(True)
                self.camera_combo.setEnabled(False)
                self.status_label.setText("状态: 已连接")
                
                # 设置像素格式
                pixel_format = self.format_combo.currentText()
                self.camera_manager.set_format(pixel_format)
                
                # 重置统计
                self.capture_count = 0
                self.successful_captures = 0
                self._update_stats()
                
                self.status_message.emit("相机连接成功")
                logger.info(f"相机连接成功: {camera_id}")
            else:
                QMessageBox.critical(self, "错误", "相机连接失败")
                
        except Exception as e:
            error_msg = f"连接相机失败: {str(e)}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def _disconnect_camera(self):
        """断开相机"""
        try:
            # 停止连续拍照
            if self.is_capturing:
                self._stop_continuous_capture()
            
            self.camera_manager.close_camera()
            self.is_connected = False
            
            # 更新UI状态
            self.connect_btn.setText("连接相机")
            self.capture_btn.setEnabled(False)
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)
            self.camera_combo.setEnabled(True)
            self.status_label.setText("状态: 未连接")
            
            self.status_message.emit("相机已断开")
            logger.info("相机已断开")
            
        except Exception as e:
            error_msg = f"断开相机失败: {str(e)}"
            logger.error(error_msg)
    
    def _start_continuous_capture(self):
        """开始连续拍照"""
        if not self.is_connected:
            QMessageBox.warning(self, "警告", "请先连接相机")
            return
        
        self.is_capturing = True
        
        # 设置定时器间隔
        interval = self.interval_spin.value()
        self.capture_timer.setInterval(interval)
        self.capture_timer.start()
        
        # 更新UI状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.capture_btn.setEnabled(False)
        self.status_label.setText(f"状态: 连续拍照中 (间隔: {interval}ms)")
        
        # 重置统计
        self.capture_count = 0
        self.successful_captures = 0
        self._update_stats()
        
        self.status_message.emit(f"开始连续拍照，间隔: {interval}ms")
        logger.info(f"开始连续拍照，间隔: {interval}ms")
    
    def _stop_continuous_capture(self):
        """停止连续拍照"""
        if not self.is_capturing:
            return
        
        self.is_capturing = False
        self.capture_timer.stop()
        
        # 更新UI状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.capture_btn.setEnabled(True)
        self.status_label.setText("状态: 已连接")
        
        success_rate = (self.successful_captures / max(self.capture_count, 1)) * 100
        self.status_message.emit(f"连续拍照已停止，成功率: {success_rate:.1f}%")
        logger.info(f"连续拍照已停止，总计{self.capture_count}次，成功{self.successful_captures}次")
    
    def _continuous_capture(self):
        """连续拍照执行函数"""
        if not self.is_connected or not self.is_capturing:
            return
        
        try:
            success = self._capture_single_frame()
            self.capture_count += 1
            
            if success:
                self.successful_captures += 1
            
            self._update_stats()
            
        except Exception as e:
            logger.error(f"连续拍照出错: {e}")
            # 出错时不停止连续拍照，只记录错误
    
    def _update_stats(self):
        """更新拍照统计显示"""
        success_rate = (self.successful_captures / max(self.capture_count, 1)) * 100
        self.stats_label.setText(f"拍照统计: 总计{self.capture_count}次, 成功{self.successful_captures}次 ({success_rate:.1f}%)")
    
    def capture_image(self):
        """捕获单帧图像"""
        if not self.is_connected:
            QMessageBox.warning(self, "警告", "请先连接相机")
            return
        
        try:
            success = self._capture_single_frame()
            
            # 更新统计（仅单次拍照时）
            if not self.is_capturing:
                self.capture_count += 1
                if success:
                    self.successful_captures += 1
                self._update_stats()
                
        except Exception as e:
            error_msg = f"捕获图像时出错: {str(e)}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def _capture_single_frame(self) -> bool:
        """捕获单帧图像的核心逻辑"""
        try:
            logger.debug("开始捕获图像...")
            
            # 优化的相机预热：只在需要时进行
            if self.capture_count == 0 or not self.is_capturing:
                # 单次拍照或连续拍照的第一张，进行预热
                for i in range(3):  # 减少预热帧数，提高效率
                    success, warmup_frame = self.camera_manager.capture()
                    if success and warmup_frame is not None:
                        mean_val = np.mean(warmup_frame)
                        if mean_val > 10:  # 如果找到正常图像，提前结束预热
                            logger.debug(f"预热完成，在第{i+1}帧找到正常图像")
                            break
            
            # 捕获最终图像
            success, image = self.camera_manager.capture()
            
            if success and image is not None:
                # 快速图像质量检查
                mean_val = np.mean(image)
                if mean_val < 5:  # 降低阈值，只检查严重问题
                    logger.warning(f"图像较暗，均值={mean_val:.1f}")
                    if not self.is_capturing:  # 只在单次拍照时显示警告
                        self.status_message.emit(f"图像较暗，请检查光线条件")
                else:
                    if not self.is_capturing:  # 只在单次拍照时显示成功消息
                        self.status_message.emit("图像捕获成功")
                
                # 发送图像信号
                self.image_captured.emit(image)
                logger.debug(f"图像捕获成功: {image.shape}")
                return True
                
            else:
                if not self.is_capturing:  # 只在单次拍照时显示错误
                    error_msg = "图像捕获失败"
                    logger.error(error_msg)
                    QMessageBox.warning(self, "错误", error_msg)
                return False
                
        except Exception as e:
            logger.error(f"捕获图像异常: {e}")
            return False
    
    def get_camera_config(self) -> Dict[str, Any]:
        """
        获取相机配置
        
        Returns:
            Dict[str, Any]: 相机配置
        """
        return {
            'camera_id': self.camera_combo.currentData(),
            'pixel_format': self.format_combo.currentText(),
            'interval': self.interval_spin.value(),
            'auto_capture': self.auto_capture_check.isChecked(),
            'save_images': self.save_images_check.isChecked()
        }
    
    def set_camera_config(self, config: Dict[str, Any]):
        """
        设置相机配置
        
        Args:
            config: 相机配置
        """
        try:
            # 设置相机选择
            camera_id = config.get('camera_id')
            if camera_id is not None:
                for i in range(self.camera_combo.count()):
                    if self.camera_combo.itemData(i) == camera_id:
                        self.camera_combo.setCurrentIndex(i)
                        break
            
            # 设置像素格式
            pixel_format = config.get('pixel_format', 'MONO8')
            index = self.format_combo.findText(pixel_format)
            if index >= 0:
                self.format_combo.setCurrentIndex(index)
            
            # 设置间隔
            interval = config.get('interval', 0)
            self.interval_spin.setValue(interval)
            
            # 设置自动捕获
            auto_capture = config.get('auto_capture', True)
            self.auto_capture_check.setChecked(auto_capture)
            
            # 设置保存图像
            save_images = config.get('save_images', False)
            self.save_images_check.setChecked(save_images)
            
            logger.info("相机配置已设置")
            
        except Exception as e:
            logger.error(f"设置相机配置失败: {e}")
    
    def is_camera_connected(self) -> bool:
        """
        检查相机是否连接
        
        Returns:
            bool: 是否连接
        """
        return self.is_connected 