"""
WireVision UI模块 - 现代化版本

只导出明确使用的现代化UI组件和核心类。
其他旧的或待确认的模块已被注释掉，以确保新UI的稳定运行。
"""

# 导入Python基础模块 (如果需要)
from typing import List, Dict, Any, Optional # 示例

# 现代化UI核心组件
from .modern_frameless_window import ModernFramelessWindow, ModernTitleBar
from .modern_sidebar import ModernSidebar, NavigationItem, NavigationGroup
from .modern_components import (
    ModernCard, ModernButton, ModernInput, ModernSwitch, 
    ModernProgressBar, ModernDialog, ModernStatusBar, THEME_COLORS
)
from .modern_table import ModernTable, ModernTableHeader, ModernTablePagination
from .modern_camera_widget import ModernCameraWidget, CameraPreviewWidget, CameraControlPanel
from .modern_workflow_editor import ModernWorkflowEditor, ModernFlowNode, ModernFlowConnection, ModernWorkflowCanvas, ModernNodeLibrary
from .modern_algorithm_library import ModernAlgorithmLibrary, AlgorithmCard, AlgorithmDetailPanel, AlgorithmInfo
from .modern_connection_renderer import OptimizedConnection, ConnectionRenderer, ConnectionType, ConnectionStyle, PathCache

# 主应用程序 (如果需要从其他地方导入主应用类)
# from .modern_main_application import ModernMainApplication 

# 明确定义公开的接口
__all__ = [
    # 现代化UI组件
    'ModernFramelessWindow', 'ModernTitleBar',
    'ModernSidebar', 'NavigationItem', 'NavigationGroup',
    'ModernCard', 'ModernButton', 'ModernInput', 'ModernSwitch',
    'ModernProgressBar', 'ModernDialog', 'ModernStatusBar', 'THEME_COLORS',
    'ModernTable', 'ModernTableHeader', 'ModernTablePagination',
    'ModernCameraWidget', 'CameraPreviewWidget', 'CameraControlPanel',
    'ModernWorkflowEditor', 'ModernFlowNode', 'ModernFlowConnection', 'ModernWorkflowCanvas', 'ModernNodeLibrary',
    'ModernAlgorithmLibrary', 'AlgorithmCard', 'AlgorithmDetailPanel', 'AlgorithmInfo',
    'OptimizedConnection', 'ConnectionRenderer', 'ConnectionType', 'ConnectionStyle', 'PathCache',
    
    # 'ModernMainApplication', # 通常主应用类不需要在此处导出，它会作为程序的入口点
]

# 可以在这里添加一些UI相关的辅助函数或常量（如果需要）

# logger (如果UI模块需要自己的日志记录)
# from loguru import logger
# logger.add("logs/wirevision_ui.log", rotation="10 MB")
