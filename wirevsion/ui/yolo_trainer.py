"""
YOLO训练器模块
用于YOLO模型的训练、验证和数据集管理
"""

import os
import json
import shutil
import time
import yaml
from typing import Dict, Any, List, Tuple, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QThread
import cv2
import numpy as np
from loguru import logger


class YOLOTrainer(QObject):
    """YOLO训练器"""
    
    # 信号定义
    trainingStarted = pyqtSignal()
    trainingCompleted = pyqtSignal(dict)
    trainingFailed = pyqtSignal(str)
    trainingProgress = pyqtSignal(dict)  # epoch, loss, metrics等
    validationCompleted = pyqtSignal(dict)
    datasetPrepared = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.model = None
        self.dataset_path = None
        self.training_thread = None
        self.is_training = False
        self.training_config = {}
    
    def prepare_dataset(self, images_path: str, labels_path: str, 
                       dataset_name: str, split_ratio: Dict[str, float] = None) -> bool:
        """准备数据集
        
        Args:
            images_path: 图像文件夹路径
            labels_path: 标注文件夹路径
            dataset_name: 数据集名称
            split_ratio: 数据集划分比例 {"train": 0.7, "val": 0.2, "test": 0.1}
            
        Returns:
            是否准备成功
        """
        try:
            if split_ratio is None:
                split_ratio = {"train": 0.7, "val": 0.2, "test": 0.1}
            
            # 创建数据集目录结构
            dataset_root = os.path.join("datasets", dataset_name)
            os.makedirs(dataset_root, exist_ok=True)
            
            # 创建子目录
            for split in ["train", "val", "test"]:
                os.makedirs(os.path.join(dataset_root, "images", split), exist_ok=True)
                os.makedirs(os.path.join(dataset_root, "labels", split), exist_ok=True)
            
            # 获取所有图像文件
            image_files = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                image_files.extend([f for f in os.listdir(images_path) 
                                  if f.lower().endswith(ext)])
            
            # 随机打乱
            np.random.shuffle(image_files)
            
            # 计算划分点
            total = len(image_files)
            train_count = int(total * split_ratio["train"])
            val_count = int(total * split_ratio["val"])
            
            # 划分数据集
            train_files = image_files[:train_count]
            val_files = image_files[train_count:train_count + val_count]
            test_files = image_files[train_count + val_count:]
            
            # 复制文件到对应目录
            splits = {
                "train": train_files,
                "val": val_files,
                "test": test_files
            }
            
            for split_name, files in splits.items():
                for filename in files:
                    # 复制图像
                    src_image = os.path.join(images_path, filename)
                    dst_image = os.path.join(dataset_root, "images", split_name, filename)
                    shutil.copy2(src_image, dst_image)
                    
                    # 复制标注（如果存在）
                    label_name = os.path.splitext(filename)[0] + ".txt"
                    src_label = os.path.join(labels_path, label_name)
                    if os.path.exists(src_label):
                        dst_label = os.path.join(dataset_root, "labels", split_name, label_name)
                        shutil.copy2(src_label, dst_label)
            
            # 创建数据集配置文件
            dataset_yaml = os.path.join(dataset_root, "dataset.yaml")
            self._create_dataset_yaml(dataset_yaml, dataset_root)
            
            self.dataset_path = dataset_root
            self.datasetPrepared.emit(dataset_root)
            
            logger.info(f"数据集准备完成: {dataset_name}")
            logger.info(f"训练集: {len(train_files)}, 验证集: {len(val_files)}, 测试集: {len(test_files)}")
            
            return True
            
        except Exception as e:
            logger.error(f"准备数据集失败: {e}")
            return False
    
    def _create_dataset_yaml(self, yaml_path: str, dataset_root: str):
        """创建数据集配置文件"""
        # 分析标注文件获取类别信息
        classes = self._analyze_labels(dataset_root)
        
        config = {
            "path": os.path.abspath(dataset_root),
            "train": "images/train",
            "val": "images/val",
            "test": "images/test",
            "nc": len(classes),  # 类别数量
            "names": classes  # 类别名称
        }
        
        with open(yaml_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
    
    def _analyze_labels(self, dataset_root: str) -> List[str]:
        """分析标注文件获取类别信息"""
        classes = set()
        labels_dir = os.path.join(dataset_root, "labels", "train")
        
        if os.path.exists(labels_dir):
            for label_file in os.listdir(labels_dir):
                if label_file.endswith('.txt'):
                    file_path = os.path.join(labels_dir, label_file)
                    with open(file_path, 'r') as f:
                        for line in f:
                            parts = line.strip().split()
                            if parts:
                                class_id = int(parts[0])
                                classes.add(class_id)
        
        # 如果没有找到类别，使用默认类别
        if not classes:
            return ["object"]
        
        # 生成类别名称
        return [f"class_{i}" for i in sorted(classes)]
    
    def train(self, model_path: str, dataset_path: str, **kwargs) -> bool:
        """开始训练
        
        Args:
            model_path: 预训练模型路径
            dataset_path: 数据集路径
            **kwargs: 训练参数
                - epochs: 训练轮数
                - imgsz: 输入图像尺寸
                - batch: 批量大小
                - device: 训练设备
                - lr0: 初始学习率
                - optimizer: 优化器
                - save_period: 保存周期
                - patience: 早停耐心值
                
        Returns:
            是否成功开始训练
        """
        if self.is_training:
            logger.warning("训练已在进行中")
            return False
        
        try:
            # 检查ultralytics是否安装
            try:
                from ultralytics import YOLO
            except ImportError:
                error_msg = "未安装ultralytics库，请先安装: pip install ultralytics"
                logger.error(error_msg)
                self.trainingFailed.emit(error_msg)
                return False
            
            # 加载模型
            self.model = YOLO(model_path)
            
            # 准备训练配置
            self.training_config = {
                "data": os.path.join(dataset_path, "dataset.yaml"),
                "epochs": kwargs.get("epochs", 100),
                "imgsz": kwargs.get("imgsz", 640),
                "batch": kwargs.get("batch", 16),
                "device": kwargs.get("device", "cpu"),
                "lr0": kwargs.get("lr0", 0.01),
                "optimizer": kwargs.get("optimizer", "SGD"),
                "save_period": kwargs.get("save_period", 10),
                "patience": kwargs.get("patience", 50),
                "project": kwargs.get("project", "runs/train"),
                "name": kwargs.get("name", "exp"),
                "exist_ok": kwargs.get("exist_ok", True),
                "verbose": True
            }
            
            # 创建训练线程
            self.training_thread = TrainingThread(self.model, self.training_config)
            self.training_thread.progressUpdated.connect(self.trainingProgress.emit)
            self.training_thread.trainingCompleted.connect(self._on_training_completed)
            self.training_thread.errorOccurred.connect(self._on_training_failed)
            
            # 开始训练
            self.is_training = True
            self.training_thread.start()
            self.trainingStarted.emit()
            
            logger.info(f"开始训练: {model_path}")
            return True
            
        except Exception as e:
            error_msg = f"启动训练失败: {str(e)}"
            logger.error(error_msg)
            self.trainingFailed.emit(error_msg)
            return False
    
    def stop_training(self):
        """停止训练"""
        if self.training_thread and self.training_thread.isRunning():
            self.training_thread.stop()
            self.training_thread.wait()
            self.is_training = False
            logger.info("训练已停止")
    
    def validate(self, model_path: str, dataset_path: str, **kwargs) -> Dict[str, Any]:
        """验证模型
        
        Args:
            model_path: 模型路径
            dataset_path: 数据集路径
            **kwargs: 验证参数
            
        Returns:
            验证结果
        """
        try:
            from ultralytics import YOLO
            
            model = YOLO(model_path)
            
            # 验证参数
            val_config = {
                "data": os.path.join(dataset_path, "dataset.yaml"),
                "imgsz": kwargs.get("imgsz", 640),
                "batch": kwargs.get("batch", 16),
                "device": kwargs.get("device", "cpu"),
                "split": kwargs.get("split", "val")
            }
            
            # 执行验证
            results = model.val(**val_config)
            
            # 解析结果
            metrics = {
                "precision": float(results.box.mp),  # Mean precision
                "recall": float(results.box.mr),     # Mean recall
                "mAP50": float(results.box.map50),   # mAP@0.5
                "mAP50-95": float(results.box.map),  # mAP@0.5:0.95
                "fitness": float(results.fitness()),
                "speed": {
                    "preprocess": results.speed['preprocess'],
                    "inference": results.speed['inference'],
                    "postprocess": results.speed['postprocess']
                }
            }
            
            self.validationCompleted.emit(metrics)
            return metrics
            
        except Exception as e:
            error_msg = f"验证失败: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def export_model(self, model_path: str, format: str = "onnx", **kwargs) -> str:
        """导出模型
        
        Args:
            model_path: 模型路径
            format: 导出格式
            **kwargs: 导出参数
            
        Returns:
            导出路径
        """
        try:
            from ultralytics import YOLO
            
            model = YOLO(model_path)
            export_path = model.export(format=format, **kwargs)
            
            logger.info(f"模型导出成功: {export_path}")
            return export_path
            
        except Exception as e:
            logger.error(f"模型导出失败: {e}")
            raise
    
    def create_annotation_file(self, image_path: str, detections: List[Dict], 
                             output_path: str, format: str = "yolo"):
        """创建标注文件
        
        Args:
            image_path: 图像路径
            detections: 检测结果列表
            output_path: 输出路径
            format: 标注格式 (yolo, coco, voc)
        """
        if format == "yolo":
            self._create_yolo_annotation(image_path, detections, output_path)
        elif format == "coco":
            self._create_coco_annotation(image_path, detections, output_path)
        elif format == "voc":
            self._create_voc_annotation(image_path, detections, output_path)
        else:
            raise ValueError(f"不支持的标注格式: {format}")
    
    def _create_yolo_annotation(self, image_path: str, detections: List[Dict], 
                               output_path: str):
        """创建YOLO格式标注"""
        # 获取图像尺寸
        image = cv2.imread(image_path)
        height, width = image.shape[:2]
        
        lines = []
        for det in detections:
            class_id = det["class_id"]
            bbox = det["bbox_dict"]
            
            # 计算归一化坐标
            center_x = bbox["center_x"] / width
            center_y = bbox["center_y"] / height
            bbox_width = bbox["width"] / width
            bbox_height = bbox["height"] / height
            
            line = f"{class_id} {center_x:.6f} {center_y:.6f} {bbox_width:.6f} {bbox_height:.6f}"
            lines.append(line)
        
        # 写入文件
        with open(output_path, 'w') as f:
            f.write('\n'.join(lines))
    
    def _on_training_completed(self, results: Dict[str, Any]):
        """训练完成处理"""
        self.is_training = False
        self.trainingCompleted.emit(results)
        logger.info("训练完成")
    
    def _on_training_failed(self, error: str):
        """训练失败处理"""
        self.is_training = False
        self.trainingFailed.emit(error)
        logger.error(f"训练失败: {error}")


class TrainingThread(QThread):
    """训练线程"""
    
    progressUpdated = pyqtSignal(dict)
    trainingCompleted = pyqtSignal(dict)
    errorOccurred = pyqtSignal(str)
    
    def __init__(self, model, config: Dict[str, Any]):
        super().__init__()
        self.model = model
        self.config = config
        self._stop = False
    
    def run(self):
        """运行训练"""
        try:
            # 训练回调
            def on_epoch_end(trainer):
                if self._stop:
                    trainer.stop = True
                    return
                
                # 获取训练进度
                epoch = trainer.epoch + 1
                total_epochs = trainer.epochs
                
                # 获取损失值
                loss_dict = {}
                if hasattr(trainer, 'loss_items'):
                    loss_names = trainer.loss_names
                    loss_values = trainer.loss_items
                    for name, value in zip(loss_names, loss_values):
                        loss_dict[name] = float(value)
                
                # 获取指标
                metrics = {}
                if hasattr(trainer, 'metrics'):
                    metrics = {k: float(v) for k, v in trainer.metrics.items()}
                
                progress_data = {
                    "epoch": epoch,
                    "total_epochs": total_epochs,
                    "progress": epoch / total_epochs * 100,
                    "loss": loss_dict,
                    "metrics": metrics
                }
                
                self.progressUpdated.emit(progress_data)
            
            # 设置回调
            self.model.add_callback("on_epoch_end", on_epoch_end)
            
            # 开始训练
            results = self.model.train(**self.config)
            
            # 训练完成
            if not self._stop:
                result_data = {
                    "best_model": str(results.save_dir / "weights" / "best.pt"),
                    "last_model": str(results.save_dir / "weights" / "last.pt"),
                    "results_dir": str(results.save_dir),
                    "final_metrics": results.results_dict
                }
                self.trainingCompleted.emit(result_data)
            
        except Exception as e:
            self.errorOccurred.emit(str(e))
    
    def stop(self):
        """停止训练"""
        self._stop = True


class DatasetAnalyzer:
    """数据集分析器"""
    
    @staticmethod
    def analyze_dataset(dataset_path: str) -> Dict[str, Any]:
        """分析数据集
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            分析结果
        """
        result = {
            "total_images": 0,
            "total_labels": 0,
            "splits": {},
            "class_distribution": {},
            "image_sizes": [],
            "annotation_stats": {
                "total_boxes": 0,
                "avg_boxes_per_image": 0,
                "min_boxes": 0,
                "max_boxes": 0
            }
        }
        
        # 分析各个数据集划分
        for split in ["train", "val", "test"]:
            split_result = DatasetAnalyzer._analyze_split(dataset_path, split)
            result["splits"][split] = split_result
            result["total_images"] += split_result["num_images"]
            result["total_labels"] += split_result["num_labels"]
            
            # 合并类别分布
            for class_id, count in split_result["class_distribution"].items():
                if class_id not in result["class_distribution"]:
                    result["class_distribution"][class_id] = 0
                result["class_distribution"][class_id] += count
        
        return result
    
    @staticmethod
    def _analyze_split(dataset_path: str, split: str) -> Dict[str, Any]:
        """分析单个数据集划分"""
        images_dir = os.path.join(dataset_path, "images", split)
        labels_dir = os.path.join(dataset_path, "labels", split)
        
        result = {
            "num_images": 0,
            "num_labels": 0,
            "class_distribution": {},
            "boxes_per_image": []
        }
        
        if os.path.exists(images_dir):
            image_files = [f for f in os.listdir(images_dir) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            result["num_images"] = len(image_files)
        
        if os.path.exists(labels_dir):
            label_files = [f for f in os.listdir(labels_dir) if f.endswith('.txt')]
            result["num_labels"] = len(label_files)
            
            # 分析标注
            for label_file in label_files:
                file_path = os.path.join(labels_dir, label_file)
                boxes = 0
                
                with open(file_path, 'r') as f:
                    for line in f:
                        parts = line.strip().split()
                        if parts:
                            class_id = int(parts[0])
                            if class_id not in result["class_distribution"]:
                                result["class_distribution"][class_id] = 0
                            result["class_distribution"][class_id] += 1
                            boxes += 1
                
                result["boxes_per_image"].append(boxes)
        
        return result 