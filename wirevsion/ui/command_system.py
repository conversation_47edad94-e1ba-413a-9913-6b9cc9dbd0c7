"""
命令系统 - 实现撤销/重做功能

功能：
- 命令模式实现
- 撤销/重做栈管理
- 命令合并
- 历史记录
"""

from typing import Any, Optional, List, Dict
from abc import ABC, abstractmethod
from dataclasses import dataclass
import time
import copy

from PyQt5.QtCore import QObject, pyqtSignal

from loguru import logger


class Command(ABC):
    """抽象命令基类"""
    
    def __init__(self, description: str = ""):
        self.description = description
        self.timestamp = time.time()
        self.can_merge = False
        
    @abstractmethod
    def execute(self) -> bool:
        """执行命令"""
        pass
    
    @abstractmethod
    def undo(self) -> bool:
        """撤销命令"""
        pass
    
    def merge_with(self, other: 'Command') -> bool:
        """尝试与另一个命令合并"""
        return False
    
    def __str__(self):
        return self.description or self.__class__.__name__


# 具体命令实现

class CreateNodeCommand(Command):
    """创建节点命令"""
    
    def __init__(self, canvas, node_data: Dict[str, Any], position):
        super().__init__(f"创建节点: {node_data.get('algorithm', 'Unknown')}")
        self.canvas = canvas
        self.node_data = node_data
        self.position = position
        self.node_id = None
        self.node = None
    
    def execute(self) -> bool:
        """执行创建节点"""
        try:
            self.node = self.canvas.create_node(self.node_data, self.position)
            self.node_id = self.node.node_id
            return True
        except Exception as e:
            logger.error(f"创建节点失败: {e}")
            return False
    
    def undo(self) -> bool:
        """撤销创建节点"""
        try:
            if self.node_id and self.node_id in self.canvas.nodes:
                node = self.canvas.nodes[self.node_id]
                self.canvas.scene.removeItem(node)
                del self.canvas.nodes[self.node_id]
                return True
            return False
        except Exception as e:
            logger.error(f"撤销创建节点失败: {e}")
            return False


class DeleteNodeCommand(Command):
    """删除节点命令"""
    
    def __init__(self, canvas, node):
        super().__init__(f"删除节点: {node.metadata.get('display_name', 'Unknown')}")
        self.canvas = canvas
        self.node = node
        self.node_data = {
            "node_id": node.node_id,
            "category": node.category,
            "algorithm": node.algorithm,
            "metadata": copy.deepcopy(node.metadata),
            "parameters": copy.deepcopy(node.parameters),
            "position": {"x": node.x(), "y": node.y()}
        }
        self.connections = []  # 保存相关连接
    
    def execute(self) -> bool:
        """执行删除节点"""
        try:
            # 保存连接信息
            self._save_connections()
            
            # 删除节点
            self.canvas.scene.removeItem(self.node)
            del self.canvas.nodes[self.node.node_id]
            
            # 删除相关连接
            self._remove_connections()
            
            return True
        except Exception as e:
            logger.error(f"删除节点失败: {e}")
            return False
    
    def undo(self) -> bool:
        """撤销删除节点"""
        try:
            # 恢复节点
            from PyQt5.QtCore import QPointF
            position = QPointF(self.node_data["position"]["x"], 
                             self.node_data["position"]["y"])
            
            node = self.canvas.create_node(
                {
                    "category": self.node_data["category"],
                    "algorithm": self.node_data["algorithm"],
                    "metadata": self.node_data["metadata"]
                },
                position
            )
            
            # 恢复参数
            node.parameters = self.node_data["parameters"]
            
            # 恢复连接
            self._restore_connections()
            
            return True
        except Exception as e:
            logger.error(f"撤销删除节点失败: {e}")
            return False
    
    def _save_connections(self):
        """保存节点的连接信息"""
        # TODO: 实现连接保存逻辑
        pass
    
    def _remove_connections(self):
        """删除节点的连接"""
        # TODO: 实现连接删除逻辑
        pass
    
    def _restore_connections(self):
        """恢复节点的连接"""
        # TODO: 实现连接恢复逻辑
        pass


class MoveNodeCommand(Command):
    """移动节点命令"""
    
    def __init__(self, node, old_pos, new_pos):
        super().__init__(f"移动节点")
        self.node = node
        self.old_pos = old_pos
        self.new_pos = new_pos
        self.can_merge = True  # 可以合并连续的移动
    
    def execute(self) -> bool:
        """执行移动"""
        try:
            self.node.setPos(self.new_pos)
            return True
        except Exception as e:
            logger.error(f"移动节点失败: {e}")
            return False
    
    def undo(self) -> bool:
        """撤销移动"""
        try:
            self.node.setPos(self.old_pos)
            return True
        except Exception as e:
            logger.error(f"撤销移动失败: {e}")
            return False
    
    def merge_with(self, other: Command) -> bool:
        """合并移动命令"""
        if isinstance(other, MoveNodeCommand) and other.node == self.node:
            # 合并：保留原始位置，更新最终位置
            self.new_pos = other.new_pos
            self.description = f"移动节点 ({self.old_pos} -> {self.new_pos})"
            return True
        return False


class ChangeParametersCommand(Command):
    """修改参数命令"""
    
    def __init__(self, node, old_params: Dict[str, Any], new_params: Dict[str, Any]):
        super().__init__(f"修改参数: {node.metadata.get('display_name', 'Unknown')}")
        self.node = node
        self.old_params = copy.deepcopy(old_params)
        self.new_params = copy.deepcopy(new_params)
    
    def execute(self) -> bool:
        """执行修改参数"""
        try:
            self.node.parameters = copy.deepcopy(self.new_params)
            return True
        except Exception as e:
            logger.error(f"修改参数失败: {e}")
            return False
    
    def undo(self) -> bool:
        """撤销修改参数"""
        try:
            self.node.parameters = copy.deepcopy(self.old_params)
            return True
        except Exception as e:
            logger.error(f"撤销修改参数失败: {e}")
            return False


class CreateConnectionCommand(Command):
    """创建连接命令"""
    
    def __init__(self, canvas, start_point, end_point):
        super().__init__("创建连接")
        self.canvas = canvas
        self.start_point = start_point
        self.end_point = end_point
        self.connection = None
    
    def execute(self) -> bool:
        """执行创建连接"""
        try:
            # TODO: 实现连接创建逻辑
            return True
        except Exception as e:
            logger.error(f"创建连接失败: {e}")
            return False
    
    def undo(self) -> bool:
        """撤销创建连接"""
        try:
            # TODO: 实现连接删除逻辑
            return True
        except Exception as e:
            logger.error(f"撤销创建连接失败: {e}")
            return False


class CompositeCommand(Command):
    """复合命令 - 包含多个子命令"""
    
    def __init__(self, description: str, commands: List[Command]):
        super().__init__(description)
        self.commands = commands
    
    def execute(self) -> bool:
        """执行所有子命令"""
        for command in self.commands:
            if not command.execute():
                # 如果某个命令失败，撤销已执行的命令
                for i in range(self.commands.index(command) - 1, -1, -1):
                    self.commands[i].undo()
                return False
        return True
    
    def undo(self) -> bool:
        """撤销所有子命令（反向）"""
        for command in reversed(self.commands):
            if not command.undo():
                return False
        return True


class CommandHistory(QObject):
    """命令历史管理器"""
    
    # 信号
    can_undo_changed = pyqtSignal(bool)
    can_redo_changed = pyqtSignal(bool)
    history_changed = pyqtSignal()
    
    def __init__(self, max_history: int = 100):
        super().__init__()
        self.max_history = max_history
        self.undo_stack: List[Command] = []
        self.redo_stack: List[Command] = []
        
        # 当前状态
        self._is_executing = False
        self._last_saved_index = 0
    
    def execute_command(self, command: Command) -> bool:
        """执行命令并添加到历史"""
        if self._is_executing:
            return False
        
        self._is_executing = True
        
        try:
            # 尝试合并命令
            if (self.undo_stack and 
                command.can_merge and 
                self.undo_stack[-1].merge_with(command)):
                # 命令已合并，更新信号
                self.history_changed.emit()
                return True
            
            # 执行命令
            if command.execute():
                # 添加到撤销栈
                self.undo_stack.append(command)
                
                # 限制历史大小
                if len(self.undo_stack) > self.max_history:
                    self.undo_stack.pop(0)
                    if self._last_saved_index > 0:
                        self._last_saved_index -= 1
                
                # 清空重做栈
                self.redo_stack.clear()
                
                # 更新信号
                self._update_signals()
                
                logger.debug(f"执行命令: {command}")
                return True
            else:
                logger.error(f"命令执行失败: {command}")
                return False
                
        finally:
            self._is_executing = False
    
    def undo(self) -> bool:
        """撤销上一个命令"""
        if not self.can_undo() or self._is_executing:
            return False
        
        self._is_executing = True
        
        try:
            command = self.undo_stack.pop()
            
            if command.undo():
                # 添加到重做栈
                self.redo_stack.append(command)
                
                # 更新信号
                self._update_signals()
                
                logger.debug(f"撤销命令: {command}")
                return True
            else:
                # 撤销失败，恢复到撤销栈
                self.undo_stack.append(command)
                logger.error(f"撤销失败: {command}")
                return False
                
        finally:
            self._is_executing = False
    
    def redo(self) -> bool:
        """重做下一个命令"""
        if not self.can_redo() or self._is_executing:
            return False
        
        self._is_executing = True
        
        try:
            command = self.redo_stack.pop()
            
            if command.execute():
                # 添加回撤销栈
                self.undo_stack.append(command)
                
                # 更新信号
                self._update_signals()
                
                logger.debug(f"重做命令: {command}")
                return True
            else:
                # 重做失败，恢复到重做栈
                self.redo_stack.append(command)
                logger.error(f"重做失败: {command}")
                return False
                
        finally:
            self._is_executing = False
    
    def can_undo(self) -> bool:
        """是否可以撤销"""
        return len(self.undo_stack) > 0
    
    def can_redo(self) -> bool:
        """是否可以重做"""
        return len(self.redo_stack) > 0
    
    def get_undo_text(self) -> str:
        """获取撤销操作描述"""
        if self.can_undo():
            return f"撤销 {self.undo_stack[-1].description}"
        return "撤销"
    
    def get_redo_text(self) -> str:
        """获取重做操作描述"""
        if self.can_redo():
            return f"重做 {self.redo_stack[-1].description}"
        return "重做"
    
    def clear(self):
        """清空历史"""
        self.undo_stack.clear()
        self.redo_stack.clear()
        self._last_saved_index = 0
        self._update_signals()
    
    def mark_as_saved(self):
        """标记当前状态为已保存"""
        self._last_saved_index = len(self.undo_stack)
    
    def is_modified(self) -> bool:
        """检查是否有未保存的修改"""
        return len(self.undo_stack) != self._last_saved_index
    
    def get_history(self) -> List[str]:
        """获取历史记录描述"""
        history = []
        
        # 撤销栈中的命令
        for i, cmd in enumerate(self.undo_stack):
            prefix = "→ " if i == self._last_saved_index else "  "
            history.append(f"{prefix}{cmd.description}")
        
        # 当前位置标记
        if len(self.undo_stack) == self._last_saved_index:
            history.append("→ [当前位置]")
        else:
            history.append("  [当前位置]")
        
        # 重做栈中的命令（反向）
        for cmd in reversed(self.redo_stack):
            history.append(f"  {cmd.description} (可重做)")
        
        return history
    
    def _update_signals(self):
        """更新信号"""
        self.can_undo_changed.emit(self.can_undo())
        self.can_redo_changed.emit(self.can_redo())
        self.history_changed.emit()


# 命令工厂
class CommandFactory:
    """命令工厂 - 简化命令创建"""
    
    @staticmethod
    def create_node(canvas, node_data: Dict[str, Any], position) -> Command:
        """创建节点命令"""
        return CreateNodeCommand(canvas, node_data, position)
    
    @staticmethod
    def delete_nodes(canvas, nodes: List[Any]) -> Command:
        """删除多个节点的复合命令"""
        if len(nodes) == 1:
            return DeleteNodeCommand(canvas, nodes[0])
        
        commands = [DeleteNodeCommand(canvas, node) for node in nodes]
        return CompositeCommand(f"删除 {len(nodes)} 个节点", commands)
    
    @staticmethod
    def move_nodes(nodes_positions: List[tuple]) -> Command:
        """移动多个节点的复合命令"""
        if len(nodes_positions) == 1:
            node, old_pos, new_pos = nodes_positions[0]
            return MoveNodeCommand(node, old_pos, new_pos)
        
        commands = []
        for node, old_pos, new_pos in nodes_positions:
            commands.append(MoveNodeCommand(node, old_pos, new_pos))
        
        return CompositeCommand(f"移动 {len(nodes_positions)} 个节点", commands) 