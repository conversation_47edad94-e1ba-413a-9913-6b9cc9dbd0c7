#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI组件基类模块
提供通用的UI组件基础功能，减少代码重复
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Callable
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QGroupBox, QLabel, QPushButton, QMessageBox
)
from PyQt6.QtCore import QObject, pyqtSignal, QMetaObject
from PyQt6.QtGui import QPixmap
from loguru import logger
import numpy as np

from wirevsion.utils.image_utils import cv_to_qpixmap


class BaseWidget(QWidget):
    """
    UI组件基类
    提供通用的UI功能和样式设置
    """
    
    # 通用信号
    error_occurred = pyqtSignal(str)  # 错误信号
    status_changed = pyqtSignal(str)  # 状态变更信号
    
    def __init__(self, parent=None):
        """
        初始化基类组件
        
        Args:
            parent: 父窗口对象
        """
        super().__init__(parent)
        
        # 错误处理回调
        self.error_handlers: list[Callable[[str], None]] = []
        
        # 设置UI
        self._setup_ui()
        
        # 连接默认信号
        self._connect_default_signals()
    
    def _setup_ui(self):
        """
        设置UI组件 - 子类需要重写此方法
        """
        pass
    
    def _connect_default_signals(self):
        """连接默认信号处理"""
        self.error_occurred.connect(self._handle_error)
    
    def _handle_error(self, error_message: str):
        """
        处理错误信息
        
        Args:
            error_message: 错误消息
        """
        logger.error(f"组件错误 [{self.__class__.__name__}]: {error_message}")
        
        # 调用注册的错误处理器
        for handler in self.error_handlers:
            try:
                handler(error_message)
            except Exception as e:
                logger.error(f"错误处理器执行失败: {str(e)}")
    
    def add_error_handler(self, handler: Callable[[str], None]):
        """
        添加错误处理器
        
        Args:
            handler: 错误处理函数
        """
        self.error_handlers.append(handler)
    
    def create_group_box(self, title: str, layout_type: str = "vertical") -> QGroupBox:
        """
        创建分组框
        
        Args:
            title: 分组框标题
            layout_type: 布局类型 ("vertical", "horizontal", "form")
            
        Returns:
            QGroupBox: 创建的分组框
        """
        group_box = QGroupBox(title)
        
        if layout_type == "vertical":
            layout = QVBoxLayout()
        elif layout_type == "horizontal":
            layout = QHBoxLayout()
        elif layout_type == "form":
            layout = QFormLayout()
        else:
            layout = QVBoxLayout()
        
        group_box.setLayout(layout)
        return group_box
    
    def create_button(self, text: str, callback: Optional[Callable] = None, 
                     tooltip: str = "", enabled: bool = True) -> QPushButton:
        """
        创建按钮
        
        Args:
            text: 按钮文本
            callback: 点击回调函数
            tooltip: 工具提示
            enabled: 是否启用
            
        Returns:
            QPushButton: 创建的按钮
        """
        button = QPushButton(text)
        button.setEnabled(enabled)
        
        if tooltip:
            button.setToolTip(tooltip)
        
        if callback:
            button.clicked.connect(callback)
        
        return button
    
    def show_error_message(self, title: str, message: str):
        """
        显示错误消息对话框
        
        Args:
            title: 对话框标题
            message: 错误消息
        """
        QMessageBox.critical(self, title, message)
    
    def show_info_message(self, title: str, message: str):
        """
        显示信息消息对话框
        
        Args:
            title: 对话框标题
            message: 信息消息
        """
        QMessageBox.information(self, title, message)
    
    def show_warning_message(self, title: str, message: str):
        """
        显示警告消息对话框
        
        Args:
            title: 对话框标题
            message: 警告消息
        """
        QMessageBox.warning(self, title, message)
    
    def cleanup(self):
        """
        清理资源 - 子类可以重写此方法
        """
        # 断开信号连接
        try:
            self.error_occurred.disconnect()
            self.status_changed.disconnect()
        except TypeError:
            # 信号可能已经断开
            pass
        
        # 清空错误处理器
        self.error_handlers.clear()
        
        logger.debug(f"组件资源已清理: {self.__class__.__name__}")


class ImageDisplayMixin:
    """
    图像显示混入类
    提供图像显示相关的通用功能
    """
    
    def __init__(self):
        """初始化图像显示混入"""
        self.current_image: Optional[np.ndarray] = None
        self.original_pixmap: Optional[QPixmap] = None
    
    def set_current_image(self, image: np.ndarray):
        """
        设置当前图像
        
        Args:
            image: 图像数据
        """
        if image is None:
            self.current_image = None
            self.original_pixmap = None
            return
        
        try:
            self.current_image = image.copy()
            self.original_pixmap = cv_to_qpixmap(image)
            logger.debug(f"图像已设置，尺寸: {image.shape}")
        except Exception as e:
            logger.error(f"设置图像失败: {str(e)}")
            self.current_image = None
            self.original_pixmap = None
    
    def get_current_image(self) -> Optional[np.ndarray]:
        """
        获取当前图像
        
        Returns:
            Optional[np.ndarray]: 当前图像数据
        """
        return self.current_image.copy() if self.current_image is not None else None
    
    def clear_image(self):
        """清除当前图像"""
        self.current_image = None
        self.original_pixmap = None
    
    def has_image(self) -> bool:
        """
        检查是否有图像
        
        Returns:
            bool: 是否有图像
        """
        return self.current_image is not None
    
    def get_image_info(self) -> Dict[str, Any]:
        """
        获取图像信息
        
        Returns:
            Dict[str, Any]: 图像信息
        """
        if self.current_image is None:
            return {"has_image": False}
        
        height, width = self.current_image.shape[:2]
        channels = 1 if len(self.current_image.shape) < 3 else self.current_image.shape[2]
        
        return {
            "has_image": True,
            "width": width,
            "height": height,
            "channels": channels,
            "dtype": str(self.current_image.dtype),
            "size_mb": self.current_image.nbytes / 1024 / 1024
        }


class ConfigurableWidget(BaseWidget):
    """
    可配置的组件基类
    提供配置保存和加载功能
    """
    
    def __init__(self, parent=None):
        """
        初始化可配置组件
        
        Args:
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_data: Dict[str, Any] = {}
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取组件配置
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        return self.config_data.copy()
    
    def set_config(self, config: Dict[str, Any]):
        """
        设置组件配置
        
        Args:
            config: 配置数据
        """
        self.config_data = config.copy()
        self._apply_config()
    
    def _apply_config(self):
        """
        应用配置 - 子类需要重写此方法
        """
        pass
    
    def reset_config(self):
        """重置配置为默认值"""
        self.config_data.clear()
        self._apply_config()


class ValidatedWidget(BaseWidget):
    """
    带验证功能的组件基类
    提供输入验证功能
    """
    
    # 验证信号
    validation_changed = pyqtSignal(bool)  # 验证状态变更
    
    def __init__(self, parent=None):
        """
        初始化验证组件
        
        Args:
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.validators: list[Callable[[], tuple[bool, str]]] = []
        self.is_valid = True
    
    def add_validator(self, validator: Callable[[], tuple[bool, str]]):
        """
        添加验证器
        
        Args:
            validator: 验证函数，返回(是否有效, 错误消息)
        """
        self.validators.append(validator)
    
    def validate(self) -> tuple[bool, list[str]]:
        """
        执行验证
        
        Returns:
            tuple[bool, list[str]]: (是否全部有效, 错误消息列表)
        """
        errors = []
        all_valid = True
        
        for validator in self.validators:
            try:
                is_valid, error_msg = validator()
                if not is_valid:
                    all_valid = False
                    errors.append(error_msg)
            except Exception as e:
                all_valid = False
                errors.append(f"验证器执行失败: {str(e)}")
        
        # 更新验证状态
        if self.is_valid != all_valid:
            self.is_valid = all_valid
            self.validation_changed.emit(all_valid)
        
        return all_valid, errors
    
    def is_component_valid(self) -> bool:
        """
        检查组件是否有效
        
        Returns:
            bool: 是否有效
        """
        valid, _ = self.validate()
        return valid 