#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行模式界面模块
提供流程执行和结果显示功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QProgressBar
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
import numpy as np
from loguru import logger

from wirevsion.config.config_manager import ConfigManager


class RunModeWidget(QWidget):
    """
    运行模式界面
    提供流程执行和结果显示功能
    """
    
    # 信号定义
    execution_started = pyqtSignal()
    execution_finished = pyqtSignal(dict)
    execution_error = pyqtSignal(str)
    workflow_loaded = pyqtSignal(str)
    detection_result = pyqtSignal(dict)
    status_message = pyqtSignal(str)
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化运行模式界面
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_manager = config_manager
        
        # 运行状态
        self.is_running = False
        self.current_workflow = None
        
        # 创建UI组件
        self._setup_ui()
        
        # 连接信号
        self._connect_signals()
        
        logger.info("运行模式界面初始化完成")
    
    def _setup_ui(self):
        """设置UI组件"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel("运行模式")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #FFFFFF;")
        title_label.setAlignment(Qt.AlignCenter)
        
        # 状态信息
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("状态：就绪")
        self.status_label.setStyleSheet("color: #CCCCCC; padding: 5px;")
        
        self.workflow_info_label = QLabel("工作流程：未加载")
        self.workflow_info_label.setStyleSheet("color: #CCCCCC; padding: 5px;")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.workflow_info_label)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始运行")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #666666;
            }
        """)
        self.start_button.clicked.connect(self._start_execution)
        
        self.stop_button = QPushButton("停止运行")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #666666;
            }
        """)
        self.stop_button.clicked.connect(self._stop_execution)
        self.stop_button.setEnabled(False)
        
        self.reset_button = QPushButton("重置")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.reset_button.clicked.connect(self._reset_execution)
        
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addWidget(self.reset_button)
        control_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 结果显示区域
        results_label = QLabel("执行结果：")
        results_label.setStyleSheet("color: #FFFFFF; font-weight: bold; margin-top: 10px;")
        
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet("""
            QTextEdit {
                background-color: #2a2a2a;
                color: #FFFFFF;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 5px;
                font-family: 'Courier New', monospace;
            }
        """)
        self.results_text.setPlainText("等待执行...")
        
        # 添加到主布局
        main_layout.addWidget(title_label)
        main_layout.addLayout(status_layout)
        main_layout.addLayout(control_layout)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(results_label)
        main_layout.addWidget(self.results_text)
    
    def _connect_signals(self):
        """连接信号和槽"""
        self.execution_started.connect(self._on_execution_started)
        self.execution_finished.connect(self._on_execution_finished)
        self.execution_error.connect(self._on_execution_error)
    
    def _start_execution(self):
        """开始执行工作流程"""
        if self.is_running:
            return
        
        try:
            # 检查是否有可用的工作流程
            if not self._validate_workflow():
                return
            
            self.is_running = True
            self.execution_started.emit()
            
            # 模拟执行过程
            self._simulate_execution()
            
        except Exception as e:
            error_msg = f"启动执行失败: {str(e)}"
            logger.error(error_msg)
            self.execution_error.emit(error_msg)
    
    def _stop_execution(self):
        """停止执行工作流程"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.status_label.setText("状态：已停止")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        self.results_text.append("\n=== 执行已停止 ===")
        logger.info("工作流程执行已停止")
    
    def _reset_execution(self):
        """重置执行状态"""
        self.is_running = False
        self.status_label.setText("状态：就绪")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)
        
        self.results_text.clear()
        self.results_text.setPlainText("等待执行...")
        
        logger.info("执行状态已重置")
    
    def _validate_workflow(self) -> bool:
        """
        验证工作流程是否有效
        
        Returns:
            bool: 工作流程是否有效
        """
        # 这里应该检查配置管理器中的工作流程配置
        # 暂时返回True，表示总是有效
        return True
    
    def _simulate_execution(self):
        """模拟执行过程（用于演示）"""
        # 创建定时器模拟执行过程
        self.execution_timer = QTimer()
        self.execution_timer.timeout.connect(self._update_execution_progress)
        self.execution_step = 0
        self.execution_timer.start(500)  # 每500ms更新一次
    
    def _update_execution_progress(self):
        """更新执行进度"""
        if not self.is_running:
            self.execution_timer.stop()
            return
        
        self.execution_step += 1
        
        # 模拟执行步骤
        steps = [
            "初始化图像源...",
            "加载模板配置...",
            "执行图像预处理...",
            "进行模板匹配...",
            "应用位置校正...",
            "生成执行结果..."
        ]
        
        if self.execution_step <= len(steps):
            step_msg = f"步骤 {self.execution_step}/{len(steps)}: {steps[self.execution_step-1]}"
            self.results_text.append(step_msg)
            
            # 更新进度条
            progress = int((self.execution_step / len(steps)) * 100)
            self.progress_bar.setValue(progress)
        else:
            # 执行完成
            self.execution_timer.stop()
        
            # 模拟结果
            results = {
                "execution_time": "1.2秒",
                "processed_images": 1,
                "matches_found": 3,
                "success_rate": "100%"
            }
            
            self.execution_finished.emit(results)
    
    def _on_execution_started(self):
        """处理执行开始事件"""
        self.status_label.setText("状态：正在执行")
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        self.results_text.clear()
        self.results_text.append("=== 开始执行工作流程 ===")
        
        logger.info("工作流程执行已开始")
    
    def _on_execution_finished(self, results: dict):
        """
        处理执行完成事件
        
        Args:
            results: 执行结果
        """
        self.is_running = False
        self.status_label.setText("状态：执行完成")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setValue(100)
        
        # 显示结果
        self.results_text.append("\n=== 执行完成 ===")
        for key, value in results.items():
            self.results_text.append(f"{key}: {value}")
        
        logger.info(f"工作流程执行完成: {results}")
    
    def _on_execution_error(self, error_message: str):
        """
        处理执行错误事件
        
        Args:
            error_message: 错误消息
        """
        self.is_running = False
        self.status_label.setText("状态：执行错误")
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
        
        self.results_text.append(f"\n=== 执行错误 ===")
        self.results_text.append(f"错误信息: {error_message}")
        
        logger.error(f"工作流程执行错误: {error_message}")
    
    def load_workflow(self, workflow_config: dict):
        """
        加载工作流程配置
        
        Args:
            workflow_config: 工作流程配置
        """
        self.current_workflow = workflow_config
        
        # 更新界面显示
        workflow_name = workflow_config.get("name", "未命名工作流程")
        self.workflow_info_label.setText(f"工作流程：{workflow_name}")
        
        # 发射信号
        self.workflow_loaded.emit(workflow_name)
        
        logger.info(f"已加载工作流程: {workflow_name}")
    
    def get_execution_status(self) -> dict:
        """
        获取执行状态
        
        Returns:
            dict: 执行状态信息
        """
        return {
            "is_running": self.is_running,
            "current_workflow": self.current_workflow,
            "status": self.status_label.text()
        }
