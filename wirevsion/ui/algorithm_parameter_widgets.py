"""
算法参数控件 - 为完善的算法提供专门的UI组件

功能：
- 图像处理算法参数控件
- 特征检测算法参数控件
- 目标检测算法参数控件
- 深度学习算法参数控件
- 测量算法参数控件
- 位置校正算法参数控件
"""

from typing import Dict, Any, List, Optional, Callable
import json

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QSlider, QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
    QGroupBox, QGridLayout, QTextEdit, QTabWidget, QFrame,
    QScrollArea, QSplitter, QProgressBar, QListWidget,
    QListWidgetItem, QTableWidget, QTableWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

from loguru import logger
from .extended_parameter_widgets import FileSelectWidget, ROISelectorWidget


class AlgorithmParameterWidget(QWidget):
    """算法参数控件基类"""
    
    parameter_changed = pyqtSignal(str, object)  # 参数名, 参数值
    preview_requested = pyqtSignal()
    
    def __init__(self, algorithm_name: str, parameter_schema: Dict[str, Any], 
                 default_parameters: Dict[str, Any], parent=None):
        super().__init__(parent)
        
        self.algorithm_name = algorithm_name
        self.parameter_schema = parameter_schema
        self.default_parameters = default_parameters
        self.current_parameters = default_parameters.copy()
        
        self.parameter_widgets = {}
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(self.algorithm_name)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 4px;
                border-left: 4px solid #3498db;
            }
        """)
        layout.addWidget(title_label)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # 参数容器
        param_widget = QWidget()
        param_layout = QVBoxLayout(param_widget)
        
        # 创建参数控件
        self._create_parameter_widgets(param_layout)
        
        scroll_area.setWidget(param_widget)
        layout.addWidget(scroll_area)
        
        # 操作按钮
        self._create_action_buttons(layout)
    
    def _create_parameter_widgets(self, layout: QVBoxLayout):
        """创建参数控件"""
        for param_name, param_config in self.parameter_schema.items():
            param_widget = self._create_single_parameter_widget(param_name, param_config)
            if param_widget:
                layout.addWidget(param_widget)
    
    def _create_single_parameter_widget(self, param_name: str, param_config: Dict[str, Any]) -> QWidget:
        """创建单个参数控件"""
        param_type = param_config.get("type", "string")
        description = param_config.get("description", param_name)
        
        # 参数组
        group = QGroupBox(description)
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QHBoxLayout(group)
        
        # 根据类型创建控件
        if param_type == "integer":
            widget = self._create_integer_widget(param_name, param_config)
        elif param_type == "number":
            widget = self._create_number_widget(param_name, param_config)
        elif param_type == "boolean":
            widget = self._create_boolean_widget(param_name, param_config)
        elif param_type == "string":
            if "enum" in param_config:
                widget = self._create_enum_widget(param_name, param_config)
            else:
                widget = self._create_string_widget(param_name, param_config)
        elif param_type == "array":
            widget = self._create_array_widget(param_name, param_config)
        else:
            widget = QLabel(f"不支持的参数类型: {param_type}")
        
        layout.addWidget(widget)
        
        # 存储控件引用
        self.parameter_widgets[param_name] = widget
        
        return group
    
    def _create_integer_widget(self, param_name: str, param_config: Dict[str, Any]) -> QWidget:
        """创建整数参数控件"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 滑块
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(param_config.get("minimum", 0))
        slider.setMaximum(param_config.get("maximum", 100))
        slider.setValue(self.default_parameters.get(param_name, 0))
        
        # 数值框
        spinbox = QSpinBox()
        spinbox.setMinimum(param_config.get("minimum", 0))
        spinbox.setMaximum(param_config.get("maximum", 100))
        spinbox.setValue(self.default_parameters.get(param_name, 0))
        
        # 连接信号
        slider.valueChanged.connect(spinbox.setValue)
        spinbox.valueChanged.connect(slider.setValue)
        spinbox.valueChanged.connect(lambda v: self._on_parameter_changed(param_name, v))
        
        layout.addWidget(slider, 3)
        layout.addWidget(spinbox, 1)
        
        return container
    
    def _create_number_widget(self, param_name: str, param_config: Dict[str, Any]) -> QWidget:
        """创建浮点数参数控件"""
        spinbox = QDoubleSpinBox()
        spinbox.setMinimum(param_config.get("minimum", 0.0))
        spinbox.setMaximum(param_config.get("maximum", 100.0))
        spinbox.setDecimals(3)
        spinbox.setSingleStep(0.1)
        spinbox.setValue(self.default_parameters.get(param_name, 0.0))
        
        spinbox.valueChanged.connect(lambda v: self._on_parameter_changed(param_name, v))
        
        return spinbox
    
    def _create_boolean_widget(self, param_name: str, param_config: Dict[str, Any]) -> QWidget:
        """创建布尔参数控件"""
        checkbox = QCheckBox("启用")
        checkbox.setChecked(self.default_parameters.get(param_name, False))
        
        checkbox.toggled.connect(lambda v: self._on_parameter_changed(param_name, v))
        
        return checkbox
    
    def _create_enum_widget(self, param_name: str, param_config: Dict[str, Any]) -> QWidget:
        """创建枚举参数控件"""
        combobox = QComboBox()
        
        enum_values = param_config.get("enum", [])
        combobox.addItems([str(v) for v in enum_values])
        
        # 设置默认值
        default_value = self.default_parameters.get(param_name, "")
        if default_value in enum_values:
            combobox.setCurrentText(str(default_value))
        
        combobox.currentTextChanged.connect(lambda v: self._on_parameter_changed(param_name, v))
        
        return combobox
    
    def _create_string_widget(self, param_name: str, param_config: Dict[str, Any]) -> QWidget:
        """创建字符串参数控件"""
        # 检查是否是文件路径
        if "path" in param_name.lower() or "file" in param_name.lower():
            file_widget = FileSelectWidget()
            file_widget.set_path(self.default_parameters.get(param_name, ""))
            file_widget.file_selected.connect(lambda v: self._on_parameter_changed(param_name, v))
            return file_widget
        else:
            from PyQt5.QtWidgets import QLineEdit
            line_edit = QLineEdit()
            line_edit.setText(str(self.default_parameters.get(param_name, "")))
            line_edit.textChanged.connect(lambda v: self._on_parameter_changed(param_name, v))
            return line_edit
    
    def _create_array_widget(self, param_name: str, param_config: Dict[str, Any]) -> QWidget:
        """创建数组参数控件"""
        container = QWidget()
        layout = QVBoxLayout(container)
        
        # 列表控件
        list_widget = QListWidget()
        list_widget.setMaximumHeight(100)
        
        # 添加默认值
        default_array = self.default_parameters.get(param_name, [])
        for item in default_array:
            list_widget.addItem(str(item))
        
        layout.addWidget(list_widget)
        
        # 操作按钮
        btn_layout = QHBoxLayout()
        
        add_btn = QPushButton("添加")
        remove_btn = QPushButton("删除")
        
        btn_layout.addWidget(add_btn)
        btn_layout.addWidget(remove_btn)
        layout.addLayout(btn_layout)
        
        # 连接信号
        def update_array():
            array_value = []
            for i in range(list_widget.count()):
                array_value.append(list_widget.item(i).text())
            self._on_parameter_changed(param_name, array_value)
        
        add_btn.clicked.connect(lambda: self._add_array_item(list_widget, update_array))
        remove_btn.clicked.connect(lambda: self._remove_array_item(list_widget, update_array))
        
        return container
    
    def _add_array_item(self, list_widget: QListWidget, callback: Callable):
        """添加数组项"""
        from PyQt5.QtWidgets import QInputDialog
        text, ok = QInputDialog.getText(self, "添加项", "请输入值:")
        if ok and text:
            list_widget.addItem(text)
            callback()
    
    def _remove_array_item(self, list_widget: QListWidget, callback: Callable):
        """删除数组项"""
        current_row = list_widget.currentRow()
        if current_row >= 0:
            list_widget.takeItem(current_row)
            callback()
    
    def _create_action_buttons(self, layout: QVBoxLayout):
        """创建操作按钮"""
        btn_layout = QHBoxLayout()
        
        # 预览按钮
        preview_btn = QPushButton("预览效果")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        preview_btn.clicked.connect(self.preview_requested.emit)
        btn_layout.addWidget(preview_btn)
        
        # 重置按钮
        reset_btn = QPushButton("重置默认")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        reset_btn.clicked.connect(self.reset_parameters)
        btn_layout.addWidget(reset_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
    
    def _on_parameter_changed(self, param_name: str, value: Any):
        """参数改变事件"""
        self.current_parameters[param_name] = value
        self.parameter_changed.emit(param_name, value)
    
    def reset_parameters(self):
        """重置参数为默认值"""
        self.current_parameters = self.default_parameters.copy()
        
        # 更新所有控件
        for param_name, widget in self.parameter_widgets.items():
            default_value = self.default_parameters.get(param_name)
            self._set_widget_value(widget, default_value)
    
    def _set_widget_value(self, widget: QWidget, value: Any):
        """设置控件值"""
        if isinstance(widget, QSpinBox):
            widget.setValue(int(value) if value is not None else 0)
        elif isinstance(widget, QDoubleSpinBox):
            widget.setValue(float(value) if value is not None else 0.0)
        elif isinstance(widget, QCheckBox):
            widget.setChecked(bool(value) if value is not None else False)
        elif isinstance(widget, QComboBox):
            widget.setCurrentText(str(value) if value is not None else "")
        elif hasattr(widget, 'setText'):
            widget.setText(str(value) if value is not None else "")
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        return self.current_parameters.copy()
    
    def set_parameters(self, parameters: Dict[str, Any]):
        """设置参数"""
        self.current_parameters.update(parameters)
        
        # 更新控件
        for param_name, value in parameters.items():
            if param_name in self.parameter_widgets:
                self._set_widget_value(self.parameter_widgets[param_name], value)
