"""
参数依赖系统 - 实现参数间的联动关系

功能：
- 参数依赖定义
- 条件触发
- 值计算和传播
- 循环依赖检测
"""

from typing import Dict, Any, List, Optional, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import re

from PyQt6.QtCore import QObject, pyqtSignal

from loguru import logger


class DependencyType(Enum):
    """依赖类型"""
    VISIBILITY = "visibility"  # 可见性依赖
    VALUE = "value"  # 值依赖
    RANGE = "range"  # 范围依赖
    ENABLED = "enabled"  # 启用状态依赖
    OPTIONS = "options"  # 选项依赖（枚举类型）


@dataclass
class DependencyCondition:
    """依赖条件"""
    source_param: str  # 源参数
    operator: str  # 操作符: ==, !=, >, <, >=, <=, in, not_in
    value: Any  # 比较值
    
    def evaluate(self, source_value: Any) -> bool:
        """评估条件"""
        try:
            if self.operator == "==":
                return source_value == self.value
            elif self.operator == "!=":
                return source_value != self.value
            elif self.operator == ">":
                return float(source_value) > float(self.value)
            elif self.operator == "<":
                return float(source_value) < float(self.value)
            elif self.operator == ">=":
                return float(source_value) >= float(self.value)
            elif self.operator == "<=":
                return float(source_value) <= float(self.value)
            elif self.operator == "in":
                return source_value in self.value
            elif self.operator == "not_in":
                return source_value not in self.value
            else:
                logger.warning(f"未知操作符: {self.operator}")
                return False
        except Exception as e:
            logger.error(f"条件评估失败: {e}")
            return False


@dataclass
class ParameterDependency:
    """参数依赖定义"""
    target_param: str  # 目标参数
    dependency_type: DependencyType  # 依赖类型
    conditions: List[DependencyCondition]  # 条件列表
    logic: str = "AND"  # 逻辑关系: AND, OR
    value_calculator: Optional[Callable] = None  # 值计算函数
    
    def evaluate_conditions(self, param_values: Dict[str, Any]) -> bool:
        """评估所有条件"""
        if not self.conditions:
            return True
        
        results = []
        for condition in self.conditions:
            if condition.source_param in param_values:
                source_value = param_values[condition.source_param]
                results.append(condition.evaluate(source_value))
            else:
                results.append(False)
        
        if self.logic == "AND":
            return all(results)
        elif self.logic == "OR":
            return any(results)
        else:
            return False
    
    def calculate_value(self, param_values: Dict[str, Any]) -> Any:
        """计算目标值"""
        if self.value_calculator:
            try:
                return self.value_calculator(param_values)
            except Exception as e:
                logger.error(f"值计算失败: {e}")
        return None


class ParameterDependencyManager(QObject):
    """参数依赖管理器"""
    
    # 信号
    parameter_visibility_changed = pyqtSignal(str, bool)  # param_name, visible
    parameter_enabled_changed = pyqtSignal(str, bool)  # param_name, enabled
    parameter_value_changed = pyqtSignal(str, object)  # param_name, value
    parameter_range_changed = pyqtSignal(str, float, float)  # param_name, min, max
    parameter_options_changed = pyqtSignal(str, list)  # param_name, options
    
    def __init__(self):
        super().__init__()
        
        # 依赖图
        self.dependencies: Dict[str, List[ParameterDependency]] = {}
        
        # 参数值缓存
        self.param_values: Dict[str, Any] = {}
        
        # 循环依赖检测
        self.dependency_graph: Dict[str, Set[str]] = {}
    
    def add_dependency(self, dependency: ParameterDependency):
        """添加依赖"""
        # 更新依赖图
        for condition in dependency.conditions:
            source = condition.source_param
            target = dependency.target_param
            
            if source not in self.dependency_graph:
                self.dependency_graph[source] = set()
            self.dependency_graph[source].add(target)
            
            # 检测循环依赖
            if self._has_circular_dependency(source):
                logger.error(f"检测到循环依赖: {source} -> {target}")
                return False
        
        # 添加到依赖列表
        target = dependency.target_param
        if target not in self.dependencies:
            self.dependencies[target] = []
        self.dependencies[target].append(dependency)
        
        logger.debug(f"添加依赖: {dependency.target_param} 依赖于 "
                    f"{[c.source_param for c in dependency.conditions]}")
        
        return True
    
    def _has_circular_dependency(self, start_node: str) -> bool:
        """检测循环依赖"""
        visited = set()
        rec_stack = set()
        
        def dfs(node: str) -> bool:
            visited.add(node)
            rec_stack.add(node)
            
            if node in self.dependency_graph:
                for neighbor in self.dependency_graph[node]:
                    if neighbor not in visited:
                        if dfs(neighbor):
                            return True
                    elif neighbor in rec_stack:
                        return True
            
            rec_stack.remove(node)
            return False
        
        return dfs(start_node)
    
    def update_parameter(self, param_name: str, value: Any):
        """更新参数值"""
        # 更新值缓存
        self.param_values[param_name] = value
        
        # 触发依赖更新
        self._propagate_changes(param_name)
    
    def _propagate_changes(self, changed_param: str):
        """传播参数变化"""
        # 获取所有受影响的参数
        affected_params = self._get_affected_parameters(changed_param)
        
        for param in affected_params:
            if param in self.dependencies:
                for dependency in self.dependencies[param]:
                    # 评估条件
                    if dependency.evaluate_conditions(self.param_values):
                        # 根据依赖类型执行动作
                        self._apply_dependency(dependency)
    
    def _get_affected_parameters(self, source_param: str) -> List[str]:
        """获取受影响的参数列表"""
        affected = []
        
        if source_param in self.dependency_graph:
            # 使用BFS遍历依赖图
            queue = list(self.dependency_graph[source_param])
            visited = set(queue)
            
            while queue:
                param = queue.pop(0)
                affected.append(param)
                
                if param in self.dependency_graph:
                    for next_param in self.dependency_graph[param]:
                        if next_param not in visited:
                            queue.append(next_param)
                            visited.add(next_param)
        
        return affected
    
    def _apply_dependency(self, dependency: ParameterDependency):
        """应用依赖"""
        target = dependency.target_param
        
        if dependency.dependency_type == DependencyType.VISIBILITY:
            # 可见性依赖：条件满足时显示，否则隐藏
            visible = dependency.evaluate_conditions(self.param_values)
            self.parameter_visibility_changed.emit(target, visible)
            
        elif dependency.dependency_type == DependencyType.ENABLED:
            # 启用状态依赖
            enabled = dependency.evaluate_conditions(self.param_values)
            self.parameter_enabled_changed.emit(target, enabled)
            
        elif dependency.dependency_type == DependencyType.VALUE:
            # 值依赖
            if dependency.value_calculator:
                new_value = dependency.calculate_value(self.param_values)
                if new_value is not None:
                    self.param_values[target] = new_value
                    self.parameter_value_changed.emit(target, new_value)
                    
        elif dependency.dependency_type == DependencyType.RANGE:
            # 范围依赖
            if dependency.value_calculator:
                range_values = dependency.calculate_value(self.param_values)
                if isinstance(range_values, (list, tuple)) and len(range_values) >= 2:
                    self.parameter_range_changed.emit(
                        target, float(range_values[0]), float(range_values[1])
                    )
                    
        elif dependency.dependency_type == DependencyType.OPTIONS:
            # 选项依赖
            if dependency.value_calculator:
                options = dependency.calculate_value(self.param_values)
                if isinstance(options, list):
                    self.parameter_options_changed.emit(target, options)
    
    def get_dependencies(self, param_name: str) -> List[ParameterDependency]:
        """获取参数的所有依赖"""
        return self.dependencies.get(param_name, [])
    
    def clear_dependencies(self):
        """清除所有依赖"""
        self.dependencies.clear()
        self.dependency_graph.clear()
        self.param_values.clear()


# 预定义的依赖模板
DEPENDENCY_TEMPLATES = {
    "camera": {
        # 自动曝光关闭时，曝光值参数才可用
        "exposure": [
            ParameterDependency(
                target_param="exposure",
                dependency_type=DependencyType.ENABLED,
                conditions=[
                    DependencyCondition("auto_exposure", "==", False)
                ]
            )
        ]
    },
    
    "edge_detection": {
        # 高阈值必须大于低阈值
        "high_threshold": [
            ParameterDependency(
                target_param="high_threshold",
                dependency_type=DependencyType.RANGE,
                conditions=[
                    DependencyCondition("low_threshold", ">", 0)
                ],
                value_calculator=lambda params: (
                    params.get("low_threshold", 0) + 1,
                    255
                )
            )
        ]
    },
    
    "template_matching": {
        # 最大匹配数大于0时，最小间距参数才有意义
        "min_distance": [
            ParameterDependency(
                target_param="min_distance",
                dependency_type=DependencyType.VISIBILITY,
                conditions=[
                    DependencyCondition("max_matches", ">", 1)
                ]
            )
        ]
    },
    
    "yolo_detection": {
        # 设备选择为auto时，显示提示信息
        "device_info": [
            ParameterDependency(
                target_param="device_info",
                dependency_type=DependencyType.VISIBILITY,
                conditions=[
                    DependencyCondition("device", "==", "auto")
                ]
            )
        ],
        
        # 根据置信度阈值自动调整NMS阈值建议范围
        "nms_threshold": [
            ParameterDependency(
                target_param="nms_threshold",
                dependency_type=DependencyType.RANGE,
                conditions=[
                    DependencyCondition("confidence_threshold", ">", 0)
                ],
                value_calculator=lambda params: (
                    max(0.1, params.get("confidence_threshold", 0.5) - 0.2),
                    min(0.9, params.get("confidence_threshold", 0.5) + 0.2)
                )
            )
        ]
    },
    
    "affine_transform": {
        # 锁定纵横比时，scale_y跟随scale_x
        "scale_y": [
            ParameterDependency(
                target_param="scale_y",
                dependency_type=DependencyType.VALUE,
                conditions=[
                    DependencyCondition("lock_aspect_ratio", "==", True)
                ],
                value_calculator=lambda params: params.get("scale_x", 1.0)
            )
        ]
    }
}


def create_dependencies_from_template(algorithm_name: str) -> List[ParameterDependency]:
    """从模板创建依赖列表"""
    dependencies = []
    
    if algorithm_name in DEPENDENCY_TEMPLATES:
        template = DEPENDENCY_TEMPLATES[algorithm_name]
        
        for target_param, dep_list in template.items():
            dependencies.extend(dep_list)
    
    return dependencies


# 依赖表达式解析器（支持字符串定义依赖）
class DependencyParser:
    """依赖表达式解析器"""
    
    @staticmethod
    def parse(expression: str) -> Optional[ParameterDependency]:
        """
        解析依赖表达式
        
        示例：
        - "visible_if: auto_mode == True"
        - "enabled_when: threshold > 0 AND threshold < 100"
        - "value_from: scale_x when lock_ratio == True"
        - "range: (min_val, max_val * 2)"
        """
        expression = expression.strip()
        
        # 可见性依赖
        if expression.startswith("visible_if:"):
            condition_str = expression[11:].strip()
            conditions = DependencyParser._parse_conditions(condition_str)
            if conditions:
                return ParameterDependency(
                    target_param="",  # 需要外部设置
                    dependency_type=DependencyType.VISIBILITY,
                    conditions=conditions
                )
        
        # 启用状态依赖
        elif expression.startswith("enabled_when:"):
            condition_str = expression[13:].strip()
            conditions = DependencyParser._parse_conditions(condition_str)
            if conditions:
                return ParameterDependency(
                    target_param="",
                    dependency_type=DependencyType.ENABLED,
                    conditions=conditions
                )
        
        return None
    
    @staticmethod
    def _parse_conditions(condition_str: str) -> List[DependencyCondition]:
        """解析条件字符串"""
        conditions = []
        
        # 简单的条件解析（支持 AND/OR）
        # 示例: "param1 > 0 AND param2 == 'test'"
        
        # 分割AND/OR
        parts = re.split(r'\s+(AND|OR)\s+', condition_str)
        
        for part in parts:
            if part in ["AND", "OR"]:
                continue
                
            # 解析单个条件
            match = re.match(r'(\w+)\s*(==|!=|>|<|>=|<=)\s*(.+)', part.strip())
            if match:
                param = match.group(1)
                operator = match.group(2)
                value_str = match.group(3).strip()
                
                # 解析值
                value = DependencyParser._parse_value(value_str)
                
                conditions.append(DependencyCondition(param, operator, value))
        
        return conditions
    
    @staticmethod
    def _parse_value(value_str: str) -> Any:
        """解析值字符串"""
        # 布尔值
        if value_str.lower() == "true":
            return True
        elif value_str.lower() == "false":
            return False
        
        # 字符串（带引号）
        if (value_str.startswith('"') and value_str.endswith('"')) or \
           (value_str.startswith("'") and value_str.endswith("'")):
            return value_str[1:-1]
        
        # 数字
        try:
            if '.' in value_str:
                return float(value_str)
            else:
                return int(value_str)
        except ValueError:
            pass
        
        # 默认作为字符串
        return value_str 