#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机配置界面模块
提供相机参数设置功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QComboBox, QSpinBox, QFormLayout, QGroupBox,
    QCheckBox, QSlider
)
from PyQt6.QtCore import Qt

from wirevsion.config.config_manager import ConfigManager
from wirevsion.camera.camera_manager import CameraManager


class CameraConfigWidget(QWidget):
    """
    相机配置界面
    提供相机参数设置功能
    """
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化相机配置界面
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_manager = config_manager
        self.camera_manager = CameraManager()
        
        # 创建UI组件
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI组件"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 相机选择组
        camera_group = QGroupBox("相机选择")
        camera_layout = QFormLayout()
        
        # 相机选择下拉框
        camera_layout.addRow(QLabel("相机:"), self._create_camera_combo())
        
        # 刷新相机列表按钮
        refresh_btn = QPushButton("刷新列表")
        refresh_btn.clicked.connect(self.refresh_cameras)
        camera_layout.addRow("", refresh_btn)
        
        camera_group.setLayout(camera_layout)
        
        # 相机参数组
        params_group = QGroupBox("相机参数")
        params_layout = QFormLayout()
        
        # 分辨率选择
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["640x480", "800x600", "1280x720", "1920x1080"])
        params_layout.addRow(QLabel("分辨率:"), self.resolution_combo)
        
        # 帧率设置
        self.fps_spinbox = QSpinBox()
        self.fps_spinbox.setRange(1, 120)
        self.fps_spinbox.setValue(60)
        params_layout.addRow(QLabel("帧率:"), self.fps_spinbox)
        
        # 自动曝光
        self.auto_exposure_checkbox = QCheckBox("自动")
        self.auto_exposure_checkbox.setChecked(True)
        self.auto_exposure_checkbox.toggled.connect(self.toggle_auto_exposure)
        
        self.exposure_slider = QSlider(Qt.Orientation.Horizontal)
        self.exposure_slider.setRange(0, 100)
        self.exposure_slider.setValue(50)
        self.exposure_slider.setEnabled(False)
        
        exposure_layout = QHBoxLayout()
        exposure_layout.addWidget(self.exposure_slider)
        exposure_layout.addWidget(self.auto_exposure_checkbox)
        
        params_layout.addRow(QLabel("曝光:"), exposure_layout)
        
        # 自动白平衡
        self.auto_wb_checkbox = QCheckBox("自动")
        self.auto_wb_checkbox.setChecked(True)
        self.auto_wb_checkbox.toggled.connect(self.toggle_auto_wb)
        
        self.wb_slider = QSlider(Qt.Orientation.Horizontal)
        self.wb_slider.setRange(0, 100)
        self.wb_slider.setValue(50)
        self.wb_slider.setEnabled(False)
        
        wb_layout = QHBoxLayout()
        wb_layout.addWidget(self.wb_slider)
        wb_layout.addWidget(self.auto_wb_checkbox)
        
        params_layout.addRow(QLabel("白平衡:"), wb_layout)
        
        # 对比度
        self.contrast_slider = QSlider(Qt.Orientation.Horizontal)
        self.contrast_slider.setRange(0, 100)
        self.contrast_slider.setValue(50)
        params_layout.addRow(QLabel("对比度:"), self.contrast_slider)
        
        # 亮度
        self.brightness_slider = QSlider(Qt.Orientation.Horizontal)
        self.brightness_slider.setRange(0, 100)
        self.brightness_slider.setValue(50)
        params_layout.addRow(QLabel("亮度:"), self.brightness_slider)
        
        params_group.setLayout(params_layout)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.connect_btn = QPushButton("连接相机")
        self.connect_btn.clicked.connect(self.connect_camera)
        
        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.clicked.connect(self.disconnect_camera)
        self.disconnect_btn.setEnabled(False)
        
        apply_btn = QPushButton("应用设置")
        apply_btn.clicked.connect(self.apply_settings)
        
        buttons_layout.addWidget(self.connect_btn)
        buttons_layout.addWidget(self.disconnect_btn)
        buttons_layout.addWidget(apply_btn)
        
        # 添加组件到主布局
        main_layout.addWidget(camera_group)
        main_layout.addWidget(params_group)
        main_layout.addLayout(buttons_layout)
        main_layout.addStretch()
    
    def _create_camera_combo(self):
        """创建相机选择下拉框"""
        self.camera_combo = QComboBox()
        # 获取可用相机列表
        cameras = self.camera_manager.get_available_cameras()
        for camera in cameras:
            self.camera_combo.addItem(f"相机 {camera['id']}: {camera['name']}", camera['id'])
        return self.camera_combo
    
    def refresh_cameras(self):
        """刷新相机列表"""
        self.camera_combo.clear()
        cameras = self.camera_manager.get_available_cameras()
        for camera in cameras:
            self.camera_combo.addItem(f"相机 {camera['id']}: {camera['name']}", camera['id'])
    
    def toggle_auto_exposure(self, checked):
        """切换自动曝光"""
        self.exposure_slider.setEnabled(not checked)
    
    def toggle_auto_wb(self, checked):
        """切换自动白平衡"""
        self.wb_slider.setEnabled(not checked)
    
    def connect_camera(self):
        """连接相机"""
        camera_id = self.camera_combo.currentData()
        if camera_id is not None:
            # 连接相机
            self.connect_btn.setEnabled(False)
            self.disconnect_btn.setEnabled(True)
    
    def disconnect_camera(self):
        """断开相机连接"""
        # 断开相机
        self.connect_btn.setEnabled(True)
        self.disconnect_btn.setEnabled(False)
    
    def apply_settings(self):
        """应用相机设置"""
        # 获取参数
        resolution = self.resolution_combo.currentText()
        fps = self.fps_spinbox.value()
        auto_exposure = self.auto_exposure_checkbox.isChecked()
        exposure = self.exposure_slider.value() if not auto_exposure else None
        auto_wb = self.auto_wb_checkbox.isChecked()
        wb = self.wb_slider.value() if not auto_wb else None
        contrast = self.contrast_slider.value()
        brightness = self.brightness_slider.value()
        
        # 应用参数设置
        # 这里添加应用相机参数的逻辑
