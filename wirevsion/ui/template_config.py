#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模板配置界面模块
提供模板选择、编辑和参数配置功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
    QPushButton, QLabel, QGroupBox, QTableWidget,
    QTableWidgetItem, QComboBox, QSpinBox, QDoubleSpinBox,
    QToolBar, QAction, QFormLayout, QFileDialog, QDialog,
    QDialogButtonBox, QListWidget, QListWidgetItem, QCheckBox,
    QRadioButton, QButtonGroup, QSlider, QMessageBox
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QPoint, QRect
from PyQt6.QtGui import QPixmap, QIcon, QImage, QPainter, QPen, QColor, QMouseEvent

import cv2
import numpy as np
import os
from typing import List, Dict, Optional, Tuple

from wirevsion.config.config_manager import ConfigManager
from wirevsion.config.workflow_config import TemplateConfig
from wirevsion.utils.image_utils import cv_to_qpixmap, resize_image


class TemplateConfigWidget(QWidget):
    """
    模板配置界面
    提供模板选择、编辑和参数配置功能
    """
    
    # 信号：模板配置已更改
    template_config_changed = pyqtSignal(TemplateConfig)
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化模板配置界面
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_manager = config_manager
        
        # 当前图像
        self.current_image = None
        self.template_image = None
        
        # 当前模板配置
        self.template_config = TemplateConfig()
        
        # 模板列表
        self.template_list = []
        
        # ROI相关属性
        self.drawing_roi = False
        self.roi_rect = None
        self.roi_start_pos = None
        self.enable_roi = False
        
        # 创建UI组件
        self._setup_ui()
        
        # 初始化控件状态
        if hasattr(self, 'matcher_type_combo'):
            if self.template_config.matcher_type == "contour":
                self.matcher_type_combo.setCurrentIndex(1)
            else:
                self.matcher_type_combo.setCurrentIndex(0)
    
    def _setup_ui(self):
        """设置UI组件"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建顶部标题和关闭按钮
        header_layout = QHBoxLayout()
        
        title_label = QLabel("模板配置")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("font-size: 16px; font-weight: bold;")
        close_btn.clicked.connect(self.hide)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(close_btn)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 基本参数选项卡
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 模板选择按钮
        template_buttons_layout = QHBoxLayout()
        
        self.current_template_btn = QPushButton("选择当前图像")
        self.current_template_btn.clicked.connect(self._select_current_image)
        
        self.other_template_btn = QPushButton("选择其他图像")
        self.other_template_btn.clicked.connect(self._select_other_image)
        
        self.save_template_btn = QPushButton("模板存图")
        self.save_template_btn.clicked.connect(self._save_template)
        
        # 添加开关状态
        self.template_enabled_checkbox = QCheckBox()
        self.template_enabled_checkbox.setChecked(True)
        
        template_buttons_layout.addWidget(self.current_template_btn)
        template_buttons_layout.addWidget(self.other_template_btn)
        template_buttons_layout.addWidget(self.save_template_btn)
        template_buttons_layout.addWidget(self.template_enabled_checkbox)
        
        # 工具栏
        template_tools_layout = QHBoxLayout()
        
        # 添加ROI控制按钮
        self.roi_enable_btn = QPushButton("启用ROI")
        self.roi_enable_btn.setCheckable(True)
        self.roi_enable_btn.setFixedHeight(30)
        self.roi_enable_btn.clicked.connect(self._toggle_roi_mode)
        
        self.clear_roi_btn = QPushButton("清除ROI")
        self.clear_roi_btn.setFixedHeight(30)
        self.clear_roi_btn.clicked.connect(self._clear_roi)
        self.clear_roi_btn.setEnabled(False)
        
        # 添加各种工具按钮
        tools = [
            "选择", "矩形", "圆形", "多边形", "拾取颜色", 
            "删除", "撤销", "重做", "放大", "缩小", "1:1", "全屏"
        ]
        
        for tool in tools:
            btn = QPushButton()
            btn.setFixedSize(30, 30)
            btn.setToolTip(tool)
            template_tools_layout.addWidget(btn)
        
        # 添加ROI按钮到工具栏
        template_tools_layout.addWidget(self.roi_enable_btn)
        template_tools_layout.addWidget(self.clear_roi_btn)
        
        # 模板预览区域
        preview_layout = QVBoxLayout()
        
        self.template_preview = QLabel("无模板图像")
        self.template_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.template_preview.setMinimumSize(600, 400)
        self.template_preview.setStyleSheet("border: 1px solid #CCCCCC; background-color: #444444;")
        # 支持鼠标追踪
        self.template_preview.setMouseTracking(True)
        # 安装事件过滤器以处理鼠标事件
        self.template_preview.installEventFilter(self)
        
        preview_layout.addWidget(self.template_preview)
        
        # 显示坐标信息
        self.coord_label = QLabel("坐标：(0,0) | 大小：5120×5120 | X:0000 Y:0000 | R:000 G:000 B:000")
        preview_layout.addWidget(self.coord_label)
        
        # 配置参数
        params_group = QGroupBox("配置参数")
        params_layout = QFormLayout()
        
        # 匹配器类型
        self.matcher_type_combo = QComboBox()
        self.matcher_type_combo.addItem("模板匹配")
        self.matcher_type_combo.addItem("轮廓匹配")
        self.matcher_type_combo.currentIndexChanged.connect(self._on_matcher_type_changed)
        
        # 尺度模式
        self.scale_mode_combo = QComboBox()
        self.scale_mode_combo.addItem("自动")
        self.scale_mode_combo.addItem("固定")
        self.scale_mode_combo.addItem("范围")
        
        # 特征尺度
        self.scale_factor_spin = QDoubleSpinBox()
        self.scale_factor_spin.setRange(0.1, 100)
        self.scale_factor_spin.setSingleStep(0.1)
        self.scale_factor_spin.setValue(19.5)
        
        # 阈值模式
        self.threshold_mode_combo = QComboBox()
        self.threshold_mode_combo.addItem("自动")
        self.threshold_mode_combo.addItem("固定")
        
        # 对比度阈值
        self.contrast_threshold_spin = QDoubleSpinBox()
        self.contrast_threshold_spin.setRange(0, 100)
        self.contrast_threshold_spin.setSingleStep(0.1)
        self.contrast_threshold_spin.setValue(5)
        
        # 添加表单项
        params_layout.addRow(QLabel("匹配器类型:"), self.matcher_type_combo)
        params_layout.addRow(QLabel("尺度模式:"), self.scale_mode_combo)
        params_layout.addRow(QLabel("特征尺度:"), self.scale_factor_spin)
        params_layout.addRow(QLabel("阈值模式:"), self.threshold_mode_combo)
        params_layout.addRow(QLabel("对比度阈值:"), self.contrast_threshold_spin)
        
        params_group.setLayout(params_layout)
        
        # 添加组件到基本参数选项卡
        basic_layout.addLayout(template_buttons_layout)
        basic_layout.addLayout(template_tools_layout)
        basic_layout.addLayout(preview_layout)
        basic_layout.addWidget(params_group)
        
        # 特征模板选项卡
        feature_tab = QWidget()
        feature_layout = QVBoxLayout(feature_tab)
        
        # 模板列表
        template_list_group = QGroupBox("模板列表")
        template_list_layout = QVBoxLayout()
        
        # 模板操作按钮
        template_actions_layout = QHBoxLayout()
        
        self.create_template_btn = QPushButton("创建")
        self.import_template_btn = QPushButton("导入")
        
        template_actions_layout.addWidget(self.create_template_btn)
        template_actions_layout.addWidget(self.import_template_btn)
        template_actions_layout.addStretch()
        
        # 模板列表表格
        self.template_table = QTableWidget(0, 3)
        self.template_table.setHorizontalHeaderLabels(["序号", "模板名称", "操作"])
        self.template_table.horizontalHeader().setStretchLastSection(True)
        
        template_list_layout.addLayout(template_actions_layout)
        template_list_layout.addWidget(self.template_table)
        
        template_list_group.setLayout(template_list_layout)
        
        # 模板预览
        template_preview_group = QGroupBox("模板预览")
        template_preview_layout = QVBoxLayout()
        
        self.feature_template_preview = QLabel("未选择模板")
        self.feature_template_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.feature_template_preview.setMinimumSize(300, 200)
        
        template_preview_layout.addWidget(self.feature_template_preview)
        
        # 模板操作按钮
        template_buttons = QHBoxLayout()
        
        self.edit_template_btn = QPushButton("编辑模板")
        self.delete_template_btn = QPushButton("删除所有模板")
        
        template_buttons.addWidget(self.edit_template_btn)
        template_buttons.addWidget(self.delete_template_btn)
        
        template_preview_layout.addLayout(template_buttons)
        
        template_preview_group.setLayout(template_preview_layout)
        
        # 添加组件到特征模板选项卡
        feature_layout.addWidget(template_list_group)
        feature_layout.addWidget(template_preview_group)
        
        # 运行参数选项卡
        run_tab = QWidget()
        run_layout = QVBoxLayout(run_tab)
        
        # 运行参数表单
        run_params_form = QFormLayout()
        
        # 角度范围
        angle_layout = QHBoxLayout()
        
        self.angle_min_spin = QSpinBox()
        self.angle_min_spin.setRange(-180, 180)
        self.angle_min_spin.setValue(-180)
        
        self.angle_max_spin = QSpinBox()
        self.angle_max_spin.setRange(-180, 180)
        self.angle_max_spin.setValue(180)
        
        angle_layout.addWidget(self.angle_min_spin)
        angle_layout.addWidget(QLabel(" 到 "))
        angle_layout.addWidget(self.angle_max_spin)
        
        # 尺度范围
        scale_layout = QHBoxLayout()
        
        self.scale_min_spin = QDoubleSpinBox()
        self.scale_min_spin.setRange(0.1, 10)
        self.scale_min_spin.setSingleStep(0.1)
        self.scale_min_spin.setValue(0.8)
        
        self.scale_max_spin = QDoubleSpinBox()
        self.scale_max_spin.setRange(0.1, 10)
        self.scale_max_spin.setSingleStep(0.1)
        self.scale_max_spin.setValue(1.2)
        
        scale_layout.addWidget(self.scale_min_spin)
        scale_layout.addWidget(QLabel(" 到 "))
        scale_layout.addWidget(self.scale_max_spin)
        
        # 超时时间
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(0, 10000)
        self.timeout_spin.setSingleStep(100)
        self.timeout_spin.setValue(2000)
        self.timeout_spin.setSuffix(" ms")
        
        # 多目标检测
        self.multi_target_check = QCheckBox("启用")
        
        # 最大目标数
        self.max_targets_spin = QSpinBox()
        self.max_targets_spin.setRange(1, 100)
        self.max_targets_spin.setValue(10)
        
        # 目标排序
        self.sort_combo = QComboBox()
        self.sort_combo.addItem("分数优先")
        self.sort_combo.addItem("从左到右")
        self.sort_combo.addItem("从右到左")
        self.sort_combo.addItem("从上到下")
        self.sort_combo.addItem("从下到上")
        
        # 添加表单项
        run_params_form.addRow(QLabel("角度范围:"), angle_layout)
        run_params_form.addRow(QLabel("尺度范围:"), scale_layout)
        run_params_form.addRow(QLabel("超时时间:"), self.timeout_spin)
        run_params_form.addRow(QLabel("多目标检测:"), self.multi_target_check)
        run_params_form.addRow(QLabel("最大目标数:"), self.max_targets_spin)
        run_params_form.addRow(QLabel("目标排序:"), self.sort_combo)
        
        run_layout.addLayout(run_params_form)
        run_layout.addStretch()
        
        # 结果显示选项卡
        result_tab = QWidget()
        result_layout = QVBoxLayout(result_tab)
        
        # 结果显示选项
        display_group = QGroupBox("结果显示")
        display_form = QFormLayout()
        
        # 显示匹配框
        self.show_box_check = QCheckBox("启用")
        self.show_box_check.setChecked(True)
        
        # 显示得分
        self.show_score_check = QCheckBox("启用")
        self.show_score_check.setChecked(True)
        
        # 显示位置
        self.show_position_check = QCheckBox("启用")
        self.show_position_check.setChecked(True)
        
        # 显示角度
        self.show_angle_check = QCheckBox("启用")
        self.show_angle_check.setChecked(True)
        
        # 添加表单项
        display_form.addRow(QLabel("显示匹配框:"), self.show_box_check)
        display_form.addRow(QLabel("显示得分:"), self.show_score_check)
        display_form.addRow(QLabel("显示位置:"), self.show_position_check)
        display_form.addRow(QLabel("显示角度:"), self.show_angle_check)
        
        display_group.setLayout(display_form)
        
        result_layout.addWidget(display_group)
        result_layout.addStretch()
        
        # 添加选项卡到选项卡组件
        self.tab_widget.addTab(basic_tab, "基本参数")
        self.tab_widget.addTab(feature_tab, "特征模板")
        self.tab_widget.addTab(run_tab, "运行参数")
        self.tab_widget.addTab(result_tab, "结果显示")
        
        # 底部按钮
        buttons_layout = QHBoxLayout()
        
        self.run_btn = QPushButton("执行")
        self.run_btn.clicked.connect(self._execute_template_matching)
        
        self.continue_btn = QPushButton("连续执行")
        self.continue_btn.clicked.connect(self._continue_execute)
        
        self.confirm_btn = QPushButton("确定")
        self.confirm_btn.clicked.connect(self._confirm_template)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.run_btn)
        buttons_layout.addWidget(self.continue_btn)
        buttons_layout.addWidget(self.confirm_btn)
        
        # 添加组件到主布局
        main_layout.addLayout(header_layout)
        main_layout.addWidget(self.tab_widget)
        main_layout.addLayout(buttons_layout)
    
    def set_current_image(self, image: np.ndarray):
        """
        设置当前图像
        
        Args:
            image: 图像数据
        """
        self.current_image = image
    
    def _select_current_image(self):
        """选择当前图像作为模板"""
        if self.current_image is None:
            QMessageBox.warning(self, "错误", "当前没有可用图像")
            return
        
        # 设置模板图像
        self.template_image = self.current_image.copy()
        
        # 显示图像
        self._display_template(self.template_image)
    
    def _select_other_image(self):
        """选择其他图像作为模板"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模板图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        
        if file_path:
            try:
                # 加载图像
                self.template_image = cv2.imread(file_path)
                
                # 显示图像
                self._display_template(self.template_image)
                
                # 更新模板配置
                self.template_config.path = file_path
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"加载图像出错: {str(e)}")
    
    def _save_template(self):
        """保存模板图像"""
        if self.template_image is None:
            QMessageBox.warning(self, "错误", "没有可保存的模板图像")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存模板图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        
        if file_path:
            try:
                cv2.imwrite(file_path, self.template_image)
                
                # 更新模板配置
                self.template_config.path = file_path
                
                QMessageBox.information(self, "成功", "模板图像已保存")
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存图像出错: {str(e)}")
    
    def _display_template(self, image: np.ndarray):
        """
        显示模板图像
        
        Args:
            image: 模板图像
        """
        if image is None:
            self.template_preview.setText("无模板图像")
            return
        
        # 复制图像以在上面绘制
        display_image = image.copy()
        
        # 如果有ROI，绘制ROI矩形
        if self.roi_rect is not None:
            x, y, w, h = self.roi_rect
            cv2.rectangle(display_image, (x, y), (x + w, y + h), (255, 0, 255), 2)  # 紫色
        
        # 调整图像大小以适应预览区域
        max_size = 600
        resized = resize_image(display_image, width=max_size)
        
        # 转换为QPixmap并显示
        pixmap = cv_to_qpixmap(resized)
        self.template_preview.setPixmap(pixmap)
    
    def _execute_template_matching(self):
        """执行模板匹配"""
        if self.template_image is None or self.current_image is None:
            QMessageBox.warning(self, "错误", "缺少模板图像或当前图像")
            return
        
        # 获取参数
        method = cv2.TM_CCOEFF_NORMED  # 默认使用归一化相关系数方法
        threshold = self.contrast_threshold_spin.value() / 100.0
        matcher_type = "contour" if self.matcher_type_combo.currentIndex() == 1 else "template"
        
        # 更新模板配置
        self.template_config.method = method
        self.template_config.threshold = threshold
        self.template_config.matcher_type = matcher_type
        
        # 设置额外参数的信号数据
        extra_data = {}
        if self.roi_rect is not None and self.enable_roi:
            extra_data["roi_rect"] = self.roi_rect
        
        # 发送模板配置更改信号，附带额外数据
        self.template_config.extra_data = extra_data
        self.template_config_changed.emit(self.template_config)
    
    def _continue_execute(self):
        """连续执行模板匹配"""
        # 实际应用中可能需要启动一个定时器来连续执行
        self._execute_template_matching()
    
    def _confirm_template(self):
        """确认模板配置"""
        if self.template_image is None:
            QMessageBox.warning(self, "警告", "尚未选择模板图像")
            return
        
        # 更新模板配置
        self.template_config.method = cv2.TM_CCOEFF_NORMED  # 默认使用归一化相关系数方法
        self.template_config.threshold = self.contrast_threshold_spin.value() / 100.0
        self.template_config.matcher_type = "contour" if self.matcher_type_combo.currentIndex() == 1 else "template"
        
        # 发送模板配置更改信号
        self.template_config_changed.emit(self.template_config)
        
        # 关闭窗口
        self.hide()
    
    def get_template_config(self) -> TemplateConfig:
        """
        获取当前模板配置
        
        Returns:
            TemplateConfig: 模板配置
        """
        # 更新匹配器类型
        matcher_type = "contour" if self.matcher_type_combo.currentIndex() == 1 else "template"
        self.template_config.matcher_type = matcher_type
        
        return self.template_config
    
    def _on_matcher_type_changed(self, index):
        """
        匹配器类型变更处理函数
        
        Args:
            index: 选择的索引
        """
        # 更新模板配置中的匹配器类型
        matcher_type = "contour" if index == 1 else "template"
        self.template_config.matcher_type = matcher_type
        
        # 根据选择的匹配器类型，可以调整界面上的其他控件状态
        is_contour_matcher = (index == 1)  # 1为轮廓匹配
        
        # 对于轮廓匹配，一些模板匹配特有的设置可能不适用
        # 如果有当前模板，可能需要重新执行匹配以预览效果
        if hasattr(self, 'template_image') and self.template_image is not None:
            self._execute_template_matching()
    
    def _toggle_roi_mode(self):
        """切换ROI模式"""
        self.enable_roi = self.roi_enable_btn.isChecked()
        self.drawing_roi = self.enable_roi
        self.clear_roi_btn.setEnabled(self.roi_rect is not None)
        
        # 如果启用ROI模式，更新UI提示
        if self.enable_roi:
            self.template_preview.setCursor(Qt.CrossCursor)
            # 在状态栏或某处显示提示：请在图像上绘制ROI区域
            self.coord_label.setText("请在图像上拖动鼠标绘制ROI区域")
        else:
            self.template_preview.setCursor(Qt.ArrowCursor)
            self.coord_label.setText("坐标：(0,0) | 大小：5120×5120 | X:0000 Y:0000 | R:000 G:000 B:000")
            
            # 如果关闭ROI模式，清除ROI
            self._clear_roi()
            
        # 执行模板匹配以更新结果
        if self.template_image is not None and self.current_image is not None:
            self._execute_template_matching()
    
    def _clear_roi(self):
        """清除ROI"""
        self.roi_rect = None
        self.roi_start_pos = None
        self.clear_roi_btn.setEnabled(False)
        
        # 更新显示
        if self.template_image is not None:
            self._display_template(self.template_image)
    
    def eventFilter(self, obj, event):
        """
        事件过滤器，处理模板预览区域的鼠标事件
        
        Args:
            obj: 事件源对象
            event: 事件
            
        Returns:
            bool: 是否已处理事件
        """
        if obj == self.template_preview:
            # 检查是否在绘制ROI模式
            if not self.drawing_roi or self.template_image is None:
                return super().eventFilter(obj, event)
            
            # 获取图像尺寸
            img_h, img_w = self.template_image.shape[:2]
            
            # 获取预览区域尺寸
            pixmap = self.template_preview.pixmap()
            if pixmap is None:
                return super().eventFilter(obj, event)
                
            label_w = pixmap.width()
            label_h = pixmap.height()
            
            # 计算缩放比例
            scale_w = img_w / label_w
            scale_h = img_h / label_h
            scale = max(scale_w, scale_h)
            
            if event.type() == event.MouseButtonPress and event.button() == Qt.MouseButton.LeftButton:
                # 获取鼠标点击位置，并转换为图像坐标
                pos = event.pos()
                img_x = int(pos.x() * scale)
                img_y = int(pos.y() * scale)
                
                # 记录起始点
                self.roi_start_pos = (img_x, img_y)
                self.roi_rect = None
                return True
                
            elif event.type() == event.MouseMove and self.roi_start_pos:
                # 获取当前鼠标位置，并转换为图像坐标
                pos = event.pos()
                img_x = int(pos.x() * scale)
                img_y = int(pos.y() * scale)
                
                # 计算ROI矩形
                x = min(self.roi_start_pos[0], img_x)
                y = min(self.roi_start_pos[1], img_y)
                w = abs(img_x - self.roi_start_pos[0])
                h = abs(img_y - self.roi_start_pos[1])
                
                # 确保ROI在图像范围内
                x = max(0, min(x, img_w - 1))
                y = max(0, min(y, img_h - 1))
                w = max(1, min(w, img_w - x))
                h = max(1, min(h, img_h - y))
                
                self.roi_rect = (x, y, w, h)
                
                # 重新显示图像以绘制ROI
                self._display_template(self.template_image)
                return True
                
            elif event.type() == event.MouseButtonRelease and event.button() == Qt.MouseButton.LeftButton:
                if self.roi_start_pos:
                    # 获取当前鼠标位置，并转换为图像坐标
                    pos = event.pos()
                    img_x = int(pos.x() * scale)
                    img_y = int(pos.y() * scale)
                    
                    # 计算ROI矩形
                    x = min(self.roi_start_pos[0], img_x)
                    y = min(self.roi_start_pos[1], img_y)
                    w = abs(img_x - self.roi_start_pos[0])
                    h = abs(img_y - self.roi_start_pos[1])
                    
                    # 确保ROI在图像范围内
                    x = max(0, min(x, img_w - 1))
                    y = max(0, min(y, img_h - 1))
                    w = max(1, min(w, img_w - x))
                    h = max(1, min(h, img_h - y))
                    
                    # 检查ROI是否太小
                    if w < 10 or h < 10:
                        self.roi_rect = None
                    else:
                        self.roi_rect = (x, y, w, h)
                        self.enable_roi = True
                        self.clear_roi_btn.setEnabled(True)
                    
                    # 重置起始点
                    self.roi_start_pos = None
                    
                    # 重新显示图像以绘制ROI
                    self._display_template(self.template_image)
                    
                    # 自动执行模板匹配
                    if self.current_image is not None and self.roi_rect is not None:
                        self._execute_template_matching()
                        
                    # 更新坐标标签，显示ROI信息
                    if self.roi_rect:
                        x, y, w, h = self.roi_rect
                        self.coord_label.setText(f"ROI: ({x},{y}) | 大小: {w}×{h}")
                        
                    return True
        
        return super().eventFilter(obj, event) 