"""
算法专用配置界面组件

为每个算法提供定制化的配置界面，支持：
- 算法特定的参数配置
- 上级结果输入选择
- 实时参数验证
- 预览功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QPushButton, QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox,
    QLineEdit, QGroupBox, QSlider, QTextEdit, QTabWidget,
    QListWidget, QListWidgetItem, QSplitter, QFrame, QMessageBox,
    QGridLayout
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor
from typing import Dict, Any, List, Optional
from loguru import logger
import cv2
import numpy as np

from wirevsion.ui.modern_components import ModernButton, THEME_COLORS


class BaseAlgorithmConfigWidget(QWidget):
    """算法配置基类"""

    parameter_changed = pyqtSignal(str, object)  # 参数名, 参数值
    preview_requested = pyqtSignal()

    def __init__(self, algorithm_name: str, parent=None):
        super().__init__(parent)
        self.algorithm_name = algorithm_name
        self.parameters = {}
        self.input_sources = []  # 可用的输入源
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置UI - 子类重写"""
        pass

    def setup_connections(self):
        """设置信号连接 - 子类重写"""
        pass

    def set_input_sources(self, sources: List[Dict[str, Any]]):
        """设置可用的输入源"""
        self.input_sources = sources
        self.update_input_selector()

    def update_input_selector(self):
        """更新输入选择器 - 子类重写"""
        pass

    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        return self.parameters.copy()

    def set_parameters(self, parameters: Dict[str, Any]):
        """设置参数"""
        self.parameters.update(parameters)
        self.update_ui_from_parameters()

    def update_ui_from_parameters(self):
        """从参数更新UI - 子类重写"""
        pass

    def validate_parameters(self) -> bool:
        """验证参数 - 子类重写"""
        return True

    def _get_modern_slider_style(self):
        """获取现代化滑块样式"""
        return f"""
            QSlider::groove:horizontal {{
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                height: 8px;
                background: {THEME_COLORS["dark_bg_input"]};
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {THEME_COLORS["primary"]};
                border: 2px solid {THEME_COLORS["primary"]};
                width: 20px;
                height: 20px;
                border-radius: 10px;
                margin: -6px 0;
            }}
            QSlider::handle:horizontal:hover {{
                background: {THEME_COLORS["primary_hover"]};
                border: 2px solid {THEME_COLORS["primary_hover"]};
            }}
            QSlider::sub-page:horizontal {{
                background: {THEME_COLORS["primary"]};
                border-radius: 4px;
            }}
        """

    def _get_modern_input_style(self):
        """获取现代化输入框样式"""
        return f"""
            QSpinBox, QDoubleSpinBox, QLineEdit {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QSpinBox:focus, QDoubleSpinBox:focus, QLineEdit:focus {{
                border-color: {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["dark_bg_secondary"]};
            }}
            QSpinBox:hover, QDoubleSpinBox:hover, QLineEdit:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QSpinBox::up-button, QSpinBox::down-button,
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                width: 20px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover,
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _get_modern_combo_style(self):
        """获取现代化下拉框样式"""
        return f"""
            QComboBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QComboBox:focus {{
                border-color: {THEME_COLORS["primary"]};
            }}
            QComboBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                selection-background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _get_modern_checkbox_style(self):
        """获取现代化复选框样式"""
        return f"""
            QCheckBox {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                padding: 8px;
            }}
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                background-color: {THEME_COLORS["dark_bg_input"]};
            }}
            QCheckBox::indicator:checked {{
                border: 2px solid {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _get_modern_button_style(self):
        """获取现代化按钮样式"""
        return f"""
            QPushButton {{
                background-color: {THEME_COLORS["primary"]};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS["primary_hover"]};
            }}
            QPushButton:pressed {{
                background-color: {THEME_COLORS["primary_pressed"]};
            }}
            QPushButton:disabled {{
                background-color: {THEME_COLORS["dark_border_primary"]};
                color: {THEME_COLORS["text_secondary"]};
            }}
        """

    def _create_action_buttons(self):
        """创建操作按钮区域"""
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setSpacing(15)
        button_layout.setContentsMargins(0, 20, 0, 0)

        # 预览按钮
        preview_btn = QPushButton("🔍 预览效果")
        preview_btn.setStyleSheet(self._get_modern_button_style())
        preview_btn.clicked.connect(self.preview_requested.emit)

        # 重置按钮
        reset_btn = QPushButton("🔄 重置参数")
        reset_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                border-color: {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["dark_bg_primary"]};
            }}
        """)
        reset_btn.clicked.connect(self._reset_parameters)

        button_layout.addStretch()
        button_layout.addWidget(reset_btn)
        button_layout.addWidget(preview_btn)

        return button_widget

    def _reset_parameters(self):
        """重置参数到默认值 - 子类重写"""
        pass

    def _emit_parameter_changed(self, param_name: str, value: Any):
        """发射参数改变信号"""
        self.parameters[param_name] = value
        self.parameter_changed.emit(param_name, value)


class CameraSourceConfigWidget(BaseAlgorithmConfigWidget):
    """相机源配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("📷 相机配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 18px;
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid {THEME_COLORS["primary"]};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)

        # 主配置区域
        config_widget = QWidget()
        config_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)

        # 设备选择区域
        device_section = self._create_device_section()
        config_layout.addWidget(device_section)

        # 分辨率配置区域
        resolution_section = self._create_resolution_section()
        config_layout.addWidget(resolution_section)

        # 高级设置区域
        advanced_section = self._create_advanced_section()
        config_layout.addWidget(advanced_section)

        layout.addWidget(config_widget)

        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        preview_btn = ModernButton("🎥 预览相机", ModernButton.PRIMARY)
        preview_btn.setMinimumHeight(45)
        preview_btn.clicked.connect(self.preview_requested.emit)

        refresh_btn = ModernButton("🔄 刷新设备", ModernButton.SECONDARY)
        refresh_btn.setMinimumHeight(45)
        refresh_btn.clicked.connect(self._refresh_devices)

        button_layout.addWidget(refresh_btn)
        button_layout.addWidget(preview_btn)

        layout.addLayout(button_layout)
        layout.addStretch()

    def _create_device_section(self):
        """创建设备选择区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📱 设备选择")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 相机索引选择
        device_layout = QHBoxLayout()
        device_layout.setSpacing(15)

        device_label = QLabel("相机索引:")
        device_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.camera_index = QSpinBox()
        self.camera_index.setRange(0, 10)
        self.camera_index.setValue(0)
        self.camera_index.setMinimumWidth(100)
        self.camera_index.setStyleSheet(self._get_modern_input_style())

        device_layout.addWidget(device_label)
        device_layout.addWidget(self.camera_index)
        device_layout.addStretch()

        section_layout.addLayout(device_layout)
        return section

    def _create_resolution_section(self):
        """创建分辨率配置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📐 分辨率设置")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 分辨率控件
        resolution_layout = QHBoxLayout()
        resolution_layout.setSpacing(15)

        # 宽度
        width_label = QLabel("宽度:")
        width_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.width_spin = QSpinBox()
        self.width_spin.setRange(320, 4096)
        self.width_spin.setValue(640)
        self.width_spin.setMinimumWidth(100)
        self.width_spin.setStyleSheet(self._get_modern_input_style())

        # 高度
        height_label = QLabel("高度:")
        height_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.height_spin = QSpinBox()
        self.height_spin.setRange(240, 3072)
        self.height_spin.setValue(480)
        self.height_spin.setMinimumWidth(100)
        self.height_spin.setStyleSheet(self._get_modern_input_style())

        resolution_layout.addWidget(width_label)
        resolution_layout.addWidget(self.width_spin)
        resolution_layout.addWidget(height_label)
        resolution_layout.addWidget(self.height_spin)
        resolution_layout.addStretch()

        section_layout.addLayout(resolution_layout)
        return section

    def _create_advanced_section(self):
        """创建高级设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("⚙️ 高级设置")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 帧率设置
        fps_layout = QHBoxLayout()
        fps_layout.setSpacing(15)

        fps_label = QLabel("帧率:")
        fps_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(1, 120)
        self.fps_spin.setValue(30)
        self.fps_spin.setMinimumWidth(100)
        self.fps_spin.setStyleSheet(self._get_modern_input_style())

        fps_unit = QLabel("FPS")
        fps_unit.setStyleSheet(f"color: {THEME_COLORS['text_secondary']}; font-size: 14px;")

        fps_layout.addWidget(fps_label)
        fps_layout.addWidget(self.fps_spin)
        fps_layout.addWidget(fps_unit)
        fps_layout.addStretch()

        section_layout.addLayout(fps_layout)

        # 自动曝光
        self.auto_exposure = QCheckBox("🔆 启用自动曝光")
        self.auto_exposure.setChecked(True)
        self.auto_exposure.setStyleSheet(f"""
            QCheckBox {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                padding: 8px;
            }}
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                background-color: {THEME_COLORS["dark_bg_input"]};
            }}
            QCheckBox::indicator:checked {{
                border: 2px solid {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["primary"]};
            }}
        """)
        section_layout.addWidget(self.auto_exposure)

        return section

    def _get_modern_input_style(self):
        """获取现代化输入框样式"""
        return f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QSpinBox:focus {{
                border-color: {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["dark_bg_secondary"]};
            }}
            QSpinBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                width: 20px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _refresh_devices(self):
        """刷新相机设备列表"""
        try:
            import cv2
            # 检测可用的相机设备
            available_cameras = []
            for i in range(10):
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    available_cameras.append(i)
                    cap.release()

            if available_cameras:
                QMessageBox.information(self, "设备检测", f"检测到相机设备: {', '.join(map(str, available_cameras))}")
                # 设置第一个可用设备
                self.camera_index.setValue(available_cameras[0])
            else:
                QMessageBox.warning(self, "设备检测", "未检测到可用的相机设备")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"设备检测失败: {str(e)}")

    def setup_connections(self):
        self.camera_index.valueChanged.connect(lambda v: self._emit_parameter_changed("camera_index", v))
        self.width_spin.valueChanged.connect(lambda v: self._emit_parameter_changed("width", v))
        self.height_spin.valueChanged.connect(lambda v: self._emit_parameter_changed("height", v))
        self.fps_spin.valueChanged.connect(lambda v: self._emit_parameter_changed("fps", v))
        self.auto_exposure.toggled.connect(lambda v: self._emit_parameter_changed("auto_exposure", v))

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class EdgeDetectionConfigWidget(BaseAlgorithmConfigWidget):
    """边缘检测配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("🔍 边缘检测配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 18px;
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid {THEME_COLORS["primary"]};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)

        # 主配置区域
        config_widget = QWidget()
        config_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)

        # 输入源选择（现代化）
        input_section = self._create_input_section()
        config_layout.addWidget(input_section)

        # Canny参数区域
        canny_section = self._create_canny_section()
        config_layout.addWidget(canny_section)

        # 预处理区域
        preprocess_section = self._create_preprocess_section()
        config_layout.addWidget(preprocess_section)

        layout.addWidget(config_widget)

        # 操作按钮
        preview_btn = ModernButton("🎯 预览效果", ModernButton.PRIMARY)
        preview_btn.setMinimumHeight(45)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def _create_input_section(self):
        """创建输入源选择区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📥 输入源选择")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 输入选择器
        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        self.input_selector.setMinimumHeight(40)
        self.input_selector.setStyleSheet(self._get_modern_combo_style())
        section_layout.addWidget(self.input_selector)

        return section

    def _create_canny_section(self):
        """创建Canny参数区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("⚙️ Canny边缘检测")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 阈值设置
        threshold_layout = QHBoxLayout()
        threshold_layout.setSpacing(15)

        # 低阈值
        low_label = QLabel("低阈值:")
        low_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.low_threshold = QSpinBox()
        self.low_threshold.setRange(0, 255)
        self.low_threshold.setValue(50)
        self.low_threshold.setMinimumWidth(100)
        self.low_threshold.setStyleSheet(self._get_modern_input_style())

        # 高阈值
        high_label = QLabel("高阈值:")
        high_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.high_threshold = QSpinBox()
        self.high_threshold.setRange(0, 255)
        self.high_threshold.setValue(150)
        self.high_threshold.setMinimumWidth(100)
        self.high_threshold.setStyleSheet(self._get_modern_input_style())

        threshold_layout.addWidget(low_label)
        threshold_layout.addWidget(self.low_threshold)
        threshold_layout.addWidget(high_label)
        threshold_layout.addWidget(self.high_threshold)
        threshold_layout.addStretch()

        section_layout.addLayout(threshold_layout)

        # 核大小和L2梯度
        options_layout = QHBoxLayout()
        options_layout.setSpacing(15)

        # 核大小
        kernel_label = QLabel("核大小:")
        kernel_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.aperture_size = QComboBox()
        self.aperture_size.addItems(["3", "5", "7"])
        self.aperture_size.setCurrentText("3")
        self.aperture_size.setMinimumWidth(80)
        self.aperture_size.setStyleSheet(self._get_modern_combo_style())

        # L2梯度
        self.l2_gradient = QCheckBox("🔬 使用L2梯度")
        self.l2_gradient.setStyleSheet(self._get_modern_checkbox_style())

        options_layout.addWidget(kernel_label)
        options_layout.addWidget(self.aperture_size)
        options_layout.addWidget(self.l2_gradient)
        options_layout.addStretch()

        section_layout.addLayout(options_layout)

        return section

    def _create_preprocess_section(self):
        """创建预处理区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🔧 预处理设置")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 高斯模糊设置
        blur_layout = QHBoxLayout()
        blur_layout.setSpacing(15)

        # 高斯模糊开关
        self.gaussian_blur = QCheckBox("🌀 启用高斯模糊")
        self.gaussian_blur.setChecked(True)
        self.gaussian_blur.setStyleSheet(self._get_modern_checkbox_style())

        # 模糊核大小
        kernel_label = QLabel("核大小:")
        kernel_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.blur_kernel = QSpinBox()
        self.blur_kernel.setRange(1, 15)
        self.blur_kernel.setValue(5)
        self.blur_kernel.setSingleStep(2)
        self.blur_kernel.setMinimumWidth(100)
        self.blur_kernel.setStyleSheet(self._get_modern_input_style())

        blur_layout.addWidget(self.gaussian_blur)
        blur_layout.addWidget(kernel_label)
        blur_layout.addWidget(self.blur_kernel)
        blur_layout.addStretch()

        section_layout.addLayout(blur_layout)

        return section

    def _get_modern_input_style(self):
        """获取现代化输入框样式"""
        return f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QSpinBox:focus {{
                border-color: {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["dark_bg_secondary"]};
            }}
            QSpinBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                width: 20px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _get_modern_combo_style(self):
        """获取现代化下拉框样式"""
        return f"""
            QComboBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QComboBox:focus {{
                border-color: {THEME_COLORS["primary"]};
            }}
            QComboBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                selection-background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _get_modern_checkbox_style(self):
        """获取现代化复选框样式"""
        return f"""
            QCheckBox {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                padding: 8px;
            }}
            QCheckBox::indicator {{
                width: 20px;
                height: 20px;
                border-radius: 4px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                background-color: {THEME_COLORS["dark_bg_input"]};
            }}
            QCheckBox::indicator:checked {{
                border: 2px solid {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.low_threshold.valueChanged.connect(lambda v: self._emit_parameter_changed("low_threshold", v))
        self.high_threshold.valueChanged.connect(lambda v: self._emit_parameter_changed("high_threshold", v))
        self.aperture_size.currentTextChanged.connect(lambda v: self._emit_parameter_changed("aperture_size", int(v)))
        self.l2_gradient.toggled.connect(lambda v: self._emit_parameter_changed("l2_gradient", v))
        self.gaussian_blur.toggled.connect(lambda v: self._emit_parameter_changed("gaussian_blur", v))
        self.blur_kernel.valueChanged.connect(lambda v: self._emit_parameter_changed("blur_kernel", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class GaussianBlurConfigWidget(BaseAlgorithmConfigWidget):
    """高斯模糊配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("🌀 高斯模糊配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 18px;
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid {THEME_COLORS["primary"]};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)

        # 主配置区域
        config_widget = QWidget()
        config_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)

        # 输入源选择
        input_section = self._create_input_section()
        config_layout.addWidget(input_section)

        # 模糊参数区域
        blur_section = self._create_blur_section()
        config_layout.addWidget(blur_section)

        layout.addWidget(config_widget)

        # 操作按钮
        preview_btn = ModernButton("🎯 预览效果", ModernButton.PRIMARY)
        preview_btn.setMinimumHeight(45)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def _create_input_section(self):
        """创建输入源选择区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📥 输入源选择")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 输入选择器
        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        self.input_selector.setMinimumHeight(40)
        self.input_selector.setStyleSheet(self._get_modern_combo_style())
        section_layout.addWidget(self.input_selector)

        return section

    def _create_blur_section(self):
        """创建模糊参数区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(15)

        # 区域标题
        title = QLabel("⚙️ 高斯模糊参数")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 核大小设置
        kernel_layout = QHBoxLayout()
        kernel_layout.setSpacing(15)

        # 核大小X
        x_label = QLabel("核大小X:")
        x_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.kernel_x = QSpinBox()
        self.kernel_x.setRange(1, 31)
        self.kernel_x.setValue(15)
        self.kernel_x.setSingleStep(2)
        self.kernel_x.setMinimumWidth(100)
        self.kernel_x.setStyleSheet(self._get_modern_input_style())

        # 核大小Y
        y_label = QLabel("核大小Y:")
        y_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.kernel_y = QSpinBox()
        self.kernel_y.setRange(1, 31)
        self.kernel_y.setValue(15)
        self.kernel_y.setSingleStep(2)
        self.kernel_y.setMinimumWidth(100)
        self.kernel_y.setStyleSheet(self._get_modern_input_style())

        kernel_layout.addWidget(x_label)
        kernel_layout.addWidget(self.kernel_x)
        kernel_layout.addWidget(y_label)
        kernel_layout.addWidget(self.kernel_y)
        kernel_layout.addStretch()

        section_layout.addLayout(kernel_layout)

        # Sigma设置
        sigma_layout = QHBoxLayout()
        sigma_layout.setSpacing(15)

        # Sigma X
        sigma_x_label = QLabel("Sigma X:")
        sigma_x_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.sigma_x = QDoubleSpinBox()
        self.sigma_x.setRange(0.0, 10.0)
        self.sigma_x.setValue(0.0)
        self.sigma_x.setDecimals(2)
        self.sigma_x.setSingleStep(0.1)
        self.sigma_x.setMinimumWidth(100)
        self.sigma_x.setStyleSheet(self._get_modern_double_input_style())

        # Sigma Y
        sigma_y_label = QLabel("Sigma Y:")
        sigma_y_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.sigma_y = QDoubleSpinBox()
        self.sigma_y.setRange(0.0, 10.0)
        self.sigma_y.setValue(0.0)
        self.sigma_y.setDecimals(2)
        self.sigma_y.setSingleStep(0.1)
        self.sigma_y.setMinimumWidth(100)
        self.sigma_y.setStyleSheet(self._get_modern_double_input_style())

        sigma_layout.addWidget(sigma_x_label)
        sigma_layout.addWidget(self.sigma_x)
        sigma_layout.addWidget(sigma_y_label)
        sigma_layout.addWidget(self.sigma_y)
        sigma_layout.addStretch()

        section_layout.addLayout(sigma_layout)

        return section

    def _get_modern_input_style(self):
        """获取现代化输入框样式"""
        return f"""
            QSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QSpinBox:focus {{
                border-color: {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["dark_bg_secondary"]};
            }}
            QSpinBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QSpinBox::up-button, QSpinBox::down-button {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                width: 20px;
            }}
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _get_modern_double_input_style(self):
        """获取现代化双精度输入框样式"""
        return f"""
            QDoubleSpinBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QDoubleSpinBox:focus {{
                border-color: {THEME_COLORS["primary"]};
                background-color: {THEME_COLORS["dark_bg_secondary"]};
            }}
            QDoubleSpinBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 4px;
                width: 20px;
            }}
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {{
                background-color: {THEME_COLORS["primary"]};
            }}
        """

    def _get_modern_combo_style(self):
        """获取现代化下拉框样式"""
        return f"""
            QComboBox {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 2px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-height: 20px;
            }}
            QComboBox:focus {{
                border-color: {THEME_COLORS["primary"]};
            }}
            QComboBox:hover {{
                border-color: {THEME_COLORS["primary_hover"]};
            }}
            QComboBox::drop-down {{
                border: none;
                width: 20px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                selection-background-color: {THEME_COLORS["primary"]};
            }}
        """

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.kernel_x.valueChanged.connect(lambda v: self._emit_parameter_changed("kernel_size_x", v))
        self.kernel_y.valueChanged.connect(lambda v: self._emit_parameter_changed("kernel_size_y", v))
        self.sigma_x.valueChanged.connect(lambda v: self._emit_parameter_changed("sigma_x", v))
        self.sigma_y.valueChanged.connect(lambda v: self._emit_parameter_changed("sigma_y", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class TemplateMatchingConfigWidget(BaseAlgorithmConfigWidget):
    """模板匹配配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # 模板设置
        template_group = QGroupBox("模板设置")
        template_layout = QFormLayout(template_group)

        # 模板文件选择
        template_file_layout = QHBoxLayout()
        self.template_path = QLineEdit()
        self.template_path.setPlaceholderText("选择模板图像...")
        template_browse_btn = ModernButton("浏览", ModernButton.SECONDARY)
        template_file_layout.addWidget(self.template_path)
        template_file_layout.addWidget(template_browse_btn)
        template_layout.addRow("模板文件:", template_file_layout)

        # 匹配方法
        self.match_method = QComboBox()
        self.match_method.addItems([
            "TM_CCOEFF", "TM_CCOEFF_NORMED",
            "TM_CCORR", "TM_CCORR_NORMED",
            "TM_SQDIFF", "TM_SQDIFF_NORMED"
        ])
        self.match_method.setCurrentText("TM_CCOEFF_NORMED")
        template_layout.addRow("匹配方法:", self.match_method)

        # 匹配阈值
        self.threshold = QDoubleSpinBox()
        self.threshold.setRange(0.0, 1.0)
        self.threshold.setValue(0.8)
        self.threshold.setDecimals(3)
        self.threshold.setSingleStep(0.01)
        template_layout.addRow("匹配阈值:", self.threshold)

        # 最大匹配数
        self.max_matches = QSpinBox()
        self.max_matches.setRange(1, 100)
        self.max_matches.setValue(10)
        template_layout.addRow("最大匹配数:", self.max_matches)

        layout.addWidget(template_group)

        # 多尺度匹配
        scale_group = QGroupBox("多尺度匹配")
        scale_layout = QFormLayout(scale_group)

        self.enable_multi_scale = QCheckBox("启用多尺度匹配")
        scale_layout.addRow(self.enable_multi_scale)

        # 尺度范围
        scale_range_layout = QHBoxLayout()
        self.scale_min = QDoubleSpinBox()
        self.scale_min.setRange(0.1, 2.0)
        self.scale_min.setValue(0.5)
        self.scale_min.setDecimals(2)
        self.scale_max = QDoubleSpinBox()
        self.scale_max.setRange(0.1, 5.0)
        self.scale_max.setValue(2.0)
        self.scale_max.setDecimals(2)

        scale_range_layout.addWidget(QLabel("最小:"))
        scale_range_layout.addWidget(self.scale_min)
        scale_range_layout.addWidget(QLabel("最大:"))
        scale_range_layout.addWidget(self.scale_max)
        scale_layout.addRow("尺度范围:", scale_range_layout)

        # 尺度步长
        self.scale_step = QDoubleSpinBox()
        self.scale_step.setRange(0.01, 0.5)
        self.scale_step.setValue(0.1)
        self.scale_step.setDecimals(3)
        scale_layout.addRow("尺度步长:", self.scale_step)

        layout.addWidget(scale_group)

        # 预览按钮
        preview_btn = ModernButton("预览匹配", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.template_path.textChanged.connect(lambda v: self._emit_parameter_changed("template_path", v))
        self.match_method.currentTextChanged.connect(lambda v: self._emit_parameter_changed("method", v))
        self.threshold.valueChanged.connect(lambda v: self._emit_parameter_changed("threshold", v))
        self.max_matches.valueChanged.connect(lambda v: self._emit_parameter_changed("max_matches", v))
        self.enable_multi_scale.toggled.connect(lambda v: self._emit_parameter_changed("enable_multi_scale", v))
        self.scale_min.valueChanged.connect(lambda v: self._emit_parameter_changed("scale_min", v))
        self.scale_max.valueChanged.connect(lambda v: self._emit_parameter_changed("scale_max", v))
        self.scale_step.valueChanged.connect(lambda v: self._emit_parameter_changed("scale_step", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class ContourDetectionConfigWidget(BaseAlgorithmConfigWidget):
    """轮廓检测配置界面"""

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # 预处理参数
        preprocess_group = QGroupBox("预处理")
        preprocess_layout = QFormLayout(preprocess_group)

        # 阈值方法
        self.threshold_method = QComboBox()
        self.threshold_method.addItems([
            "binary", "binary_inv", "adaptive_mean", "adaptive_gaussian"
        ])
        preprocess_layout.addRow("阈值方法:", self.threshold_method)

        # 阈值
        self.threshold_value = QSpinBox()
        self.threshold_value.setRange(0, 255)
        self.threshold_value.setValue(127)
        preprocess_layout.addRow("阈值:", self.threshold_value)

        # 最大值
        self.max_value = QSpinBox()
        self.max_value.setRange(0, 255)
        self.max_value.setValue(255)
        preprocess_layout.addRow("最大值:", self.max_value)

        layout.addWidget(preprocess_group)

        # 轮廓检测参数
        contour_group = QGroupBox("轮廓检测")
        contour_layout = QFormLayout(contour_group)

        # 检索模式
        self.retrieval_mode = QComboBox()
        self.retrieval_mode.addItems(["external", "tree", "ccomp", "list"])
        self.retrieval_mode.setCurrentText("external")
        contour_layout.addRow("检索模式:", self.retrieval_mode)

        # 近似方法
        self.approximation_method = QComboBox()
        self.approximation_method.addItems(["simple", "tc89_l1", "tc89_kcos"])
        self.approximation_method.setCurrentText("simple")
        contour_layout.addRow("近似方法:", self.approximation_method)

        layout.addWidget(contour_group)

        # 过滤参数
        filter_group = QGroupBox("轮廓过滤")
        filter_layout = QFormLayout(filter_group)

        # 面积过滤
        self.filter_by_area = QCheckBox("按面积过滤")
        self.filter_by_area.setChecked(True)
        filter_layout.addRow(self.filter_by_area)

        area_range_layout = QHBoxLayout()
        self.min_area = QSpinBox()
        self.min_area.setRange(0, 100000)
        self.min_area.setValue(100)
        self.max_area = QSpinBox()
        self.max_area.setRange(0, 1000000)
        self.max_area.setValue(100000)

        area_range_layout.addWidget(QLabel("最小:"))
        area_range_layout.addWidget(self.min_area)
        area_range_layout.addWidget(QLabel("最大:"))
        area_range_layout.addWidget(self.max_area)
        filter_layout.addRow("面积范围:", area_range_layout)

        # 周长过滤
        self.filter_by_perimeter = QCheckBox("按周长过滤")
        filter_layout.addRow(self.filter_by_perimeter)

        self.min_perimeter = QSpinBox()
        self.min_perimeter.setRange(0, 10000)
        self.min_perimeter.setValue(0)
        filter_layout.addRow("最小周长:", self.min_perimeter)

        layout.addWidget(filter_group)

        # 预览按钮
        preview_btn = ModernButton("预览轮廓", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))
        self.threshold_method.currentTextChanged.connect(lambda v: self._emit_parameter_changed("threshold_method", v))
        self.threshold_value.valueChanged.connect(lambda v: self._emit_parameter_changed("threshold_value", v))
        self.max_value.valueChanged.connect(lambda v: self._emit_parameter_changed("max_value", v))
        self.retrieval_mode.currentTextChanged.connect(lambda v: self._emit_parameter_changed("retrieval_mode", v))
        self.approximation_method.currentTextChanged.connect(lambda v: self._emit_parameter_changed("approximation_method", v))
        self.filter_by_area.toggled.connect(lambda v: self._emit_parameter_changed("filter_by_area", v))
        self.min_area.valueChanged.connect(lambda v: self._emit_parameter_changed("min_area", v))
        self.max_area.valueChanged.connect(lambda v: self._emit_parameter_changed("max_area", v))
        self.filter_by_perimeter.toggled.connect(lambda v: self._emit_parameter_changed("filter_by_perimeter", v))
        self.min_perimeter.valueChanged.connect(lambda v: self._emit_parameter_changed("min_perimeter", v))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)


class ColorDetectionConfigWidget(BaseAlgorithmConfigWidget):
    """颜色检测配置界面"""

    def __init__(self, algorithm_name: str, parent=None):
        self.color_ranges = []  # 存储多个颜色范围
        self.current_color_index = 0
        super().__init__(algorithm_name, parent)

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("🎨 颜色检测配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 18px;
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid {THEME_COLORS["primary"]};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)

        # 主配置区域
        config_widget = QWidget()
        config_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)

        # 颜色空间选择
        config_layout.addWidget(self._create_color_space_section())

        # 颜色范围设置
        config_layout.addWidget(self._create_color_range_section())

        # 后处理设置
        config_layout.addWidget(self._create_postprocess_section())

        # 区域过滤设置
        config_layout.addWidget(self._create_area_filter_section())

        layout.addWidget(config_widget)

        # 预览和操作按钮
        layout.addWidget(self._create_action_buttons())

    def _create_color_space_section(self):
        """创建颜色空间选择区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🌈 颜色空间")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 颜色空间选择
        space_layout = QHBoxLayout()
        space_layout.setSpacing(15)

        self.color_space = QComboBox()
        self.color_space.addItems(["HSV", "RGB", "LAB", "GRAY"])
        self.color_space.setCurrentText("HSV")
        self.color_space.setStyleSheet(self._get_modern_combo_style())
        self.color_space.currentTextChanged.connect(self._on_color_space_changed)

        space_label = QLabel("颜色空间:")
        space_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        space_layout.addWidget(space_label)
        space_layout.addWidget(self.color_space)
        space_layout.addStretch()

        section_layout.addLayout(space_layout)
        return section

    def _create_color_range_section(self):
        """创建颜色范围设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title_layout = QHBoxLayout()
        title = QLabel("🎯 颜色范围")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)

        # 添加/删除颜色按钮
        add_color_btn = QPushButton("+ 添加颜色")
        add_color_btn.setStyleSheet(self._get_modern_button_style())
        add_color_btn.clicked.connect(self._add_color_range)

        remove_color_btn = QPushButton("- 删除颜色")
        remove_color_btn.setStyleSheet(self._get_modern_button_style())
        remove_color_btn.clicked.connect(self._remove_color_range)

        title_layout.addWidget(title)
        title_layout.addStretch()
        title_layout.addWidget(add_color_btn)
        title_layout.addWidget(remove_color_btn)

        section_layout.addLayout(title_layout)

        # 颜色选择标签页
        self.color_tabs = QTabWidget()
        self.color_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                background-color: {THEME_COLORS["dark_bg_primary"]};
                border-radius: 8px;
            }}
            QTabBar::tab {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                color: {THEME_COLORS["text_primary"]};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }}
            QTabBar::tab:selected {{
                background-color: {THEME_COLORS["primary"]};
                color: white;
            }}
        """)

        # 添加默认颜色范围
        self._add_color_range()

        section_layout.addWidget(self.color_tabs)
        return section

    def _create_color_range_tab(self, index: int):
        """创建单个颜色范围标签页"""
        tab = QWidget()
        tab_layout = QVBoxLayout(tab)
        tab_layout.setSpacing(15)

        # HSV范围设置
        hsv_group = QGroupBox("HSV 颜色范围")
        hsv_group.setStyleSheet(f"""
            QGroupBox {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 14px;
                font-weight: bold;
                border: 2px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
            }}
        """)
        hsv_layout = QGridLayout(hsv_group)

        # H (色调) 范围
        hsv_layout.addWidget(QLabel("H (色调):"), 0, 0)
        h_min_slider = QSlider(Qt.Orientation.Horizontal)
        h_min_slider.setRange(0, 179)
        h_min_slider.setValue(0)
        h_min_slider.setStyleSheet(self._get_modern_input_style())
        h_min_label = QLabel("0")
        h_min_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        h_min_slider.valueChanged.connect(lambda v: h_min_label.setText(str(v)))

        h_max_slider = QSlider(Qt.Orientation.Horizontal)
        h_max_slider.setRange(0, 179)
        h_max_slider.setValue(10)
        h_max_slider.setStyleSheet(self._get_modern_input_style())
        h_max_label = QLabel("10")
        h_max_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        h_max_slider.valueChanged.connect(lambda v: h_max_label.setText(str(v)))

        hsv_layout.addWidget(h_min_slider, 0, 1)
        hsv_layout.addWidget(h_min_label, 0, 2)
        hsv_layout.addWidget(QLabel("~"), 0, 3)
        hsv_layout.addWidget(h_max_slider, 0, 4)
        hsv_layout.addWidget(h_max_label, 0, 5)

        # S (饱和度) 范围
        hsv_layout.addWidget(QLabel("S (饱和度):"), 1, 0)
        s_min_slider = QSlider(Qt.Orientation.Horizontal)
        s_min_slider.setRange(0, 255)
        s_min_slider.setValue(50)
        s_min_slider.setStyleSheet(self._get_modern_slider_style())
        s_min_label = QLabel("50")
        s_min_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        s_min_slider.valueChanged.connect(lambda v: s_min_label.setText(str(v)))

        s_max_slider = QSlider(Qt.Orientation.Horizontal)
        s_max_slider.setRange(0, 255)
        s_max_slider.setValue(255)
        s_max_slider.setStyleSheet(self._get_modern_slider_style())
        s_max_label = QLabel("255")
        s_max_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        s_max_slider.valueChanged.connect(lambda v: s_max_label.setText(str(v)))

        hsv_layout.addWidget(s_min_slider, 1, 1)
        hsv_layout.addWidget(s_min_label, 1, 2)
        hsv_layout.addWidget(QLabel("~"), 1, 3)
        hsv_layout.addWidget(s_max_slider, 1, 4)
        hsv_layout.addWidget(s_max_label, 1, 5)

        # V (明度) 范围
        hsv_layout.addWidget(QLabel("V (明度):"), 2, 0)
        v_min_slider = QSlider(Qt.Orientation.Horizontal)
        v_min_slider.setRange(0, 255)
        v_min_slider.setValue(50)
        v_min_slider.setStyleSheet(self._get_modern_slider_style())
        v_min_label = QLabel("50")
        v_min_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        v_min_slider.valueChanged.connect(lambda v: v_min_label.setText(str(v)))

        v_max_slider = QSlider(Qt.Orientation.Horizontal)
        v_max_slider.setRange(0, 255)
        v_max_slider.setValue(255)
        v_max_slider.setStyleSheet(self._get_modern_slider_style())
        v_max_label = QLabel("255")
        v_max_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        v_max_slider.valueChanged.connect(lambda v: v_max_label.setText(str(v)))

        hsv_layout.addWidget(v_min_slider, 2, 1)
        hsv_layout.addWidget(v_min_label, 2, 2)
        hsv_layout.addWidget(QLabel("~"), 2, 3)
        hsv_layout.addWidget(v_max_slider, 2, 4)
        hsv_layout.addWidget(v_max_label, 2, 5)

        tab_layout.addWidget(hsv_group)

        # 存储滑块引用
        setattr(tab, 'h_min_slider', h_min_slider)
        setattr(tab, 'h_max_slider', h_max_slider)
        setattr(tab, 's_min_slider', s_min_slider)
        setattr(tab, 's_max_slider', s_max_slider)
        setattr(tab, 'v_min_slider', v_min_slider)
        setattr(tab, 'v_max_slider', v_max_slider)

        # 预设颜色按钮
        preset_group = QGroupBox("预设颜色")
        preset_group.setStyleSheet(hsv_group.styleSheet())
        preset_layout = QGridLayout(preset_group)

        preset_colors = [
            ("红色", [0, 10, 50, 255, 50, 255]),
            ("绿色", [40, 80, 50, 255, 50, 255]),
            ("蓝色", [100, 130, 50, 255, 50, 255]),
            ("黄色", [20, 30, 50, 255, 50, 255]),
            ("橙色", [10, 20, 50, 255, 50, 255]),
            ("紫色", [130, 160, 50, 255, 50, 255])
        ]

        for i, (name, values) in enumerate(preset_colors):
            btn = QPushButton(name)
            btn.setStyleSheet(self._get_modern_button_style())
            btn.clicked.connect(lambda checked, v=values, t=tab: self._apply_preset_color(v, t))
            preset_layout.addWidget(btn, i // 3, i % 3)

        tab_layout.addWidget(preset_group)
        return tab

    def _add_color_range(self):
        """添加新的颜色范围"""
        index = len(self.color_ranges)
        tab = self._create_color_range_tab(index)
        self.color_tabs.addTab(tab, f"颜色 {index + 1}")

        # 添加到颜色范围列表
        self.color_ranges.append({
            'lower': [0, 50, 50],
            'upper': [10, 255, 255]
        })

    def _remove_color_range(self):
        """删除当前颜色范围"""
        if self.color_tabs.count() > 1:  # 至少保留一个
            current_index = self.color_tabs.currentIndex()
            self.color_tabs.removeTab(current_index)
            if current_index < len(self.color_ranges):
                self.color_ranges.pop(current_index)

    def _apply_preset_color(self, values, tab):
        """应用预设颜色"""
        tab.h_min_slider.setValue(values[0])
        tab.h_max_slider.setValue(values[1])
        tab.s_min_slider.setValue(values[2])
        tab.s_max_slider.setValue(values[3])
        tab.v_min_slider.setValue(values[4])
        tab.v_max_slider.setValue(values[5])

    def _create_postprocess_section(self):
        """创建后处理设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🔧 后处理")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 形态学处理
        morph_layout = QHBoxLayout()
        morph_layout.setSpacing(15)

        self.morphology_enabled = QCheckBox("🔄 启用形态学处理")
        self.morphology_enabled.setChecked(True)
        self.morphology_enabled.setStyleSheet(self._get_modern_checkbox_style())

        kernel_label = QLabel("核大小:")
        kernel_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.morphology_kernel = QSlider(Qt.Orientation.Horizontal)
        self.morphology_kernel.setRange(3, 15)
        self.morphology_kernel.setValue(5)
        self.morphology_kernel.setStyleSheet(self._get_modern_slider_style())

        self.kernel_value_label = QLabel("5")
        self.kernel_value_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        self.morphology_kernel.valueChanged.connect(lambda v: self.kernel_value_label.setText(str(v)))

        morph_layout.addWidget(self.morphology_enabled)
        morph_layout.addWidget(kernel_label)
        morph_layout.addWidget(self.morphology_kernel)
        morph_layout.addWidget(self.kernel_value_label)
        morph_layout.addStretch()

        section_layout.addLayout(morph_layout)
        return section

    def _create_area_filter_section(self):
        """创建区域过滤设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📏 区域过滤")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 最小面积
        min_area_layout = QHBoxLayout()
        min_area_layout.setSpacing(15)

        min_area_label = QLabel("最小面积:")
        min_area_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.min_area = QSlider(Qt.Orientation.Horizontal)
        self.min_area.setRange(10, 1000)
        self.min_area.setValue(100)
        self.min_area.setStyleSheet(self._get_modern_slider_style())

        self.min_area_label = QLabel("100")
        self.min_area_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 50px;")
        self.min_area.valueChanged.connect(lambda v: self.min_area_label.setText(str(v)))

        min_area_layout.addWidget(min_area_label)
        min_area_layout.addWidget(self.min_area)
        min_area_layout.addWidget(self.min_area_label)

        # 最大面积
        max_area_layout = QHBoxLayout()
        max_area_layout.setSpacing(15)

        max_area_label = QLabel("最大面积:")
        max_area_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.max_area = QSlider(Qt.Orientation.Horizontal)
        self.max_area.setRange(1000, 50000)
        self.max_area.setValue(10000)
        self.max_area.setStyleSheet(self._get_modern_slider_style())

        self.max_area_label = QLabel("10000")
        self.max_area_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 50px;")
        self.max_area.valueChanged.connect(lambda v: self.max_area_label.setText(str(v)))

        max_area_layout.addWidget(max_area_label)
        max_area_layout.addWidget(self.max_area)
        max_area_layout.addWidget(self.max_area_label)

        section_layout.addLayout(min_area_layout)
        section_layout.addLayout(max_area_layout)
        return section

    def _on_color_space_changed(self, color_space):
        """颜色空间改变时的处理"""
        # 更新参数并触发预览
        self._emit_parameter_changed("color_space", color_space)

    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        # 收集所有颜色范围
        color_ranges = []
        for i in range(self.color_tabs.count()):
            tab = self.color_tabs.widget(i)
            color_range = {
                'lower': [
                    tab.h_min_slider.value(),
                    tab.s_min_slider.value(),
                    tab.v_min_slider.value()
                ],
                'upper': [
                    tab.h_max_slider.value(),
                    tab.s_max_slider.value(),
                    tab.v_max_slider.value()
                ]
            }
            color_ranges.append(color_range)

        return {
            "color_space": self.color_space.currentText(),
            "color_ranges": color_ranges,
            "morphology_enabled": self.morphology_enabled.isChecked(),
            "morphology_kernel_size": self.morphology_kernel.value(),
            "min_area": self.min_area.value(),
            "max_area": self.max_area.value()
        }

    def set_parameters(self, parameters: Dict[str, Any]):
        """设置参数"""
        if "color_space" in parameters:
            self.color_space.setCurrentText(parameters["color_space"])

        if "color_ranges" in parameters:
            # 清除现有标签页
            while self.color_tabs.count() > 0:
                self.color_tabs.removeTab(0)
            self.color_ranges.clear()

            # 添加新的颜色范围
            for i, color_range in enumerate(parameters["color_ranges"]):
                tab = self._create_color_range_tab(i)
                self.color_tabs.addTab(tab, f"颜色 {i + 1}")

                # 设置滑块值
                if 'lower' in color_range and 'upper' in color_range:
                    lower = color_range['lower']
                    upper = color_range['upper']
                    if len(lower) >= 3 and len(upper) >= 3:
                        tab.h_min_slider.setValue(lower[0])
                        tab.s_min_slider.setValue(lower[1])
                        tab.v_min_slider.setValue(lower[2])
                        tab.h_max_slider.setValue(upper[0])
                        tab.s_max_slider.setValue(upper[1])
                        tab.v_max_slider.setValue(upper[2])

                self.color_ranges.append(color_range)

        if "morphology_enabled" in parameters:
            self.morphology_enabled.setChecked(parameters["morphology_enabled"])

        if "morphology_kernel_size" in parameters:
            self.morphology_kernel.setValue(parameters["morphology_kernel_size"])

        if "min_area" in parameters:
            self.min_area.setValue(parameters["min_area"])

        if "max_area" in parameters:
            self.max_area.setValue(parameters["max_area"])


class ThresholdConfigWidget(BaseAlgorithmConfigWidget):
    """阈值处理配置界面"""

    def __init__(self, algorithm_name: str, parent=None):
        super().__init__(algorithm_name, parent)

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("🎯 阈值处理配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 18px;
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid {THEME_COLORS["primary"]};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)

        # 主配置区域
        config_widget = QWidget()
        config_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)

        # 阈值类型选择
        config_layout.addWidget(self._create_threshold_type_section())

        # 阈值参数设置
        config_layout.addWidget(self._create_threshold_params_section())

        # 自适应阈值设置
        config_layout.addWidget(self._create_adaptive_section())

        # 后处理设置
        config_layout.addWidget(self._create_postprocess_section())

        layout.addWidget(config_widget)

        # 预览和操作按钮
        layout.addWidget(self._create_action_buttons())

    def _create_threshold_type_section(self):
        """创建阈值类型选择区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("📊 阈值类型")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 阈值类型选择
        type_layout = QHBoxLayout()
        type_layout.setSpacing(15)

        self.threshold_type = QComboBox()
        self.threshold_type.addItems([
            "THRESH_BINARY (二值化)",
            "THRESH_BINARY_INV (反二值化)",
            "THRESH_TRUNC (截断)",
            "THRESH_TOZERO (阈值化为零)",
            "THRESH_TOZERO_INV (反阈值化为零)"
        ])
        self.threshold_type.setCurrentIndex(0)
        self.threshold_type.setStyleSheet(self._get_modern_combo_style())
        self.threshold_type.currentTextChanged.connect(self._on_threshold_type_changed)

        type_label = QLabel("阈值类型:")
        type_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        type_layout.addWidget(type_label)
        type_layout.addWidget(self.threshold_type)
        type_layout.addStretch()

        section_layout.addLayout(type_layout)

        # 阈值方法选择
        method_layout = QHBoxLayout()
        method_layout.setSpacing(15)

        self.threshold_method = QComboBox()
        self.threshold_method.addItems([
            "固定阈值",
            "自适应阈值 (均值)",
            "自适应阈值 (高斯)",
            "OTSU自动阈值"
        ])
        self.threshold_method.setCurrentIndex(0)
        self.threshold_method.setStyleSheet(self._get_modern_combo_style())
        self.threshold_method.currentTextChanged.connect(self._on_threshold_method_changed)

        method_label = QLabel("阈值方法:")
        method_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        method_layout.addWidget(method_label)
        method_layout.addWidget(self.threshold_method)
        method_layout.addStretch()

        section_layout.addLayout(method_layout)
        return section

    def _create_threshold_params_section(self):
        """创建阈值参数设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("⚙️ 阈值参数")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 阈值设置
        threshold_layout = QHBoxLayout()
        threshold_layout.setSpacing(15)

        threshold_label = QLabel("阈值:")
        threshold_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.threshold_value = QSlider(Qt.Orientation.Horizontal)
        self.threshold_value.setRange(0, 255)
        self.threshold_value.setValue(127)
        self.threshold_value.setStyleSheet(self._get_modern_slider_style())

        self.threshold_value_label = QLabel("127")
        self.threshold_value_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 40px;")
        self.threshold_value.valueChanged.connect(lambda v: self.threshold_value_label.setText(str(v)))

        threshold_layout.addWidget(threshold_label)
        threshold_layout.addWidget(self.threshold_value)
        threshold_layout.addWidget(self.threshold_value_label)

        # 最大值设置
        maxval_layout = QHBoxLayout()
        maxval_layout.setSpacing(15)

        maxval_label = QLabel("最大值:")
        maxval_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.max_value = QSlider(Qt.Orientation.Horizontal)
        self.max_value.setRange(1, 255)
        self.max_value.setValue(255)
        self.max_value.setStyleSheet(self._get_modern_slider_style())

        self.max_value_label = QLabel("255")
        self.max_value_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 40px;")
        self.max_value.valueChanged.connect(lambda v: self.max_value_label.setText(str(v)))

        maxval_layout.addWidget(maxval_label)
        maxval_layout.addWidget(self.max_value)
        maxval_layout.addWidget(self.max_value_label)

        section_layout.addLayout(threshold_layout)
        section_layout.addLayout(maxval_layout)
        return section

    def _create_adaptive_section(self):
        """创建自适应阈值设置区域"""
        self.adaptive_section = QWidget()
        section_layout = QVBoxLayout(self.adaptive_section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🔄 自适应阈值参数")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 块大小设置
        block_layout = QHBoxLayout()
        block_layout.setSpacing(15)

        block_label = QLabel("块大小:")
        block_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.block_size = QSlider(Qt.Orientation.Horizontal)
        self.block_size.setRange(3, 51)
        self.block_size.setValue(11)
        # 确保是奇数
        self.block_size.valueChanged.connect(self._ensure_odd_block_size)
        self.block_size.setStyleSheet(self._get_modern_slider_style())

        self.block_size_label = QLabel("11")
        self.block_size_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 40px;")

        block_layout.addWidget(block_label)
        block_layout.addWidget(self.block_size)
        block_layout.addWidget(self.block_size_label)

        # C常数设置
        c_layout = QHBoxLayout()
        c_layout.setSpacing(15)

        c_label = QLabel("C常数:")
        c_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.c_constant = QSlider(Qt.Orientation.Horizontal)
        self.c_constant.setRange(-20, 20)
        self.c_constant.setValue(2)
        self.c_constant.setStyleSheet(self._get_modern_slider_style())

        self.c_constant_label = QLabel("2")
        self.c_constant_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 40px;")
        self.c_constant.valueChanged.connect(lambda v: self.c_constant_label.setText(str(v)))

        c_layout.addWidget(c_label)
        c_layout.addWidget(self.c_constant)
        c_layout.addWidget(self.c_constant_label)

        section_layout.addLayout(block_layout)
        section_layout.addLayout(c_layout)

        # 默认隐藏自适应参数
        self.adaptive_section.setVisible(False)
        return self.adaptive_section

    def _create_postprocess_section(self):
        """创建后处理设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🔧 后处理")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 形态学处理
        morph_layout = QHBoxLayout()
        morph_layout.setSpacing(15)

        self.morphology_enabled = QCheckBox("🔄 启用形态学处理")
        self.morphology_enabled.setChecked(False)
        self.morphology_enabled.setStyleSheet(self._get_modern_checkbox_style())

        kernel_label = QLabel("核大小:")
        kernel_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.morphology_kernel = QSlider(Qt.Orientation.Horizontal)
        self.morphology_kernel.setRange(3, 15)
        self.morphology_kernel.setValue(5)
        self.morphology_kernel.setStyleSheet(self._get_modern_slider_style())

        self.kernel_value_label = QLabel("5")
        self.kernel_value_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        self.morphology_kernel.valueChanged.connect(lambda v: self.kernel_value_label.setText(str(v)))

        morph_layout.addWidget(self.morphology_enabled)
        morph_layout.addWidget(kernel_label)
        morph_layout.addWidget(self.morphology_kernel)
        morph_layout.addWidget(self.kernel_value_label)
        morph_layout.addStretch()

        # 反转输出
        invert_layout = QHBoxLayout()
        self.invert_output = QCheckBox("🔄 反转输出")
        self.invert_output.setChecked(False)
        self.invert_output.setStyleSheet(self._get_modern_checkbox_style())
        invert_layout.addWidget(self.invert_output)
        invert_layout.addStretch()

        section_layout.addLayout(morph_layout)
        section_layout.addLayout(invert_layout)
        return section

    def _ensure_odd_block_size(self, value):
        """确保块大小为奇数"""
        if value % 2 == 0:
            value += 1
            self.block_size.setValue(value)
        self.block_size_label.setText(str(value))

    def _on_threshold_type_changed(self, threshold_type):
        """阈值类型改变时的处理"""
        self._emit_parameter_changed("threshold_type", threshold_type)

    def _on_threshold_method_changed(self, method):
        """阈值方法改变时的处理"""
        # 根据方法显示/隐藏自适应参数
        is_adaptive = "自适应" in method
        self.adaptive_section.setVisible(is_adaptive)

        # 根据方法启用/禁用固定阈值
        is_fixed = method == "固定阈值"
        self.threshold_value.setEnabled(is_fixed)

        self._emit_parameter_changed("threshold_method", method)

    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        # 获取阈值类型的OpenCV常量值
        threshold_type_map = {
            "THRESH_BINARY (二值化)": 0,  # cv2.THRESH_BINARY
            "THRESH_BINARY_INV (反二值化)": 1,  # cv2.THRESH_BINARY_INV
            "THRESH_TRUNC (截断)": 2,  # cv2.THRESH_TRUNC
            "THRESH_TOZERO (阈值化为零)": 3,  # cv2.THRESH_TOZERO
            "THRESH_TOZERO_INV (反阈值化为零)": 4  # cv2.THRESH_TOZERO_INV
        }

        return {
            "threshold_type": threshold_type_map.get(self.threshold_type.currentText(), 0),
            "threshold_method": self.threshold_method.currentText(),
            "threshold_value": self.threshold_value.value(),
            "max_value": self.max_value.value(),
            "block_size": self.block_size.value(),
            "c_constant": self.c_constant.value(),
            "morphology_enabled": self.morphology_enabled.isChecked(),
            "morphology_kernel_size": self.morphology_kernel.value(),
            "invert_output": self.invert_output.isChecked()
        }

    def set_parameters(self, parameters: Dict[str, Any]):
        """设置参数"""
        if "threshold_type" in parameters:
            # 反向映射OpenCV常量到文本
            type_map = {
                0: "THRESH_BINARY (二值化)",
                1: "THRESH_BINARY_INV (反二值化)",
                2: "THRESH_TRUNC (截断)",
                3: "THRESH_TOZERO (阈值化为零)",
                4: "THRESH_TOZERO_INV (反阈值化为零)"
            }
            type_text = type_map.get(parameters["threshold_type"], "THRESH_BINARY (二值化)")
            self.threshold_type.setCurrentText(type_text)

        if "threshold_method" in parameters:
            self.threshold_method.setCurrentText(parameters["threshold_method"])

        if "threshold_value" in parameters:
            self.threshold_value.setValue(parameters["threshold_value"])

        if "max_value" in parameters:
            self.max_value.setValue(parameters["max_value"])

        if "block_size" in parameters:
            self.block_size.setValue(parameters["block_size"])

        if "c_constant" in parameters:
            self.c_constant.setValue(parameters["c_constant"])

        if "morphology_enabled" in parameters:
            self.morphology_enabled.setChecked(parameters["morphology_enabled"])

        if "morphology_kernel_size" in parameters:
            self.morphology_kernel.setValue(parameters["morphology_kernel_size"])

        if "invert_output" in parameters:
            self.invert_output.setChecked(parameters["invert_output"])


class MorphologyConfigWidget(BaseAlgorithmConfigWidget):
    """形态学处理配置界面"""

    def __init__(self, algorithm_name: str, parent=None):
        super().__init__(algorithm_name, parent)

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题
        title_label = QLabel("🔄 形态学处理配置")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_primary"]};
                font-size: 18px;
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid {THEME_COLORS["primary"]};
                margin-bottom: 15px;
            }}
        """)
        layout.addWidget(title_label)

        # 主配置区域
        config_widget = QWidget()
        config_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {THEME_COLORS["dark_bg_secondary"]};
                border: 1px solid {THEME_COLORS["dark_border_secondary"]};
                border-radius: 12px;
                padding: 20px;
            }}
        """)
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(20)

        # 形态学操作选择
        config_layout.addWidget(self._create_operation_section())

        # 核参数设置
        config_layout.addWidget(self._create_kernel_section())

        # 迭代次数设置
        config_layout.addWidget(self._create_iterations_section())

        layout.addWidget(config_widget)

        # 预览和操作按钮
        layout.addWidget(self._create_action_buttons())

    def _create_operation_section(self):
        """创建形态学操作选择区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🔧 形态学操作")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 操作类型选择
        operation_layout = QHBoxLayout()
        operation_layout.setSpacing(15)

        self.operation_type = QComboBox()
        self.operation_type.addItems([
            "MORPH_ERODE (腐蚀)",
            "MORPH_DILATE (膨胀)",
            "MORPH_OPEN (开运算)",
            "MORPH_CLOSE (闭运算)",
            "MORPH_GRADIENT (形态学梯度)",
            "MORPH_TOPHAT (顶帽)",
            "MORPH_BLACKHAT (黑帽)"
        ])
        self.operation_type.setCurrentIndex(2)  # 默认开运算
        self.operation_type.setStyleSheet(self._get_modern_combo_style())
        self.operation_type.currentTextChanged.connect(self._on_operation_changed)

        operation_label = QLabel("操作类型:")
        operation_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        operation_layout.addWidget(operation_label)
        operation_layout.addWidget(self.operation_type)
        operation_layout.addStretch()

        section_layout.addLayout(operation_layout)

        # 操作说明
        self.operation_description = QLabel()
        self.operation_description.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_secondary"]};
                font-size: 12px;
                font-style: italic;
                padding: 8px;
                background-color: {THEME_COLORS["dark_bg_primary"]};
                border-radius: 6px;
                border-left: 3px solid {THEME_COLORS["primary"]};
            }}
        """)
        self.operation_description.setWordWrap(True)
        self._update_operation_description()
        section_layout.addWidget(self.operation_description)

        return section

    def _create_kernel_section(self):
        """创建核参数设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🔲 结构元素(核)")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 核形状选择
        shape_layout = QHBoxLayout()
        shape_layout.setSpacing(15)

        self.kernel_shape = QComboBox()
        self.kernel_shape.addItems([
            "MORPH_RECT (矩形)",
            "MORPH_ELLIPSE (椭圆)",
            "MORPH_CROSS (十字形)"
        ])
        self.kernel_shape.setCurrentIndex(0)
        self.kernel_shape.setStyleSheet(self._get_modern_combo_style())
        self.kernel_shape.currentTextChanged.connect(self._on_kernel_changed)

        shape_label = QLabel("核形状:")
        shape_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        shape_layout.addWidget(shape_label)
        shape_layout.addWidget(self.kernel_shape)
        shape_layout.addStretch()

        section_layout.addLayout(shape_layout)

        # 核大小设置
        size_layout = QGridLayout()
        size_layout.setSpacing(15)

        # 宽度
        width_label = QLabel("核宽度:")
        width_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.kernel_width = QSlider(Qt.Orientation.Horizontal)
        self.kernel_width.setRange(3, 21)
        self.kernel_width.setValue(5)
        self.kernel_width.valueChanged.connect(self._ensure_odd_kernel_size)
        self.kernel_width.setStyleSheet(self._get_modern_slider_style())

        self.kernel_width_label = QLabel("5")
        self.kernel_width_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")

        # 高度
        height_label = QLabel("核高度:")
        height_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.kernel_height = QSlider(Qt.Orientation.Horizontal)
        self.kernel_height.setRange(3, 21)
        self.kernel_height.setValue(5)
        self.kernel_height.valueChanged.connect(self._ensure_odd_kernel_size)
        self.kernel_height.setStyleSheet(self._get_modern_slider_style())

        self.kernel_height_label = QLabel("5")
        self.kernel_height_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")

        # 同步大小复选框
        self.sync_kernel_size = QCheckBox("🔗 同步宽高")
        self.sync_kernel_size.setChecked(True)
        self.sync_kernel_size.setStyleSheet(self._get_modern_checkbox_style())
        self.sync_kernel_size.toggled.connect(self._on_sync_kernel_toggled)

        size_layout.addWidget(width_label, 0, 0)
        size_layout.addWidget(self.kernel_width, 0, 1)
        size_layout.addWidget(self.kernel_width_label, 0, 2)
        size_layout.addWidget(height_label, 1, 0)
        size_layout.addWidget(self.kernel_height, 1, 1)
        size_layout.addWidget(self.kernel_height_label, 1, 2)
        size_layout.addWidget(self.sync_kernel_size, 2, 0, 1, 3)

        section_layout.addLayout(size_layout)
        return section

    def _create_iterations_section(self):
        """创建迭代次数设置区域"""
        section = QWidget()
        section_layout = QVBoxLayout(section)
        section_layout.setSpacing(12)

        # 区域标题
        title = QLabel("🔄 迭代设置")
        title.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["primary"]};
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }}
        """)
        section_layout.addWidget(title)

        # 迭代次数设置
        iterations_layout = QHBoxLayout()
        iterations_layout.setSpacing(15)

        iterations_label = QLabel("迭代次数:")
        iterations_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 14px;")

        self.iterations = QSlider(Qt.Orientation.Horizontal)
        self.iterations.setRange(1, 10)
        self.iterations.setValue(1)
        self.iterations.setStyleSheet(self._get_modern_slider_style())

        self.iterations_label = QLabel("1")
        self.iterations_label.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; min-width: 30px;")
        self.iterations.valueChanged.connect(lambda v: self.iterations_label.setText(str(v)))

        iterations_layout.addWidget(iterations_label)
        iterations_layout.addWidget(self.iterations)
        iterations_layout.addWidget(self.iterations_label)

        section_layout.addLayout(iterations_layout)

        # 迭代说明
        iterations_info = QLabel("💡 提示：迭代次数越多，形态学效果越明显，但处理时间也会增加。")
        iterations_info.setStyleSheet(f"""
            QLabel {{
                color: {THEME_COLORS["text_secondary"]};
                font-size: 12px;
                font-style: italic;
                padding: 8px;
                background-color: {THEME_COLORS["dark_bg_primary"]};
                border-radius: 6px;
                border-left: 3px solid {THEME_COLORS["primary"]};
            }}
        """)
        iterations_info.setWordWrap(True)
        section_layout.addWidget(iterations_info)

        return section

    def _ensure_odd_kernel_size(self, value):
        """确保核大小为奇数"""
        sender = self.sender()
        if value % 2 == 0:
            value += 1
            sender.setValue(value)

        if sender == self.kernel_width:
            self.kernel_width_label.setText(str(value))
            # 如果同步开启，同时更新高度
            if self.sync_kernel_size.isChecked():
                self.kernel_height.setValue(value)
        elif sender == self.kernel_height:
            self.kernel_height_label.setText(str(value))
            # 如果同步开启，同时更新宽度
            if self.sync_kernel_size.isChecked():
                self.kernel_width.setValue(value)

    def _on_sync_kernel_toggled(self, checked):
        """同步核大小开关切换"""
        if checked:
            # 同步高度到宽度
            self.kernel_height.setValue(self.kernel_width.value())

    def _on_operation_changed(self, operation):
        """形态学操作改变时的处理"""
        self._update_operation_description()
        self._emit_parameter_changed("operation_type", operation)

    def _on_kernel_changed(self, kernel_shape):
        """核形状改变时的处理"""
        self._emit_parameter_changed("kernel_shape", kernel_shape)

    def _update_operation_description(self):
        """更新操作说明"""
        descriptions = {
            "MORPH_ERODE (腐蚀)": "腐蚀操作：缩小白色区域，去除小的白色噪点，分离连接的对象。",
            "MORPH_DILATE (膨胀)": "膨胀操作：扩大白色区域，填充小的黑色空洞，连接邻近的对象。",
            "MORPH_OPEN (开运算)": "开运算：先腐蚀后膨胀，去除小的白色噪点，保持大对象的形状。",
            "MORPH_CLOSE (闭运算)": "闭运算：先膨胀后腐蚀，填充对象内部的小空洞，连接邻近的对象。",
            "MORPH_GRADIENT (形态学梯度)": "形态学梯度：膨胀图像与腐蚀图像的差，突出对象的边界。",
            "MORPH_TOPHAT (顶帽)": "顶帽运算：原图像与开运算结果的差，突出比结构元素小的亮区域。",
            "MORPH_BLACKHAT (黑帽)": "黑帽运算：闭运算结果与原图像的差，突出比结构元素小的暗区域。"
        }

        current_operation = self.operation_type.currentText()
        description = descriptions.get(current_operation, "")
        self.operation_description.setText(description)

    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        # 获取形态学操作的OpenCV常量值
        operation_map = {
            "MORPH_ERODE (腐蚀)": 0,  # cv2.MORPH_ERODE
            "MORPH_DILATE (膨胀)": 1,  # cv2.MORPH_DILATE
            "MORPH_OPEN (开运算)": 2,  # cv2.MORPH_OPEN
            "MORPH_CLOSE (闭运算)": 3,  # cv2.MORPH_CLOSE
            "MORPH_GRADIENT (形态学梯度)": 4,  # cv2.MORPH_GRADIENT
            "MORPH_TOPHAT (顶帽)": 5,  # cv2.MORPH_TOPHAT
            "MORPH_BLACKHAT (黑帽)": 6  # cv2.MORPH_BLACKHAT
        }

        # 获取核形状的OpenCV常量值
        shape_map = {
            "MORPH_RECT (矩形)": 0,  # cv2.MORPH_RECT
            "MORPH_ELLIPSE (椭圆)": 1,  # cv2.MORPH_ELLIPSE
            "MORPH_CROSS (十字形)": 2  # cv2.MORPH_CROSS
        }

        return {
            "operation_type": operation_map.get(self.operation_type.currentText(), 2),
            "kernel_shape": shape_map.get(self.kernel_shape.currentText(), 0),
            "kernel_width": self.kernel_width.value(),
            "kernel_height": self.kernel_height.value(),
            "iterations": self.iterations.value(),
            "sync_kernel_size": self.sync_kernel_size.isChecked()
        }

    def set_parameters(self, parameters: Dict[str, Any]):
        """设置参数"""
        if "operation_type" in parameters:
            # 反向映射OpenCV常量到文本
            operation_map = {
                0: "MORPH_ERODE (腐蚀)",
                1: "MORPH_DILATE (膨胀)",
                2: "MORPH_OPEN (开运算)",
                3: "MORPH_CLOSE (闭运算)",
                4: "MORPH_GRADIENT (形态学梯度)",
                5: "MORPH_TOPHAT (顶帽)",
                6: "MORPH_BLACKHAT (黑帽)"
            }
            operation_text = operation_map.get(parameters["operation_type"], "MORPH_OPEN (开运算)")
            self.operation_type.setCurrentText(operation_text)

        if "kernel_shape" in parameters:
            # 反向映射OpenCV常量到文本
            shape_map = {
                0: "MORPH_RECT (矩形)",
                1: "MORPH_ELLIPSE (椭圆)",
                2: "MORPH_CROSS (十字形)"
            }
            shape_text = shape_map.get(parameters["kernel_shape"], "MORPH_RECT (矩形)")
            self.kernel_shape.setCurrentText(shape_text)

        if "kernel_width" in parameters:
            self.kernel_width.setValue(parameters["kernel_width"])

        if "kernel_height" in parameters:
            self.kernel_height.setValue(parameters["kernel_height"])

        if "iterations" in parameters:
            self.iterations.setValue(parameters["iterations"])

        if "sync_kernel_size" in parameters:
            self.sync_kernel_size.setChecked(parameters["sync_kernel_size"])


class AlgorithmConfigWidgetFactory:
    """算法配置界面工厂类"""

    # 算法名称到配置界面类的映射
    WIDGET_MAPPING = {
        # 图像源 (4种)
        "camera": CameraSourceConfigWidget,
        "image_source.camera": CameraSourceConfigWidget,
        "file": CameraSourceConfigWidget,  # 暂时复用相机界面
        "image_source.file": CameraSourceConfigWidget,
        "network": CameraSourceConfigWidget,  # 暂时复用相机界面
        "image_source.network": CameraSourceConfigWidget,
        "video": CameraSourceConfigWidget,  # 暂时复用相机界面
        "image_source.video": CameraSourceConfigWidget,

        # 图像处理 (10种)
        "gaussian_blur": GaussianBlurConfigWidget,
        "image_processing.gaussian_blur": GaussianBlurConfigWidget,
        "edge_detection": EdgeDetectionConfigWidget,
        "image_processing.edge_detection": EdgeDetectionConfigWidget,
        "median_blur": GaussianBlurConfigWidget,  # 暂时复用高斯模糊界面
        "image_processing.median_blur": GaussianBlurConfigWidget,
        "bilateral_filter": GaussianBlurConfigWidget,  # 暂时复用高斯模糊界面
        "image_processing.bilateral_filter": GaussianBlurConfigWidget,
        "morphology": MorphologyConfigWidget,  # 专用形态学处理界面
        "image_processing.morphology": MorphologyConfigWidget,
        "threshold": ThresholdConfigWidget,  # 专用阈值处理界面
        "image_processing.threshold": ThresholdConfigWidget,
        "color_space": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.color_space": EdgeDetectionConfigWidget,
        "histogram": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.histogram": EdgeDetectionConfigWidget,
        "contrast": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "image_processing.contrast": EdgeDetectionConfigWidget,
        "noise_reduction": GaussianBlurConfigWidget,  # 暂时复用高斯模糊界面
        "image_processing.noise_reduction": GaussianBlurConfigWidget,

        # 特征检测 (7种)
        "template_matching": TemplateMatchingConfigWidget,
        "feature_detection.template_matching": TemplateMatchingConfigWidget,
        "contour_detection": ContourDetectionConfigWidget,
        "feature_detection.contour_detection": ContourDetectionConfigWidget,
        "corner_detection": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "feature_detection.corner_detection": EdgeDetectionConfigWidget,
        "blob_detection": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "feature_detection.blob_detection": ContourDetectionConfigWidget,
        "line_detection": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "feature_detection.line_detection": EdgeDetectionConfigWidget,
        "circle_detection": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "feature_detection.circle_detection": ContourDetectionConfigWidget,
        "keypoint_detection": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "feature_detection.keypoint_detection": EdgeDetectionConfigWidget,

        # 目标检测 (5种)
        "color_detection": ColorDetectionConfigWidget,  # 专用颜色检测界面
        "object_detection.color_detection": ColorDetectionConfigWidget,
        "shape_detection": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "object_detection.shape_detection": ContourDetectionConfigWidget,
        "text_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "object_detection.text_detection": TemplateMatchingConfigWidget,
        "barcode_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "object_detection.barcode_detection": TemplateMatchingConfigWidget,
        "face_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "object_detection.face_detection": TemplateMatchingConfigWidget,

        # 测量算法 (5种)
        "distance_measurement": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "measurement.distance_measurement": EdgeDetectionConfigWidget,
        "angle_measurement": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "measurement.angle_measurement": EdgeDetectionConfigWidget,
        "area_measurement": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "measurement.area_measurement": ContourDetectionConfigWidget,
        "geometry_analysis": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "measurement.geometry_analysis": ContourDetectionConfigWidget,
        "dimension_measurement": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "measurement.dimension_measurement": EdgeDetectionConfigWidget,

        # 深度学习 (4种)
        "yolo_detection": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "deep_learning.yolo_detection": TemplateMatchingConfigWidget,
        "classification": TemplateMatchingConfigWidget,  # 暂时复用模板匹配界面
        "deep_learning.classification": TemplateMatchingConfigWidget,
        "segmentation": ContourDetectionConfigWidget,  # 暂时复用轮廓检测界面
        "deep_learning.segmentation": ContourDetectionConfigWidget,
        "pose_estimation": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "deep_learning.pose_estimation": EdgeDetectionConfigWidget,

        # 位置修正 (5种)
        "affine_transform": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.affine_transform": EdgeDetectionConfigWidget,
        "perspective_transform": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.perspective_transform": EdgeDetectionConfigWidget,
        "rotation_correction": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.rotation_correction": EdgeDetectionConfigWidget,
        "translation_correction": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.translation_correction": EdgeDetectionConfigWidget,
        "scale_correction": EdgeDetectionConfigWidget,  # 暂时复用边缘检测界面
        "position_correction.scale_correction": EdgeDetectionConfigWidget,
    }

    @classmethod
    def get_widget_class(cls, algorithm_name: str):
        """获取算法配置界面类"""
        return cls.WIDGET_MAPPING.get(algorithm_name)

    @classmethod
    def create_config_widget(cls, algorithm_name: str, parent=None) -> BaseAlgorithmConfigWidget:
        """创建算法配置界面"""
        widget_class = cls.WIDGET_MAPPING.get(algorithm_name)

        if widget_class:
            logger.info(f"创建专用配置界面: {algorithm_name}")
            return widget_class(algorithm_name, parent)
        else:
            logger.warning(f"未找到专用配置界面，使用通用界面: {algorithm_name}")
            return cls._create_generic_widget(algorithm_name, parent)

    @classmethod
    def _create_generic_widget(cls, algorithm_name: str, parent=None) -> BaseAlgorithmConfigWidget:
        """创建通用配置界面"""
        return GenericAlgorithmConfigWidget(algorithm_name, parent)

    @classmethod
    def register_widget(cls, algorithm_name: str, widget_class):
        """注册新的算法配置界面"""
        cls.WIDGET_MAPPING[algorithm_name] = widget_class
        logger.info(f"注册算法配置界面: {algorithm_name} -> {widget_class.__name__}")

    @classmethod
    def get_supported_algorithms(cls) -> List[str]:
        """获取支持的算法列表"""
        return list(cls.WIDGET_MAPPING.keys())


class GenericAlgorithmConfigWidget(BaseAlgorithmConfigWidget):
    """通用算法配置界面"""

    def __init__(self, algorithm_name: str, parent=None):
        self.algorithm_schema = None
        super().__init__(algorithm_name, parent)

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # 输入源选择
        input_group = QGroupBox("输入源")
        input_layout = QVBoxLayout(input_group)

        self.input_selector = QComboBox()
        self.input_selector.addItem("选择输入源...", None)
        input_layout.addWidget(self.input_selector)

        layout.addWidget(input_group)

        # 参数配置区域
        self.param_group = QGroupBox("算法参数")
        self.param_layout = QFormLayout(self.param_group)
        layout.addWidget(self.param_group)

        # 预览按钮
        preview_btn = ModernButton("预览效果", ModernButton.PRIMARY)
        preview_btn.clicked.connect(self.preview_requested.emit)
        layout.addWidget(preview_btn)

        layout.addStretch()

        # 尝试加载算法参数模式
        self._load_algorithm_schema()

    def _load_algorithm_schema(self):
        """加载算法参数模式"""
        try:
            # 尝试从算法注册表获取参数模式
            from wirevsion.algorithms.registry import AlgorithmRegistry
            registry = AlgorithmRegistry()

            # 解析算法名称
            if "." in self.algorithm_name:
                category, name = self.algorithm_name.split(".", 1)
                algorithm_class = registry.get_algorithm_class(category, name)
            else:
                # 尝试在所有类别中查找
                algorithm_class = None
                for category in ["image_source", "image_processing", "feature_detection",
                               "object_detection", "measurement", "deep_learning", "position_correction"]:
                    try:
                        algorithm_class = registry.get_algorithm_class(category, self.algorithm_name)
                        if algorithm_class:
                            break
                    except:
                        continue

            if algorithm_class:
                algorithm_instance = algorithm_class()
                self.algorithm_schema = algorithm_instance.get_parameter_schema()
                self._create_parameter_widgets()
                logger.info(f"加载算法参数模式: {self.algorithm_name}")
            else:
                logger.warning(f"未找到算法类: {self.algorithm_name}")
                self._create_default_parameters()

        except Exception as e:
            logger.error(f"加载算法参数模式失败: {e}")
            self._create_default_parameters()

    def _create_parameter_widgets(self):
        """根据参数模式创建控件"""
        if not self.algorithm_schema:
            return

        for param_name, param_config in self.algorithm_schema.items():
            param_type = param_config.get("type", "string")
            description = param_config.get("description", param_name)
            default_value = param_config.get("default")

            widget = self._create_parameter_widget(param_name, param_type, param_config, default_value)
            if widget:
                self.param_layout.addRow(f"{description}:", widget)

    def _create_parameter_widget(self, param_name: str, param_type: str,
                                param_config: Dict[str, Any], default_value: Any):
        """创建参数控件"""
        widget = None

        if param_type == "integer":
            widget = QSpinBox()
            widget.setRange(param_config.get("minimum", -999999),
                          param_config.get("maximum", 999999))
            if default_value is not None:
                widget.setValue(default_value)
            widget.valueChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))

        elif param_type == "number":
            widget = QDoubleSpinBox()
            widget.setRange(param_config.get("minimum", -999999.0),
                          param_config.get("maximum", 999999.0))
            widget.setDecimals(param_config.get("decimals", 2))
            if default_value is not None:
                widget.setValue(default_value)
            widget.valueChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))

        elif param_type == "boolean":
            widget = QCheckBox()
            if default_value is not None:
                widget.setChecked(default_value)
            widget.toggled.connect(lambda v: self._emit_parameter_changed(param_name, v))

        elif param_type == "string":
            if "enum" in param_config:
                widget = QComboBox()
                widget.addItems(param_config["enum"])
                if default_value is not None:
                    widget.setCurrentText(str(default_value))
                widget.currentTextChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))
            else:
                widget = QLineEdit()
                if default_value is not None:
                    widget.setText(str(default_value))
                widget.textChanged.connect(lambda v: self._emit_parameter_changed(param_name, v))

        return widget

    def _create_default_parameters(self):
        """创建默认参数"""
        # 添加一个启用开关
        enable_widget = QCheckBox()
        enable_widget.setChecked(True)
        enable_widget.toggled.connect(lambda v: self._emit_parameter_changed("enabled", v))
        self.param_layout.addRow("启用算法:", enable_widget)

    def setup_connections(self):
        self.input_selector.currentIndexChanged.connect(lambda: self._emit_parameter_changed("input_source", self.input_selector.currentData()))

    def update_input_selector(self):
        """更新输入选择器"""
        self.input_selector.clear()
        self.input_selector.addItem("选择输入源...", None)

        for source in self.input_sources:
            display_name = f"{source['node_name']} - {source['output_name']}"
            self.input_selector.addItem(display_name, source)

    def _emit_parameter_changed(self, name: str, value: Any):
        self.parameters[name] = value
        self.parameter_changed.emit(name, value)