#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模板配置组件
提供模板匹配器配置功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QLabel, QGroupBox, QComboBox,
    QSpinBox, QDoubleSpinBox, QCheckBox, QFileDialog,
    QMessageBox, QTextEdit, QSlider
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap

import cv2
import numpy as np
from typing import Optional, Dict, Any
from loguru import logger

from wirevsion.core.template_matcher import TemplateMatcher
from wirevsion.utils.image_utils import cv_to_qpixmap


class TemplateWidget(QWidget):
    """
    模板配置组件
    
    提供模板匹配器的配置功能
    """
    
    # 信号定义
    template_updated = pyqtSignal(dict)  # 模板更新信号
    status_message = pyqtSignal(str)  # 状态消息信号
    
    def __init__(self, template_matcher: TemplateMatcher):
        """
        初始化模板配置组件
        
        Args:
            template_matcher: 模板匹配器实例
        """
        super().__init__()
        
        self.template_matcher = template_matcher
        self.current_template_name = "default"
        
        # 创建UI
        self._create_ui()
        
        # 连接信号
        self._connect_signals()
        
        logger.info("模板配置组件初始化完成")
    
    def _create_ui(self):
        """创建UI组件"""
        layout = QVBoxLayout(self)
        
        # 模板管理组
        template_group = QGroupBox("模板管理")
        template_layout = QVBoxLayout(template_group)
        
        # 模板操作按钮
        btn_layout = QHBoxLayout()
        
        self.load_template_btn = QPushButton("加载模板")
        self.load_template_btn.clicked.connect(self._load_template)
        btn_layout.addWidget(self.load_template_btn)
        
        self.save_template_btn = QPushButton("保存模板")
        self.save_template_btn.clicked.connect(self._save_template)
        btn_layout.addWidget(self.save_template_btn)
        
        self.clear_template_btn = QPushButton("清除模板")
        self.clear_template_btn.clicked.connect(self._clear_template)
        btn_layout.addWidget(self.clear_template_btn)
        
        template_layout.addLayout(btn_layout)
        
        # 模板预览
        self.template_preview = QLabel("无模板图像")
        self.template_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.template_preview.setMinimumHeight(200)
        self.template_preview.setStyleSheet("border: 1px solid #555555; background-color: #2b2b2b;")
        template_layout.addWidget(self.template_preview)
        
        layout.addWidget(template_group)
        
        # 匹配参数组
        params_group = QGroupBox("匹配参数")
        params_layout = QFormLayout(params_group)
        
        # 匹配方法
        self.method_combo = QComboBox()
        self.method_combo.addItem("TM_CCOEFF_NORMED", cv2.TM_CCOEFF_NORMED)
        self.method_combo.addItem("TM_CCORR_NORMED", cv2.TM_CCORR_NORMED)
        self.method_combo.addItem("TM_SQDIFF_NORMED", cv2.TM_SQDIFF_NORMED)
        self.method_combo.currentIndexChanged.connect(self._update_config)
        params_layout.addRow("匹配方法:", self.method_combo)
        
        # 匹配阈值
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(0.0, 1.0)
        self.threshold_spin.setSingleStep(0.01)
        self.threshold_spin.setValue(0.8)
        self.threshold_spin.valueChanged.connect(self._update_config)
        params_layout.addRow("匹配阈值:", self.threshold_spin)
        
        # 多尺度匹配
        self.multi_scale_check = QCheckBox()
        self.multi_scale_check.stateChanged.connect(self._update_config)
        params_layout.addRow("多尺度匹配:", self.multi_scale_check)
        
        # 缩放范围
        scale_layout = QHBoxLayout()
        
        self.scale_min_spin = QDoubleSpinBox()
        self.scale_min_spin.setRange(0.1, 2.0)
        self.scale_min_spin.setSingleStep(0.1)
        self.scale_min_spin.setValue(0.8)
        self.scale_min_spin.valueChanged.connect(self._update_config)
        scale_layout.addWidget(self.scale_min_spin)
        
        scale_layout.addWidget(QLabel("到"))
        
        self.scale_max_spin = QDoubleSpinBox()
        self.scale_max_spin.setRange(0.1, 2.0)
        self.scale_max_spin.setSingleStep(0.1)
        self.scale_max_spin.setValue(1.2)
        self.scale_max_spin.valueChanged.connect(self._update_config)
        scale_layout.addWidget(self.scale_max_spin)
        
        params_layout.addRow("缩放范围:", scale_layout)
        
        # 缩放步数
        self.scale_steps_spin = QSpinBox()
        self.scale_steps_spin.setRange(3, 20)
        self.scale_steps_spin.setValue(5)
        self.scale_steps_spin.valueChanged.connect(self._update_config)
        params_layout.addRow("缩放步数:", self.scale_steps_spin)
        
        layout.addWidget(params_group)
        
        # 模板信息
        info_group = QGroupBox("模板信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        info_layout.addWidget(self.info_text)
        
        layout.addWidget(info_group)
        
        layout.addStretch()
    
    def _connect_signals(self):
        """连接信号"""
        pass
    
    def _load_template(self):
        """加载模板图像"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模板图像",
            "", "图像文件 (*.png *.jpg *.jpeg *.bmp *.tiff)"
        )
        
        if file_path:
            try:
                if self.template_matcher.load_template(file_path, self.current_template_name):
                    # 显示模板预览
                    template_image = cv2.imread(file_path)
                    if template_image is not None:
                        self._display_template(template_image)
                        self._update_template_info()
                        self.status_message.emit(f"模板加载成功: {file_path}")
                        
                        # 发送更新信号
                        self.template_updated.emit({
                            'action': 'loaded',
                            'template_name': self.current_template_name,
                            'template_path': file_path
                        })
                else:
                    QMessageBox.warning(self, "错误", "模板加载失败")
                    
            except Exception as e:
                error_msg = f"加载模板时出错: {str(e)}"
                logger.error(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
    
    def _save_template(self):
        """保存当前模板"""
        if not self.template_matcher.has_templates():
            QMessageBox.warning(self, "警告", "没有可保存的模板")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存模板图像",
            f"{self.current_template_name}.png",
            "PNG文件 (*.png);;JPEG文件 (*.jpg);;所有文件 (*.*)"
        )
        
        if file_path:
            try:
                template_info = self.template_matcher.get_template_info(self.current_template_name)
                if template_info and template_info.get('path'):
                    # 复制原始模板文件
                    import shutil
                    shutil.copy2(template_info['path'], file_path)
                    self.status_message.emit(f"模板已保存: {file_path}")
                else:
                    QMessageBox.warning(self, "警告", "无法获取模板图像")
                    
            except Exception as e:
                error_msg = f"保存模板时出错: {str(e)}"
                logger.error(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
    
    def _clear_template(self):
        """清除当前模板"""
        if self.template_matcher.remove_template(self.current_template_name):
            self.template_preview.setText("无模板图像")
            self.info_text.clear()
            self.status_message.emit("模板已清除")
            
            # 发送更新信号
            self.template_updated.emit({
                'action': 'cleared',
                'template_name': self.current_template_name
            })
        else:
            QMessageBox.warning(self, "警告", "没有可清除的模板")
    
    def _display_template(self, image: np.ndarray):
        """显示模板图像"""
        try:
            if image is not None:
                # 转换为QPixmap并缩放以适应预览区域
                pixmap = cv_to_qpixmap(image)
                
                # 缩放图像以适应预览区域
                label_size = self.template_preview.size()
                scaled_pixmap = pixmap.scaled(
                    label_size.width() - 10,
                    label_size.height() - 10,
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                
                self.template_preview.setPixmap(scaled_pixmap)
            else:
                self.template_preview.setText("无效的模板图像")
                
        except Exception as e:
            logger.error(f"显示模板图像时出错: {e}")
            self.template_preview.setText("显示错误")
    
    def _update_template_info(self):
        """更新模板信息显示"""
        try:
            template_info = self.template_matcher.get_template_info(self.current_template_name)
            if template_info:
                info_text = f"""模板信息:
名称: {self.current_template_name}
尺寸: {template_info['size'][0]} x {template_info['size'][1]}
路径: {template_info.get('path', '未知')}
"""
                self.info_text.setText(info_text)
            else:
                self.info_text.clear()
                
        except Exception as e:
            logger.error(f"更新模板信息时出错: {e}")
    
    def _update_config(self):
        """更新匹配器配置"""
        try:
            config = {
                'method': self.method_combo.currentData(),
                'threshold': self.threshold_spin.value(),
                'multi_scale': self.multi_scale_check.isChecked(),
                'scale_range': (self.scale_min_spin.value(), self.scale_max_spin.value()),
                'scale_steps': self.scale_steps_spin.value()
            }
            
            self.template_matcher.set_config(config)
            
            # 发送更新信号
            self.template_updated.emit({
                'action': 'config_updated',
                'config': config
            })
            
            logger.debug(f"模板匹配器配置已更新: {config}")
            
        except Exception as e:
            logger.error(f"更新配置时出错: {e}")
    
    def load_template_from_image(self, image: np.ndarray, name: str = None):
        """
        从图像数组加载模板
        
        Args:
            image: 模板图像
            name: 模板名称
        """
        if name is None:
            name = self.current_template_name
        
        try:
            if self.template_matcher.load_template_from_image(image, name):
                self._display_template(image)
                self._update_template_info()
                self.status_message.emit(f"从图像加载模板成功: {name}")
                
                # 发送更新信号
                self.template_updated.emit({
                    'action': 'loaded_from_image',
                    'template_name': name
                })
                return True
            else:
                self.status_message.emit("从图像加载模板失败")
                return False
                
        except Exception as e:
            error_msg = f"从图像加载模板时出错: {str(e)}"
            logger.error(error_msg)
            self.status_message.emit(error_msg)
            return False
    
    def get_template_config(self) -> Dict[str, Any]:
        """
        获取当前模板配置
        
        Returns:
            Dict[str, Any]: 模板配置
        """
        return {
            'template_name': self.current_template_name,
            'method': self.method_combo.currentData(),
            'threshold': self.threshold_spin.value(),
            'multi_scale': self.multi_scale_check.isChecked(),
            'scale_range': (self.scale_min_spin.value(), self.scale_max_spin.value()),
            'scale_steps': self.scale_steps_spin.value(),
            'has_template': self.template_matcher.has_templates()
        }
    
    def set_template_config(self, config: Dict[str, Any]):
        """
        设置模板配置
        
        Args:
            config: 模板配置
        """
        try:
            # 设置匹配方法
            method = config.get('method', cv2.TM_CCOEFF_NORMED)
            for i in range(self.method_combo.count()):
                if self.method_combo.itemData(i) == method:
                    self.method_combo.setCurrentIndex(i)
                    break
            
            # 设置阈值
            self.threshold_spin.setValue(config.get('threshold', 0.8))
            
            # 设置多尺度匹配
            self.multi_scale_check.setChecked(config.get('multi_scale', False))
            
            # 设置缩放范围
            scale_range = config.get('scale_range', (0.8, 1.2))
            self.scale_min_spin.setValue(scale_range[0])
            self.scale_max_spin.setValue(scale_range[1])
            
            # 设置缩放步数
            self.scale_steps_spin.setValue(config.get('scale_steps', 5))
            
            # 更新匹配器配置
            self._update_config()
            
            logger.info("模板配置已设置")
            
        except Exception as e:
            logger.error(f"设置模板配置失败: {e}") 