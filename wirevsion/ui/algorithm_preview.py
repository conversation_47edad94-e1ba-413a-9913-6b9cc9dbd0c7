"""
算法预览管理器 - 实时预览算法效果

功能：
- 异步算法执行
- 结果缓存
- 错误处理
- 性能监控
"""

import time
import traceback
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import numpy as np

from PyQt6.QtCore import QObject, pyqtSignal, QThread, QMutex, QMutexLocker
from PyQt6.QtGui import QImage, QPixmap

from loguru import logger

# 导入算法注册表
try:
    from ..algorithms import algorithm_registry
    from ..core.data_types import ImageData, ProcessingResult
except ImportError:
    algorithm_registry = None
    ImageData = None
    ProcessingResult = None


@dataclass
class PreviewTask:
    """预览任务"""
    node_id: str
    algorithm_category: str
    algorithm_name: str
    parameters: Dict[str, Any]
    input_data: Any
    timestamp: float = 0.0


@dataclass
class PreviewResult:
    """预览结果"""
    task: PreviewTask
    success: bool
    result_data: Any = None
    result_image: Optional[QPixmap] = None
    error_message: str = ""
    execution_time: float = 0.0
    memory_usage: float = 0.0


class PreviewWorker(QThread):
    """预览工作线程"""
    
    result_ready = pyqtSignal(PreviewResult)
    progress_update = pyqtSignal(int)
    
    def __init__(self):
        super().__init__()
        self.task_queue = []
        self.mutex = QMutex()
        self.is_running = True
        
    def add_task(self, task: PreviewTask):
        """添加预览任务"""
        with QMutexLocker(self.mutex):
            # 清理旧任务，只保留最新的
            self.task_queue = [task]
    
    def stop(self):
        """停止工作线程"""
        self.is_running = False
        self.wait()
    
    def run(self):
        """执行预览任务"""
        while self.is_running:
            task = None
            
            # 获取任务
            with QMutexLocker(self.mutex):
                if self.task_queue:
                    task = self.task_queue.pop(0)
            
            if task:
                result = self._execute_task(task)
                self.result_ready.emit(result)
            else:
                self.msleep(50)  # 空闲时休眠
    
    def _execute_task(self, task: PreviewTask) -> PreviewResult:
        """执行单个预览任务"""
        start_time = time.time()
        
        try:
            # 创建算法实例
            if not algorithm_registry:
                return PreviewResult(
                    task=task,
                    success=False,
                    error_message="算法注册表未初始化"
                )
            
            algorithm = algorithm_registry.create_algorithm(
                task.algorithm_category,
                task.algorithm_name
            )
            
            if not algorithm:
                return PreviewResult(
                    task=task,
                    success=False,
                    error_message=f"无法创建算法: {task.algorithm_name}"
                )
            
            # 设置参数
            for param_name, param_value in task.parameters.items():
                if hasattr(algorithm, f"set_{param_name}"):
                    setter = getattr(algorithm, f"set_{param_name}")
                    setter(param_value)
                else:
                    setattr(algorithm, param_name, param_value)
            
            # 执行算法
            self.progress_update.emit(30)
            
            # 处理输入数据
            input_image = self._prepare_input(task.input_data)
            if input_image is None:
                # 创建测试图像
                input_image = self._create_test_image()
            
            self.progress_update.emit(50)
            
            # 执行处理
            if hasattr(algorithm, 'process_image'):
                result = algorithm.process_image(input_image)
            elif hasattr(algorithm, 'process'):
                result = algorithm.process(input_image)
            else:
                return PreviewResult(
                    task=task,
                    success=False,
                    error_message="算法缺少process方法"
                )
            
            self.progress_update.emit(80)
            
            # 转换结果为QPixmap
            result_pixmap = self._convert_to_pixmap(result)
            
            # 计算执行时间
            execution_time = time.time() - start_time
            
            self.progress_update.emit(100)
            
            return PreviewResult(
                task=task,
                success=True,
                result_data=result,
                result_image=result_pixmap,
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"预览执行失败: {e}")
            return PreviewResult(
                task=task,
                success=False,
                error_message=str(e),
                execution_time=time.time() - start_time
            )
    
    def _prepare_input(self, input_data: Any) -> Optional[np.ndarray]:
        """准备输入数据"""
        if input_data is None:
            return None
        
        if isinstance(input_data, np.ndarray):
            return input_data
        
        if isinstance(input_data, QPixmap):
            return self._pixmap_to_array(input_data)
        
        if isinstance(input_data, QImage):
            return self._qimage_to_array(input_data)
        
        return None
    
    def _create_test_image(self) -> np.ndarray:
        """创建测试图像"""
        # 创建一个渐变测试图像
        height, width = 480, 640
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加渐变
        for y in range(height):
            for x in range(width):
                image[y, x] = [
                    int(x * 255 / width),
                    int(y * 255 / height),
                    128
                ]
        
        # 添加一些形状
        # 矩形
        cv2.rectangle(image, (50, 50), (200, 150), (255, 255, 255), 2)
        
        # 圆形
        cv2.circle(image, (400, 200), 50, (255, 0, 0), -1)
        
        # 文本
        cv2.putText(image, "Preview Test", (250, 400),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        return image
    
    def _convert_to_pixmap(self, result: Any) -> Optional[QPixmap]:
        """转换结果为QPixmap"""
        try:
            # 如果已经是QPixmap
            if isinstance(result, QPixmap):
                return result
            
            # 如果是numpy数组
            if isinstance(result, np.ndarray):
                return self._array_to_pixmap(result)
            
            # 如果是ProcessingResult
            if hasattr(result, 'image') and isinstance(result.image, np.ndarray):
                return self._array_to_pixmap(result.image)
            
            # 如果是ImageData
            if hasattr(result, 'data') and isinstance(result.data, np.ndarray):
                return self._array_to_pixmap(result.data)
            
            return None
            
        except Exception as e:
            logger.error(f"转换结果失败: {e}")
            return None
    
    def _array_to_pixmap(self, array: np.ndarray) -> QPixmap:
        """numpy数组转QPixmap"""
        if array.dtype != np.uint8:
            array = (array * 255).astype(np.uint8)
        
        height, width = array.shape[:2]
        
        if len(array.shape) == 2:
            # 灰度图
            qimage = QImage(array.data, width, height, width,
                          QImage.Format_Grayscale8)
        elif len(array.shape) == 3:
            if array.shape[2] == 3:
                # BGR转RGB
                rgb_array = cv2.cvtColor(array, cv2.COLOR_BGR2RGB)
                bytes_per_line = 3 * width
                qimage = QImage(rgb_array.data, width, height,
                              bytes_per_line, QImage.Format_RGB888)
            elif array.shape[2] == 4:
                # BGRA转RGBA
                rgba_array = cv2.cvtColor(array, cv2.COLOR_BGRA2RGBA)
                bytes_per_line = 4 * width
                qimage = QImage(rgba_array.data, width, height,
                              bytes_per_line, QImage.Format_RGBA8888)
            else:
                raise ValueError(f"不支持的通道数: {array.shape[2]}")
        else:
            raise ValueError(f"不支持的数组形状: {array.shape}")
        
        return QPixmap.fromImage(qimage)
    
    def _pixmap_to_array(self, pixmap: QPixmap) -> np.ndarray:
        """QPixmap转numpy数组"""
        qimage = pixmap.toImage()
        return self._qimage_to_array(qimage)
    
    def _qimage_to_array(self, qimage: QImage) -> np.ndarray:
        """QImage转numpy数组"""
        width = qimage.width()
        height = qimage.height()
        
        # 转换为RGB888格式
        qimage = qimage.convertToFormat(QImage.Format_RGB888)
        
        # 获取数据
        ptr = qimage.bits()
        ptr.setsize(height * width * 3)
        array = np.frombuffer(ptr, dtype=np.uint8).reshape((height, width, 3))
        
        # RGB转BGR
        return cv2.cvtColor(array, cv2.COLOR_RGB2BGR)


class AlgorithmPreviewManager(QObject):
    """算法预览管理器"""
    
    preview_updated = pyqtSignal(str, QPixmap)  # node_id, preview_image
    preview_error = pyqtSignal(str, str)  # node_id, error_message
    preview_progress = pyqtSignal(str, int)  # node_id, progress
    
    def __init__(self):
        super().__init__()
        
        # 预览缓存
        self.preview_cache = {}
        self.cache_mutex = QMutex()
        
        # 工作线程池
        self.workers = []
        self.worker_count = 2  # 预览工作线程数
        
        # 初始化工作线程
        self._init_workers()
        
        # 性能统计
        self.stats = {
            "total_previews": 0,
            "cache_hits": 0,
            "average_time": 0.0
        }
        
        logger.info(f"预览管理器初始化完成，{self.worker_count}个工作线程")
    
    def _init_workers(self):
        """初始化工作线程"""
        for i in range(self.worker_count):
            worker = PreviewWorker()
            worker.result_ready.connect(self._on_preview_result)
            worker.progress_update.connect(self._on_progress_update)
            worker.start()
            self.workers.append(worker)
    
    def request_preview(self, node_id: str, category: str, algorithm: str,
                       parameters: Dict[str, Any], input_data: Any = None):
        """请求预览"""
        # 检查缓存
        cache_key = self._generate_cache_key(node_id, parameters)
        
        with QMutexLocker(self.cache_mutex):
            if cache_key in self.preview_cache:
                cached_result = self.preview_cache[cache_key]
                if time.time() - cached_result.task.timestamp < 5.0:  # 5秒缓存
                    self.stats["cache_hits"] += 1
                    self.preview_updated.emit(node_id, cached_result.result_image)
                    return
        
        # 创建预览任务
        task = PreviewTask(
            node_id=node_id,
            algorithm_category=category,
            algorithm_name=algorithm,
            parameters=parameters,
            input_data=input_data,
            timestamp=time.time()
        )
        
        # 分配给最空闲的工作线程
        worker = self._get_idle_worker()
        worker.add_task(task)
        
        self.stats["total_previews"] += 1
    
    def _get_idle_worker(self) -> PreviewWorker:
        """获取最空闲的工作线程"""
        # 简单的轮询分配
        return self.workers[self.stats["total_previews"] % self.worker_count]
    
    def _generate_cache_key(self, node_id: str, parameters: Dict[str, Any]) -> str:
        """生成缓存键"""
        import hashlib
        param_str = str(sorted(parameters.items()))
        return f"{node_id}_{hashlib.md5(param_str.encode()).hexdigest()[:8]}"
    
    def _on_preview_result(self, result: PreviewResult):
        """处理预览结果"""
        if result.success:
            # 更新缓存
            cache_key = self._generate_cache_key(
                result.task.node_id,
                result.task.parameters
            )
            
            with QMutexLocker(self.cache_mutex):
                self.preview_cache[cache_key] = result
                
                # 限制缓存大小
                if len(self.preview_cache) > 100:
                    # 删除最旧的缓存
                    oldest_key = min(
                        self.preview_cache.keys(),
                        key=lambda k: self.preview_cache[k].task.timestamp
                    )
                    del self.preview_cache[oldest_key]
            
            # 更新统计
            self._update_stats(result.execution_time)
            
            # 发送信号
            if result.result_image:
                self.preview_updated.emit(result.task.node_id, result.result_image)
            
            logger.debug(
                f"预览完成: {result.task.algorithm_name} "
                f"耗时: {result.execution_time:.3f}s"
            )
        else:
            # 发送错误信号
            self.preview_error.emit(
                result.task.node_id,
                result.error_message
            )
            logger.error(f"预览失败: {result.error_message}")
    
    def _on_progress_update(self, progress: int):
        """处理进度更新"""
        # 这里可以关联到具体的节点
        # 暂时忽略
        pass
    
    def _update_stats(self, execution_time: float):
        """更新统计信息"""
        # 计算平均执行时间
        total = self.stats["total_previews"]
        old_avg = self.stats["average_time"]
        self.stats["average_time"] = (old_avg * (total - 1) + execution_time) / total
    
    def clear_cache(self, node_id: Optional[str] = None):
        """清理缓存"""
        with QMutexLocker(self.cache_mutex):
            if node_id:
                # 清理特定节点的缓存
                keys_to_remove = [
                    k for k in self.preview_cache.keys()
                    if k.startswith(f"{node_id}_")
                ]
                for key in keys_to_remove:
                    del self.preview_cache[key]
            else:
                # 清理所有缓存
                self.preview_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        cache_rate = (
            self.stats["cache_hits"] / self.stats["total_previews"] * 100
            if self.stats["total_previews"] > 0 else 0
        )
        
        return {
            "总预览次数": self.stats["total_previews"],
            "缓存命中次数": self.stats["cache_hits"],
            "缓存命中率": f"{cache_rate:.1f}%",
            "平均执行时间": f"{self.stats['average_time']:.3f}s",
            "缓存大小": len(self.preview_cache)
        }
    
    def shutdown(self):
        """关闭管理器"""
        # 停止所有工作线程
        for worker in self.workers:
            worker.stop()
        
        # 清理缓存
        self.clear_cache()
        
        logger.info("预览管理器已关闭")


# 尝试导入OpenCV
try:
    import cv2
except ImportError:
    logger.warning("OpenCV未安装，某些预览功能可能不可用")
    cv2 = None 