"""
图像显示控件
用于在PyQt5界面中显示和交互处理图像
"""

from PyQt6.QtCore import Qt, pyqtSignal, QPoint, QRect, QRectF
from PyQt6.QtGui import QImage, QPixmap, QPainter, QPen, QBrush, QColor, QFont
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea, QSlider, QPushButton, QCheckBox
import numpy as np
import cv2


class ImageDisplayWidget(QWidget):
    """图像显示控件"""
    
    # 信号定义
    imageClicked = pyqtSignal(QPoint)  # 图像点击信号
    roiSelected = pyqtSignal(QRect)    # ROI选择信号
    zoomChanged = pyqtSignal(float)    # 缩放改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image = None
        self.pixmap = None
        self.scale_factor = 1.0
        self.fit_to_window = True
        self.show_info = True
        self.drawing_roi = False
        self.roi_start = None
        self.roi_end = None
        self.current_roi = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = QHBoxLayout()
        
        # 适应窗口按钮
        self.fit_button = QPushButton("适应窗口")
        self.fit_button.setCheckable(True)
        self.fit_button.setChecked(True)
        self.fit_button.clicked.connect(self.toggle_fit_to_window)
        toolbar.addWidget(self.fit_button)
        
        # 原始大小按钮
        self.original_button = QPushButton("原始大小")
        self.original_button.clicked.connect(self.show_original_size)
        toolbar.addWidget(self.original_button)
        
        # 显示信息复选框
        self.info_checkbox = QCheckBox("显示信息")
        self.info_checkbox.setChecked(True)
        self.info_checkbox.stateChanged.connect(self.toggle_info_display)
        toolbar.addWidget(self.info_checkbox)
        
        # ROI选择按钮
        self.roi_button = QPushButton("选择ROI")
        self.roi_button.setCheckable(True)
        self.roi_button.clicked.connect(self.toggle_roi_selection)
        toolbar.addWidget(self.roi_button)
        
        # 清除ROI按钮
        self.clear_roi_button = QPushButton("清除ROI")
        self.clear_roi_button.clicked.connect(self.clear_roi)
        toolbar.addWidget(self.clear_roi_button)
        
        toolbar.addStretch()
        
        # 缩放滑块
        toolbar.addWidget(QLabel("缩放:"))
        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        self.zoom_slider.setRange(10, 500)
        self.zoom_slider.setValue(100)
        self.zoom_slider.setTickInterval(50)
        self.zoom_slider.setTickPosition(QSlider.TicksBelow)
        self.zoom_slider.valueChanged.connect(self.zoom_changed)
        toolbar.addWidget(self.zoom_slider)
        
        self.zoom_label = QLabel("100%")
        toolbar.addWidget(self.zoom_label)
        
        layout.addLayout(toolbar)
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setBackgroundRole(self.palette().Dark)
        
        # 图像标签
        self.image_label = QLabel()
        self.image_label.setBackgroundRole(self.palette().Base)
        self.image_label.setSizePolicy(self.sizePolicy().Preferred, self.sizePolicy().Preferred)
        self.image_label.setScaledContents(False)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("QLabel { background-color: #2b2b2b; }")
        
        self.scroll_area.setWidget(self.image_label)
        layout.addWidget(self.scroll_area)
        
        # 设置鼠标跟踪
        self.image_label.setMouseTracking(True)
        self.image_label.installEventFilter(self)
    
    def set_image(self, image):
        """设置显示的图像
        
        Args:
            image: numpy数组格式的图像(BGR或RGB)或QImage
        """
        if image is None:
            self.clear_image()
            return
        
        if isinstance(image, np.ndarray):
            # 转换numpy数组为QImage
            if len(image.shape) == 2:
                # 灰度图
                height, width = image.shape
                bytes_per_line = width
                q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_Grayscale8)
            else:
                # 彩色图
                height, width, channel = image.shape
                bytes_per_line = 3 * width
                # OpenCV使用BGR，需要转换为RGB
                if channel == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888)
            
            self.image = q_image.copy()
        elif isinstance(image, QImage):
            self.image = image.copy()
        else:
            raise ValueError("图像必须是numpy数组或QImage格式")
        
        self.update_display()
    
    def update_display(self):
        """更新显示"""
        if self.image is None:
            return
        
        # 创建带标注的图像
        display_image = self.image.copy()
        painter = QPainter(display_image)
        
        # 绘制ROI
        if self.current_roi:
            painter.setPen(QPen(QColor(0, 255, 0), 2))
            painter.drawRect(self.current_roi)
        
        # 绘制正在选择的ROI
        if self.drawing_roi and self.roi_start and self.roi_end:
            painter.setPen(QPen(QColor(255, 255, 0), 2, Qt.DashLine))
            rect = QRect(self.roi_start, self.roi_end).normalized()
            painter.drawRect(rect)
        
        # 绘制图像信息
        if self.show_info:
            painter.setPen(QColor(255, 255, 255))
            painter.setFont(QFont("Arial", 10))
            info_text = f"尺寸: {self.image.width()} x {self.image.height()}"
            painter.drawText(10, 20, info_text)
        
        painter.end()
        
        # 转换为QPixmap
        self.pixmap = QPixmap.fromImage(display_image)
        
        # 应用缩放
        if self.fit_to_window:
            self.fit_image_to_window()
        else:
            scaled_pixmap = self.pixmap.scaled(
                self.pixmap.size() * self.scale_factor,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.image_label.setPixmap(scaled_pixmap)
            self.image_label.adjustSize()
    
    def fit_image_to_window(self):
        """使图像适应窗口大小"""
        if self.pixmap is None:
            return
        
        # 获取可用大小
        available_size = self.scroll_area.size()
        
        # 计算缩放比例
        scale_x = available_size.width() / self.pixmap.width()
        scale_y = available_size.height() / self.pixmap.height()
        scale = min(scale_x, scale_y) * 0.95  # 留一点边距
        
        # 应用缩放
        scaled_pixmap = self.pixmap.scaled(
            self.pixmap.size() * scale,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        self.image_label.setPixmap(scaled_pixmap)
        self.image_label.adjustSize()
        
        # 更新缩放因子和滑块
        self.scale_factor = scale
        self.zoom_slider.setValue(int(scale * 100))
    
    def toggle_fit_to_window(self):
        """切换适应窗口模式"""
        self.fit_to_window = self.fit_button.isChecked()
        self.zoom_slider.setEnabled(not self.fit_to_window)
        self.update_display()
    
    def show_original_size(self):
        """显示原始大小"""
        self.fit_to_window = False
        self.fit_button.setChecked(False)
        self.scale_factor = 1.0
        self.zoom_slider.setValue(100)
        self.zoom_slider.setEnabled(True)
        self.update_display()
    
    def zoom_changed(self, value):
        """缩放改变"""
        if not self.fit_to_window:
            self.scale_factor = value / 100.0
            self.zoom_label.setText(f"{value}%")
            self.update_display()
            self.zoomChanged.emit(self.scale_factor)
    
    def toggle_info_display(self):
        """切换信息显示"""
        self.show_info = self.info_checkbox.isChecked()
        self.update_display()
    
    def toggle_roi_selection(self):
        """切换ROI选择模式"""
        self.drawing_roi = self.roi_button.isChecked()
        if self.drawing_roi:
            self.image_label.setCursor(Qt.CrossCursor)
        else:
            self.image_label.setCursor(Qt.ArrowCursor)
            self.roi_start = None
            self.roi_end = None
            self.update_display()
    
    def clear_roi(self):
        """清除ROI"""
        self.current_roi = None
        self.roi_start = None
        self.roi_end = None
        self.update_display()
    
    def clear_image(self):
        """清除图像"""
        self.image = None
        self.pixmap = None
        self.current_roi = None
        self.image_label.clear()
        self.image_label.setText("无图像")
    
    def eventFilter(self, obj, event):
        """事件过滤器"""
        if obj == self.image_label and self.pixmap:
            if event.type() == event.MouseButtonPress:
                if event.button() == Qt.MouseButton.LeftButton:
                    pos = self.map_to_image(event.pos())
                    if pos:
                        if self.drawing_roi:
                            self.roi_start = pos
                            self.roi_end = pos
                        else:
                            self.imageClicked.emit(pos)
            
            elif event.type() == event.MouseMove:
                if self.drawing_roi and self.roi_start:
                    pos = self.map_to_image(event.pos())
                    if pos:
                        self.roi_end = pos
                        self.update_display()
            
            elif event.type() == event.MouseButtonRelease:
                if event.button() == Qt.MouseButton.LeftButton and self.drawing_roi and self.roi_start and self.roi_end:
                    rect = QRect(self.roi_start, self.roi_end).normalized()
                    if rect.width() > 5 and rect.height() > 5:  # 最小ROI大小
                        self.current_roi = rect
                        self.roiSelected.emit(rect)
                    self.roi_start = None
                    self.roi_end = None
                    self.update_display()
        
        return super().eventFilter(obj, event)
    
    def map_to_image(self, widget_pos):
        """将控件坐标映射到图像坐标"""
        if not self.pixmap:
            return None
        
        # 获取显示的pixmap大小
        display_pixmap = self.image_label.pixmap()
        if not display_pixmap:
            return None
        
        # 计算偏移
        x_offset = (self.image_label.width() - display_pixmap.width()) // 2
        y_offset = (self.image_label.height() - display_pixmap.height()) // 2
        
        # 检查是否在图像范围内
        x = widget_pos.x() - x_offset
        y = widget_pos.y() - y_offset
        
        if x < 0 or y < 0 or x >= display_pixmap.width() or y >= display_pixmap.height():
            return None
        
        # 映射到原始图像坐标
        if self.fit_to_window:
            scale = self.scale_factor
        else:
            scale = self.scale_factor
        
        image_x = int(x / scale)
        image_y = int(y / scale)
        
        return QPoint(image_x, image_y)
    
    def get_roi(self):
        """获取当前ROI"""
        return self.current_roi
    
    def set_roi(self, roi):
        """设置ROI"""
        if isinstance(roi, (list, tuple)) and len(roi) == 4:
            self.current_roi = QRect(roi[0], roi[1], roi[2], roi[3])
        elif isinstance(roi, QRect):
            self.current_roi = roi
        else:
            self.current_roi = None
        self.update_display()
    
    def get_image_array(self):
        """获取当前图像的numpy数组"""
        if self.image is None:
            return None
        
        # 转换QImage为numpy数组
        width = self.image.width()
        height = self.image.height()
        
        # 转换为RGB888格式
        image = self.image.convertToFormat(QImage.Format_RGB888)
        
        # 获取数据
        ptr = image.bits()
        ptr.setsize(height * width * 3)
        arr = np.array(ptr).reshape(height, width, 3)
        
        # 转换为BGR格式（OpenCV格式）
        return cv2.cvtColor(arr, cv2.COLOR_RGB2BGR)
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if self.fit_to_window and self.pixmap:
            self.update_display() 