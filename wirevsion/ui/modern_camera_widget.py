#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化相机管理UI组件

提供相机管理的完整UI功能：
- 相机列表和选择
- 实时预览
- 参数调整
- 录制和截图
- 状态监控
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QComboBox, QSlider, QSpinBox, QCheckBox,
    QGroupBox, QGridLayout, QPushButton, QFrame,
    QTabWidget, QListWidget, QListWidgetItem,
    QGraphicsView, QGraphicsScene, QGraphicsPixmapItem
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QTimer, QThread, QSize,
    QRectF, pyqtProperty, QPropertyAnimation
)
from PyQt5.QtGui import (
    QPixmap, QImage, QPainter, QColor, QFont,
    QPen, QBrush, QIcon
)
import cv2
import numpy as np
from typing import Optional, Dict, List, Any
from loguru import logger

from wirevsion.ui.modern_components import (
    ModernCard, ModernButton, ModernSwitch,
    ModernProgressBar
)


class CameraPreviewWidget(QGraphicsView):
    """相机预览组件"""
    
    # 信号
    frame_clicked = pyqtSignal(int, int)  # x, y
    roi_selected = pyqtSignal(QRectF)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 场景设置
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        # 图像项
        self.pixmap_item = QGraphicsPixmapItem()
        self.scene.addItem(self.pixmap_item)
        
        # 设置视图属性
        self._setup_view()
        
        # ROI选择
        self.selecting_roi = False
        self.roi_start = None
        self.roi_rect = None
        
        # 叠加信息
        self.show_fps = True
        self.show_info = True
        self.fps = 0
        
        logger.debug("相机预览组件初始化完成")
    
    def _setup_view(self):
        """设置视图属性"""
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.setDragMode(QGraphicsView.NoDrag)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # 背景样式
        self.setStyleSheet("""
            QGraphicsView {
                background-color: #1a1a1a;
                border: 2px solid #3d3d3d;
                border-radius: 8px;
            }
        """)
    
    def update_frame(self, frame: np.ndarray):
        """更新显示帧"""
        if frame is None:
            return
        
        try:
            # 转换为QImage
            height, width = frame.shape[:2]
            if len(frame.shape) == 3:
                bytes_per_line = 3 * width
                q_image = QImage(frame.data, width, height, 
                               bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            else:
                bytes_per_line = width
                q_image = QImage(frame.data, width, height,
                               bytes_per_line, QImage.Format_Grayscale8)
            
            # 转换为QPixmap并显示
            pixmap = QPixmap.fromImage(q_image)
            
            # 添加叠加信息
            if self.show_fps or self.show_info:
                pixmap = self._add_overlay_info(pixmap)
            
            self.pixmap_item.setPixmap(pixmap)
            
            # 自适应大小
            self.fitInView(self.pixmap_item, Qt.KeepAspectRatio)
            
        except Exception as e:
            logger.error(f"更新预览帧失败: {str(e)}")
    
    def _add_overlay_info(self, pixmap: QPixmap) -> QPixmap:
        """添加叠加信息"""
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 设置字体
        font = QFont("Arial", 12)
        painter.setFont(font)
        
        # 绘制FPS
        if self.show_fps:
            fps_text = f"FPS: {self.fps:.1f}"
            
            # 背景
            painter.fillRect(10, 10, 100, 30, QColor(0, 0, 0, 128))
            
            # 文字
            painter.setPen(QPen(QColor(0, 255, 0)))
            painter.drawText(15, 30, fps_text)
        
        # 绘制其他信息
        if self.show_info:
            info_text = f"{pixmap.width()} x {pixmap.height()}"
            
            # 背景
            painter.fillRect(10, 50, 120, 30, QColor(0, 0, 0, 128))
            
            # 文字
            painter.setPen(QPen(Qt.white))
            painter.drawText(15, 70, info_text)
        
        painter.end()
        return pixmap
    
    def set_fps(self, fps: float):
        """设置FPS值"""
        self.fps = fps
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            scene_pos = self.mapToScene(event.pos())
            self.frame_clicked.emit(int(scene_pos.x()), int(scene_pos.y()))
            
            if self.selecting_roi:
                self.roi_start = scene_pos
        
        super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.selecting_roi and self.roi_start:
            current_pos = self.mapToScene(event.pos())
            
            # 更新ROI矩形
            if self.roi_rect:
                self.scene.removeItem(self.roi_rect)
            
            rect = QRectF(self.roi_start, current_pos).normalized()
            self.roi_rect = self.scene.addRect(rect, QPen(Qt.yellow, 2))
        
        super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.selecting_roi and self.roi_start:
            current_pos = self.mapToScene(event.pos())
            rect = QRectF(self.roi_start, current_pos).normalized()
            
            self.roi_selected.emit(rect)
            self.selecting_roi = False
            self.roi_start = None
            
            if self.roi_rect:
                self.scene.removeItem(self.roi_rect)
                self.roi_rect = None
        
        super().mouseReleaseEvent(event)


class CameraControlPanel(QWidget):
    """相机控制面板"""
    
    # 信号
    parameter_changed = pyqtSignal(str, Any)  # 参数名, 值
    action_triggered = pyqtSignal(str)  # 动作名
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self._setup_ui()
        self._connect_signals()
        
        logger.debug("相机控制面板初始化完成")
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(16)
        
        # 基本控制
        basic_card = ModernCard("基本控制")
        basic_layout = QGridLayout()
        basic_layout.setSpacing(12)
        
        # 曝光控制
        basic_layout.addWidget(QLabel("曝光:"), 0, 0)
        self.exposure_slider = QSlider(Qt.Horizontal)
        self.exposure_slider.setRange(-10, 10)
        self.exposure_slider.setValue(0)
        basic_layout.addWidget(self.exposure_slider, 0, 1)
        
        self.exposure_spin = QSpinBox()
        self.exposure_spin.setRange(-10, 10)
        self.exposure_spin.setValue(0)
        self.exposure_spin.setSuffix(" EV")
        basic_layout.addWidget(self.exposure_spin, 0, 2)
        
        # 增益控制
        basic_layout.addWidget(QLabel("增益:"), 1, 0)
        self.gain_slider = QSlider(Qt.Horizontal)
        self.gain_slider.setRange(0, 100)
        self.gain_slider.setValue(50)
        basic_layout.addWidget(self.gain_slider, 1, 1)
        
        self.gain_spin = QSpinBox()
        self.gain_spin.setRange(0, 100)
        self.gain_spin.setValue(50)
        self.gain_spin.setSuffix(" %")
        basic_layout.addWidget(self.gain_spin, 1, 2)
        
        # 亮度控制
        basic_layout.addWidget(QLabel("亮度:"), 2, 0)
        self.brightness_slider = QSlider(Qt.Horizontal)
        self.brightness_slider.setRange(-100, 100)
        self.brightness_slider.setValue(0)
        basic_layout.addWidget(self.brightness_slider, 2, 1)
        
        self.brightness_spin = QSpinBox()
        self.brightness_spin.setRange(-100, 100)
        self.brightness_spin.setValue(0)
        basic_layout.addWidget(self.brightness_spin, 2, 2)
        
        # 对比度控制
        basic_layout.addWidget(QLabel("对比度:"), 3, 0)
        self.contrast_slider = QSlider(Qt.Horizontal)
        self.contrast_slider.setRange(-100, 100)
        self.contrast_slider.setValue(0)
        basic_layout.addWidget(self.contrast_slider, 3, 1)
        
        self.contrast_spin = QSpinBox()
        self.contrast_spin.setRange(-100, 100)
        self.contrast_spin.setValue(0)
        basic_layout.addWidget(self.contrast_spin, 3, 2)
        
        basic_card.content_layout.addLayout(basic_layout)
        layout.addWidget(basic_card)
        
        # 高级控制
        advanced_card = ModernCard("高级设置")
        advanced_layout = QVBoxLayout()
        advanced_layout.setSpacing(12)
        
        # 自动曝光
        self.auto_exposure_check = ModernSwitch("自动曝光")
        self.auto_exposure_check.setChecked(True)
        advanced_layout.addWidget(self.auto_exposure_check)
        
        # 自动白平衡
        self.auto_wb_check = ModernSwitch("自动白平衡")
        self.auto_wb_check.setChecked(True)
        advanced_layout.addWidget(self.auto_wb_check)
        
        # 镜像翻转
        mirror_layout = QHBoxLayout()
        self.h_flip_check = QCheckBox("水平翻转")
        self.v_flip_check = QCheckBox("垂直翻转")
        mirror_layout.addWidget(self.h_flip_check)
        mirror_layout.addWidget(self.v_flip_check)
        mirror_layout.addStretch()
        advanced_layout.addLayout(mirror_layout)
        
        advanced_card.content_layout.addLayout(advanced_layout)
        layout.addWidget(advanced_card)
        
        # 操作按钮
        action_card = ModernCard("操作")
        action_layout = QGridLayout()
        action_layout.setSpacing(8)
        
        # 截图按钮
        self.capture_btn = ModernButton("📷 截图", ModernButton.PRIMARY)
        action_layout.addWidget(self.capture_btn, 0, 0)
        
        # 录制按钮
        self.record_btn = ModernButton("⏺️ 录制", ModernButton.SUCCESS)
        self.record_btn.setCheckable(True)
        action_layout.addWidget(self.record_btn, 0, 1)
        
        # ROI选择按钮
        self.roi_btn = ModernButton("⬚ ROI", ModernButton.SECONDARY)
        self.roi_btn.setCheckable(True)
        action_layout.addWidget(self.roi_btn, 1, 0)
        
        # 重置按钮
        self.reset_btn = ModernButton("↺ 重置", ModernButton.WARNING)
        action_layout.addWidget(self.reset_btn, 1, 1)
        
        action_card.content_layout.addLayout(action_layout)
        layout.addWidget(action_card)
        
        layout.addStretch()
    
    def _connect_signals(self):
        """连接信号"""
        # 滑块和数值框同步
        self.exposure_slider.valueChanged.connect(self.exposure_spin.setValue)
        self.exposure_spin.valueChanged.connect(self.exposure_slider.setValue)
        self.exposure_slider.valueChanged.connect(
            lambda v: self.parameter_changed.emit("exposure", v))
        
        self.gain_slider.valueChanged.connect(self.gain_spin.setValue)
        self.gain_spin.valueChanged.connect(self.gain_slider.setValue)
        self.gain_slider.valueChanged.connect(
            lambda v: self.parameter_changed.emit("gain", v))
        
        self.brightness_slider.valueChanged.connect(self.brightness_spin.setValue)
        self.brightness_spin.valueChanged.connect(self.brightness_slider.setValue)
        self.brightness_slider.valueChanged.connect(
            lambda v: self.parameter_changed.emit("brightness", v))
        
        self.contrast_slider.valueChanged.connect(self.contrast_spin.setValue)
        self.contrast_spin.valueChanged.connect(self.contrast_slider.setValue)
        self.contrast_slider.valueChanged.connect(
            lambda v: self.parameter_changed.emit("contrast", v))
        
        # 复选框信号
        self.auto_exposure_check.toggled.connect(
            lambda v: self.parameter_changed.emit("auto_exposure", v))
        self.auto_wb_check.toggled.connect(
            lambda v: self.parameter_changed.emit("auto_white_balance", v))
        self.h_flip_check.toggled.connect(
            lambda v: self.parameter_changed.emit("horizontal_flip", v))
        self.v_flip_check.toggled.connect(
            lambda v: self.parameter_changed.emit("vertical_flip", v))
        
        # 按钮信号
        self.capture_btn.clicked.connect(lambda: self.action_triggered.emit("capture"))
        self.record_btn.toggled.connect(
            lambda checked: self.action_triggered.emit("record" if checked else "stop_record"))
        self.roi_btn.toggled.connect(
            lambda checked: self.action_triggered.emit("roi" if checked else "cancel_roi"))
        self.reset_btn.clicked.connect(lambda: self.action_triggered.emit("reset"))


class ModernCameraWidget(QWidget):
    """现代化相机管理主界面"""
    
    def __init__(self, camera_manager=None, parent=None):
        super().__init__(parent)
        
        self.camera_manager = camera_manager
        self.current_camera_index = -1
        
        # 帧率计算
        self.fps_timer = QTimer()
        self.fps_timer.timeout.connect(self._update_fps)
        self.frame_count = 0
        self.fps = 0
        
        # 初始化UI
        self._setup_ui()
        self._connect_signals()
        
        # 启动相机更新
        self._start_camera_update()
        
        logger.info("现代化相机管理界面初始化完成")
    
    def _setup_ui(self):
        """设置UI布局"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # 左侧预览区域
        preview_layout = QVBoxLayout()
        preview_layout.setSpacing(8)
        
        # 相机选择
        camera_select_layout = QHBoxLayout()
        camera_select_layout.addWidget(QLabel("相机:"))
        
        self.camera_combo = QComboBox()
        self.camera_combo.setMinimumWidth(200)
        camera_select_layout.addWidget(self.camera_combo)
        
        self.refresh_btn = ModernButton("刷新", ModernButton.SECONDARY)
        self.refresh_btn.setFixedWidth(80)
        camera_select_layout.addWidget(self.refresh_btn)
        
        camera_select_layout.addStretch()
        preview_layout.addLayout(camera_select_layout)
        
        # 预览窗口
        self.preview_widget = CameraPreviewWidget()
        self.preview_widget.setMinimumSize(640, 480)
        preview_layout.addWidget(self.preview_widget)
        
        # 状态栏
        self.status_bar = QLabel("就绪")
        self.status_bar.setStyleSheet("""
            QLabel {
                background-color: #2b2b2b;
                color: #ffffff;
                padding: 8px;
                border-radius: 4px;
            }
        """)
        preview_layout.addWidget(self.status_bar)
        
        layout.addLayout(preview_layout, 2)
        
        # 右侧控制面板
        self.control_panel = CameraControlPanel()
        self.control_panel.setMaximumWidth(350)
        layout.addWidget(self.control_panel, 1)
    
    def _connect_signals(self):
        """连接信号"""
        # 相机选择
        self.camera_combo.currentIndexChanged.connect(self._on_camera_changed)
        self.refresh_btn.clicked.connect(self._refresh_camera_list)
        
        # 控制面板
        self.control_panel.parameter_changed.connect(self._on_parameter_changed)
        self.control_panel.action_triggered.connect(self._on_action_triggered)
        
        # 预览窗口
        self.preview_widget.frame_clicked.connect(self._on_frame_clicked)
        self.preview_widget.roi_selected.connect(self._on_roi_selected)
    
    def _start_camera_update(self):
        """启动相机更新"""
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_camera_frame)
        self.update_timer.start(33)  # 约30 FPS
        
        # FPS计算定时器
        self.fps_timer.start(1000)  # 每秒更新
        
        # 初始化相机列表
        self._refresh_camera_list()
    
    def _refresh_camera_list(self):
        """刷新相机列表"""
        self.camera_combo.clear()
        
        if self.camera_manager:
            cameras = self.camera_manager.get_available_cameras()
            for i, camera_info in enumerate(cameras):
                self.camera_combo.addItem(f"相机 {i}: {camera_info.get('name', 'Unknown')}")
        
        if self.camera_combo.count() == 0:
            self.camera_combo.addItem("未检测到相机")
            self.status_bar.setText("未检测到相机")
        else:
            self.status_bar.setText(f"检测到 {self.camera_combo.count()} 个相机")
    
    def _on_camera_changed(self, index: int):
        """相机切换事件"""
        if index >= 0 and self.camera_manager:
            self.current_camera_index = index
            success = self.camera_manager.open_camera(index)
            
            if success:
                self.status_bar.setText(f"相机 {index} 已打开")
                logger.info(f"切换到相机 {index}")
            else:
                self.status_bar.setText(f"打开相机 {index} 失败")
                logger.error(f"打开相机 {index} 失败")
    
    def _update_camera_frame(self):
        """更新相机帧"""
        if self.camera_manager and self.current_camera_index >= 0:
            success, frame = self.camera_manager.get_frame()
            if success and frame is not None:
                self.preview_widget.update_frame(frame)
                self.frame_count += 1
            elif not success:
                pass
    
    def _update_fps(self):
        """更新FPS"""
        self.fps = self.frame_count
        self.frame_count = 0
        self.preview_widget.set_fps(self.fps)
        
        # 更新状态栏
        if self.current_camera_index >= 0:
            self.status_bar.setText(f"相机 {self.current_camera_index} - FPS: {self.fps}")
    
    def _on_parameter_changed(self, param_name: str, value: Any):
        """参数变化事件"""
        logger.debug(f"相机参数变化: {param_name} = {value}")
        
        if self.camera_manager and self.current_camera_index >= 0:
            self.camera_manager.set_parameter(param_name, value)
    
    def _on_action_triggered(self, action: str):
        """动作触发事件"""
        logger.debug(f"相机动作触发: {action}")
        
        if action == "capture":
            self._capture_image()
        elif action == "record":
            self._start_recording()
        elif action == "stop_record":
            self._stop_recording()
        elif action == "roi":
            self.preview_widget.selecting_roi = True
        elif action == "cancel_roi":
            self.preview_widget.selecting_roi = False
        elif action == "reset":
            self._reset_parameters()
    
    def _on_frame_clicked(self, x: int, y: int):
        """帧点击事件"""
        logger.debug(f"帧点击位置: ({x}, {y})")
    
    def _on_roi_selected(self, rect: QRectF):
        """ROI选择事件"""
        logger.info(f"ROI选择: {rect}")
        self.control_panel.roi_btn.setChecked(False)
    
    def _capture_image(self):
        """截图"""
        if self.camera_manager:
            # TODO: 实现截图功能
            self.status_bar.setText("截图已保存")
            logger.info("执行截图")
    
    def _start_recording(self):
        """开始录制"""
        if self.camera_manager:
            # TODO: 实现录制功能
            self.status_bar.setText("开始录制...")
            self.control_panel.record_btn.setText("⏹️ 停止")
            logger.info("开始录制")
    
    def _stop_recording(self):
        """停止录制"""
        if self.camera_manager:
            # TODO: 实现停止录制
            self.status_bar.setText("录制已停止")
            self.control_panel.record_btn.setText("⏺️ 录制")
            logger.info("停止录制")
    
    def _reset_parameters(self):
        """重置参数"""
        # 重置控制面板
        self.control_panel.exposure_slider.setValue(0)
        self.control_panel.gain_slider.setValue(50)
        self.control_panel.brightness_slider.setValue(0)
        self.control_panel.contrast_slider.setValue(0)
        self.control_panel.auto_exposure_check.setChecked(True)
        self.control_panel.auto_wb_check.setChecked(True)
        self.control_panel.h_flip_check.setChecked(False)
        self.control_panel.v_flip_check.setChecked(False)
        
        self.status_bar.setText("参数已重置")
        logger.info("相机参数已重置") 