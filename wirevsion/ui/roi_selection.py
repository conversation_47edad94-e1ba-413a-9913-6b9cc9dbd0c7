#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ROI区域选择和配置界面模块
提供ROI区域选择、颜色范围配置等功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QLabel, QGroupBox, QComboBox,
    QSlider, QCheckBox, QLineEdit, QSpinBox,
    QColorDialog, QListWidget, QListWidgetItem,
    QMessageBox, QRadioButton, QButtonGroup
)
from PyQt5.QtCore import Qt, QRect, pyqtSignal
from PyQt5.QtGui import QPixmap, QColor, QPainter, QPen, QBrush

import cv2
import numpy as np
from typing import List, Dict, Any, Optional, Tuple

from wirevsion.config.config_manager import ConfigManager
from wirevsion.config.workflow_config import ROIConfig
from wirevsion.utils.image_utils import cv_to_qpixmap, resize_image
from wirevsion.ui.modern_components import THEME_COLORS


class ROIWidget(QWidget):
    """
    ROI区域选择和配置界面
    提供ROI区域选择、颜色范围配置等功能
    """

    # 信号：ROI列表已更改
    roi_list_changed = pyqtSignal(list)

    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化ROI区域选择界面

        Args:
            config_manager: 配置管理器实例
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_manager = config_manager

        # ROI列表
        self.roi_list = []

        # 当前ROI索引
        self.current_roi_index = -1

        # 图像数据
        self.image = None
        self.display_image = None
        self.drawing = False
        self.roi_rect = None

        # 创建UI组件
        self._setup_ui()

    def _setup_ui(self):
        """设置UI组件"""
        # 主布局
        main_layout = QHBoxLayout(self)

        # 左侧：ROI列表和操作
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # ROI列表分组
        roi_list_group = QGroupBox("ROI列表")
        roi_list_layout = QVBoxLayout()

        # ROI列表
        self.roi_listwidget = QListWidget()
        self.roi_listwidget.currentRowChanged.connect(self._handle_roi_selection)

        # ROI操作按钮
        roi_buttons_layout = QHBoxLayout()

        self.add_roi_btn = QPushButton("添加")
        self.add_roi_btn.clicked.connect(self._add_roi)

        self.remove_roi_btn = QPushButton("删除")
        self.remove_roi_btn.clicked.connect(self._remove_roi)

        self.clear_roi_btn = QPushButton("清空")
        self.clear_roi_btn.clicked.connect(self._clear_roi_list)

        roi_buttons_layout.addWidget(self.add_roi_btn)
        roi_buttons_layout.addWidget(self.remove_roi_btn)
        roi_buttons_layout.addWidget(self.clear_roi_btn)

        roi_list_layout.addWidget(self.roi_listwidget)
        roi_list_layout.addLayout(roi_buttons_layout)

        roi_list_group.setLayout(roi_list_layout)

        # ROI配置分组
        roi_config_group = QGroupBox("ROI配置")
        roi_config_layout = QFormLayout()

        # ROI名称
        self.roi_name_edit = QLineEdit()
        self.roi_name_edit.setPlaceholderText("输入ROI名称")
        self.roi_name_edit.textChanged.connect(self._update_current_roi)

        # ROI位置和大小
        position_layout = QHBoxLayout()

        self.roi_x_spin = QSpinBox()
        self.roi_x_spin.setRange(0, 9999)
        self.roi_x_spin.valueChanged.connect(self._update_current_roi)

        self.roi_y_spin = QSpinBox()
        self.roi_y_spin.setRange(0, 9999)
        self.roi_y_spin.valueChanged.connect(self._update_current_roi)

        position_layout.addWidget(QLabel("X:"))
        position_layout.addWidget(self.roi_x_spin)
        position_layout.addWidget(QLabel("Y:"))
        position_layout.addWidget(self.roi_y_spin)

        size_layout = QHBoxLayout()

        self.roi_w_spin = QSpinBox()
        self.roi_w_spin.setRange(1, 9999)
        self.roi_w_spin.valueChanged.connect(self._update_current_roi)

        self.roi_h_spin = QSpinBox()
        self.roi_h_spin.setRange(1, 9999)
        self.roi_h_spin.valueChanged.connect(self._update_current_roi)

        size_layout.addWidget(QLabel("宽:"))
        size_layout.addWidget(self.roi_w_spin)
        size_layout.addWidget(QLabel("高:"))
        size_layout.addWidget(self.roi_h_spin)

        # ROI类型
        self.roi_type_group = QButtonGroup()
        type_layout = QHBoxLayout()

        self.roi_image_radio = QRadioButton("图形类型")
        self.roi_image_radio.setChecked(True)
        self.roi_type_group.addButton(self.roi_image_radio, 0)

        self.roi_color_radio = QRadioButton("颜色类型")
        self.roi_type_group.addButton(self.roi_color_radio, 1)

        self.roi_type_group.buttonClicked.connect(self._update_current_roi)

        type_layout.addWidget(self.roi_image_radio)
        type_layout.addWidget(self.roi_color_radio)

        # ROI创建方式
        self.roi_create_group = QButtonGroup()
        create_layout = QHBoxLayout()

        self.roi_draw_radio = QRadioButton("绘制")
        self.roi_draw_radio.setChecked(True)
        self.roi_create_group.addButton(self.roi_draw_radio, 0)

        self.roi_select_radio = QRadioButton("选择")
        self.roi_create_group.addButton(self.roi_select_radio, 1)

        create_layout.addWidget(self.roi_draw_radio)
        create_layout.addWidget(self.roi_select_radio)

        # 添加表单项
        roi_config_layout.addRow(QLabel("名称:"), self.roi_name_edit)
        roi_config_layout.addRow(QLabel("位置:"), position_layout)
        roi_config_layout.addRow(QLabel("尺寸:"), size_layout)
        roi_config_layout.addRow(QLabel("ROI类型:"), type_layout)
        roi_config_layout.addRow(QLabel("创建方式:"), create_layout)

        roi_config_group.setLayout(roi_config_layout)

        # 颜色范围设置
        color_group = QGroupBox("颜色范围")
        color_layout = QVBoxLayout()

        # 颜色通道标签和滑块
        channels_layout = QFormLayout()

        # 下限B通道
        self.lower_b_slider = QSlider(Qt.Horizontal)
        self.lower_b_slider.setRange(0, 255)
        self.lower_b_slider.setValue(0)
        self.lower_b_slider.valueChanged.connect(self._update_color_range)

        # 上限B通道
        self.upper_b_slider = QSlider(Qt.Horizontal)
        self.upper_b_slider.setRange(0, 255)
        self.upper_b_slider.setValue(255)
        self.upper_b_slider.valueChanged.connect(self._update_color_range)

        # 下限G通道
        self.lower_g_slider = QSlider(Qt.Horizontal)
        self.lower_g_slider.setRange(0, 255)
        self.lower_g_slider.setValue(0)
        self.lower_g_slider.valueChanged.connect(self._update_color_range)

        # 上限G通道
        self.upper_g_slider = QSlider(Qt.Horizontal)
        self.upper_g_slider.setRange(0, 255)
        self.upper_g_slider.setValue(255)
        self.upper_g_slider.valueChanged.connect(self._update_color_range)

        # 下限R通道
        self.lower_r_slider = QSlider(Qt.Horizontal)
        self.lower_r_slider.setRange(0, 255)
        self.lower_r_slider.setValue(0)
        self.lower_r_slider.valueChanged.connect(self._update_color_range)

        # 上限R通道
        self.upper_r_slider = QSlider(Qt.Horizontal)
        self.upper_r_slider.setRange(0, 255)
        self.upper_r_slider.setValue(255)
        self.upper_r_slider.valueChanged.connect(self._update_color_range)

        # 添加通道滑块到表单
        channels_layout.addRow(QLabel("下限B:"), self.lower_b_slider)
        channels_layout.addRow(QLabel("上限B:"), self.upper_b_slider)
        channels_layout.addRow(QLabel("下限G:"), self.lower_g_slider)
        channels_layout.addRow(QLabel("上限G:"), self.upper_g_slider)
        channels_layout.addRow(QLabel("下限R:"), self.lower_r_slider)
        channels_layout.addRow(QLabel("上限R:"), self.upper_r_slider)

        # 颜色值标签
        self.color_range_label = QLabel("颜色范围: [0,0,0] - [255,255,255]")

        # 颜色测试按钮
        self.test_color_btn = QPushButton("测试颜色范围")
        self.test_color_btn.clicked.connect(self._test_color_range)

        # 采集颜色按钮
        self.pick_color_btn = QPushButton("从ROI采集颜色")
        self.pick_color_btn.clicked.connect(self._pick_color_from_roi)

        # 添加组件到颜色布局
        color_layout.addLayout(channels_layout)
        color_layout.addWidget(self.color_range_label)
        color_layout.addWidget(self.test_color_btn)
        color_layout.addWidget(self.pick_color_btn)

        color_group.setLayout(color_layout)

        # 添加组件到左侧面板
        left_layout.addWidget(roi_list_group)
        left_layout.addWidget(roi_config_group)
        left_layout.addWidget(color_group)

        # 右侧：图像预览
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # 图像预览分组
        preview_group = QGroupBox("图像预览")
        preview_layout = QVBoxLayout()

        self.image_label = QLabel("无图像")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(500, 400)
        self.image_label.setStyleSheet(f"border: 1px solid {THEME_COLORS['dark_border_primary']}; background-color: {THEME_COLORS['dark_bg_input']};")
        self.image_label.setMouseTracking(True)

        # 连接鼠标事件
        self.image_label.mousePressEvent = self._mouse_press_event
        self.image_label.mouseMoveEvent = self._mouse_move_event
        self.image_label.mouseReleaseEvent = self._mouse_release_event

        preview_layout.addWidget(self.image_label)
        preview_group.setLayout(preview_layout)

        # 添加组件到右侧面板
        right_layout.addWidget(preview_group)

        # 添加左右面板到主布局
        main_layout.addWidget(left_panel, 1)
        main_layout.addWidget(right_panel, 2)

        # 初始化界面状态
        self._update_ui_state()

    def _update_ui_state(self):
        """更新界面状态"""
        has_roi = self.current_roi_index >= 0
        has_image = self.image is not None

        # 设置删除按钮状态
        self.remove_roi_btn.setEnabled(has_roi)
        self.clear_roi_btn.setEnabled(len(self.roi_list) > 0)

        # 设置颜色相关控件状态
        is_color_roi = has_roi and self.roi_color_radio.isChecked()

        self.lower_b_slider.setEnabled(is_color_roi)
        self.upper_b_slider.setEnabled(is_color_roi)
        self.lower_g_slider.setEnabled(is_color_roi)
        self.upper_g_slider.setEnabled(is_color_roi)
        self.lower_r_slider.setEnabled(is_color_roi)
        self.upper_r_slider.setEnabled(is_color_roi)
        self.test_color_btn.setEnabled(is_color_roi and has_image)
        self.pick_color_btn.setEnabled(is_color_roi and has_image)

    def set_image(self, image: np.ndarray):
        """
        设置图像

        Args:
            image: 图像数据
        """
        self.image = image

        # 更新显示
        self._update_display()

        # 更新界面状态
        self._update_ui_state()

    def _update_display(self):
        """更新图像显示"""
        if self.image is None:
            self.image_label.setText("无图像")
            self.display_image = None
            return

        # 复制图像用于显示
        self.display_image = self.image.copy()

        # 绘制所有ROI
        for i, roi in enumerate(self.roi_list):
            x, y = roi.x, roi.y
            w, h = roi.width, roi.height

            # 选择颜色，当前选中ROI用主色调，其他用成功色
            # 注意：OpenCV使用BGR格式，需要转换主题颜色
            if i == self.current_roi_index:
                # 主色调 - 蓝色
                color = (255, 121, 41)  # BGR格式的主色调
            else:
                # 成功色 - 绿色
                color = (71, 160, 67)   # BGR格式的成功色

            # 绘制矩形
            cv2.rectangle(self.display_image, (x, y), (x + w, y + h), color, 2)
            cv2.putText(self.display_image, roi.name, (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # 绘制正在绘制的矩形
        if self.drawing and self.roi_rect:
            x, y, w, h = self.roi_rect
            # 使用警告色 - 橙色
            color = (0, 179, 255)  # BGR格式的警告色
            cv2.rectangle(self.display_image, (x, y), (x + w, y + h), color, 2)

        # 转换为QPixmap并显示
        pixmap = cv_to_qpixmap(self.display_image)
        self.image_label.setPixmap(pixmap)
        self.image_label.adjustSize()

    def _add_roi(self):
        """添加新ROI"""
        # 创建新ROI配置
        roi_config = ROIConfig(
            name=f"ROI_{len(self.roi_list) + 1}",
            x=50,
            y=50,
            width=100,
            height=100,
            is_color_roi=False,
            color_lower=[0, 0, 0],
            color_upper=[255, 255, 255]
        )

        # 添加到列表
        self.roi_list.append(roi_config)

        # 添加到列表控件
        self.roi_listwidget.addItem(roi_config.name)

        # 选择新添加的ROI
        self.roi_listwidget.setCurrentRow(len(self.roi_list) - 1)

        # 更新显示
        self._update_display()

        # 发送ROI列表变更信号
        self.roi_list_changed.emit(self.roi_list)

    def _remove_roi(self):
        """删除当前ROI"""
        if self.current_roi_index < 0:
            return

        # 从列表中删除
        self.roi_list.pop(self.current_roi_index)

        # 从列表控件中删除
        self.roi_listwidget.takeItem(self.current_roi_index)

        # 更新当前索引
        if self.roi_listwidget.count() > 0:
            self.roi_listwidget.setCurrentRow(0)
        else:
            self.current_roi_index = -1
            self._load_roi_config(None)

        # 更新显示
        self._update_display()

        # 发送ROI列表变更信号
        self.roi_list_changed.emit(self.roi_list)

    def _clear_roi_list(self):
        """清空ROI列表"""
        # 确认对话框
        reply = QMessageBox.question(
            self, "确认操作", "确定要清空所有ROI吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 清空列表
            self.roi_list.clear()
            self.roi_listwidget.clear()

            # 更新当前索引
            self.current_roi_index = -1
            self._load_roi_config(None)

            # 更新显示
            self._update_display()

            # 更新界面状态
            self._update_ui_state()

            # 发送ROI列表变更信号
            self.roi_list_changed.emit(self.roi_list)

    def _handle_roi_selection(self, index: int):
        """
        处理ROI选择

        Args:
            index: ROI索引
        """
        self.current_roi_index = index

        if index >= 0 and index < len(self.roi_list):
            # 加载ROI配置
            self._load_roi_config(self.roi_list[index])
        else:
            self._load_roi_config(None)

        # 更新显示
        self._update_display()

        # 更新界面状态
        self._update_ui_state()

    def _load_roi_config(self, roi: Optional[ROIConfig]):
        """
        加载ROI配置到界面

        Args:
            roi: ROI配置，None表示清空界面
        """
        if roi is None:
            # 清空界面
            self.roi_name_edit.setText("")
            self.roi_x_spin.setValue(0)
            self.roi_y_spin.setValue(0)
            self.roi_w_spin.setValue(0)
            self.roi_h_spin.setValue(0)
            self.roi_image_radio.setChecked(True)
            self.roi_color_radio.setChecked(False)
            self.lower_b_slider.setValue(0)
            self.upper_b_slider.setValue(255)
            self.lower_g_slider.setValue(0)
            self.upper_g_slider.setValue(255)
            self.lower_r_slider.setValue(0)
            self.upper_r_slider.setValue(255)
        else:
            # 加载ROI配置
            self.roi_name_edit.setText(roi.name)
            self.roi_x_spin.setValue(roi.x)
            self.roi_y_spin.setValue(roi.y)
            self.roi_w_spin.setValue(roi.width)
            self.roi_h_spin.setValue(roi.height)

            if roi.is_color_roi:
                self.roi_color_radio.setChecked(True)
            else:
                self.roi_image_radio.setChecked(True)

            # 加载颜色范围
            lower = roi.color_lower
            upper = roi.color_upper

            self.lower_b_slider.setValue(lower[0])
            self.upper_b_slider.setValue(upper[0])
            self.lower_g_slider.setValue(lower[1])
            self.upper_g_slider.setValue(upper[1])
            self.lower_r_slider.setValue(lower[2])
            self.upper_r_slider.setValue(upper[2])

            # 更新颜色范围标签
            self._update_color_label()

    def _update_current_roi(self):
        """更新当前ROI配置"""
        if self.current_roi_index < 0:
            return

        roi = self.roi_list[self.current_roi_index]

        # 更新基本属性
        roi.name = self.roi_name_edit.text()
        roi.x = self.roi_x_spin.value()
        roi.y = self.roi_y_spin.value()
        roi.width = self.roi_w_spin.value()
        roi.height = self.roi_h_spin.value()
        roi.is_color_roi = self.roi_color_radio.isChecked()

        # 更新颜色范围
        roi.color_lower = [
            self.lower_b_slider.value(),
            self.lower_g_slider.value(),
            self.lower_r_slider.value()
        ]

        roi.color_upper = [
            self.upper_b_slider.value(),
            self.upper_g_slider.value(),
            self.upper_r_slider.value()
        ]

        # 更新列表控件项
        self.roi_listwidget.item(self.current_roi_index).setText(roi.name)

        # 更新显示
        self._update_display()

        # 更新界面状态
        self._update_ui_state()

        # 发送ROI列表变更信号
        self.roi_list_changed.emit(self.roi_list)

    def _update_color_range(self):
        """更新颜色范围"""
        # 更新颜色范围标签
        self._update_color_label()

        # 更新当前ROI
        self._update_current_roi()

    def _update_color_label(self):
        """更新颜色范围标签"""
        lower = [
            self.lower_b_slider.value(),
            self.lower_g_slider.value(),
            self.lower_r_slider.value()
        ]

        upper = [
            self.upper_b_slider.value(),
            self.upper_g_slider.value(),
            self.upper_r_slider.value()
        ]

        self.color_range_label.setText(f"颜色范围: {lower} - {upper}")

    def _test_color_range(self):
        """测试颜色范围"""
        if self.image is None or self.current_roi_index < 0:
            return

        roi = self.roi_list[self.current_roi_index]

        # 获取颜色范围
        lower = np.array(roi.color_lower, dtype=np.uint8)
        upper = np.array(roi.color_upper, dtype=np.uint8)

        # 创建掩码
        mask = cv2.inRange(self.image, lower, upper)

        # 创建结果图像
        result = cv2.bitwise_and(self.image, self.image, mask=mask)

        # 显示结果
        pixmap = cv_to_qpixmap(result)
        self.image_label.setPixmap(pixmap)

    def _pick_color_from_roi(self):
        """从ROI区域采集颜色"""
        if self.image is None or self.current_roi_index < 0:
            return

        roi = self.roi_list[self.current_roi_index]

        # 提取ROI区域
        roi_img = self.image[roi.y:roi.y + roi.height, roi.x:roi.x + roi.width]

        if roi_img.size == 0:
            QMessageBox.warning(self, "错误", "ROI区域超出图像范围")
            return

        # 计算平均颜色
        avg_color = np.mean(roi_img, axis=(0, 1)).astype(int)

        # 设置颜色范围，使用平均值的±50作为范围
        lower = np.clip(avg_color - 50, 0, 255).tolist()
        upper = np.clip(avg_color + 50, 0, 255).tolist()

        # 更新滑块
        self.lower_b_slider.setValue(lower[0])
        self.upper_b_slider.setValue(upper[0])
        self.lower_g_slider.setValue(lower[1])
        self.upper_g_slider.setValue(upper[1])
        self.lower_r_slider.setValue(lower[2])
        self.upper_r_slider.setValue(upper[2])

        # 更新颜色范围标签
        self._update_color_label()

        # 更新当前ROI
        self._update_current_roi()

    def _mouse_press_event(self, event):
        """
        鼠标按下事件

        Args:
            event: 鼠标事件
        """
        if self.image is None or not self.roi_draw_radio.isChecked():
            return

        # 开始绘制
        self.drawing = True

        # 获取鼠标位置
        x = event.x()
        y = event.y()

        # 创建初始矩形
        self.start_x = x
        self.start_y = y
        self.roi_rect = (x, y, 0, 0)

        # 更新显示
        self._update_display()

    def _mouse_move_event(self, event):
        """
        鼠标移动事件

        Args:
            event: 鼠标事件
        """
        if not self.drawing:
            return

        # 获取鼠标位置
        x = event.x()
        y = event.y()

        # 更新矩形
        width = x - self.start_x
        height = y - self.start_y

        self.roi_rect = (self.start_x, self.start_y, width, height)

        # 更新显示
        self._update_display()

    def _mouse_release_event(self, event):
        """
        鼠标释放事件

        Args:
            event: 鼠标事件
        """
        if not self.drawing:
            return

        # 结束绘制
        self.drawing = False

        # 获取鼠标位置
        x = event.x()
        y = event.y()

        # 计算矩形
        width = abs(x - self.start_x)
        height = abs(y - self.start_y)
        start_x = min(self.start_x, x)
        start_y = min(self.start_y, y)

        # 创建新ROI或更新当前ROI
        if width > 10 and height > 10:
            if self.current_roi_index >= 0:
                # 更新当前ROI
                roi = self.roi_list[self.current_roi_index]
                roi.x = start_x
                roi.y = start_y
                roi.width = width
                roi.height = height

                # 更新界面
                self._load_roi_config(roi)
            else:
                # 创建新ROI
                roi_config = ROIConfig(
                    name=f"ROI_{len(self.roi_list) + 1}",
                    x=start_x,
                    y=start_y,
                    width=width,
                    height=height,
                    is_color_roi=self.roi_color_radio.isChecked(),
                    color_lower=[0, 0, 0],
                    color_upper=[255, 255, 255]
                )

                # 添加到列表
                self.roi_list.append(roi_config)

                # 添加到列表控件
                self.roi_listwidget.addItem(roi_config.name)

                # 选择新添加的ROI
                self.roi_listwidget.setCurrentRow(len(self.roi_list) - 1)

            # 发送ROI列表变更信号
            self.roi_list_changed.emit(self.roi_list)

        # 清除临时绘制矩形
        self.roi_rect = None

        # 更新显示
        self._update_display()

    def get_roi_list(self) -> List[ROIConfig]:
        """
        获取ROI列表

        Returns:
            List[ROIConfig]: ROI配置列表
        """
        return self.roi_list
