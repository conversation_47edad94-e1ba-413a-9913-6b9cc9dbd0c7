#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
位置修正界面模块
提供位置补正和坐标系转换功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
    QPushButton, QLabel, QGroupBox, QFormLayout,
    QComboBox, QSpinBox, QDoubleSpinBox, QRadioButton,
    QButtonGroup, QLineEdit, QMessageBox
)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple

from wirevsion.config.config_manager import ConfigManager
from wirevsion.config.workflow_config import PositionCorrectionConfig


class PositionCorrectionWidget(QWidget):
    """
    位置修正界面
    提供位置补正和坐标系转换功能
    """
    
    # 信号：位置修正配置已更改
    position_correction_changed = pyqtSignal(PositionCorrectionConfig)
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化位置修正界面
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_manager = config_manager
        
        # 当前位置修正配置
        self.position_config = PositionCorrectionConfig()
        
        # 创建UI组件
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI组件"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建顶部标题和关闭按钮
        header_layout = QHBoxLayout()
        
        title_label = QLabel("位置修正")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("font-size: 16px; font-weight: bold;")
        close_btn.clicked.connect(self.hide)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(close_btn)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 基本参数选项卡
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)
        
        # 位置补正组
        position_group = QGroupBox("位置补正")
        position_layout = QFormLayout()
        
        # 选择方式
        correction_layout = QHBoxLayout()
        
        self.point_radio = QRadioButton("按点")
        self.point_radio.setChecked(True)
        
        self.coordinate_radio = QRadioButton("按坐标")
        
        self.correction_group = QButtonGroup()
        self.correction_group.addButton(self.point_radio, 0)
        self.correction_group.addButton(self.coordinate_radio, 1)
        
        correction_layout.addWidget(self.point_radio)
        correction_layout.addWidget(self.coordinate_radio)
        
        # 参照点
        self.reference_combo = QComboBox()
        self.reference_combo.setMinimumWidth(300)
        
        # 动态添加参照点后，可以设置文本
        # self.reference_combo.addItem("1 快速匹配1.匹配点[]")
        
        # 编辑按钮
        edit_btn = QPushButton()
        edit_btn.setFixedSize(20, 20)
        edit_btn.setToolTip("编辑")
        
        reference_layout = QHBoxLayout()
        reference_layout.addWidget(self.reference_combo)
        reference_layout.addWidget(edit_btn)
        
        # 角度
        self.angle_combo = QComboBox()
        self.angle_combo.setMinimumWidth(300)
        
        angle_layout = QHBoxLayout()
        angle_layout.addWidget(self.angle_combo)
        angle_layout.addWidget(QPushButton())
        
        # X方向尺度
        self.x_scale_combo = QComboBox()
        self.x_scale_combo.setMinimumWidth(300)
        
        x_scale_layout = QHBoxLayout()
        x_scale_layout.addWidget(self.x_scale_combo)
        x_scale_layout.addWidget(QPushButton())
        
        # Y方向尺度
        self.y_scale_combo = QComboBox()
        self.y_scale_combo.setMinimumWidth(300)
        
        y_scale_layout = QHBoxLayout()
        y_scale_layout.addWidget(self.y_scale_combo)
        y_scale_layout.addWidget(QPushButton())
        
        # 创建基准按钮
        self.create_reference_btn = QPushButton("创建基准")
        self.create_reference_btn.clicked.connect(self._create_reference)
        
        # 添加到表单
        position_layout.addRow(QLabel("选择方式:"), correction_layout)
        position_layout.addRow(QLabel("原点:"), reference_layout)
        position_layout.addRow(QLabel("角度:"), angle_layout)
        position_layout.addRow(QLabel("X方向尺度:"), x_scale_layout)
        position_layout.addRow(QLabel("Y方向尺度:"), y_scale_layout)
        position_layout.addRow(QLabel(""), self.create_reference_btn)
        
        position_group.setLayout(position_layout)
        
        # 添加到基本参数选项卡
        basic_layout.addWidget(position_group)
        basic_layout.addStretch()
        
        # 结果显示选项卡
        result_tab = QWidget()
        result_layout = QVBoxLayout(result_tab)
        
        # 结果显示选项
        display_group = QGroupBox("结果显示")
        display_layout = QFormLayout()
        
        # 添加显示选项，使用与基本参数类似的布局
        
        display_group.setLayout(display_layout)
        
        result_layout.addWidget(display_group)
        result_layout.addStretch()
        
        # 添加选项卡到选项卡组件
        self.tab_widget.addTab(basic_tab, "基本参数")
        self.tab_widget.addTab(result_tab, "结果显示")
        
        # 底部按钮
        buttons_layout = QHBoxLayout()
        
        self.run_btn = QPushButton("执行")
        self.run_btn.clicked.connect(self._execute_correction)
        
        self.continue_btn = QPushButton("连续执行")
        self.continue_btn.clicked.connect(self._continue_execute)
        
        self.confirm_btn = QPushButton("确定")
        self.confirm_btn.clicked.connect(self._confirm_correction)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.run_btn)
        buttons_layout.addWidget(self.continue_btn)
        buttons_layout.addWidget(self.confirm_btn)
        
        # 添加组件到主布局
        main_layout.addLayout(header_layout)
        main_layout.addWidget(self.tab_widget)
        main_layout.addLayout(buttons_layout)
    
    def _create_reference(self):
        """创建坐标系基准"""
        # 实际应用中，这里可能需要打开一个对话框来设置坐标系基准
        QMessageBox.information(self, "创建基准", "已创建新的坐标系基准")
        
        # 更新位置修正配置
        self.position_config.reference_point = "新创建的基准点"
        
        # 发送配置更改信号
        self.position_correction_changed.emit(self.position_config)
    
    def _execute_correction(self):
        """执行位置修正"""
        # 获取参数并执行位置修正逻辑
        
        # 更新位置修正配置
        self.position_config.use_points = self.point_radio.isChecked()
        
        # 发送配置更改信号
        self.position_correction_changed.emit(self.position_config)
        
        QMessageBox.information(self, "执行位置修正", "位置修正已执行")
    
    def _continue_execute(self):
        """连续执行位置修正"""
        # 实际应用中可能需要启动一个定时器来连续执行
        self._execute_correction()
    
    def _confirm_correction(self):
        """确认位置修正配置"""
        # 更新位置修正配置
        self.position_config.use_points = self.point_radio.isChecked()
        
        # 发送配置更改信号
        self.position_correction_changed.emit(self.position_config)
        
        # 关闭窗口
        self.hide()
    
    def get_position_correction_config(self) -> PositionCorrectionConfig:
        """
        获取当前位置修正配置
        
        Returns:
            PositionCorrectionConfig: 位置修正配置
        """
        return self.position_config 