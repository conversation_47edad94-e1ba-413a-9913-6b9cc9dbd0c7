"""
YOLO检测器模块
用于YOLO模型的加载、推理和结果解析
"""

import os
import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QThread
from loguru import logger


class YOLODetector(QObject):
    """YOLO检测器"""
    
    # 信号定义
    detectionCompleted = pyqtSignal(dict)  # 检测完成信号
    detectionFailed = pyqtSignal(str)     # 检测失败信号
    modelLoaded = pyqtSignal(str)         # 模型加载完成信号
    progressUpdated = pyqtSignal(int)     # 进度更新信号
    
    def __init__(self):
        super().__init__()
        self.model = None
        self.model_info = {}
        self.is_loaded = False
        self.device = "cpu"
        self.supported_models = {
            "YOLOv8n": "yolov8n.pt",
            "YOLOv8s": "yolov8s.pt",
            "YOLOv8m": "yolov8m.pt",
            "YOLOv8l": "yolov8l.pt",
            "YOLOv8x": "yolov8x.pt",
            "YOLOv5n": "yolov5n.pt",
            "YOLOv5s": "yolov5s.pt",
            "YOLOv5m": "yolov5m.pt",
            "YOLOv5l": "yolov5l.pt",
            "YOLOv5x": "yolov5x.pt"
        }
    
    def load_model(self, model_path: str, device: str = "auto") -> bool:
        """加载YOLO模型
        
        Args:
            model_path: 模型文件路径
            device: 运行设备 (auto, cpu, cuda, mps)
            
        Returns:
            是否加载成功
        """
        try:
            # 检查ultralytics是否安装
            try:
                from ultralytics import YOLO
            except ImportError:
                logger.error("未安装ultralytics库，请先安装: pip install ultralytics")
                self.detectionFailed.emit("未安装ultralytics库")
                return False
            
            # 检查模型文件是否存在
            if not os.path.exists(model_path):
                # 尝试下载预训练模型
                logger.info(f"模型文件不存在，尝试下载: {model_path}")
            
            # 自动选择设备
            if device == "auto":
                import torch
                if torch.cuda.is_available():
                    device = "cuda"
                    device_name = torch.cuda.get_device_name(0)
                    logger.info(f"使用CUDA设备: {device_name}")
                elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    device = "mps"
                    logger.info("使用Apple Silicon GPU")
                else:
                    device = "cpu"
                    logger.info("使用CPU")
            
            self.device = device
            
            # 加载模型
            logger.info(f"加载YOLO模型: {model_path}")
            self.model = YOLO(model_path)
            self.model.to(device)
            
            # 获取模型信息
            self.model_info = {
                "model_path": model_path,
                "device": device,
                "model_type": self._get_model_type(model_path),
                "num_classes": len(self.model.names),
                "class_names": list(self.model.names.values()),
                "input_size": 640  # 默认输入尺寸
            }
            
            self.is_loaded = True
            self.modelLoaded.emit(model_path)
            logger.info("YOLO模型加载成功")
            
            return True
            
        except Exception as e:
            logger.error(f"加载YOLO模型失败: {e}")
            self.detectionFailed.emit(f"加载模型失败: {str(e)}")
            return False
    
    def detect(self, image: np.ndarray, **kwargs) -> Dict[str, Any]:
        """执行目标检测
        
        Args:
            image: 输入图像 (numpy数组，BGR格式)
            **kwargs: 检测参数
                - confidence_threshold: 置信度阈值 (默认0.5)
                - iou_threshold: IoU阈值 (默认0.45)
                - max_detections: 最大检测数量 (默认100)
                - imgsz: 输入图像尺寸 (默认640)
                - classes: 要检测的类别ID列表 (默认None，检测所有类别)
                
        Returns:
            检测结果字典
        """
        if not self.is_loaded or self.model is None:
            error_msg = "模型未加载"
            logger.error(error_msg)
            self.detectionFailed.emit(error_msg)
            return {"success": False, "error": error_msg}
        
        try:
            start_time = time.time()
            
            # 获取检测参数
            conf = kwargs.get('confidence_threshold', 0.5)
            iou = kwargs.get('iou_threshold', 0.45)
            max_det = kwargs.get('max_detections', 100)
            imgsz = kwargs.get('imgsz', 640)
            classes = kwargs.get('classes', None)
            
            # 执行检测
            results = self.model(
                image,
                conf=conf,
                iou=iou,
                imgsz=imgsz,
                max_det=max_det,
                classes=classes,
                verbose=False
            )
            
            # 解析结果
            detections = []
            annotated_image = image.copy()
            
            if results and len(results) > 0:
                result = results[0]
                
                if result.boxes is not None and len(result.boxes) > 0:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy()
                    
                    for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                        cls_id = int(cls_id)
                        class_name = self.model.names.get(cls_id, f"class_{cls_id}")
                        
                        x1, y1, x2, y2 = map(int, box)
                        
                        detection = {
                            "id": i,
                            "class_id": cls_id,
                            "class_name": class_name,
                            "confidence": float(conf),
                            "bbox": [x1, y1, x2, y2],
                            "bbox_dict": {
                                "x1": x1, "y1": y1, "x2": x2, "y2": y2,
                                "width": x2 - x1,
                                "height": y2 - y1,
                                "center_x": (x1 + x2) // 2,
                                "center_y": (y1 + y2) // 2
                            }
                        }
                        detections.append(detection)
                        
                        # 绘制检测结果
                        color = self._get_class_color(cls_id)
                        cv2.rectangle(annotated_image, (x1, y1), (x2, y2), color, 2)
                        
                        # 绘制标签
                        label = f"{class_name}: {conf:.2f}"
                        self._draw_label(annotated_image, label, (x1, y1), color)
            
            # 统计信息
            class_counts = {}
            for det in detections:
                class_name = det["class_name"]
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            inference_time = time.time() - start_time
            
            result_data = {
                "success": True,
                "detections": detections,
                "annotated_image": annotated_image,
                "num_detections": len(detections),
                "class_counts": class_counts,
                "inference_time": inference_time,
                "model_info": self.model_info,
                "parameters": {
                    "confidence_threshold": conf,
                    "iou_threshold": iou,
                    "input_size": imgsz
                }
            }
            
            self.detectionCompleted.emit(result_data)
            return result_data
            
        except Exception as e:
            error_msg = f"检测失败: {str(e)}"
            logger.error(error_msg)
            self.detectionFailed.emit(error_msg)
            return {"success": False, "error": error_msg}
    
    def detect_batch(self, images: List[np.ndarray], **kwargs) -> List[Dict[str, Any]]:
        """批量检测
        
        Args:
            images: 图像列表
            **kwargs: 检测参数
            
        Returns:
            检测结果列表
        """
        results = []
        total = len(images)
        
        for i, image in enumerate(images):
            # 更新进度
            progress = int((i + 1) / total * 100)
            self.progressUpdated.emit(progress)
            
            # 执行检测
            result = self.detect(image, **kwargs)
            results.append(result)
        
        return results
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return self.model_info.copy()
    
    def get_supported_models(self) -> Dict[str, str]:
        """获取支持的模型列表"""
        return self.supported_models.copy()
    
    def set_device(self, device: str):
        """设置运行设备"""
        if self.model and device != self.device:
            try:
                self.model.to(device)
                self.device = device
                self.model_info["device"] = device
                logger.info(f"设备切换到: {device}")
            except Exception as e:
                logger.error(f"设备切换失败: {e}")
    
    def _get_model_type(self, model_path: str) -> str:
        """从模型路径推断模型类型"""
        model_name = os.path.basename(model_path).lower()
        if "yolov8" in model_name:
            return "YOLOv8"
        elif "yolov5" in model_name:
            return "YOLOv5"
        elif "yolov3" in model_name:
            return "YOLOv3"
        else:
            return "YOLO"
    
    def _get_class_color(self, class_id: int) -> Tuple[int, int, int]:
        """获取类别对应的颜色"""
        # 使用HSV颜色空间生成不同的颜色
        hue = (class_id * 30) % 180
        color_hsv = np.array([[[hue, 255, 255]]], dtype=np.uint8)
        color_bgr = cv2.cvtColor(color_hsv, cv2.COLOR_HSV2BGR)[0][0]
        return tuple(map(int, color_bgr))
    
    def _draw_label(self, image: np.ndarray, label: str, position: Tuple[int, int], 
                   color: Tuple[int, int, int]):
        """绘制标签
        
        Args:
            image: 图像
            label: 标签文本
            position: 位置 (x, y)
            color: 颜色
        """
        x, y = position
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2
        
        # 获取文本大小
        (text_width, text_height), baseline = cv2.getTextSize(
            label, font, font_scale, thickness
        )
        
        # 绘制背景
        cv2.rectangle(
            image,
            (x, y - text_height - 10),
            (x + text_width + 10, y),
            color,
            -1
        )
        
        # 绘制文本
        cv2.putText(
            image,
            label,
            (x + 5, y - 5),
            font,
            font_scale,
            (255, 255, 255),
            thickness
        )
    
    def export_model(self, format: str = "onnx", **kwargs) -> str:
        """导出模型
        
        Args:
            format: 导出格式 (onnx, torchscript, coreml, tflite等)
            **kwargs: 导出参数
            
        Returns:
            导出的模型路径
        """
        if not self.is_loaded or self.model is None:
            raise RuntimeError("模型未加载")
        
        try:
            export_path = self.model.export(format=format, **kwargs)
            logger.info(f"模型导出成功: {export_path}")
            return export_path
        except Exception as e:
            logger.error(f"模型导出失败: {e}")
            raise


class YOLODetectorThread(QThread):
    """YOLO检测线程"""
    
    resultReady = pyqtSignal(dict)
    errorOccurred = pyqtSignal(str)
    progressUpdated = pyqtSignal(int)
    
    def __init__(self, detector: YOLODetector):
        super().__init__()
        self.detector = detector
        self.image = None
        self.parameters = {}
    
    def set_task(self, image: np.ndarray, parameters: Dict[str, Any]):
        """设置检测任务"""
        self.image = image
        self.parameters = parameters
    
    def run(self):
        """运行检测"""
        if self.image is None:
            self.errorOccurred.emit("未设置输入图像")
            return
        
        try:
            result = self.detector.detect(self.image, **self.parameters)
            if result["success"]:
                self.resultReady.emit(result)
            else:
                self.errorOccurred.emit(result.get("error", "检测失败"))
        except Exception as e:
            self.errorOccurred.emit(str(e)) 