"""
YOLO深度学习UI组件

该模块提供YOLO相关功能的用户界面，包括：
- 模型加载和管理
- 数据集准备和训练
- 实时检测和结果展示
- 模型评估和导出

作者: 张玉龙
创建时间: 2025-01-XX
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
import json

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QSpinBox,
    QDoubleSpinBox, QTextEdit, QProgressBar, QTabWidget,
    QGroupBox, QFileDialog, QMessageBox, QTableWidget,
    QTableWidgetItem, QHeaderView, QSlider, QCheckBox,
    QSplitter, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QPixmap, QFont, QIcon

from loguru import logger

# 导入核心模块
try:
    from ..core.yolo_detector import YOLODetector, DetectionResult, create_yolo_detector
    from ..core.yolo_trainer import YOLOTrainer, TrainingConfig, DatasetInfo, create_yolo_trainer
except ImportError:
    logger.warning("无法导入YOLO核心模块，某些功能可能不可用")
    YOLODetector = None
    YOLOTrainer = None

class TrainingThread(QThread):
    """训练线程"""
    progress_updated = pyqtSignal(str)  # 进度信息
    training_completed = pyqtSignal(dict)  # 训练完成
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self, trainer: 'YOLOTrainer', dataset_path: str, 
                 config: 'TrainingConfig', model_name: str):
        super().__init__()
        self.trainer = trainer
        self.dataset_path = dataset_path
        self.config = config
        self.model_name = model_name
        
    def run(self):
        try:
            self.progress_updated.emit("开始训练模型...")
            
            # 执行训练
            result = self.trainer.train_model(
                self.dataset_path,
                self.config,
                self.model_name
            )
            
            if result.status == "completed":
                self.training_completed.emit({
                    'model_path': result.model_path,
                    'metrics': result.final_metrics
                })
            else:
                self.error_occurred.emit(result.error_message)
                
        except Exception as e:
            self.error_occurred.emit(str(e))

class YOLOWidget(QWidget):
    """YOLO深度学习功能组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.detector: Optional[YOLODetector] = None
        self.trainer: Optional[YOLOTrainer] = None
        self.training_thread: Optional[TrainingThread] = None
        
        self.init_ui()
        self.init_components()
        self.connect_signals()
        
        logger.info("YOLO组件初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 模型管理选项卡
        self.model_tab = self.create_model_tab()
        self.tab_widget.addTab(self.model_tab, "模型管理")
        
        # 数据集管理选项卡
        self.dataset_tab = self.create_dataset_tab()
        self.tab_widget.addTab(self.dataset_tab, "数据集管理")
        
        # 模型训练选项卡
        self.training_tab = self.create_training_tab()
        self.tab_widget.addTab(self.training_tab, "模型训练")
        
        # 检测配置选项卡
        self.detection_tab = self.create_detection_tab()
        self.tab_widget.addTab(self.detection_tab, "检测配置")
    
    def create_model_tab(self) -> QWidget:
        """创建模型管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 模型选择区域
        model_group = QGroupBox("模型选择")
        model_layout = QGridLayout()
        model_group.setLayout(model_layout)
        
        # 预训练模型选择
        model_layout.addWidget(QLabel("预训练模型:"), 0, 0)
        self.pretrained_combo = QComboBox()
        self.pretrained_combo.addItems([
            "yolov8n.pt", "yolov8s.pt", "yolov8m.pt", 
            "yolov8l.pt", "yolov8x.pt"
        ])
        model_layout.addWidget(self.pretrained_combo, 0, 1)
        
        # 自定义模型选择
        model_layout.addWidget(QLabel("自定义模型:"), 1, 0)
        self.custom_model_path = QLineEdit()
        model_layout.addWidget(self.custom_model_path, 1, 1)
        
        self.browse_model_btn = QPushButton("浏览...")
        model_layout.addWidget(self.browse_model_btn, 1, 2)
        
        # 模型操作按钮
        buttons_layout = QHBoxLayout()
        self.load_model_btn = QPushButton("加载模型")
        self.model_info_btn = QPushButton("模型信息")
        self.export_model_btn = QPushButton("导出模型")
        
        buttons_layout.addWidget(self.load_model_btn)
        buttons_layout.addWidget(self.model_info_btn)
        buttons_layout.addWidget(self.export_model_btn)
        buttons_layout.addStretch()
        
        model_layout.addLayout(buttons_layout, 2, 0, 1, 3)
        layout.addWidget(model_group)
        
        # 模型信息显示
        info_group = QGroupBox("模型信息")
        info_layout = QVBoxLayout()
        info_group.setLayout(info_layout)
        
        self.model_info_text = QTextEdit()
        self.model_info_text.setMaximumHeight(150)
        self.model_info_text.setReadOnly(True)
        info_layout.addWidget(self.model_info_text)
        
        layout.addWidget(info_group)
        
        # 已训练模型列表
        trained_group = QGroupBox("已训练模型")
        trained_layout = QVBoxLayout()
        trained_group.setLayout(trained_layout)
        
        self.trained_models_table = QTableWidget()
        self.trained_models_table.setColumnCount(4)
        self.trained_models_table.setHorizontalHeaderLabels([
            "模型名称", "状态", "mAP50", "数据集"
        ])
        self.trained_models_table.horizontalHeader().setStretchLastSection(True)
        trained_layout.addWidget(self.trained_models_table)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新列表")
        trained_layout.addWidget(refresh_btn)
        refresh_btn.clicked.connect(self.refresh_trained_models)
        
        layout.addWidget(trained_group)
        layout.addStretch()
        
        return widget
    
    def create_dataset_tab(self) -> QWidget:
        """创建数据集管理选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 数据集创建区域
        create_group = QGroupBox("创建数据集")
        create_layout = QGridLayout()
        create_group.setLayout(create_layout)
        
        # 数据集信息
        create_layout.addWidget(QLabel("数据集名称:"), 0, 0)
        self.dataset_name_edit = QLineEdit()
        create_layout.addWidget(self.dataset_name_edit, 0, 1)
        
        create_layout.addWidget(QLabel("图像目录:"), 1, 0)
        self.images_dir_edit = QLineEdit()
        create_layout.addWidget(self.images_dir_edit, 1, 1)
        
        browse_images_btn = QPushButton("浏览...")
        create_layout.addWidget(browse_images_btn, 1, 2)
        
        create_layout.addWidget(QLabel("标注目录:"), 2, 0)
        self.annotations_dir_edit = QLineEdit()
        create_layout.addWidget(self.annotations_dir_edit, 2, 1)
        
        browse_annotations_btn = QPushButton("浏览...")
        create_layout.addWidget(browse_annotations_btn, 2, 2)
        
        # 类别设置
        create_layout.addWidget(QLabel("类别名称:"), 3, 0)
        self.class_names_edit = QTextEdit()
        self.class_names_edit.setMaximumHeight(80)
        self.class_names_edit.setPlaceholderText("每行一个类别名称，例如:\nperson\ncar\nbike")
        create_layout.addWidget(self.class_names_edit, 3, 1, 1, 2)
        
        # 数据分割比例
        create_layout.addWidget(QLabel("训练集比例:"), 4, 0)
        self.train_ratio_spin = QDoubleSpinBox()
        self.train_ratio_spin.setRange(0.1, 0.9)
        self.train_ratio_spin.setValue(0.8)
        self.train_ratio_spin.setDecimals(1)
        self.train_ratio_spin.setSingleStep(0.1)
        create_layout.addWidget(self.train_ratio_spin, 4, 1)
        
        # 创建按钮
        self.create_dataset_btn = QPushButton("创建数据集")
        create_layout.addWidget(self.create_dataset_btn, 5, 0, 1, 3)
        
        layout.addWidget(create_group)
        
        # 数据集列表
        list_group = QGroupBox("数据集列表")
        list_layout = QVBoxLayout()
        list_group.setLayout(list_layout)
        
        self.datasets_table = QTableWidget()
        self.datasets_table.setColumnCount(5)
        self.datasets_table.setHorizontalHeaderLabels([
            "名称", "训练图像", "验证图像", "类别数", "路径"
        ])
        self.datasets_table.horizontalHeader().setStretchLastSection(True)
        list_layout.addWidget(self.datasets_table)
        
        # 数据集操作按钮
        dataset_buttons = QHBoxLayout()
        self.validate_dataset_btn = QPushButton("验证数据集")
        self.delete_dataset_btn = QPushButton("删除数据集")
        refresh_datasets_btn = QPushButton("刷新列表")
        
        dataset_buttons.addWidget(self.validate_dataset_btn)
        dataset_buttons.addWidget(self.delete_dataset_btn)
        dataset_buttons.addWidget(refresh_datasets_btn)
        dataset_buttons.addStretch()
        
        list_layout.addLayout(dataset_buttons)
        layout.addWidget(list_group)
        
        # 连接信号
        browse_images_btn.clicked.connect(self.browse_images_dir)
        browse_annotations_btn.clicked.connect(self.browse_annotations_dir)
        refresh_datasets_btn.clicked.connect(self.refresh_datasets)
        
        layout.addStretch()
        return widget
    
    def create_training_tab(self) -> QWidget:
        """创建模型训练选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 训练配置
        config_group = QGroupBox("训练配置")
        config_layout = QGridLayout()
        config_group.setLayout(config_layout)
        
        # 基础参数
        config_layout.addWidget(QLabel("训练轮数:"), 0, 0)
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 1000)
        self.epochs_spin.setValue(100)
        config_layout.addWidget(self.epochs_spin, 0, 1)
        
        config_layout.addWidget(QLabel("批次大小:"), 0, 2)
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 64)
        self.batch_size_spin.setValue(16)
        config_layout.addWidget(self.batch_size_spin, 0, 3)
        
        config_layout.addWidget(QLabel("图像尺寸:"), 1, 0)
        self.image_size_spin = QSpinBox()
        self.image_size_spin.setRange(320, 1280)
        self.image_size_spin.setValue(640)
        self.image_size_spin.setSingleStep(32)
        config_layout.addWidget(self.image_size_spin, 1, 1)
        
        config_layout.addWidget(QLabel("学习率:"), 1, 2)
        self.lr_spin = QDoubleSpinBox()
        self.lr_spin.setRange(0.0001, 0.1)
        self.lr_spin.setValue(0.01)
        self.lr_spin.setDecimals(4)
        config_layout.addWidget(self.lr_spin, 1, 3)
        
        # 高级参数
        config_layout.addWidget(QLabel("权重衰减:"), 2, 0)
        self.weight_decay_spin = QDoubleSpinBox()
        self.weight_decay_spin.setRange(0, 0.01)
        self.weight_decay_spin.setValue(0.0005)
        self.weight_decay_spin.setDecimals(6)
        config_layout.addWidget(self.weight_decay_spin, 2, 1)
        
        config_layout.addWidget(QLabel("预热轮数:"), 2, 2)
        self.warmup_epochs_spin = QSpinBox()
        self.warmup_epochs_spin.setRange(0, 10)
        self.warmup_epochs_spin.setValue(3)
        config_layout.addWidget(self.warmup_epochs_spin, 2, 3)
        
        config_layout.addWidget(QLabel("早停耐心值:"), 3, 0)
        self.patience_spin = QSpinBox()
        self.patience_spin.setRange(10, 200)
        self.patience_spin.setValue(50)
        config_layout.addWidget(self.patience_spin, 3, 1)
        
        config_layout.addWidget(QLabel("工作进程数:"), 3, 2)
        self.workers_spin = QSpinBox()
        self.workers_spin.setRange(0, 16)
        self.workers_spin.setValue(8)
        config_layout.addWidget(self.workers_spin, 3, 3)
        
        layout.addWidget(config_group)
        
        # 训练控制
        control_group = QGroupBox("训练控制")
        control_layout = QVBoxLayout()
        control_group.setLayout(control_layout)
        
        # 模型名称和数据集选择
        control_params = QGridLayout()
        control_params.addWidget(QLabel("模型名称:"), 0, 0)
        self.model_name_edit = QLineEdit()
        self.model_name_edit.setPlaceholderText("例如: my_custom_model")
        control_params.addWidget(self.model_name_edit, 0, 1)
        
        control_params.addWidget(QLabel("选择数据集:"), 1, 0)
        self.dataset_combo = QComboBox()
        control_params.addWidget(self.dataset_combo, 1, 1)
        
        control_layout.addLayout(control_params)
        
        # 训练按钮
        train_buttons = QHBoxLayout()
        self.start_training_btn = QPushButton("开始训练")
        self.stop_training_btn = QPushButton("停止训练")
        self.stop_training_btn.setEnabled(False)
        
        train_buttons.addWidget(self.start_training_btn)
        train_buttons.addWidget(self.stop_training_btn)
        train_buttons.addStretch()
        
        control_layout.addLayout(train_buttons)
        
        # 训练进度
        self.training_progress = QProgressBar()
        self.training_progress.setVisible(False)
        control_layout.addWidget(self.training_progress)
        
        layout.addWidget(control_group)
        
        # 训练日志
        log_group = QGroupBox("训练日志")
        log_layout = QVBoxLayout()
        log_group.setLayout(log_layout)
        
        self.training_log = QTextEdit()
        self.training_log.setMaximumHeight(200)
        self.training_log.setReadOnly(True)
        log_layout.addWidget(self.training_log)
        
        layout.addWidget(log_group)
        layout.addStretch()
        
        return widget
    
    def create_detection_tab(self) -> QWidget:
        """创建检测配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 检测参数
        params_group = QGroupBox("检测参数")
        params_layout = QGridLayout()
        params_group.setLayout(params_layout)
        
        # 置信度阈值
        params_layout.addWidget(QLabel("置信度阈值:"), 0, 0)
        self.confidence_slider = QSlider(Qt.Orientation.Horizontal)
        self.confidence_slider.setRange(1, 100)
        self.confidence_slider.setValue(50)
        params_layout.addWidget(self.confidence_slider, 0, 1)
        
        self.confidence_label = QLabel("0.50")
        params_layout.addWidget(self.confidence_label, 0, 2)
        
        # IoU阈值
        params_layout.addWidget(QLabel("IoU阈值:"), 1, 0)
        self.iou_slider = QSlider(Qt.Orientation.Horizontal)
        self.iou_slider.setRange(1, 100)
        self.iou_slider.setValue(45)
        params_layout.addWidget(self.iou_slider, 1, 1)
        
        self.iou_label = QLabel("0.45")
        params_layout.addWidget(self.iou_label, 1, 2)
        
        # 检测选项
        self.show_labels_cb = QCheckBox("显示标签")
        self.show_labels_cb.setChecked(True)
        params_layout.addWidget(self.show_labels_cb, 2, 0)
        
        self.show_confidence_cb = QCheckBox("显示置信度")
        self.show_confidence_cb.setChecked(True)
        params_layout.addWidget(self.show_confidence_cb, 2, 1)
        
        layout.addWidget(params_group)
        
        # 检测统计
        stats_group = QGroupBox("检测统计")
        stats_layout = QVBoxLayout()
        stats_group.setLayout(stats_layout)
        
        self.detection_stats = QTextEdit()
        self.detection_stats.setMaximumHeight(150)
        self.detection_stats.setReadOnly(True)
        stats_layout.addWidget(self.detection_stats)
        
        layout.addWidget(stats_group)
        layout.addStretch()
        
        return widget
    
    def init_components(self):
        """初始化组件"""
        try:
            # 初始化YOLO训练器
            if YOLOTrainer:
                self.trainer = create_yolo_trainer()
                self.refresh_datasets()
                self.refresh_trained_models()
            
            # 更新界面状态
            self.update_ui_state()
            
        except Exception as e:
            logger.error(f"初始化组件失败: {e}")
            self.show_error("初始化失败", f"无法初始化YOLO组件: {e}")
    
    def connect_signals(self):
        """连接信号和槽"""
        # 模型管理
        self.browse_model_btn.clicked.connect(self.browse_model_file)
        self.load_model_btn.clicked.connect(self.load_model)
        self.model_info_btn.clicked.connect(self.show_model_info)
        self.export_model_btn.clicked.connect(self.export_model)
        
        # 数据集管理
        self.create_dataset_btn.clicked.connect(self.create_dataset)
        self.validate_dataset_btn.clicked.connect(self.validate_dataset)
        self.delete_dataset_btn.clicked.connect(self.delete_dataset)
        
        # 模型训练
        self.start_training_btn.clicked.connect(self.start_training)
        self.stop_training_btn.clicked.connect(self.stop_training)
        
        # 检测参数
        self.confidence_slider.valueChanged.connect(self.update_confidence_label)
        self.iou_slider.valueChanged.connect(self.update_iou_label)
    
    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模型文件", "", 
            "YOLO模型 (*.pt *.onnx);;所有文件 (*)"
        )
        if file_path:
            self.custom_model_path.setText(file_path)
    
    def browse_images_dir(self):
        """浏览图像目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择图像目录")
        if dir_path:
            self.images_dir_edit.setText(dir_path)
    
    def browse_annotations_dir(self):
        """浏览标注目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择标注目录")
        if dir_path:
            self.annotations_dir_edit.setText(dir_path)
    
    def load_model(self):
        """加载模型"""
        try:
            # 获取模型路径
            if self.custom_model_path.text():
                model_path = self.custom_model_path.text()
            else:
                model_path = self.pretrained_combo.currentText()
            
            # 创建检测器并加载模型
            if YOLODetector:
                self.detector = create_yolo_detector(model_path)
                if self.detector.is_loaded:
                    self.show_info("成功", f"模型加载成功: {model_path}")
                    self.update_model_info()
                else:
                    self.show_error("失败", "模型加载失败")
            else:
                self.show_error("错误", "YOLO模块未安装")
                
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            self.show_error("加载失败", str(e))
    
    def show_model_info(self):
        """显示模型信息"""
        if self.detector and self.detector.is_loaded:
            self.update_model_info()
        else:
            self.show_warning("提示", "请先加载模型")
    
    def update_model_info(self):
        """更新模型信息显示"""
        if self.detector and self.detector.is_loaded:
            info = self.detector.get_model_info()
            info_text = "\n".join([
                f"模型路径: {info.get('model_path', 'N/A')}",
                f"计算设备: {info.get('device', 'N/A')}",
                f"类别数量: {info.get('class_count', 0)}",
                f"模型类型: {info.get('model_type', 'N/A')}",
                f"总参数: {info.get('total_parameters', 'N/A')}",
                f"部分类别: {', '.join(info.get('class_names', [])[:5])}..."
            ])
            self.model_info_text.setText(info_text)
        else:
            self.model_info_text.setText("未加载模型")
    
    def export_model(self):
        """导出模型"""
        if not self.detector or not self.detector.is_loaded:
            self.show_warning("提示", "请先加载模型")
            return
        
        try:
            # 选择导出格式和路径
            formats = ["onnx", "engine", "coreml", "saved_model"]
            format_choice, ok = self.show_input_dialog(
                "导出格式", "选择导出格式:", formats
            )
            
            if ok and format_choice:
                export_path = self.detector.export_model(format_choice)
                if export_path:
                    self.show_info("成功", f"模型已导出: {export_path}")
                else:
                    self.show_error("失败", "模型导出失败")
                    
        except Exception as e:
            logger.error(f"导出模型失败: {e}")
            self.show_error("导出失败", str(e))
    
    def create_dataset(self):
        """创建数据集"""
        try:
            # 验证输入
            dataset_name = self.dataset_name_edit.text().strip()
            images_dir = self.images_dir_edit.text().strip()
            annotations_dir = self.annotations_dir_edit.text().strip()
            class_names_text = self.class_names_edit.toPlainText().strip()
            
            if not all([dataset_name, images_dir, annotations_dir, class_names_text]):
                self.show_warning("输入错误", "请填写所有必要信息")
                return
            
            # 解析类别名称
            class_names = [name.strip() for name in class_names_text.split('\n') if name.strip()]
            if not class_names:
                self.show_warning("输入错误", "请至少输入一个类别名称")
                return
            
            # 创建数据集
            if self.trainer:
                train_ratio = self.train_ratio_spin.value()
                val_ratio = 1.0 - train_ratio
                
                dataset_info = self.trainer.prepare_dataset(
                    dataset_name, images_dir, annotations_dir, 
                    class_names, train_ratio, val_ratio
                )
                
                self.show_info("成功", f"数据集创建成功: {dataset_info.name}")
                self.refresh_datasets()
                
            else:
                self.show_error("错误", "训练器未初始化")
                
        except Exception as e:
            logger.error(f"创建数据集失败: {e}")
            self.show_error("创建失败", str(e))
    
    def refresh_datasets(self):
        """刷新数据集列表"""
        if not self.trainer:
            return
        
        try:
            datasets = self.trainer.list_datasets()
            
            self.datasets_table.setRowCount(len(datasets))
            self.dataset_combo.clear()
            
            for i, dataset in enumerate(datasets):
                self.datasets_table.setItem(i, 0, QTableWidgetItem(dataset.name))
                self.datasets_table.setItem(i, 1, QTableWidgetItem(str(dataset.train_images)))
                self.datasets_table.setItem(i, 2, QTableWidgetItem(str(dataset.val_images)))
                self.datasets_table.setItem(i, 3, QTableWidgetItem(str(dataset.class_count)))
                self.datasets_table.setItem(i, 4, QTableWidgetItem(dataset.path))
                
                # 添加到组合框
                self.dataset_combo.addItem(dataset.name, dataset.path)
                
        except Exception as e:
            logger.error(f"刷新数据集列表失败: {e}")
    
    def refresh_trained_models(self):
        """刷新训练模型列表"""
        if not self.trainer:
            return
        
        try:
            models = self.trainer.list_trained_models()
            
            self.trained_models_table.setRowCount(len(models))
            
            for i, model in enumerate(models):
                self.trained_models_table.setItem(i, 0, QTableWidgetItem(model['name']))
                self.trained_models_table.setItem(i, 1, QTableWidgetItem(model['status']))
                
                # 格式化mAP50
                metrics = model.get('final_metrics', {})
                map50 = metrics.get('mAP50', 0)
                self.trained_models_table.setItem(i, 2, QTableWidgetItem(f"{map50:.3f}"))
                
                self.trained_models_table.setItem(i, 3, QTableWidgetItem(model['dataset_name']))
                
        except Exception as e:
            logger.error(f"刷新训练模型列表失败: {e}")
    
    def validate_dataset(self):
        """验证数据集"""
        selected_row = self.datasets_table.currentRow()
        if selected_row < 0:
            self.show_warning("提示", "请选择要验证的数据集")
            return
        
        try:
            dataset_path = self.datasets_table.item(selected_row, 4).text()
            
            if self.trainer:
                result = self.trainer.validate_dataset(dataset_path)
                
                # 显示验证结果
                message = f"验证结果: {'通过' if result['valid'] else '失败'}\n\n"
                
                if result['errors']:
                    message += "错误:\n" + "\n".join(result['errors']) + "\n\n"
                
                if result['warnings']:
                    message += "警告:\n" + "\n".join(result['warnings']) + "\n\n"
                
                stats = result.get('statistics', {})
                if stats:
                    message += "统计信息:\n"
                    for key, value in stats.items():
                        if isinstance(value, list):
                            value = f"[{len(value)} 项]"
                        message += f"{key}: {value}\n"
                
                self.show_info("数据集验证", message)
                
        except Exception as e:
            logger.error(f"验证数据集失败: {e}")
            self.show_error("验证失败", str(e))
    
    def delete_dataset(self):
        """删除数据集"""
        selected_row = self.datasets_table.currentRow()
        if selected_row < 0:
            self.show_warning("提示", "请选择要删除的数据集")
            return
        
        dataset_name = self.datasets_table.item(selected_row, 0).text()
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除数据集 '{dataset_name}' 吗？\n这个操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if self.trainer and self.trainer.delete_dataset(dataset_name):
                    self.show_info("成功", f"数据集 '{dataset_name}' 已删除")
                    self.refresh_datasets()
                else:
                    self.show_error("失败", "删除数据集失败")
                    
            except Exception as e:
                logger.error(f"删除数据集失败: {e}")
                self.show_error("删除失败", str(e))
    
    def start_training(self):
        """开始训练"""
        try:
            # 验证输入
            model_name = self.model_name_edit.text().strip()
            if not model_name:
                self.show_warning("输入错误", "请输入模型名称")
                return
            
            if self.dataset_combo.currentIndex() < 0:
                self.show_warning("输入错误", "请选择数据集")
                return
            
            # 获取训练配置
            config = TrainingConfig(
                data_path=self.dataset_combo.currentData(),
                epochs=self.epochs_spin.value(),
                batch_size=self.batch_size_spin.value(),
                image_size=self.image_size_spin.value(),
                lr0=self.lr_spin.value(),
                weight_decay=self.weight_decay_spin.value(),
                warmup_epochs=self.warmup_epochs_spin.value(),
                patience=self.patience_spin.value(),
                workers=self.workers_spin.value()
            )
            
            # 启动训练线程
            if self.trainer:
                self.training_thread = TrainingThread(
                    self.trainer, config.data_path, config, model_name
                )
                
                self.training_thread.progress_updated.connect(self.update_training_log)
                self.training_thread.training_completed.connect(self.on_training_completed)
                self.training_thread.error_occurred.connect(self.on_training_error)
                
                self.training_thread.start()
                
                # 更新UI状态
                self.start_training_btn.setEnabled(False)
                self.stop_training_btn.setEnabled(True)
                self.training_progress.setVisible(True)
                self.training_progress.setRange(0, 0)  # 不确定进度
                
                self.update_training_log("训练已开始...")
                
        except Exception as e:
            logger.error(f"开始训练失败: {e}")
            self.show_error("训练失败", str(e))
    
    def stop_training(self):
        """停止训练"""
        if self.training_thread and self.training_thread.isRunning():
            self.training_thread.terminate()
            self.training_thread.wait()
            
            self.update_training_log("训练已停止")
            self.reset_training_ui()
    
    def on_training_completed(self, result: dict):
        """训练完成处理"""
        self.update_training_log(f"训练完成！模型路径: {result['model_path']}")
        self.update_training_log(f"评估指标: {result['metrics']}")
        
        self.reset_training_ui()
        self.refresh_trained_models()
        
        self.show_info("训练完成", "模型训练成功完成！")
    
    def on_training_error(self, error_msg: str):
        """训练错误处理"""
        self.update_training_log(f"训练错误: {error_msg}")
        self.reset_training_ui()
        self.show_error("训练失败", error_msg)
    
    def reset_training_ui(self):
        """重置训练UI状态"""
        self.start_training_btn.setEnabled(True)
        self.stop_training_btn.setEnabled(False)
        self.training_progress.setVisible(False)
    
    def update_training_log(self, message: str):
        """更新训练日志"""
        self.training_log.append(f"[{self.get_current_time()}] {message}")
    
    def update_confidence_label(self, value: int):
        """更新置信度标签"""
        confidence = value / 100.0
        self.confidence_label.setText(f"{confidence:.2f}")
    
    def update_iou_label(self, value: int):
        """更新IoU标签"""
        iou = value / 100.0
        self.iou_label.setText(f"{iou:.2f}")
    
    def get_detection_config(self) -> dict:
        """获取检测配置"""
        return {
            'confidence': self.confidence_slider.value() / 100.0,
            'iou_threshold': self.iou_slider.value() / 100.0,
            'show_labels': self.show_labels_cb.isChecked(),
            'show_confidence': self.show_confidence_cb.isChecked()
        }
    
    def detect_objects(self, image) -> Optional[DetectionResult]:
        """检测目标对象"""
        if not self.detector or not self.detector.is_loaded:
            return None
        
        try:
            config = self.get_detection_config()
            result = self.detector.detect(
                image,
                confidence=config['confidence'],
                iou_threshold=config['iou_threshold']
            )
            
            # 更新检测统计
            self.update_detection_stats(result)
            
            return result
            
        except Exception as e:
            logger.error(f"目标检测失败: {e}")
            return None
    
    def update_detection_stats(self, result: DetectionResult):
        """更新检测统计信息"""
        if len(result.boxes) == 0:
            stats_text = "未检测到目标"
        else:
            # 统计各类别数量
            class_counts = {}
            for class_name in result.class_names:
                class_counts[class_name] = class_counts.get(class_name, 0) + 1
            
            stats_text = f"检测到 {len(result.boxes)} 个目标:\n"
            for class_name, count in class_counts.items():
                stats_text += f"- {class_name}: {count}\n"
            
            # 平均置信度
            avg_confidence = result.confidences.mean() if len(result.confidences) > 0 else 0
            stats_text += f"\n平均置信度: {avg_confidence:.3f}"
        
        self.detection_stats.setText(stats_text)
    
    def update_ui_state(self):
        """更新UI状态"""
        # 根据模块可用性禁用/启用功能
        modules_available = YOLODetector is not None and YOLOTrainer is not None
        
        if not modules_available:
            self.tab_widget.setEnabled(False)
            self.show_warning("模块缺失", "YOLO相关模块未安装，请安装相关依赖包")
    
    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    # 工具方法
    def show_info(self, title: str, message: str):
        """显示信息对话框"""
        QMessageBox.information(self, title, message)
    
    def show_warning(self, title: str, message: str):
        """显示警告对话框"""
        QMessageBox.warning(self, title, message)
    
    def show_error(self, title: str, message: str):
        """显示错误对话框"""
        QMessageBox.critical(self, title, message)
    
    def show_input_dialog(self, title: str, label: str, items: List[str]) -> tuple:
        """显示输入对话框"""
        from PyQt6.QtWidgets import QInputDialog
        item, ok = QInputDialog.getItem(self, title, label, items, 0, False)
        return item, ok 