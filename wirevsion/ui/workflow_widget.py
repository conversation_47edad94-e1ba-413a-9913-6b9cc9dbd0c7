#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工作流配置组件
提供工作流编辑和管理功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QLabel, QGroupBox, QListWidget,
    QListWidgetItem, QLineEdit, QTextEdit, QComboBox,
    QSpinBox, QDoubleSpinBox, QCheckBox, QMessageBox,
    QSplitter, QTabWidget, QTreeWidget, QTreeWidgetItem
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon

from typing import Dict, List, Any, Optional
from loguru import logger

from wirevsion.core.workflow_manager import WorkflowManager, WorkflowConfig, WorkflowStep


class WorkflowWidget(QWidget):
    """
    工作流配置组件
    
    提供工作流步骤编辑和管理功能
    """
    
    # 信号定义
    workflow_updated = pyqtSignal(dict)  # 工作流更新信号
    status_message = pyqtSignal(str)  # 状态消息信号
    
    def __init__(self, workflow_manager: WorkflowManager):
        """
        初始化工作流配置组件
        
        Args:
            workflow_manager: 工作流管理器实例
        """
        super().__init__()
        
        self.workflow_manager = workflow_manager
        self.current_workflow: Optional[WorkflowConfig] = None
        self.current_step_index = -1
        
        # 支持的步骤类型
        self.step_types = {
            'template_match': '模板匹配',
            'roi_detection': 'ROI检测', 
            'color_detection': '颜色检测',
            'custom': '自定义步骤'
        }
        
        # 创建UI
        self._create_ui()
        
        # 连接信号
        self._connect_signals()
        
        logger.info("工作流配置组件初始化完成")
    
    def _create_ui(self):
        """创建UI组件"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：步骤列表和操作
        left_widget = self._create_left_panel()
        splitter.addWidget(left_widget)
        
        # 右侧：步骤配置
        right_widget = self._create_right_panel()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
    
    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 工作流信息组
        info_group = QGroupBox("工作流信息")
        info_layout = QFormLayout(info_group)
        
        self.workflow_name_label = QLabel("未加载工作流")
        info_layout.addRow("名称:", self.workflow_name_label)
        
        self.workflow_desc_label = QLabel("")
        info_layout.addRow("描述:", self.workflow_desc_label)
        
        self.step_count_label = QLabel("0")
        info_layout.addRow("步骤数:", self.step_count_label)
        
        layout.addWidget(info_group)
        
        # 步骤列表组
        steps_group = QGroupBox("工作流步骤")
        steps_layout = QVBoxLayout(steps_group)
        
        # 步骤列表
        self.steps_list = QListWidget()
        self.steps_list.currentRowChanged.connect(self._on_step_selected)
        steps_layout.addWidget(self.steps_list)
        
        # 步骤操作按钮
        btn_layout = QHBoxLayout()
        
        self.add_step_btn = QPushButton("添加步骤")
        self.add_step_btn.clicked.connect(self._add_step)
        btn_layout.addWidget(self.add_step_btn)
        
        self.remove_step_btn = QPushButton("删除步骤")
        self.remove_step_btn.clicked.connect(self._remove_step)
        btn_layout.addWidget(self.remove_step_btn)
        
        self.move_up_btn = QPushButton("上移")
        self.move_up_btn.clicked.connect(self._move_step_up)
        btn_layout.addWidget(self.move_up_btn)
        
        self.move_down_btn = QPushButton("下移")
        self.move_down_btn.clicked.connect(self._move_step_down)
        btn_layout.addWidget(self.move_down_btn)
        
        steps_layout.addLayout(btn_layout)
        
        layout.addWidget(steps_group)
        
        # 工作流操作
        workflow_ops_layout = QHBoxLayout()
        
        self.validate_btn = QPushButton("验证工作流")
        self.validate_btn.clicked.connect(self._validate_workflow)
        workflow_ops_layout.addWidget(self.validate_btn)
        
        self.clear_btn = QPushButton("清空步骤")
        self.clear_btn.clicked.connect(self._clear_steps)
        workflow_ops_layout.addWidget(self.clear_btn)
        
        layout.addLayout(workflow_ops_layout)
        
        return widget
    
    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 步骤配置选项卡
        self.config_tabs = QTabWidget()
        
        # 基本配置选项卡
        basic_tab = self._create_basic_config_tab()
        self.config_tabs.addTab(basic_tab, "基本配置")
        
        # 高级配置选项卡
        advanced_tab = self._create_advanced_config_tab()
        self.config_tabs.addTab(advanced_tab, "高级配置")
        
        layout.addWidget(self.config_tabs)
        
        # 配置操作按钮
        config_ops_layout = QHBoxLayout()
        
        self.apply_config_btn = QPushButton("应用配置")
        self.apply_config_btn.clicked.connect(self._apply_step_config)
        config_ops_layout.addWidget(self.apply_config_btn)
        
        self.reset_config_btn = QPushButton("重置配置")
        self.reset_config_btn.clicked.connect(self._reset_step_config)
        config_ops_layout.addWidget(self.reset_config_btn)
        
        layout.addLayout(config_ops_layout)
        
        return widget
    
    def _create_basic_config_tab(self) -> QWidget:
        """创建基本配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 步骤基本信息
        basic_group = QGroupBox("步骤信息")
        basic_layout = QFormLayout(basic_group)
        
        # 步骤名称
        self.step_name_edit = QLineEdit()
        self.step_name_edit.setPlaceholderText("输入步骤名称")
        basic_layout.addRow("名称:", self.step_name_edit)
        
        # 步骤类型
        self.step_type_combo = QComboBox()
        for type_key, type_name in self.step_types.items():
            self.step_type_combo.addItem(type_name, type_key)
        self.step_type_combo.currentIndexChanged.connect(self._on_step_type_changed)
        basic_layout.addRow("类型:", self.step_type_combo)
        
        # 步骤启用
        self.step_enabled_check = QCheckBox()
        self.step_enabled_check.setChecked(True)
        basic_layout.addRow("启用:", self.step_enabled_check)
        
        # 关键步骤
        self.critical_step_check = QCheckBox()
        self.critical_step_check.setToolTip("如果关键步骤失败，将停止整个工作流执行")
        basic_layout.addRow("关键步骤:", self.critical_step_check)
        
        layout.addWidget(basic_group)
        
        # 步骤配置
        self.step_config_group = QGroupBox("步骤配置")
        self.step_config_layout = QVBoxLayout(self.step_config_group)
        
        # 配置内容将根据步骤类型动态创建
        self.config_content_widget = QWidget()
        self.step_config_layout.addWidget(self.config_content_widget)
        
        layout.addWidget(self.step_config_group)
        
        layout.addStretch()
        
        return widget
    
    def _create_advanced_config_tab(self) -> QWidget:
        """创建高级配置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 执行条件
        condition_group = QGroupBox("执行条件")
        condition_layout = QFormLayout(condition_group)
        
        # 前置条件
        self.precondition_edit = QLineEdit()
        self.precondition_edit.setPlaceholderText("输入前置条件表达式")
        condition_layout.addRow("前置条件:", self.precondition_edit)
        
        # 超时设置
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(0, 3600)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" 秒")
        condition_layout.addRow("超时时间:", self.timeout_spin)
        
        # 重试次数
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(0, 10)
        self.retry_spin.setValue(0)
        condition_layout.addRow("重试次数:", self.retry_spin)
        
        layout.addWidget(condition_group)
        
        # 输出配置
        output_group = QGroupBox("输出配置")
        output_layout = QFormLayout(output_group)
        
        # 保存结果
        self.save_result_check = QCheckBox()
        output_layout.addRow("保存结果:", self.save_result_check)
        
        # 输出格式
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItem("JSON", "json")
        self.output_format_combo.addItem("XML", "xml")
        self.output_format_combo.addItem("CSV", "csv")
        output_layout.addRow("输出格式:", self.output_format_combo)
        
        layout.addWidget(output_group)
        
        # 调试配置
        debug_group = QGroupBox("调试配置")
        debug_layout = QFormLayout(debug_group)
        
        # 调试模式
        self.debug_mode_check = QCheckBox()
        debug_layout.addRow("调试模式:", self.debug_mode_check)
        
        # 详细日志
        self.verbose_log_check = QCheckBox()
        debug_layout.addRow("详细日志:", self.verbose_log_check)
        
        layout.addWidget(debug_group)
        
        layout.addStretch()
        
        return widget
    
    def _connect_signals(self):
        """连接信号"""
        pass
    
    def set_workflow(self, workflow: WorkflowConfig):
        """
        设置当前工作流
        
        Args:
            workflow: 工作流配置
        """
        self.current_workflow = workflow
        self._update_workflow_info()
        self._update_steps_list()
        self._update_ui_state()
        
        logger.info(f"设置工作流: {workflow.name}")
    
    def _update_workflow_info(self):
        """更新工作流信息显示"""
        if self.current_workflow:
            self.workflow_name_label.setText(self.current_workflow.name)
            self.workflow_desc_label.setText(self.current_workflow.description or "无描述")
            self.step_count_label.setText(str(len(self.current_workflow.steps)))
        else:
            self.workflow_name_label.setText("未加载工作流")
            self.workflow_desc_label.setText("")
            self.step_count_label.setText("0")
    
    def _update_steps_list(self):
        """更新步骤列表显示"""
        self.steps_list.clear()
        
        if self.current_workflow:
            for i, step in enumerate(self.current_workflow.steps):
                step_text = f"{i+1}. {step.name} ({self.step_types.get(step.step_type, step.step_type)})"
                if not step.enabled:
                    step_text += " [禁用]"
                
                item = QListWidgetItem(step_text)
                self.steps_list.addItem(item)
    
    def _update_ui_state(self):
        """更新UI状态"""
        has_workflow = self.current_workflow is not None
        has_step = self.current_step_index >= 0
        
        # 工作流操作按钮
        self.add_step_btn.setEnabled(has_workflow)
        self.validate_btn.setEnabled(has_workflow)
        self.clear_btn.setEnabled(has_workflow)
        
        # 步骤操作按钮
        self.remove_step_btn.setEnabled(has_step)
        self.move_up_btn.setEnabled(has_step and self.current_step_index > 0)
        self.move_down_btn.setEnabled(has_step and self.current_step_index < len(self.current_workflow.steps) - 1 if has_workflow else False)
        
        # 配置控件
        self.step_name_edit.setEnabled(has_step)
        self.step_type_combo.setEnabled(has_step)
        self.step_enabled_check.setEnabled(has_step)
        self.critical_step_check.setEnabled(has_step)
        self.apply_config_btn.setEnabled(has_step)
        self.reset_config_btn.setEnabled(has_step)
    
    def _add_step(self):
        """添加新步骤"""
        if not self.current_workflow:
            return
        
        step_count = len(self.current_workflow.steps)
        step_name = f"步骤_{step_count + 1}"
        
        # 添加步骤到工作流管理器
        if self.workflow_manager.add_step('template_match', step_name, {}):
            # 更新显示
            self._update_workflow_info()
            self._update_steps_list()
            
            # 选中新添加的步骤
            self.steps_list.setCurrentRow(len(self.current_workflow.steps) - 1)
            
            self.status_message.emit(f"已添加步骤: {step_name}")
            self._emit_workflow_updated()
        else:
            QMessageBox.warning(self, "错误", "添加步骤失败")
    
    def _remove_step(self):
        """删除当前步骤"""
        if self.current_step_index < 0:
            return
        
        step = self.current_workflow.steps[self.current_step_index]
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除步骤 '{step.name}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.workflow_manager.remove_step(step.step_id):
                self._update_workflow_info()
                self._update_steps_list()
                self.current_step_index = -1
                self._update_ui_state()
                
                self.status_message.emit(f"已删除步骤: {step.name}")
                self._emit_workflow_updated()
            else:
                QMessageBox.warning(self, "错误", "删除步骤失败")
    
    def _move_step_up(self):
        """上移步骤"""
        if self.current_step_index <= 0:
            return
        
        # 交换步骤位置
        steps = self.current_workflow.steps
        steps[self.current_step_index], steps[self.current_step_index - 1] = \
            steps[self.current_step_index - 1], steps[self.current_step_index]
        
        # 更新选择
        self.current_step_index -= 1
        
        self._update_steps_list()
        self.steps_list.setCurrentRow(self.current_step_index)
        self._update_ui_state()
        
        self.status_message.emit("步骤已上移")
        self._emit_workflow_updated()
    
    def _move_step_down(self):
        """下移步骤"""
        if self.current_step_index < 0 or self.current_step_index >= len(self.current_workflow.steps) - 1:
            return
        
        # 交换步骤位置
        steps = self.current_workflow.steps
        steps[self.current_step_index], steps[self.current_step_index + 1] = \
            steps[self.current_step_index + 1], steps[self.current_step_index]
        
        # 更新选择
        self.current_step_index += 1
        
        self._update_steps_list()
        self.steps_list.setCurrentRow(self.current_step_index)
        self._update_ui_state()
        
        self.status_message.emit("步骤已下移")
        self._emit_workflow_updated()
    
    def _clear_steps(self):
        """清空所有步骤"""
        if not self.current_workflow or not self.current_workflow.steps:
            return
        
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有步骤吗？此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 删除所有步骤
            for step in self.current_workflow.steps[:]:
                self.workflow_manager.remove_step(step.step_id)
            
            self._update_workflow_info()
            self._update_steps_list()
            self.current_step_index = -1
            self._update_ui_state()
            
            self.status_message.emit("已清空所有步骤")
            self._emit_workflow_updated()
    
    def _validate_workflow(self):
        """验证工作流"""
        if not self.current_workflow:
            return
        
        errors = []
        
        # 检查是否有步骤
        if not self.current_workflow.steps:
            errors.append("工作流没有任何步骤")
        
        # 检查每个步骤
        for i, step in enumerate(self.current_workflow.steps):
            if not step.name.strip():
                errors.append(f"步骤 {i+1} 没有名称")
            
            if step.step_type not in self.step_types:
                errors.append(f"步骤 {i+1} 类型无效: {step.step_type}")
        
        if errors:
            error_text = "工作流验证失败:\n\n" + "\n".join(f"• {error}" for error in errors)
            QMessageBox.warning(self, "验证失败", error_text)
        else:
            QMessageBox.information(self, "验证成功", "工作流验证通过，没有发现问题。")
    
    def _on_step_selected(self, row: int):
        """步骤选择事件处理"""
        if 0 <= row < len(self.current_workflow.steps) if self.current_workflow else False:
            self.current_step_index = row
            self._load_step_config(self.current_workflow.steps[row])
        else:
            self.current_step_index = -1
        
        self._update_ui_state()
    
    def _load_step_config(self, step: WorkflowStep):
        """加载步骤配置到界面"""
        # 基本信息
        self.step_name_edit.setText(step.name)
        
        # 设置步骤类型
        for i in range(self.step_type_combo.count()):
            if self.step_type_combo.itemData(i) == step.step_type:
                self.step_type_combo.setCurrentIndex(i)
                break
        
        self.step_enabled_check.setChecked(step.enabled)
        self.critical_step_check.setChecked(step.config.get('critical', False))
        
        # 高级配置
        self.precondition_edit.setText(step.config.get('precondition', ''))
        self.timeout_spin.setValue(step.config.get('timeout', 30))
        self.retry_spin.setValue(step.config.get('retry', 0))
        self.save_result_check.setChecked(step.config.get('save_result', False))
        self.debug_mode_check.setChecked(step.config.get('debug_mode', False))
        self.verbose_log_check.setChecked(step.config.get('verbose_log', False))
        
        # 设置输出格式
        output_format = step.config.get('output_format', 'json')
        for i in range(self.output_format_combo.count()):
            if self.output_format_combo.itemData(i) == output_format:
                self.output_format_combo.setCurrentIndex(i)
                break
        
        # 根据步骤类型创建专用配置界面
        self._create_step_specific_config(step)
    
    def _on_step_type_changed(self):
        """步骤类型变化事件处理"""
        if self.current_step_index >= 0:
            step = self.current_workflow.steps[self.current_step_index]
            new_type = self.step_type_combo.currentData()
            step.step_type = new_type
            
            # 重新创建专用配置界面
            self._create_step_specific_config(step)
            
            self._emit_workflow_updated()
    
    def _create_step_specific_config(self, step: WorkflowStep):
        """根据步骤类型创建专用配置界面"""
        # 清除现有配置界面
        for child in self.config_content_widget.findChildren(QWidget):
            child.deleteLater()
        
        layout = QVBoxLayout(self.config_content_widget)
        
        if step.step_type == 'template_match':
            self._create_template_match_config(layout, step)
        elif step.step_type == 'roi_detection':
            self._create_roi_detection_config(layout, step)
        elif step.step_type == 'color_detection':
            self._create_color_detection_config(layout, step)
        else:
            # 自定义步骤或其他类型
            self._create_generic_config(layout, step)
    
    def _create_template_match_config(self, layout: QVBoxLayout, step: WorkflowStep):
        """创建模板匹配配置界面"""
        group = QGroupBox("模板匹配配置")
        form_layout = QFormLayout(group)
        
        # 模板路径
        template_path_edit = QLineEdit()
        template_path_edit.setText(step.config.get('template_path', ''))
        template_path_edit.textChanged.connect(lambda text: step.config.update({'template_path': text}))
        form_layout.addRow("模板路径:", template_path_edit)
        
        # 匹配阈值
        threshold_spin = QDoubleSpinBox()
        threshold_spin.setRange(0.0, 1.0)
        threshold_spin.setSingleStep(0.01)
        threshold_spin.setValue(step.config.get('threshold', 0.8))
        threshold_spin.valueChanged.connect(lambda val: step.config.update({'threshold': val}))
        form_layout.addRow("匹配阈值:", threshold_spin)
        
        layout.addWidget(group)
    
    def _create_roi_detection_config(self, layout: QVBoxLayout, step: WorkflowStep):
        """创建ROI检测配置界面"""
        group = QGroupBox("ROI检测配置")
        form_layout = QFormLayout(group)
        
        # ROI偏移
        offset_x_spin = QSpinBox()
        offset_x_spin.setRange(-9999, 9999)
        offset_x_spin.setValue(step.config.get('offset_x', 0))
        offset_x_spin.valueChanged.connect(lambda val: step.config.update({'offset_x': val}))
        form_layout.addRow("X偏移:", offset_x_spin)
        
        offset_y_spin = QSpinBox()
        offset_y_spin.setRange(-9999, 9999)
        offset_y_spin.setValue(step.config.get('offset_y', 0))
        offset_y_spin.valueChanged.connect(lambda val: step.config.update({'offset_y': val}))
        form_layout.addRow("Y偏移:", offset_y_spin)
        
        # ROI尺寸
        width_spin = QSpinBox()
        width_spin.setRange(1, 9999)
        width_spin.setValue(step.config.get('width', 100))
        width_spin.valueChanged.connect(lambda val: step.config.update({'width': val}))
        form_layout.addRow("宽度:", width_spin)
        
        height_spin = QSpinBox()
        height_spin.setRange(1, 9999)
        height_spin.setValue(step.config.get('height', 100))
        height_spin.valueChanged.connect(lambda val: step.config.update({'height': val}))
        form_layout.addRow("高度:", height_spin)
        
        layout.addWidget(group)
    
    def _create_color_detection_config(self, layout: QVBoxLayout, step: WorkflowStep):
        """创建颜色检测配置界面"""
        group = QGroupBox("颜色检测配置")
        form_layout = QFormLayout(group)
        
        # 颜色空间
        color_space_combo = QComboBox()
        color_space_combo.addItem("HSV", "HSV")
        color_space_combo.addItem("RGB", "RGB")
        color_space_combo.addItem("LAB", "LAB")
        
        current_space = step.config.get('color_space', 'HSV')
        for i in range(color_space_combo.count()):
            if color_space_combo.itemData(i) == current_space:
                color_space_combo.setCurrentIndex(i)
                break
        
        color_space_combo.currentTextChanged.connect(
            lambda text: step.config.update({'color_space': color_space_combo.currentData()})
        )
        form_layout.addRow("颜色空间:", color_space_combo)
        
        # 检测方法
        method_combo = QComboBox()
        method_combo.addItem("范围检测", "range")
        method_combo.addItem("阈值检测", "threshold")
        method_combo.addItem("统计检测", "statistical")
        
        current_method = step.config.get('detection_method', 'range')
        for i in range(method_combo.count()):
            if method_combo.itemData(i) == current_method:
                method_combo.setCurrentIndex(i)
                break
        
        method_combo.currentTextChanged.connect(
            lambda text: step.config.update({'detection_method': method_combo.currentData()})
        )
        form_layout.addRow("检测方法:", method_combo)
        
        layout.addWidget(group)
    
    def _create_generic_config(self, layout: QVBoxLayout, step: WorkflowStep):
        """创建通用配置界面"""
        group = QGroupBox("自定义配置")
        form_layout = QVBoxLayout(group)
        
        # 配置文本编辑器
        config_edit = QTextEdit()
        config_edit.setPlaceholderText("输入JSON格式的配置...")
        
        # 尝试显示现有配置
        import json
        try:
            config_text = json.dumps(step.config, indent=2, ensure_ascii=False)
            config_edit.setText(config_text)
        except:
            config_edit.setText("{}")
        
        form_layout.addWidget(config_edit)
        layout.addWidget(group)
    
    def _apply_step_config(self):
        """应用步骤配置"""
        if self.current_step_index < 0:
            return
        
        step = self.current_workflow.steps[self.current_step_index]
        
        # 更新基本信息
        step.name = self.step_name_edit.text()
        step.step_type = self.step_type_combo.currentData()
        step.enabled = self.step_enabled_check.isChecked()
        
        # 更新高级配置
        step.config.update({
            'critical': self.critical_step_check.isChecked(),
            'precondition': self.precondition_edit.text(),
            'timeout': self.timeout_spin.value(),
            'retry': self.retry_spin.value(),
            'save_result': self.save_result_check.isChecked(),
            'output_format': self.output_format_combo.currentData(),
            'debug_mode': self.debug_mode_check.isChecked(),
            'verbose_log': self.verbose_log_check.isChecked()
        })
        
        # 更新工作流管理器中的步骤
        self.workflow_manager.update_step(step.step_id, step.config)
        
        # 更新显示
        self._update_steps_list()
        self.steps_list.setCurrentRow(self.current_step_index)
        
        self.status_message.emit(f"已应用步骤配置: {step.name}")
        self._emit_workflow_updated()
    
    def _reset_step_config(self):
        """重置步骤配置"""
        if self.current_step_index < 0:
            return
        
        step = self.current_workflow.steps[self.current_step_index]
        self._load_step_config(step)
        
        self.status_message.emit("已重置步骤配置")
    
    def _emit_workflow_updated(self):
        """发送工作流更新信号"""
        if self.current_workflow:
            self.workflow_updated.emit({
                'workflow': self.current_workflow,
                'step_count': len(self.current_workflow.steps),
                'current_step': self.current_step_index
            })
    
    def get_current_workflow(self) -> Optional[WorkflowConfig]:
        """获取当前工作流"""
        return self.current_workflow 