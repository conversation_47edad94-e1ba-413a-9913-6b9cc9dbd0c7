#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模板匹配设置界面模块
提供模板图像加载和匹配参数设置功能
"""

import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
    QLabel, QFileDialog, QFormLayout, QGroupBox,
    QSlider, QComboBox, QMessageBox
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QPixmap, QImage
import cv2
import numpy as np

from wirevsion.config.config_manager import ConfigManager
from wirevsion.config.workflow_config import TemplateConfig
from wirevsion.camera.camera_manager import CameraManager
from wirevsion.utils.image_utils import cv_to_qpixmap, resize_image


class TemplateMatchingWidget(QWidget):
    """
    模板匹配设置界面
    提供模板图像加载和匹配参数设置功能
    """
    
    # 信号：模板已更改
    template_changed = pyqtSignal(np.ndarray, TemplateConfig)
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        """
        初始化模板匹配设置界面
        
        Args:
            config_manager: 配置管理器实例
            parent: 父窗口对象
        """
        super().__init__(parent)
        self.config_manager = config_manager
        
        # 创建相机管理器
        self.camera_manager = CameraManager()
        
        # 模板图像和配置
        self.template_image = None
        self.template_config = None
        
        # 创建UI组件
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI组件"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 模板图像组
        image_group = QGroupBox("模板图像")
        image_layout = QVBoxLayout()
        
        # 模板图像预览
        preview_layout = QHBoxLayout()
        
        self.template_preview = QLabel("无模板图像")
        self.template_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.template_preview.setMinimumSize(300, 200)
        self.template_preview.setStyleSheet("border: 1px solid #CCCCCC; background-color: #F0F0F0;")
        
        preview_layout.addWidget(self.template_preview)
        
        # 模板图像操作按钮
        buttons_layout = QHBoxLayout()
        
        load_btn = QPushButton("加载图像")
        load_btn.clicked.connect(self.load_template)
        
        capture_btn = QPushButton("从相机捕获")
        capture_btn.clicked.connect(self.capture_from_camera)
        
        clear_btn = QPushButton("清除图像")
        clear_btn.clicked.connect(self.clear_template)
        
        buttons_layout.addWidget(load_btn)
        buttons_layout.addWidget(capture_btn)
        buttons_layout.addWidget(clear_btn)
        
        # 添加到模板图像组布局
        image_layout.addLayout(preview_layout)
        image_layout.addLayout(buttons_layout)
        
        image_group.setLayout(image_layout)
        
        # 匹配参数组
        params_group = QGroupBox("匹配参数")
        params_layout = QFormLayout()
        
        # 匹配方法
        self.method_combo = QComboBox()
        self.method_combo.addItem("平方差匹配法(CV_TM_SQDIFF)", cv2.TM_SQDIFF)
        self.method_combo.addItem("归一化平方差匹配法(CV_TM_SQDIFF_NORMED)", cv2.TM_SQDIFF_NORMED)
        self.method_combo.addItem("相关匹配法(CV_TM_CCORR)", cv2.TM_CCORR)
        self.method_combo.addItem("归一化相关匹配法(CV_TM_CCORR_NORMED)", cv2.TM_CCORR_NORMED)
        self.method_combo.addItem("相关系数匹配法(CV_TM_CCOEFF)", cv2.TM_CCOEFF)
        self.method_combo.addItem("归一化相关系数匹配法(CV_TM_CCOEFF_NORMED)", cv2.TM_CCOEFF_NORMED)
        self.method_combo.setCurrentIndex(5)  # 默认选择归一化相关系数匹配法
        
        params_layout.addRow(QLabel("匹配方法:"), self.method_combo)
        
        # 匹配阈值
        threshold_layout = QHBoxLayout()
        
        self.threshold_slider = QSlider(Qt.Orientation.Horizontal)
        self.threshold_slider.setRange(0, 100)
        self.threshold_slider.setValue(80)  # 默认阈值0.8
        
        self.threshold_label = QLabel("0.80")
        
        threshold_layout.addWidget(self.threshold_slider)
        threshold_layout.addWidget(self.threshold_label)
        
        params_layout.addRow(QLabel("匹配阈值:"), threshold_layout)
        
        # 连接阈值滑块的信号
        self.threshold_slider.valueChanged.connect(self.update_threshold_label)
        
        params_group.setLayout(params_layout)
        
        # 添加组件到主布局
        main_layout.addWidget(image_group)
        main_layout.addWidget(params_group)
        main_layout.addStretch()
    
    def update_threshold_label(self, value):
        """更新阈值标签"""
        threshold = value / 100.0
        self.threshold_label.setText(f"{threshold:.2f}")
    
    def load_template(self):
        """加载模板图像"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模板图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        
        if file_path:
            try:
                # 加载图像
                self.template_image = cv2.imread(file_path)
                
                # 显示图像
                self._display_template(self.template_image)
                
                # 创建模板配置
                self.template_config = TemplateConfig(
                    path=file_path,
                    threshold=self.threshold_slider.value() / 100.0,
                    method=self.method_combo.currentData()
                )
                
                # 发送信号
                self.template_changed.emit(self.template_image, self.template_config)
            except Exception as e:
                QMessageBox.warning(self, "错误", f"加载模板图像出错: {str(e)}")
    
    def capture_from_camera(self):
        """从相机捕获模板图像"""
        # 获取可用相机列表
        cameras = self.camera_manager.get_available_cameras()
        
        if not cameras:
            QMessageBox.warning(self, "错误", "未找到可用相机")
            return
        
        try:
            # 连接第一个可用相机
            camera_id = cameras[0]['id']
            if not self.camera_manager.connect_camera(camera_id):
                QMessageBox.warning(self, "错误", f"无法连接相机 {camera_id}")
                return
            
            # 捕获一帧图像
            success, frame = self.camera_manager.get_frame()
            
            # 断开相机连接
            self.camera_manager.disconnect_camera(camera_id)
            
            if not success or frame is None:
                QMessageBox.warning(self, "错误", "无法获取相机图像")
                return
            
            # 保存图像
            self.template_image = frame
            
            # 显示图像
            self._display_template(self.template_image)
            
            # 创建模板配置（不保存文件路径）
            self.template_config = TemplateConfig(
                path="",
                threshold=self.threshold_slider.value() / 100.0,
                method=self.method_combo.currentData()
            )
            
            # 发送信号
            self.template_changed.emit(self.template_image, self.template_config)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"捕获相机图像出错: {str(e)}")
    
    def _display_template(self, image):
        """
        显示模板图像
        
        Args:
            image: 模板图像
        """
        if image is None:
            self.template_preview.setText("无模板图像")
            return
        
        # 调整图像大小以适应预览区域
        max_size = min(300, 200)
        resized = resize_image(image, width=max_size)
        
        # 转换为QPixmap并显示
        pixmap = cv_to_qpixmap(resized)
        self.template_preview.setPixmap(pixmap)
    
    def clear_template(self):
        """清除模板图像"""
        self.template_image = None
        self.template_config = None
        self.template_preview.setPixmap(QPixmap())
        self.template_preview.setText("无模板图像")
        
        # 发送信号
        self.template_changed.emit(None, None)
    
    def get_template_config(self) -> TemplateConfig:
        """
        获取当前模板配置
        
        Returns:
            TemplateConfig: 模板配置
        """
        if self.template_config:
            # 更新配置
            self.template_config.threshold = self.threshold_slider.value() / 100.0
            self.template_config.method = self.method_combo.currentData()
        
        return self.template_config
