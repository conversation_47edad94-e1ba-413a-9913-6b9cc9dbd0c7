#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
节点配置对话框

为工作流节点提供可视化配置界面
- 配置输入输出参数
- 配置显示选项
- 配置算法特定参数
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, 
    QWidget, QLabel, QLineEdit, QComboBox, 
    QSpinBox, QDoubleSpinBox, QCheckBox, QPushButton,
    QScrollArea, QFormLayout, QGroupBox, QSlider, QRadioButton,
    QGridLayout, QFrame, QSizePolicy, QToolTip, QMessageBox
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QPoint, QRectF, QDateTime, QTimer
from PyQt6.QtGui import QFont, QIcon, QPixmap, QColor, Q<PERSON><PERSON>ter, QPen
from loguru import logger
from typing import Dict, List, Any, Optional

# 导入主题颜色
from wirevsion.ui.modern_components import THEME_COLORS

class NodeConfigDialog(QDialog):
    """节点配置对话框"""
    
    def __init__(self, node, parent=None):
        """初始化配置对话框
        
        Args:
            node: 要配置的节点
            parent: 父窗口
        """
        super().__init__(parent)
        self.node = node
        self.node_id = node.node_id
        self.node_type = node.node_type
        self.node_title = node.title
        
        # 相机预览相关属性
        self.camera_preview_active = False
        self.camera_preview_timer = None
        self.camera_device = None
        
        # 确保获取到主窗口
        self.main_window = parent
        if parent is None:
            # 尝试找到主窗口
            logger.warning("父窗口为None，尝试查找主窗口...")
            import sys
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if widget.isWindow():
                        logger.info(f"找到主窗口: {widget.objectName()}")
                        self.main_window = widget
                        break
        
        logger.info(f"配置对话框的主窗口: {self.main_window}")
        
        # 配置参数
        self.params = self._get_default_params()
        
        # 设置UI
        self._setup_ui()
        
        # 窗口标题
        self.setWindowTitle(f"配置节点 - {self.node_title}")
        
        logger.debug(f"创建节点配置对话框: {self.node_id} ({self.node_type})")

    def _setup_ui(self):
        """设置UI界面"""
        # 设置窗口大小
        self.resize(650, 500)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.FramelessWindowHint)  # 无边框窗口
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建相机状态标签，避免后续错误
        self.camera_status = QLabel("相机状态: 未连接")
        self.camera_status.setStyleSheet(f"color: #e0e0e0;")
        
        # 基本样式表 - 统一所有节点类型的风格
        self.setStyleSheet("""
            QDialog {
                background-color: #1c2126;
                border: 1px solid #3a3a3a;
                border-radius: 8px;
            }
            QLabel {
                color: #e0e0e0;
            }
            QGroupBox {
                color: #e0e0e0;
                font-weight: bold;
                border: none;
                margin-top: 1.5ex;
                padding-top: 1ex;
            }
            QPushButton {
                background-color: #404040;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QTabWidget::pane {
                border: none;
                padding: 10px;
            }
            QTabBar::tab {
                background-color: #2a2a2a;
                color: #aaa;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 15px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #3a3a3a;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #333333;
            }
            QComboBox {
                background-color: #2a2a2a;
                color: #e0e0e0;
                border: 1px solid #404040;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox {
                background-color: #2a2a2a;
                color: #e0e0e0;
                border: 1px solid #404040;
                border-radius: 4px;
                padding: 5px;
            }
            QSlider::groove:horizontal {
                height: 4px;
                background: #404040;
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: #6a6a6a;
                width: 14px;
                height: 14px;
                margin: -5px 0;
                border-radius: 7px;
            }
            QSlider::sub-page:horizontal {
                background: #606060;
                height: 4px;
                border-radius: 2px;
            }
            QCheckBox {
                color: #e0e0e0;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 1px solid #555;
            }
            QCheckBox::indicator:checked {
                background-color: #6a6a6a;
                border: 1px solid #777;
            }
            QScrollArea, QScrollBar {
                background-color: #1a1a1a;
                border: none;
            }
            QScrollBar:vertical {
                width: 8px;
                background: #1a1a1a;
            }
            QScrollBar::handle:vertical {
                background: #4a4a4a;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar:horizontal {
                height: 8px;
                background: #1a1a1a;
            }
            QScrollBar::handle:horizontal {
                background: #4a4a4a;
                border-radius: 4px;
                min-width: 20px;
            }
        """)
        
        # 根据节点类型设置顶部颜色条
        header_bg_color = "#6b3a9f"  # 默认紫色
        header_label = "节点配置"
        
        if self.node_type == "input":
            header_bg_color = "#2d6e2d"  # 绿色
            header_label = "输入节点配置"
            self.setWindowTitle(f"输入节点配置 - {self.node_title}")
        elif self.node_type == "output":
            header_bg_color = "#3a6b9f"  # 蓝色
            header_label = "输出节点配置"
            self.setWindowTitle(f"输出节点配置 - {self.node_title}")
        elif "gaussian_blur" in self.node_id or "median_filter" in self.node_id or "bilateral_filter" in self.node_id:
            header_bg_color = "#8f802d"  # 黄色
            header_label = "滤波节点配置"
            self.setWindowTitle(f"滤波节点配置 - {self.node_title}")
        elif "edge" in self.node_id:
            header_bg_color = "#9f3a6b"  # 粉红色
            header_label = "边缘检测配置"
            self.setWindowTitle(f"边缘检测配置 - {self.node_title}")
        elif "yolo" in self.node_id:
            header_bg_color = "#3a6b9f"  # 蓝色
            header_label = "YOLO检测配置"
            self.setWindowTitle(f"YOLO检测配置 - {self.node_title}")
        else:
            self.setWindowTitle(f"节点配置 - {self.node_title}")
        
        # 创建标题栏
        header = QWidget()
        header.setFixedHeight(50)
        header.setStyleSheet(f"background-color: {header_bg_color}; border-top-left-radius: 8px; border-top-right-radius: 8px;")
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(15, 0, 15, 0)
        
        # 标题
        title = QLabel(f"{header_label} - {self.node_title}")
        title.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        header_layout.addWidget(title)
        
        # 节点ID
        id_label = QLabel(f"ID: {self.node_id}")
        id_label.setStyleSheet("color: rgba(255, 255, 255, 150); font-size: 12px;")
        header_layout.addWidget(id_label)
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: white;
                font-size: 20px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 20%);
                border-radius: 15px;
            }
        """)
        close_btn.clicked.connect(self.reject)
        header_layout.addWidget(close_btn)
        
        main_layout.addWidget(header)
        
        # 内容区域
        content = QWidget()
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(10)
        
        # 选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                border-radius: 0px;
                padding: 10px;
            }
            QTabBar::tab {
                background-color: #2a2a2a;
                color: #aaa;
                border: none;
                padding: 8px 15px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #3a3a3a;
                color: white;
                border-bottom: 2px solid """ + header_bg_color + """;
            }
            QTabBar::tab:hover:!selected {
                background-color: #333333;
            }
        """)
        
        # 输入输出参数选项卡
        self.io_tab = QWidget()
        self._setup_io_tab()
        self.tab_widget.addTab(self.io_tab, "输入/输出")
        
        # 算法参数选项卡
        self.algorithm_tab = QWidget()
        self._setup_algorithm_tab()
        self.tab_widget.addTab(self.algorithm_tab, "算法参数")
        
        # 显示选项卡
        self.display_tab = QWidget()
        self._setup_display_tab()
        self.tab_widget.addTab(self.display_tab, "显示选项")
        
        content_layout.addWidget(self.tab_widget)
        
        main_layout.addWidget(content, 1)  # 内容区域伸展
        
        # 按钮布局
        buttons = QWidget()
        buttons.setFixedHeight(60)
        buttons.setStyleSheet("background-color: #1a1a1a; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;")
        button_layout = QHBoxLayout(buttons)
        button_layout.setContentsMargins(15, 0, 15, 0)
        
        button_layout.addStretch()
        
        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
        """)
        self.reset_button.clicked.connect(self._reset_params)
        button_layout.addWidget(self.reset_button)
        
        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {header_bg_color};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 80px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self._lighten_color(header_bg_color, 20)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(header_bg_color, 10)};
            }}
        """)
        self.ok_button.setDefault(True)
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)
        
        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addWidget(buttons)
        
        # 添加鼠标拖动支持
        self._drag_position = None

    def _setup_io_tab(self):
        """设置输入输出选项卡"""
        layout = QVBoxLayout(self.io_tab)
        
        # 输入部分
        input_group = QGroupBox("输入参数")
        input_layout = QFormLayout(input_group)
        
        # 根据节点类型添加不同的输入参数
        if self.node_type == "input":
            # 输入节点
            if "image_input" in self.node_id:
                # 图像输入
                input_source_label = QLabel("输入源:")
                self.input_source = QComboBox()
                self.input_source.addItems(["文件", "文件夹", "相机", "视频"])
                input_layout.addRow(input_source_label, self.input_source)
                
                # 文件路径
                file_path_label = QLabel("文件路径:")
                self.file_path = QLineEdit()
                browse_button = QPushButton("浏览...")
                file_layout = QHBoxLayout()
                file_layout.addWidget(self.file_path)
                file_layout.addWidget(browse_button)
                input_layout.addRow(file_path_label, file_layout)
                
            elif "camera_input" in self.node_id:
                # 相机输入
                camera_label = QLabel("相机:")
                self.camera_select = QComboBox()
                self.camera_select.addItems(["相机0", "相机1", "相机2", "相机3"])
                input_layout.addRow(camera_label, self.camera_select)
                
                # 相机分辨率
                resolution_label = QLabel("分辨率:")
                self.resolution = QComboBox()
                self.resolution.addItems(["640x480", "1280x720", "1920x1080"])
                input_layout.addRow(resolution_label, self.resolution)
                
                # 帧率
                fps_label = QLabel("帧率:")
                self.fps = QSpinBox()
                self.fps.setRange(1, 60)
                self.fps.setValue(30)
                input_layout.addRow(fps_label, self.fps)
                
                # 自动预览选项
                preview_label = QLabel("自动预览:")
                self.auto_preview = QCheckBox()
                self.auto_preview.setChecked(True)
                input_layout.addRow(preview_label, self.auto_preview)
                
                # 添加相机预览图像标签
                preview_container = QWidget()
                preview_layout = QVBoxLayout(preview_container)
                preview_layout.setContentsMargins(0, 10, 0, 10)
                
                # 相机预览标签
                self.preview_label = QLabel()
                self.preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.preview_label.setMinimumSize(320, 240)
                self.preview_label.setMaximumSize(480, 360)
                self.preview_label.setStyleSheet("""
                    QLabel {
                        background-color: #1a1a1a;
                        border: 1px solid #444;
                        border-radius: 4px;
                    }
                """)
                self.preview_label.setText("未启动相机预览")
                preview_layout.addWidget(self.preview_label)
                
                # 相机状态标签
                self.camera_status = QLabel("相机状态: 未连接")
                self.camera_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
                self.camera_status.setStyleSheet("color: #aaa; font-size: 12px;")
                preview_layout.addWidget(self.camera_status)
                
                # 控制按钮
                preview_btn_layout = QHBoxLayout()
                preview_btn_layout.addStretch()
                
                self.preview_button = QPushButton("预览相机")
                self.preview_button.setStyleSheet("""
                    QPushButton {
                        background-color: #2d6e2d;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 15px;
                    }
                    QPushButton:hover {
                        background-color: #3a7f3a;
                    }
                    QPushButton:pressed {
                        background-color: #255a25;
                    }
                """)
                self.preview_button.clicked.connect(self._toggle_camera_preview)
                preview_btn_layout.addWidget(self.preview_button)
                
                # 添加截图按钮
                self.snapshot_button = QPushButton("截图")
                self.snapshot_button.setEnabled(False)  # 默认禁用
                self.snapshot_button.setStyleSheet("""
                    QPushButton {
                        background-color: #2d4e6e;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 15px;
                    }
                    QPushButton:hover:enabled {
                        background-color: #3a5f7f;
                    }
                    QPushButton:pressed:enabled {
                        background-color: #254a5a;
                    }
                    QPushButton:disabled {
                        background-color: #333;
                        color: #777;
                    }
                """)
                self.snapshot_button.clicked.connect(self._take_snapshot)
                preview_btn_layout.addWidget(self.snapshot_button)
                
                preview_btn_layout.addStretch()
                preview_layout.addLayout(preview_btn_layout)
                
                input_layout.addRow("", preview_container)
                
        else:
            # 非输入节点，显示输入连接
            input_connections_label = QLabel("输入连接:")
            self.input_connections = QLabel("无")
            input_layout.addRow(input_connections_label, self.input_connections)
            
            # 检查当前输入连接
            connected_nodes = self._get_connected_input_nodes()
            if connected_nodes:
                self.input_connections.setText(", ".join(connected_nodes))
        
        layout.addWidget(input_group)
        
        # 输出部分
        output_group = QGroupBox("输出参数")
        output_layout = QFormLayout(output_group)
        
        if self.node_type == "output":
            # 输出节点
            if "result_output" in self.node_id:
                # 结果输出
                output_format_label = QLabel("输出格式:")
                self.output_format = QComboBox()
                self.output_format.addItems(["CSV", "JSON", "XML", "数据库"])
                output_layout.addRow(output_format_label, self.output_format)
                
                # 输出路径
                output_path_label = QLabel("输出路径:")
                self.output_path = QLineEdit()
                browse_output_button = QPushButton("浏览...")
                output_file_layout = QHBoxLayout()
                output_file_layout.addWidget(self.output_path)
                output_file_layout.addWidget(browse_output_button)
                output_layout.addRow(output_path_label, output_file_layout)
                
            elif "image_output" in self.node_id:
                # 图像输出
                image_format_label = QLabel("图像格式:")
                self.image_format = QComboBox()
                self.image_format.addItems(["PNG", "JPEG", "BMP", "TIFF"])
                output_layout.addRow(image_format_label, self.image_format)
                
                # 质量
                quality_label = QLabel("质量:")
                self.quality = QSlider(Qt.Orientation.Horizontal)
                self.quality.setRange(1, 100)
                self.quality.setValue(90)
                self.quality_value = QLabel("90%")
                quality_layout = QHBoxLayout()
                quality_layout.addWidget(self.quality)
                quality_layout.addWidget(self.quality_value)
                output_layout.addRow(quality_label, quality_layout)
                
                # 连接质量滑块
                self.quality.valueChanged.connect(lambda v: self.quality_value.setText(f"{v}%"))
                
        else:
            # 非输出节点，显示输出连接
            output_connections_label = QLabel("输出连接:")
            self.output_connections = QLabel("无")
            output_layout.addRow(output_connections_label, self.output_connections)
            
            # 检查当前输出连接
            connected_nodes = self._get_connected_output_nodes()
            if connected_nodes:
                self.output_connections.setText(", ".join(connected_nodes))
        
        layout.addWidget(output_group)
        
        # 添加弹性空间
        layout.addStretch()
        
    def _get_connected_input_nodes(self):
        """获取连接到此节点的输入节点
        
        简化实现，返回空列表
        
        Returns:
            List[str]: 输入节点ID列表
        """
        # 简化实现
        return []
    
    def _get_connected_output_nodes(self):
        """获取连接到当前节点输出端口的节点"""
        connected_nodes = []
        
        # 这里需要从画布或工作流获取连接信息
        # 简单示例实现，实际应从ModernWorkflowCanvas或相关类获取
        
        return connected_nodes

    def _setup_algorithm_tab(self):
        """设置算法参数页"""
        tab_layout = QVBoxLayout(self.algorithm_tab)
        tab_layout.setContentsMargins(15, 15, 15, 15)
        tab_layout.setSpacing(15)
        
        # 标题
        title = QLabel("算法参数")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #e0e0e0;")
        tab_layout.addWidget(title)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("background-color: #444444;")
        tab_layout.addWidget(line)
        
        # 添加基本说明
        param_info = QLabel(f"节点类型: {self.node_type}")
        param_info.setStyleSheet("color: #aaaaaa;")
        tab_layout.addWidget(param_info)
        
        # 简化处理，只显示基本参数而不是针对特定节点类型
        params_group = QGroupBox("参数设置")
        params_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
        """)
        params_layout = QFormLayout(params_group)
        
        # 通用参数
        enabled_label = QLabel("启用节点:")
        self.enabled_checkbox = QCheckBox()
        self.enabled_checkbox.setChecked(True)
        params_layout.addRow(enabled_label, self.enabled_checkbox)
        
        # 添加到主布局
        tab_layout.addWidget(params_group)
        
        # 按钮区域 - 保存和重置
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 重置按钮
        reset_btn = QPushButton("重置为默认值")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #7c858d;
            }
            QPushButton:pressed {
                background-color: #5c656d;
            }
        """)
        reset_btn.clicked.connect(self._reset_params)
        button_layout.addWidget(reset_btn)
        
        # 应用按钮
        apply_btn = QPushButton("应用算法")
        apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #0069d9;
            }
            QPushButton:pressed {
                background-color: #0062cc;
            }
        """)
        apply_btn.clicked.connect(self._apply_algorithm)
        button_layout.addWidget(apply_btn)
        
        tab_layout.addLayout(button_layout)
        tab_layout.addStretch(1)
    
    def _on_input_source_changed(self):
        """输入源选择变更处理"""
        # 简化实现，不再需要处理特定的UI组件
        logger.debug("输入源选择变更")
            
    def _apply_algorithm(self):
        """应用算法并预览效果"""
        try:
            # 简化实现
            QMessageBox.information(self, "应用算法", "算法已应用")
            logger.info(f"应用 {self.node_type} 类型节点的算法")
            
        except Exception as e:
            logger.error(f"应用算法时出错: {e}")
            QMessageBox.warning(self, "应用失败", f"应用算法时出错: {str(e)}")

    def _setup_display_tab(self):
        """设置显示选项卡"""
        # 创建简单的布局
        layout = QVBoxLayout(self.display_tab)
        
        # 添加提示
        info_label = QLabel("显示选项将在后续版本中提供")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("color: #aaaaaa; font-style: italic;")
        layout.addWidget(info_label)
        
        # 添加弹性空间
        layout.addStretch()
    
    def _reset_params(self):
        """重置参数为默认值"""
        # 重新获取默认参数
        self.params = self._get_default_params()
        
        # 更新UI
        QMessageBox.information(self, "重置完成", "参数已重置为默认值")
        logger.info("节点参数已重置为默认值")

    def _get_default_params(self):
        """获取默认参数设置
        
        根据节点类型返回相应的默认参数配置
        
        Returns:
            Dict: 参数字典
        """
        # 基础参数
        params = {
            "general": {
                "name": self.node_title,
                "description": "",
                "enabled": True
            }
        }
        
        # 根据节点类型添加特定参数
        if "camera_input" in self.node_id:
            params["camera"] = {
                "device_id": 0,
                "resolution": "640x480",
                "fps": 30,
                "auto_focus": True,
                "auto_exposure": True
            }
        elif "gaussian_blur" in self.node_id:
            params["algorithm"] = {
                "kernel_size": 5,
                "sigma": 1.5
            }
        elif "canny_edge" in self.node_id:
            params["algorithm"] = {
                "threshold1": 100,
                "threshold2": 200,
                "aperture_size": 3
            }
        elif "sobel_edge" in self.node_id:
            params["algorithm"] = {
                "ksize": 3,
                "scale": 1.0,
                "delta": 0.0,
                "dx": 1,
                "dy": 1
            }
        elif "laplacian_edge" in self.node_id:
            params["algorithm"] = {
                "ksize": 3,
                "scale": 1.0,
                "delta": 0.0
            }
        elif "contour_detect" in self.node_id:
            params["algorithm"] = {
                "mode": "EXTERNAL",
                "method": "SIMPLE",
                "min_area": 100,
                "max_area": 10000
            }
        elif "output" in self.node_id:
            params["output"] = {
                "save_path": "./output",
                "format": "png",
                "quality": 95,
                "auto_save": False
            }
            
        # 通用显示参数
        params["display"] = {
            "show_title": True,
            "show_border": True,
            "border_color": "#3498db",
            "background_color": "#2c3e50"
        }
        
        return params
        
    def _toggle_camera_preview(self):
        """切换相机预览状态"""
        try:
            # 切换预览状态
            self.camera_preview_active = not self.camera_preview_active
            
            if self.camera_preview_active:
                # 开始预览
                from wirevsion.ui.camera_utils import CameraManager
                
                # 创建相机管理器（如果不存在）
                if not hasattr(self, 'camera_manager'):
                    self.camera_manager = CameraManager()
                
                # 初始化相机
                if not self.camera_manager.init_camera():
                    logger.error("无法初始化相机，预览失败")
                    self.camera_status.setText("相机状态: 初始化失败")
                    self.camera_preview_active = False
                    self.preview_button.setText("开始预览")
                    return
                
                # 创建预览定时器
                if not self.camera_preview_timer:
                    self.camera_preview_timer = QTimer(self)
                    self.camera_preview_timer.timeout.connect(self._update_camera_preview)
                
                # 开始定时器，每100毫秒更新一次
                self.camera_preview_timer.start(100)
                
                # 更新UI
                self.preview_button.setText("停止预览")
                self.camera_status.setText("相机状态: 预览中")
                logger.info("相机预览已开启")
                
            else:
                # 停止预览
                if self.camera_preview_timer:
                    self.camera_preview_timer.stop()
                
                # 释放相机资源
                if hasattr(self, 'camera_manager'):
                    self.camera_manager.release()
                
                # 更新UI
                self.preview_button.setText("开始预览")
                self.camera_status.setText("相机状态: 已停止")
                logger.info("相机预览已停止")
                
        except Exception as e:
            logger.error(f"相机预览切换出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 确保在发生错误时恢复状态
            self.camera_preview_active = False
            if hasattr(self, 'camera_preview_timer') and self.camera_preview_timer:
                self.camera_preview_timer.stop()
            self.preview_button.setText("开始预览")
            self.camera_status.setText(f"相机状态: 错误 ({str(e)})")
    
    def _update_camera_preview(self):
        """更新相机预览"""
        try:
            # 确保相机管理器存在
            if not hasattr(self, 'camera_manager'):
                logger.error("相机管理器不存在")
                return
                
            # 获取一帧图像
            frame = self.camera_manager.get_frame()
            
            # 检查帧是否有效
            if frame is None or frame.size == 0:
                logger.warning("获取到空帧")
                return
                
            # 显示相机预览
            if hasattr(self, 'preview_label'):
                # 转换为RGB格式用于显示
                import cv2
                import numpy as np
                from PyQt6.QtGui import QImage, QPixmap
                
                # 确保图像数据是连续的
                if not frame.flags['C_CONTIGUOUS']:
                    frame = np.ascontiguousarray(frame)
                
                # 将BGR转为RGB
                if frame.ndim == 3 and frame.shape[2] == 3:
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                else:
                    rgb_frame = frame
                
                # 创建QImage
                h, w = rgb_frame.shape[:2]
                bytes_per_line = 3 * w
                image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
                
                # 缩放以适应预览标签
                pixmap = QPixmap.fromImage(image)
                label_size = self.preview_label.size()
                scaled_pixmap = pixmap.scaled(
                    label_size.width(), label_size.height(), 
                    Qt.KeepAspectRatio, Qt.SmoothTransformation
                )
                
                # 更新预览标签
                self.preview_label.setPixmap(scaled_pixmap)
                
                # 启用截图按钮
                self.snapshot_button.setEnabled(True)
                
                # 保存当前帧供截图使用
                self.current_frame = frame.copy()
                
                logger.debug("相机预览帧已更新")
            else:
                logger.warning("找不到预览标签")
            
        except Exception as e:
            logger.error(f"更新相机预览出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 如果连续出错，自动停止预览
            if self.camera_preview_active:
                self._toggle_camera_preview()  # 关闭预览
    
    def _take_snapshot(self):
        """从相机获取截图"""
        try:
            if not hasattr(self, 'current_frame') or self.current_frame is None:
                logger.warning("没有可用的相机帧进行截图")
                return
            
            # 保存当前帧到节点数据
            if hasattr(self.node, 'camera_frame'):
                self.node.camera_frame = self.current_frame.copy()
                logger.info("已保存截图到节点")
            
            # 显示成功消息
            QMessageBox.information(self, "截图成功", "已成功保存当前相机帧到节点数据")
            
        except Exception as e:
            logger.error(f"相机截图出错: {e}")
            QMessageBox.warning(self, "截图失败", f"保存截图时出错: {str(e)}")
    
    def _lighten_color(self, color_hex, percent):
        """使颜色变亮
        
        Args:
            color_hex: 十六进制颜色值 (例如 "#3498db")
            percent: 变亮的百分比
            
        Returns:
            变亮后的十六进制颜色值
        """
        if not color_hex.startswith('#'):
            return color_hex  # 非十六进制颜色直接返回
            
        rgb = tuple(int(color_hex.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        rgb_new = [min(255, int(c * (1 + percent/100))) for c in rgb]
        return '#{:02x}{:02x}{:02x}'.format(*rgb_new)
    
    def _darken_color(self, color_hex, percent):
        """使颜色变暗
        
        Args:
            color_hex: 十六进制颜色值 (例如 "#3498db")
            percent: 变暗的百分比
            
        Returns:
            变暗后的十六进制颜色值
        """
        if not color_hex.startswith('#'):
            return color_hex  # 非十六进制颜色直接返回
            
        rgb = tuple(int(color_hex.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        rgb_new = [max(0, int(c * (1 - percent/100))) for c in rgb]
        return '#{:02x}{:02x}{:02x}'.format(*rgb_new)
        