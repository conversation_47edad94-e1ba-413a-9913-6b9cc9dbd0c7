"""
配置控件
用于管理和编辑算法参数配置
"""

from PyQt5.QtCore import Qt, pyqtSignal, QSettings
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox,
    QLineEdit, QPushButton, QSlider, QGroupBox,
    QScrollArea, QFormLayout, QColorDialog,
    QFileDialog, QMessageBox
)
from PyQt5.QtGui import QColor, QPalette
import json
import os


class ConfigWidget(QWidget):
    """配置控件"""
    
    # 信号定义
    configChanged = pyqtSignal(dict)  # 配置改变信号
    configSaved = pyqtSignal(str)    # 配置保存信号
    configLoaded = pyqtSignal(str)   # 配置加载信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = {}
        self.parameter_widgets = {}
        self.settings = QSettings('WireVision', 'ConfigWidget')
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = QHBoxLayout()
        
        # 加载配置按钮
        self.load_button = QPushButton("加载配置")
        self.load_button.clicked.connect(self.load_config)
        toolbar.addWidget(self.load_button)
        
        # 保存配置按钮
        self.save_button = QPushButton("保存配置")
        self.save_button.clicked.connect(self.save_config)
        toolbar.addWidget(self.save_button)
        
        # 重置配置按钮
        self.reset_button = QPushButton("重置配置")
        self.reset_button.clicked.connect(self.reset_config)
        toolbar.addWidget(self.reset_button)
        
        toolbar.addStretch()
        layout.addLayout(toolbar)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        # 参数容器
        self.parameter_container = QWidget()
        self.parameter_layout = QVBoxLayout(self.parameter_container)
        self.parameter_layout.setAlignment(Qt.AlignTop)
        
        scroll_area.setWidget(self.parameter_container)
        layout.addWidget(scroll_area)
    
    def set_parameters(self, parameters):
        """设置参数定义
        
        Args:
            parameters: 参数定义字典，格式如下：
            {
                "param_name": {
                    "type": "int/float/bool/str/select/color/file",
                    "default": 默认值,
                    "min": 最小值（数值类型）,
                    "max": 最大值（数值类型）,
                    "step": 步长（数值类型）,
                    "options": 选项列表（select类型）,
                    "description": "参数描述",
                    "group": "参数组名"
                }
            }
        """
        # 清除现有控件
        self.clear_parameters()
        
        # 按组分类参数
        grouped_params = {}
        for name, param_def in parameters.items():
            group = param_def.get('group', '通用参数')
            if group not in grouped_params:
                grouped_params[group] = {}
            grouped_params[group][name] = param_def
        
        # 创建参数控件
        for group_name, group_params in grouped_params.items():
            # 创建组框
            group_box = QGroupBox(group_name)
            group_layout = QFormLayout()
            
            for param_name, param_def in group_params.items():
                # 创建标签
                label = QLabel(param_def.get('description', param_name))
                
                # 根据类型创建控件
                widget = self.create_parameter_widget(param_name, param_def)
                if widget:
                    self.parameter_widgets[param_name] = widget
                    group_layout.addRow(label, widget)
            
            group_box.setLayout(group_layout)
            self.parameter_layout.addWidget(group_box)
        
        # 添加弹性空间
        self.parameter_layout.addStretch()
    
    def create_parameter_widget(self, name, param_def):
        """创建参数控件"""
        param_type = param_def.get('type', 'str')
        default_value = param_def.get('default', '')
        
        if param_type == 'int':
            widget = QSpinBox()
            widget.setMinimum(param_def.get('min', -999999))
            widget.setMaximum(param_def.get('max', 999999))
            widget.setSingleStep(param_def.get('step', 1))
            widget.setValue(default_value)
            widget.valueChanged.connect(lambda: self.on_parameter_changed(name))
            
        elif param_type == 'float':
            widget = QDoubleSpinBox()
            widget.setMinimum(param_def.get('min', -999999.0))
            widget.setMaximum(param_def.get('max', 999999.0))
            widget.setSingleStep(param_def.get('step', 0.1))
            widget.setDecimals(param_def.get('decimals', 2))
            widget.setValue(default_value)
            widget.valueChanged.connect(lambda: self.on_parameter_changed(name))
            
        elif param_type == 'bool':
            widget = QCheckBox()
            widget.setChecked(default_value)
            widget.stateChanged.connect(lambda: self.on_parameter_changed(name))
            
        elif param_type == 'select':
            widget = QComboBox()
            options = param_def.get('options', [])
            widget.addItems([str(opt) for opt in options])
            if default_value in options:
                widget.setCurrentText(str(default_value))
            widget.currentTextChanged.connect(lambda: self.on_parameter_changed(name))
            
        elif param_type == 'color':
            widget = ColorButton()
            if isinstance(default_value, (list, tuple)) and len(default_value) >= 3:
                widget.set_color(QColor(*default_value[:3]))
            widget.colorChanged.connect(lambda: self.on_parameter_changed(name))
            
        elif param_type == 'file':
            widget = FileSelectWidget()
            widget.set_path(default_value)
            widget.pathChanged.connect(lambda: self.on_parameter_changed(name))
            
        elif param_type == 'slider':
            widget = SliderWidget()
            widget.setMinimum(param_def.get('min', 0))
            widget.setMaximum(param_def.get('max', 100))
            widget.setValue(default_value)
            widget.valueChanged.connect(lambda: self.on_parameter_changed(name))
            
        else:  # 默认为字符串类型
            widget = QLineEdit()
            widget.setText(str(default_value))
            widget.textChanged.connect(lambda: self.on_parameter_changed(name))
        
        # 保存参数定义
        widget.setProperty('param_def', param_def)
        
        return widget
    
    def clear_parameters(self):
        """清除所有参数控件"""
        while self.parameter_layout.count():
            item = self.parameter_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        self.parameter_widgets.clear()
    
    def on_parameter_changed(self, name):
        """参数改变处理"""
        self.config = self.get_config()
        self.configChanged.emit(self.config)
    
    def get_config(self):
        """获取当前配置"""
        config = {}
        
        for name, widget in self.parameter_widgets.items():
            param_def = widget.property('param_def')
            param_type = param_def.get('type', 'str')
            
            if param_type == 'int':
                value = widget.value()
            elif param_type == 'float':
                value = widget.value()
            elif param_type == 'bool':
                value = widget.isChecked()
            elif param_type == 'select':
                value = widget.currentText()
            elif param_type == 'color':
                color = widget.get_color()
                value = [color.red(), color.green(), color.blue()]
            elif param_type == 'file':
                value = widget.get_path()
            elif param_type == 'slider':
                value = widget.value()
            else:
                value = widget.text()
            
            config[name] = value
        
        return config
    
    def set_config(self, config):
        """设置配置"""
        self.config = config
        
        for name, value in config.items():
            if name in self.parameter_widgets:
                widget = self.parameter_widgets[name]
                param_def = widget.property('param_def')
                param_type = param_def.get('type', 'str')
                
                # 临时断开信号连接，避免触发多次configChanged
                try:
                    if param_type == 'int':
                        widget.valueChanged.disconnect()
                        widget.setValue(value)
                        widget.valueChanged.connect(lambda: self.on_parameter_changed(name))
                    elif param_type == 'float':
                        widget.valueChanged.disconnect()
                        widget.setValue(value)
                        widget.valueChanged.connect(lambda: self.on_parameter_changed(name))
                    elif param_type == 'bool':
                        widget.stateChanged.disconnect()
                        widget.setChecked(value)
                        widget.stateChanged.connect(lambda: self.on_parameter_changed(name))
                    elif param_type == 'select':
                        widget.currentTextChanged.disconnect()
                        widget.setCurrentText(str(value))
                        widget.currentTextChanged.connect(lambda: self.on_parameter_changed(name))
                    elif param_type == 'color':
                        widget.colorChanged.disconnect()
                        if isinstance(value, (list, tuple)) and len(value) >= 3:
                            widget.set_color(QColor(*value[:3]))
                        widget.colorChanged.connect(lambda: self.on_parameter_changed(name))
                    elif param_type == 'file':
                        widget.pathChanged.disconnect()
                        widget.set_path(value)
                        widget.pathChanged.connect(lambda: self.on_parameter_changed(name))
                    elif param_type == 'slider':
                        widget.valueChanged.disconnect()
                        widget.setValue(value)
                        widget.valueChanged.connect(lambda: self.on_parameter_changed(name))
                    else:
                        widget.textChanged.disconnect()
                        widget.setText(str(value))
                        widget.textChanged.connect(lambda: self.on_parameter_changed(name))
                except:
                    pass
    
    def save_config(self):
        """保存配置到文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存配置", 
            self.settings.value('last_save_path', ''),
            "JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if file_path:
            try:
                config = self.get_config()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                self.settings.setValue('last_save_path', os.path.dirname(file_path))
                self.configSaved.emit(file_path)
                QMessageBox.information(self, "成功", f"配置已保存到：{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存配置失败：{str(e)}")
    
    def load_config(self):
        """从文件加载配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载配置",
            self.settings.value('last_load_path', ''),
            "JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.set_config(config)
                self.settings.setValue('last_load_path', os.path.dirname(file_path))
                self.configLoaded.emit(file_path)
                QMessageBox.information(self, "成功", f"配置已加载：{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载配置失败：{str(e)}")
    
    def reset_config(self):
        """重置配置到默认值"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置所有参数到默认值吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for name, widget in self.parameter_widgets.items():
                param_def = widget.property('param_def')
                default_value = param_def.get('default', '')
                
                # 使用set_config的逻辑设置默认值
                self.set_config({name: default_value})
            
            self.config = self.get_config()
            self.configChanged.emit(self.config)


class ColorButton(QPushButton):
    """颜色选择按钮"""
    colorChanged = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.color = QColor(255, 255, 255)
        self.clicked.connect(self.select_color)
        self.update_style()
    
    def select_color(self):
        """选择颜色"""
        color = QColorDialog.getColor(self.color, self, "选择颜色")
        if color.isValid():
            self.set_color(color)
            self.colorChanged.emit()
    
    def set_color(self, color):
        """设置颜色"""
        self.color = color
        self.update_style()
    
    def get_color(self):
        """获取颜色"""
        return self.color
    
    def update_style(self):
        """更新样式"""
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color.name()};
                border: 1px solid #888;
                min-width: 60px;
                min-height: 25px;
            }}
            QPushButton:hover {{
                border: 2px solid #555;
            }}
        """)


class FileSelectWidget(QWidget):
    """文件选择控件"""
    pathChanged = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.path_edit = QLineEdit()
        layout.addWidget(self.path_edit)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_file)
        layout.addWidget(self.browse_button)
        
        self.path_edit.textChanged.connect(self.pathChanged)
    
    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文件")
        if file_path:
            self.path_edit.setText(file_path)
    
    def get_path(self):
        """获取路径"""
        return self.path_edit.text()
    
    def set_path(self, path):
        """设置路径"""
        self.path_edit.setText(path)


class SliderWidget(QWidget):
    """滑块控件"""
    valueChanged = pyqtSignal(int)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.slider = QSlider(Qt.Horizontal)
        self.slider.valueChanged.connect(self.on_value_changed)
        layout.addWidget(self.slider)
        
        self.value_label = QLabel("0")
        self.value_label.setMinimumWidth(40)
        layout.addWidget(self.value_label)
    
    def on_value_changed(self, value):
        """值改变处理"""
        self.value_label.setText(str(value))
        self.valueChanged.emit(value)
    
    def setMinimum(self, value):
        """设置最小值"""
        self.slider.setMinimum(value)
    
    def setMaximum(self, value):
        """设置最大值"""
        self.slider.setMaximum(value)
    
    def setValue(self, value):
        """设置值"""
        self.slider.setValue(value)
    
    def value(self):
        """获取值"""
        return self.slider.value() 