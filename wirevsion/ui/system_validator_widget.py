"""
系统合理性检测UI组件

该模块提供系统合理性检测的用户界面，包括：
- 运行系统检测
- 显示检测结果
- 生成检测报告
- 提供优化建议

作者: 张玉龙
创建时间: 2025-01-XX
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QTextEdit, QProgressBar,
    QGroupBox, QTableWidget, QTableWidgetItem,
    QHeaderView, QSplitter, QTabWidget, QScrollArea,
    QFrame, QFileDialog, QMessageBox
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QFont, QIcon, QColor

from loguru import logger

# 导入系统检测模块
try:
    from ..core.system_validator import SystemValidator, SystemReport, create_system_validator
except ImportError:
    logger.warning("无法导入系统检测模块，某些功能可能不可用")
    SystemValidator = None
    SystemReport = None
    create_system_validator = None

class ValidationThread(QThread):
    """系统检测线程"""
    progress_updated = pyqtSignal(str, float)  # 进度信息和百分比
    validation_completed = pyqtSignal(object)  # 检测完成，传递SystemReport
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self, validator: 'SystemValidator'):
        super().__init__()
        self.validator = validator
        
    def run(self):
        try:
            # 设置进度回调
            self.validator.set_progress_callback(self.progress_updated.emit)
            
            # 运行完整检测
            report = self.validator.run_full_validation()
            
            # 发送完成信号
            self.validation_completed.emit(report)
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class SystemValidatorWidget(QWidget):
    """系统合理性检测组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.validator: Optional[SystemValidator] = None
        self.validation_thread: Optional[ValidationThread] = None
        self.current_report: Optional[SystemReport] = None
        
        self.init_ui()
        self.init_validator()
        self.connect_signals()
        
        logger.info("系统检测组件初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 检测控制选项卡
        self.control_tab = self.create_control_tab()
        self.tab_widget.addTab(self.control_tab, "系统检测")
        
        # 检测结果选项卡
        self.results_tab = self.create_results_tab()
        self.tab_widget.addTab(self.results_tab, "检测结果")
        
        # 报告查看选项卡
        self.report_tab = self.create_report_tab()
        self.tab_widget.addTab(self.report_tab, "详细报告")
    
    def create_control_tab(self) -> QWidget:
        """创建检测控制选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 检测说明
        info_group = QGroupBox("系统合理性检测说明")
        info_layout = QVBoxLayout()
        info_group.setLayout(info_layout)
        
        info_text = QLabel("""
系统合理性检测将对以下方面进行全面检查：

• UI界面合理性 (25%)
  - PyQt5可用性和UI文件完整性
  - UI类结构和响应性设计
  - 多线程支持和信号槽机制

• 功能完整性 (30%)
  - 核心模块可用性和相机功能
  - 图像处理和工作流管理
  - YOLO深度学习功能

• 算法性能 (25%)
  - 图像处理和模板匹配性能
  - YOLO推理性能和内存效率
  - 实时检测能力评估

• 系统兼容性 (20%)
  - 操作系统和Python版本兼容性
  - 依赖包版本和硬件配置
  - GPU支持和性能评估

检测完成后将生成详细报告，包含评分和优化建议。
        """)
        info_text.setStyleSheet("QLabel { padding: 15px; line-height: 1.4; }")
        info_text.setWordWrap(True)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        # 检测控制
        control_group = QGroupBox("检测控制")
        control_layout = QVBoxLayout()
        control_group.setLayout(control_layout)
        
        # 检测按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("开始系统检测")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        self.stop_button = QPushButton("停止检测")
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        button_layout.addWidget(self.start_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addStretch()
        
        control_layout.addLayout(button_layout)
        
        # 进度显示
        progress_layout = QVBoxLayout()
        
        self.progress_label = QLabel("点击开始检测")
        self.progress_label.setStyleSheet("QLabel { font-size: 14px; color: #6c757d; }")
        progress_layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 3px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)
        
        control_layout.addLayout(progress_layout)
        
        layout.addWidget(control_group)
        
        # 快速结果显示
        quick_results_group = QGroupBox("快速结果")
        quick_results_layout = QVBoxLayout()
        quick_results_group.setLayout(quick_results_layout)
        
        self.quick_results_text = QTextEdit()
        self.quick_results_text.setMaximumHeight(200)
        self.quick_results_text.setReadOnly(True)
        self.quick_results_text.setPlaceholderText("检测结果将在这里显示...")
        quick_results_layout.addWidget(self.quick_results_text)
        
        layout.addWidget(quick_results_group)
        layout.addStretch()
        
        return widget
    
    def create_results_tab(self) -> QWidget:
        """创建检测结果选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 总体评分显示
        score_group = QGroupBox("总体评分")
        score_layout = QHBoxLayout()
        score_group.setLayout(score_layout)
        
        self.total_score_label = QLabel("--")
        self.total_score_label.setStyleSheet("""
            QLabel {
                font-size: 36px;
                font-weight: bold;
                color: #007bff;
                padding: 20px;
                text-align: center;
            }
        """)
        self.total_score_label.setAlignment(Qt.AlignCenter)
        
        self.score_status_label = QLabel("等待检测")
        self.score_status_label.setStyleSheet("QLabel { font-size: 16px; color: #6c757d; }")
        self.score_status_label.setAlignment(Qt.AlignCenter)
        
        score_info_layout = QVBoxLayout()
        score_info_layout.addWidget(self.total_score_label)
        score_info_layout.addWidget(self.score_status_label)
        
        score_layout.addLayout(score_info_layout)
        
        layout.addWidget(score_group)
        
        # 分类评分表格
        category_group = QGroupBox("分类评分")
        category_layout = QVBoxLayout()
        category_group.setLayout(category_layout)
        
        self.category_table = QTableWidget()
        self.category_table.setColumnCount(3)
        self.category_table.setHorizontalHeaderLabels(["检测类别", "得分", "状态"])
        self.category_table.horizontalHeader().setStretchLastSection(True)
        self.category_table.setMaximumHeight(200)
        category_layout.addWidget(self.category_table)
        
        layout.addWidget(category_group)
        
        # 建议列表
        recommendations_group = QGroupBox("优化建议")
        recommendations_layout = QVBoxLayout()
        recommendations_group.setLayout(recommendations_layout)
        
        self.recommendations_list = QTextEdit()
        self.recommendations_list.setReadOnly(True)
        self.recommendations_list.setPlaceholderText("优化建议将在检测完成后显示...")
        recommendations_layout.addWidget(self.recommendations_list)
        
        layout.addWidget(recommendations_group)
        
        return widget
    
    def create_report_tab(self) -> QWidget:
        """创建报告查看选项卡"""
        widget = QWidget()
        layout = QVBoxLayout()
        widget.setLayout(layout)
        
        # 报告操作
        report_actions = QHBoxLayout()
        
        self.export_button = QPushButton("导出报告")
        self.export_button.setEnabled(False)
        
        self.refresh_button = QPushButton("刷新显示")
        
        report_actions.addWidget(self.export_button)
        report_actions.addWidget(self.refresh_button)
        report_actions.addStretch()
        
        layout.addLayout(report_actions)
        
        # 详细报告显示
        self.report_text = QTextEdit()
        self.report_text.setReadOnly(True)
        self.report_text.setPlaceholderText("详细检测报告将在这里显示...")
        self.report_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(self.report_text)
        
        return widget
    
    def init_validator(self):
        """初始化系统检测器"""
        try:
            if create_system_validator:
                self.validator = create_system_validator()
                logger.info("系统检测器初始化成功")
            else:
                logger.warning("系统检测模块不可用")
                self.show_validator_unavailable()
        except Exception as e:
            logger.error(f"初始化系统检测器失败: {e}")
            self.show_validator_unavailable()
    
    def show_validator_unavailable(self):
        """显示检测器不可用提示"""
        self.start_button.setEnabled(False)
        self.start_button.setText("系统检测功能不可用")
        self.progress_label.setText("系统检测模块加载失败，请检查依赖包")
        self.progress_label.setStyleSheet("QLabel { color: #dc3545; font-weight: bold; }")
    
    def connect_signals(self):
        """连接信号和槽"""
        self.start_button.clicked.connect(self.start_validation)
        self.stop_button.clicked.connect(self.stop_validation)
        self.export_button.clicked.connect(self.export_report)
        self.refresh_button.clicked.connect(self.refresh_report_display)
    
    def start_validation(self):
        """开始系统检测"""
        if not self.validator:
            self.show_error("系统检测器未初始化")
            return
        
        try:
            # 更新UI状态
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            
            # 清除之前的结果
            self.quick_results_text.clear()
            self.clear_results()
            
            # 启动检测线程
            self.validation_thread = ValidationThread(self.validator)
            self.validation_thread.progress_updated.connect(self.update_progress)
            self.validation_thread.validation_completed.connect(self.on_validation_completed)
            self.validation_thread.error_occurred.connect(self.on_validation_error)
            self.validation_thread.start()
            
            logger.info("开始系统检测")
            
        except Exception as e:
            logger.error(f"启动系统检测失败: {e}")
            self.show_error(f"启动检测失败: {e}")
            self.reset_ui_state()
    
    def stop_validation(self):
        """停止系统检测"""
        if self.validation_thread and self.validation_thread.isRunning():
            self.validation_thread.terminate()
            self.validation_thread.wait()
            
            self.progress_label.setText("检测已停止")
            self.reset_ui_state()
            logger.info("系统检测已停止")
    
    def update_progress(self, message: str, progress: float):
        """更新检测进度"""
        self.progress_label.setText(message)
        self.progress_bar.setValue(int(progress))
        
        # 在快速结果中显示进度
        current_time = datetime.now().strftime("%H:%M:%S")
        self.quick_results_text.append(f"[{current_time}] {message}")
    
    def on_validation_completed(self, report: SystemReport):
        """检测完成处理"""
        self.current_report = report
        
        # 更新UI
        self.progress_label.setText("检测完成")
        self.progress_bar.setValue(100)
        
        # 显示结果
        self.display_results(report)
        
        # 重置UI状态
        self.reset_ui_state()
        
        # 切换到结果选项卡
        self.tab_widget.setCurrentWidget(self.results_tab)
        
        logger.success("系统检测完成")
    
    def on_validation_error(self, error_msg: str):
        """检测错误处理"""
        self.show_error(f"检测过程中发生错误: {error_msg}")
        self.progress_label.setText("检测失败")
        self.reset_ui_state()
    
    def display_results(self, report: SystemReport):
        """显示检测结果"""
        # 显示总分
        total_score = report.total_score
        self.total_score_label.setText(f"{total_score:.1f}")
        
        # 根据分数设置颜色和状态
        if total_score >= 80:
            score_color = "#28a745"  # 绿色
            status_text = "优秀"
        elif total_score >= 60:
            score_color = "#ffc107"  # 黄色
            status_text = "良好"
        else:
            score_color = "#dc3545"  # 红色
            status_text = "需要改进"
        
        self.total_score_label.setStyleSheet(f"""
            QLabel {{
                font-size: 36px;
                font-weight: bold;
                color: {score_color};
                padding: 20px;
                text-align: center;
            }}
        """)
        self.score_status_label.setText(status_text)
        
        # 显示分类评分
        self.category_table.setRowCount(len(report.category_scores))
        
        category_names = {
            'ui': 'UI界面合理性',
            'function': '功能完整性',
            'algorithm': '算法性能',
            'system': '系统兼容性'
        }
        
        for i, (category, score) in enumerate(report.category_scores.items()):
            # 类别名称
            name_item = QTableWidgetItem(category_names.get(category, category))
            self.category_table.setItem(i, 0, name_item)
            
            # 分数
            score_item = QTableWidgetItem(f"{score:.1f}")
            if score >= 80:
                score_item.setBackground(QColor("#d4edda"))
            elif score >= 60:
                score_item.setBackground(QColor("#fff3cd"))
            else:
                score_item.setBackground(QColor("#f8d7da"))
            self.category_table.setItem(i, 1, score_item)
            
            # 状态
            if score >= 80:
                status = "优秀"
            elif score >= 60:
                status = "良好"
            else:
                status = "需要改进"
            status_item = QTableWidgetItem(status)
            self.category_table.setItem(i, 2, status_item)
        
        # 显示建议
        recommendations_text = "\n".join([f"• {rec}" for rec in report.recommendations])
        self.recommendations_list.setText(recommendations_text)
        
        # 启用导出按钮
        self.export_button.setEnabled(True)
        
        # 更新详细报告
        self.update_detailed_report(report)
    
    def update_detailed_report(self, report: SystemReport):
        """更新详细报告显示"""
        report_content = f"""
系统合理性检测报告
==================

检测时间: {report.timestamp}
总体评分: {report.total_score:.1f}/100

系统信息:
--------
平台: {report.system_info.get('platform', 'N/A')}
Python版本: {report.system_info.get('python_version', 'N/A')}
CPU核心数: {report.system_info.get('cpu_count', 'N/A')}
内存总量: {report.system_info.get('memory_total', 'N/A')}
可用磁盘空间: {report.system_info.get('disk_free', 'N/A')}

分类评分:
--------
"""
        
        category_names = {
            'ui': 'UI界面合理性',
            'function': '功能完整性', 
            'algorithm': '算法性能',
            'system': '系统兼容性'
        }
        
        for category, score in report.category_scores.items():
            report_content += f"{category_names.get(category, category)}: {score:.1f}/100\n"
        
        report_content += "\n详细检测结果:\n" + "="*50 + "\n"
        
        for result in report.results:
            status_symbol = "✓" if result.status == "pass" else "⚠" if result.status == "warning" else "✗"
            report_content += f"\n{status_symbol} [{result.category}] {result.test_name}\n"
            report_content += f"   评分: {result.score:.1f}/100\n"
            report_content += f"   信息: {result.message}\n"
            
            if result.recommendations:
                report_content += "   建议:\n"
                for rec in result.recommendations:
                    report_content += f"   - {rec}\n"
        
        report_content += "\n总体建议:\n" + "="*30 + "\n"
        for i, rec in enumerate(report.recommendations, 1):
            report_content += f"{i}. {rec}\n"
        
        self.report_text.setText(report_content)
    
    def clear_results(self):
        """清除检测结果"""
        self.total_score_label.setText("--")
        self.score_status_label.setText("等待检测")
        self.category_table.setRowCount(0)
        self.recommendations_list.clear()
        self.report_text.clear()
        self.export_button.setEnabled(False)
    
    def reset_ui_state(self):
        """重置UI状态"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
    
    def export_report(self):
        """导出检测报告"""
        if not self.current_report:
            self.show_error("没有可导出的报告")
            return
        
        try:
            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出检测报告", 
                f"system_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON文件 (*.json);;所有文件 (*)"
            )
            
            if file_path:
                self.validator.save_report(self.current_report, file_path)
                self.show_info("导出成功", f"报告已保存到: {file_path}")
                
        except Exception as e:
            logger.error(f"导出报告失败: {e}")
            self.show_error(f"导出报告失败: {e}")
    
    def refresh_report_display(self):
        """刷新报告显示"""
        if self.current_report:
            self.update_detailed_report(self.current_report)
    
    def show_info(self, title: str, message: str):
        """显示信息对话框"""
        QMessageBox.information(self, title, message)
    
    def show_error(self, message: str):
        """显示错误信息"""
        QMessageBox.critical(self, "错误", message)
        logger.error(message) 