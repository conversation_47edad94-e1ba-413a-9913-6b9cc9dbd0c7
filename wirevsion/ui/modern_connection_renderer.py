#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化连接线渲染器

优化的连接线渲染系统：
- 贝塞尔曲线路径
- 智能路径规划
- 批量渲染优化
- 动画效果支持
- LOD细节层次
"""

from PyQt6.QtCore import (
    Qt, QPointF, QRectF, QLineF, pyqtSignal,
    QPropertyAnimation, QEasingCurve, QTimer,
    QObject
)
from PyQt6.QtGui import (
    QPainter, QPainterPath, QColor, QPen, QBrush,
    QLinearGradient, QRadialGradient, QPolygonF,
    QTransform
)
from PyQt6.QtWidgets import QGraphicsItem, QGraphicsPathItem
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum
import math
from loguru import logger


class ConnectionType(Enum):
    """连接类型枚举"""
    DATA = "data"           # 数据连接
    CONTROL = "control"     # 控制连接
    PARAMETER = "param"     # 参数连接
    ERROR = "error"         # 错误连接


@dataclass
class ConnectionStyle:
    """连接样式配置"""
    color: QColor
    width: float
    dash_pattern: Optional[List[float]] = None
    animated: bool = False
    glow: bool = False
    arrow_size: float = 8.0


class PathCache:
    """路径缓存管理器"""
    
    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, QPainterPath] = {}
        self.max_size = max_size
        self.access_count: Dict[str, int] = {}
    
    def get_key(self, start: QPointF, end: QPointF, style: str = "default") -> str:
        """生成缓存键"""
        return f"{start.x():.1f},{start.y():.1f}-{end.x():.1f},{end.y():.1f}-{style}"
    
    def get(self, start: QPointF, end: QPointF, style: str = "default") -> Optional[QPainterPath]:
        """获取缓存路径"""
        key = self.get_key(start, end, style)
        if key in self.cache:
            self.access_count[key] = self.access_count.get(key, 0) + 1
            return self.cache[key]
        return None
    
    def put(self, start: QPointF, end: QPointF, path: QPainterPath, style: str = "default"):
        """存储路径"""
        if len(self.cache) >= self.max_size:
            # 移除最少使用的路径
            min_key = min(self.access_count.items(), key=lambda x: x[1])[0]
            del self.cache[min_key]
            del self.access_count[min_key]
        
        key = self.get_key(start, end, style)
        self.cache[key] = path
        self.access_count[key] = 1
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_count.clear()


class OptimizedConnection(QGraphicsPathItem):
    """优化的连接线组件"""
    
    def __init__(self, start_point: QPointF, end_point: QPointF,
                 connection_type: ConnectionType = ConnectionType.DATA,
                 parent=None):
        super().__init__(parent)
        
        self.start_point = start_point
        self.end_point = end_point
        self.connection_type = connection_type
        
        # 样式配置
        self.styles = {
            ConnectionType.DATA: ConnectionStyle(
                color=QColor(13, 110, 253),
                width=3.0,
                animated=True,
                glow=True
            ),
            ConnectionType.CONTROL: ConnectionStyle(
                color=QColor(255, 193, 7),
                width=2.5,
                dash_pattern=[5, 3],
                animated=False
            ),
            ConnectionType.PARAMETER: ConnectionStyle(
                color=QColor(108, 117, 125),
                width=2.0,
                dash_pattern=[2, 2]
            ),
            ConnectionType.ERROR: ConnectionStyle(
                color=QColor(220, 53, 69),
                width=3.0,
                animated=True,
                glow=True
            )
        }
        
        # 动画相关
        self.animation_offset = 0
        self.animation_timer = None
        
        # 设置标志
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setAcceptHoverEvents(True)
        
        # 初始化
        self._update_path()
        self._setup_style()
        
        # 启动动画
        if self.get_style().animated:
            self._start_animation()
    
    def get_style(self) -> ConnectionStyle:
        """获取当前样式"""
        return self.styles.get(self.connection_type, self.styles[ConnectionType.DATA])
    
    def _update_path(self):
        """更新路径"""
        path = self._create_bezier_path(self.start_point, self.end_point)
        self.setPath(path)
    
    def _create_bezier_path(self, start: QPointF, end: QPointF) -> QPainterPath:
        """创建贝塞尔曲线路径"""
        path = QPainterPath()
        path.moveTo(start)
        
        # 计算控制点
        dx = end.x() - start.x()
        dy = end.y() - start.y()
        
        # 根据连接方向调整控制点
        if abs(dx) > abs(dy):
            # 水平为主
            ctrl1 = QPointF(start.x() + dx * 0.5, start.y())
            ctrl2 = QPointF(end.x() - dx * 0.5, end.y())
        else:
            # 垂直为主
            ctrl1 = QPointF(start.x(), start.y() + dy * 0.5)
            ctrl2 = QPointF(end.x(), end.y() - dy * 0.5)
        
        # 创建贝塞尔曲线
        path.cubicTo(ctrl1, ctrl2, end)
        
        return path
    
    def _setup_style(self):
        """设置样式"""
        style = self.get_style()
        
        # 创建画笔
        pen = QPen(style.color, style.width)
        pen.setCapStyle(Qt.RoundCap)
        pen.setJoinStyle(Qt.RoundJoin)
        
        # 设置虚线
        if style.dash_pattern:
            pen.setDashPattern(style.dash_pattern)
        
        self.setPen(pen)
        
        # 设置发光效果
        if style.glow:
            self.setGraphicsEffect(self._create_glow_effect(style.color))
    
    def _create_glow_effect(self, color: QColor):
        """创建发光效果"""
        from PyQt6.QtWidgets import QGraphicsDropShadowEffect
        
        effect = QGraphicsDropShadowEffect()
        effect.setBlurRadius(10)
        effect.setColor(color)
        effect.setOffset(0, 0)
        return effect
    
    def _start_animation(self):
        """启动动画"""
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._update_animation)
        self.animation_timer.start(50)  # 20 FPS
    
    def _update_animation(self):
        """更新动画"""
        self.animation_offset += 2
        if self.animation_offset > 20:
            self.animation_offset = 0
        
        # 更新虚线偏移
        style = self.get_style()
        if style.dash_pattern:
            pen = self.pen()
            pen.setDashOffset(self.animation_offset)
            self.setPen(pen)
        
        self.update()
    
    def paint(self, painter: QPainter, option, widget=None):
        """自定义绘制"""
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制连接线
        super().paint(painter, option, widget)
        
        # 绘制箭头
        self._draw_arrow(painter)
        
        # 绘制数据流动效果
        if self.get_style().animated:
            self._draw_flow_effect(painter)
    
    def _draw_arrow(self, painter: QPainter):
        """绘制箭头"""
        style = self.get_style()
        path = self.path()
        
        if path.length() < 50:
            return
        
        # 获取路径末端的方向
        t = 0.98  # 接近末端的位置
        point = path.pointAtPercent(t)
        angle = path.angleAtPercent(t)
        
        # 创建箭头
        arrow_size = style.arrow_size
        arrow = QPolygonF([
            QPointF(0, 0),
            QPointF(-arrow_size, arrow_size/2),
            QPointF(-arrow_size, -arrow_size/2)
        ])
        
        # 变换到正确位置
        transform = QTransform()
        transform.translate(point.x(), point.y())
        transform.rotate(-angle)
        arrow = transform.map(arrow)
        
        # 绘制
        painter.setBrush(QBrush(style.color))
        painter.setPen(Qt.NoPen)
        painter.drawPolygon(arrow)
    
    def _draw_flow_effect(self, painter: QPainter):
        """绘制数据流动效果"""
        if not self.get_style().animated:
            return
        
        path = self.path()
        length = path.length()
        
        # 绘制多个流动点
        num_points = 3
        for i in range(num_points):
            t = ((self.animation_offset + i * 20) % 100) / 100.0
            point = path.pointAtPercent(t)
            
            # 渐变圆点
            gradient = QRadialGradient(point, 6)
            gradient.setColorAt(0, QColor(255, 255, 255, 200))
            gradient.setColorAt(1, QColor(255, 255, 255, 0))
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(point, 6, 6)
    
    def update_points(self, start: QPointF, end: QPointF):
        """更新连接点"""
        self.start_point = start
        self.end_point = end
        self._update_path()
    
    def set_connection_type(self, connection_type: ConnectionType):
        """设置连接类型"""
        self.connection_type = connection_type
        self._setup_style()
        
        # 重新启动动画
        if self.animation_timer:
            self.animation_timer.stop()
        
        if self.get_style().animated:
            self._start_animation()


class ConnectionRenderer(QObject):
    """连接渲染管理器"""
    
    def __init__(self):
        super().__init__()
        
        self.connections: List[OptimizedConnection] = []
        self.path_cache = PathCache()
        self.visible_connections: Set[OptimizedConnection] = set()
        
        # 性能优化参数
        self.lod_enabled = True
        self.lod_threshold = 0.5  # 缩放阈值
        self.batch_size = 100     # 批量渲染大小
        
        logger.debug("连接渲染管理器初始化完成")
    
    def create_connection(self, start: QPointF, end: QPointF,
                         connection_type: ConnectionType = ConnectionType.DATA) -> OptimizedConnection:
        """创建连接"""
        connection = OptimizedConnection(start, end, connection_type)
        self.connections.append(connection)
        return connection
    
    def remove_connection(self, connection: OptimizedConnection):
        """移除连接"""
        if connection in self.connections:
            self.connections.remove(connection)
            if connection in self.visible_connections:
                self.visible_connections.remove(connection)
    
    def update_visible_connections(self, viewport: QRectF, scale: float = 1.0):
        """更新可见连接列表"""
        self.visible_connections.clear()
        
        for connection in self.connections:
            # 检查是否在视口内
            if self._is_connection_visible(connection, viewport):
                self.visible_connections.add(connection)
                
                # 根据缩放级别调整细节
                if self.lod_enabled:
                    self._apply_lod(connection, scale)
    
    def _is_connection_visible(self, connection: OptimizedConnection, viewport: QRectF) -> bool:
        """检查连接是否可见"""
        # 获取连接的边界框
        bbox = connection.boundingRect()
        
        # 扩展一定范围，确保部分可见的连接也被渲染
        expanded_viewport = viewport.adjusted(-50, -50, 50, 50)
        
        return expanded_viewport.intersects(bbox)
    
    def _apply_lod(self, connection: OptimizedConnection, scale: float):
        """应用细节层次优化"""
        style = connection.get_style()
        
        if scale < self.lod_threshold:
            # 低细节模式
            connection.setGraphicsEffect(None)  # 移除发光效果
            
            # 简化线条
            pen = connection.pen()
            pen.setWidth(max(1, style.width * scale))
            connection.setPen(pen)
        else:
            # 恢复完整细节
            connection._setup_style()
    
    def batch_render(self, connections: List[OptimizedConnection], painter: QPainter):
        """批量渲染连接"""
        # 按类型分组
        grouped = {}
        for conn in connections:
            conn_type = conn.connection_type
            if conn_type not in grouped:
                grouped[conn_type] = []
            grouped[conn_type].append(conn)
        
        # 批量渲染每种类型
        for conn_type, conns in grouped.items():
            style = self.connections[0].styles[conn_type]
            
            # 设置通用样式
            pen = QPen(style.color, style.width)
            pen.setCapStyle(Qt.RoundCap)
            if style.dash_pattern:
                pen.setDashPattern(style.dash_pattern)
            
            painter.setPen(pen)
            
            # 批量绘制路径
            for conn in conns[:self.batch_size]:
                painter.drawPath(conn.path())
    
    def optimize_paths(self, connections: List[Tuple[QPointF, QPointF]]) -> List[QPainterPath]:
        """优化路径计算"""
        optimized_paths = []
        
        for start, end in connections:
            # 检查缓存
            cached_path = self.path_cache.get(start, end)
            if cached_path:
                optimized_paths.append(cached_path)
            else:
                # 创建新路径
                path = self._create_optimized_path(start, end)
                self.path_cache.put(start, end, path)
                optimized_paths.append(path)
        
        return optimized_paths
    
    def _create_optimized_path(self, start: QPointF, end: QPointF) -> QPainterPath:
        """创建优化的路径"""
        # 智能路径规划
        path = QPainterPath()
        path.moveTo(start)
        
        dx = end.x() - start.x()
        dy = end.y() - start.y()
        distance = math.sqrt(dx*dx + dy*dy)
        
        if distance < 100:
            # 短距离直接连接
            path.lineTo(end)
        else:
            # 长距离使用贝塞尔曲线
            # 根据方向智能选择控制点
            if abs(dx) > abs(dy) * 2:
                # 主要是水平连接
                offset = min(abs(dx) * 0.5, 200)
                ctrl1 = QPointF(start.x() + offset, start.y())
                ctrl2 = QPointF(end.x() - offset, end.y())
            elif abs(dy) > abs(dx) * 2:
                # 主要是垂直连接
                offset = min(abs(dy) * 0.5, 200)
                ctrl1 = QPointF(start.x(), start.y() + offset)
                ctrl2 = QPointF(end.x(), end.y() - offset)
            else:
                # 对角连接
                ctrl1 = QPointF(start.x() + dx * 0.25, start.y() + dy * 0.75)
                ctrl2 = QPointF(start.x() + dx * 0.75, start.y() + dy * 0.25)
            
            path.cubicTo(ctrl1, ctrl2, end)
        
        return path
    
    def clear_cache(self):
        """清空缓存"""
        self.path_cache.clear()
        logger.debug("连接路径缓存已清空") 