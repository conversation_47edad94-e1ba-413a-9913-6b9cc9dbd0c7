#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置模式界面
提供工作流创建、模板配置、ROI设置、颜色检测、YOLO深度学习等功能
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QPushButton, QLabel, QTabWidget, QListWidget,
    QGroupBox, QFormLayout, QLineEdit, QTextEdit,
    QComboBox, QSpinBox, QCheckBox, QSlider,
    QFileDialog, QMessageBox, QFrame, QScrollArea,
    QTreeWidget, QTreeWidgetItem, QGraphicsView,
    QGraphicsScene, QGraphicsPixmapItem, QGraphicsRectItem,
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QRectF
from PyQt5.QtGui import QPixmap, QBrush, QColor, QPen, QPainter

import cv2
import numpy as np
from typing import Dict, List, Optional, Any
from loguru import logger

from wirevsion.core import VisionEngine, WorkflowManager
from wirevsion.camera.camera_manager import CameraManager
from wirevsion.config.config_manager import ConfigManager
from wirevsion.utils.image_utils import cv_to_qpixmap

from .camera_widget import CameraWidget
from .template_widget import TemplateWidget
from .roi_widget import ROIWidget
from .workflow_widget import WorkflowWidget
from .workflow_editor import WorkflowEditorWidget

# 导入YOLO组件
try:
    from .yolo_widget import YOLOWidget
except ImportError:
    logger.warning("YOLO组件导入失败，YOLO功能将不可用")
    YOLOWidget = None

# 导入系统检测组件
try:
    from .system_validator_widget import SystemValidatorWidget
except ImportError:
    logger.warning("系统检测组件导入失败，系统检测功能将不可用")
    SystemValidatorWidget = None


class ConfigModeWidget(QWidget):
    """
    配置模式界面
    
    提供完整的工作流配置功能，包括传统视觉检测和YOLO深度学习
    """
    
    # 信号定义
    workflow_created = pyqtSignal(str)  # 工作流已创建
    image_updated = pyqtSignal(np.ndarray)  # 图像已更新
    image_captured = pyqtSignal(np.ndarray)  # 图像已捕获
    status_message = pyqtSignal(str)  # 状态消息
    
    def __init__(self, config_manager: ConfigManager, vision_engine: VisionEngine,
                 workflow_manager: WorkflowManager, camera_manager: CameraManager):
        """
        初始化配置模式界面
        
        Args:
            config_manager: 配置管理器
            vision_engine: 视觉检测引擎
            workflow_manager: 工作流管理器
            camera_manager: 相机管理器
        """
        super().__init__()
        
        self.config_manager = config_manager
        self.vision_engine = vision_engine
        self.workflow_manager = workflow_manager
        self.camera_manager = camera_manager
        
        # 当前状态
        self.current_image: Optional[np.ndarray] = None
        self.current_workflow_name: str = ""
        
        # YOLO组件
        self.yolo_widget: Optional[YOLOWidget] = None
        
        # 系统检测组件
        self.system_validator_widget: Optional[SystemValidatorWidget] = None
        
        # 创建UI
        self._create_ui()
        
        # 连接信号
        self._connect_signals()
    
        # 应用样式
        self._apply_styles()
        
        logger.info("配置模式界面初始化完成")
    
    def _create_ui(self):
        """创建UI组件"""
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧配置面板
        self._create_config_panel(splitter)
        
        # 右侧图像显示和结果面板
        self._create_display_panel(splitter)
        
        # 设置分割比例
        splitter.setSizes([400, 800])
        
        main_layout.addWidget(splitter)
    
    def _create_config_panel(self, parent):
        """创建左侧配置面板"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        
        # 配置选项卡
        self.config_tabs = QTabWidget()
        
        # 相机配置选项卡
        self.camera_widget = CameraWidget(self.camera_manager)
        self.camera_widget.image_captured.connect(self._on_image_captured)
        self.config_tabs.addTab(self.camera_widget, "相机")
        
        # 现代化算法管理器选项卡
        try:
            from .modern_algorithm_manager import ModernAlgorithmManager
            self.algorithm_manager = ModernAlgorithmManager()
            self.algorithm_manager.algorithm_selected.connect(self._on_algorithm_selected)
            self.algorithm_manager.algorithm_executed.connect(self._on_algorithm_executed)
            self.config_tabs.addTab(self.algorithm_manager, "算法管理")
            logger.info("现代化算法管理器创建成功")
        except Exception as e:
            error_msg = f"创建算法管理器失败: {str(e)}"
            logger.error(error_msg)
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)
            error_label = QLabel(f"算法管理器加载失败:\n{error_msg}")
            error_label.setStyleSheet("color: #ff6b6b; padding: 20px; font-size: 14px;")
            error_layout.addWidget(error_label)
            self.config_tabs.addTab(error_widget, "算法管理")
            self.algorithm_manager = None
        
        # 可视化工作流编辑器
        try:
            self.workflow_editor = WorkflowEditorWidget(parent=self)
            self.workflow_editor.workflow_changed.connect(self._on_workflow_editor_changed)
            self.workflow_editor.open_module_config.connect(self._on_module_config_requested)
            self.config_tabs.addTab(self.workflow_editor, "可视化流程")
            
            # 延迟加载演示工作流（避免初始化时序问题）
            QTimer.singleShot(500, self._load_demo_workflow)
            
            logger.info("可视化工作流编辑器创建成功")
        except Exception as e:
            error_msg = f"创建可视化工作流编辑器失败: {str(e)}"
            logger.error(error_msg)
            # 创建失败时显示错误信息
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)
            error_label = QLabel(f"可视化编辑器加载失败:\n{error_msg}")
            error_label.setStyleSheet("color: #ff6b6b; padding: 20px; font-size: 14px;")
            error_layout.addWidget(error_label)
            self.config_tabs.addTab(error_widget, "可视化流程")
            self.workflow_editor = None
        
        # YOLO深度学习组件
        if YOLOWidget:
            try:
                self.yolo_widget = YOLOWidget()
                self.config_tabs.addTab(self.yolo_widget, "YOLO深度学习")
                logger.info("YOLO组件创建成功")
            except Exception as e:
                error_msg = f"创建YOLO组件失败: {str(e)}"
                logger.error(error_msg)
                # 创建失败时显示错误信息
                error_widget = QWidget()
                error_layout = QVBoxLayout(error_widget)
                error_label = QLabel(f"YOLO组件加载失败:\n{error_msg}")
                error_label.setStyleSheet("color: #ff6b6b; padding: 20px; font-size: 14px;")
                error_layout.addWidget(error_label)
                self.config_tabs.addTab(error_widget, "YOLO深度学习")
                self.yolo_widget = None
        else:
            # YOLO模块不可用时显示提示
            unavailable_widget = QWidget()
            unavailable_layout = QVBoxLayout(unavailable_widget)
            unavailable_label = QLabel("YOLO深度学习功能不可用\n\n请安装相关依赖:\npip install torch torchvision\npip install ultralytics")
            unavailable_label.setStyleSheet("color: #ffa500; padding: 20px; font-size: 14px; text-align: center;")
            unavailable_layout.addWidget(unavailable_label)
            
            # 添加安装提示按钮
            install_button = QPushButton("查看安装说明")
            install_button.clicked.connect(self._show_yolo_install_help)
            install_button.setStyleSheet("""
                QPushButton {
                    background-color: #ffa500;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #ff8c00;
                }
            """)
            unavailable_layout.addWidget(install_button, alignment=Qt.AlignCenter)
            unavailable_layout.addStretch()
            
            self.config_tabs.addTab(unavailable_widget, "YOLO深度学习")
        
        # 系统检测组件
        if SystemValidatorWidget:
            try:
                self.system_validator_widget = SystemValidatorWidget()
                self.config_tabs.addTab(self.system_validator_widget, "系统检测")
                logger.info("系统检测组件创建成功")
            except Exception as e:
                error_msg = f"创建系统检测组件失败: {str(e)}"
                logger.error(error_msg)
                # 创建失败时显示错误信息
                error_widget = QWidget()
                error_layout = QVBoxLayout(error_widget)
                error_label = QLabel(f"系统检测组件加载失败:\n{error_msg}")
                error_label.setStyleSheet("color: #ff6b6b; padding: 20px; font-size: 14px;")
                error_layout.addWidget(error_label)
                self.config_tabs.addTab(error_widget, "系统检测")
                self.system_validator_widget = None
        else:
            # 系统检测模块不可用时显示提示
            unavailable_widget = QWidget()
            unavailable_layout = QVBoxLayout(unavailable_widget)
            unavailable_label = QLabel("系统检测功能不可用\n\n请检查核心模块是否正常安装")
            unavailable_label.setStyleSheet("color: #ffa500; padding: 20px; font-size: 14px; text-align: center;")
            unavailable_layout.addWidget(unavailable_label)
            unavailable_layout.addStretch()
            
            self.config_tabs.addTab(unavailable_widget, "系统检测")
            self.system_validator_widget = None
        
        # 默认切换到可视化流程选项卡
        if self.workflow_editor:
            self.config_tabs.setCurrentWidget(self.workflow_editor)
        
        config_layout.addWidget(self.config_tabs)
        
        parent.addWidget(config_widget)
    
    def _create_display_panel(self, parent):
        """创建右侧显示面板"""
        display_widget = QWidget()
        display_layout = QVBoxLayout(display_widget)
        
        # 图像显示区域
        image_group = QGroupBox("图像显示")
        image_layout = QVBoxLayout(image_group)
        
        # 图像信息栏
        info_layout = QHBoxLayout()
        
        self.image_info_label = QLabel("无图像")
        info_layout.addWidget(self.image_info_label)
        
        info_layout.addStretch()
        
        # 缩放控制
        zoom_label = QLabel("缩放:")
        self.zoom_slider = QSlider(Qt.Horizontal)
        self.zoom_slider.setRange(25, 400)
        self.zoom_slider.setValue(100)
        self.zoom_slider.valueChanged.connect(self._on_zoom_changed)
        
        self.zoom_value_label = QLabel("100%")
        
        info_layout.addWidget(zoom_label)
        info_layout.addWidget(self.zoom_slider)
        info_layout.addWidget(self.zoom_value_label)
        
        image_layout.addLayout(info_layout)
        
        # 图像显示视图
        self.image_view = ImageDisplayView()
        self.image_view.setMinimumHeight(400)
        image_layout.addWidget(self.image_view)
        
        display_layout.addWidget(image_group)
        
        parent.addWidget(display_widget)
    
    def _connect_signals(self):
        """连接信号"""
        # 相机图像更新
        self.camera_widget.image_captured.connect(self._on_image_captured)
        self.camera_widget.status_message.connect(self.status_message.emit)
        
        # 工作流编辑器信号连接
        if self.workflow_editor:
            self.workflow_editor.workflow_changed.connect(self._on_workflow_editor_changed)
            self.workflow_editor.open_module_config.connect(self._on_module_config_requested)
        
        # YOLO组件信号连接
        if self.yolo_widget:
            # 当相机捕获图像时，也可以通过YOLO进行检测
            self.image_captured.connect(self._on_yolo_image_update)
        
        # 图像视图信号
        self.image_view.roi_selected.connect(self._on_roi_selected)
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 5px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QTabWidget::pane {
                border: 1px solid #555555;
                background-color: #3c3c3c;
            }
            
            QTabBar::tab {
                background-color: #4a4a4a;
                color: #ffffff;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            
            QTabBar::tab:selected {
                background-color: #0078d4;
            }
            
            QTabBar::tab:hover {
                background-color: #5a5a5a;
            }
            
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #106ebe;
            }
            
            QPushButton:pressed {
                background-color: #005a9e;
            }
            
            QLineEdit {
                background-color: #4a4a4a;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 5px;
            }
            
            QComboBox {
                background-color: #4a4a4a;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 5px;
            }
        """)
    
    def new_workflow(self):
        """新建工作流"""
        # 创建默认工作流
        workflow = self.workflow_manager.create_workflow("默认工作流", "自动创建的工作流")
        self.current_workflow_name = "默认工作流"
        
        # 更新界面
        if self.workflow_editor and hasattr(self.workflow_editor, 'load_workflow'):
            self.workflow_editor.load_workflow(workflow)
        
        # 发送信号
        self.workflow_created.emit("默认工作流")
        self.status_message.emit("已创建默认工作流")
        
        logger.info("创建默认工作流")
    
    def save_workflow(self):
        """保存工作流"""
        if not self.workflow_manager.get_current_workflow():
            # 如果没有工作流，自动创建一个
            self.new_workflow()
        
        if self.workflow_manager.save_workflow():
            self.status_message.emit("工作流已保存")
            logger.info("工作流已保存")
        else:
            self.status_message.emit("保存工作流失败")
    
    def load_workflow(self):
        """加载工作流"""
        workflow_files = self.workflow_manager.get_workflow_list()
        
        if not workflow_files:
            # 如果没有工作流文件，自动创建一个
            self.new_workflow()
            return
            
        # 加载第一个可用的工作流
        filename = workflow_files[0]
        if self.workflow_manager.load_workflow(filename):
            workflow = self.workflow_manager.get_current_workflow()
            self.current_workflow_name = workflow.name
            
            # 更新界面
            if self.workflow_editor and hasattr(self.workflow_editor, 'load_workflow'):
                self.workflow_editor.load_workflow(workflow)
            
            self.status_message.emit(f"已加载工作流: {workflow.name}")
            logger.info(f"加载工作流: {workflow.name}")
        else:
            self.status_message.emit("加载工作流失败")
    
    def _on_image_captured(self, image: np.ndarray):
        """图像捕获事件处理"""
        self.current_image = image
        
        # 更新图像显示
        self.image_view.set_image(image)
        
        # 更新图像信息
        if image is not None:
            height, width = image.shape[:2]
            channels = image.shape[2] if len(image.shape) > 2 else 1
            self.image_info_label.setText(f"图像: {width}x{height}, {channels}通道")
        else:
            self.image_info_label.setText("无图像")
        
        # 发送信号
        self.image_updated.emit(image)
        self.image_captured.emit(image)
        
        logger.debug(f"图像已更新: {image.shape if image is not None else 'None'}")
    
    def _on_workflow_editor_changed(self, workflow_config):
        """可视化工作流编辑器变更事件处理"""
        # 处理workflow_config可能是字典或对象的情况
        if workflow_config:
            if isinstance(workflow_config, dict):
                workflow_name = workflow_config.get("name", "未命名工作流")
            else:
                workflow_name = getattr(workflow_config, 'name', '未命名工作流')
        else:
            workflow_name = 'None'
            
        logger.info(f"可视化工作流已变更: {workflow_name}")
        # 同步到工作流管理器
        if workflow_config:
            # 这里可以将可视化编辑器的配置同步到workflow_manager
            self.status_message.emit(f"可视化工作流已更新: {workflow_name}")
    
    def _on_module_config_requested(self, module_id: str, module_type: str):
        """模块配置请求事件处理"""
        logger.info(f"请求配置模块: {module_id} (类型: {module_type})")
        
        # 创建并显示模块配置弹窗
        self._show_module_config_dialog(module_id, module_type)
    
    def _load_demo_workflow(self):
        """加载演示工作流"""
        if self.workflow_editor and hasattr(self.workflow_editor, 'load_demo_on_startup'):
            try:
                self.workflow_editor.load_demo_on_startup()
                logger.info("演示工作流加载完成")
            except Exception as e:
                logger.error(f"演示工作流加载失败: {e}")
    
    def _show_module_config_dialog(self, module_id: str, module_type: str):
        """显示模块配置对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"配置模块: {module_id}")
        dialog.setFixedSize(800, 600)
        dialog.setModal(True)
        
        layout = QVBoxLayout(dialog)

        # 根据模块类型创建对应的配置界面
        config_widget = None
        
        if module_type == "template_match" or module_type == "template_matching":
            # 创建模板配置界面
            from .template_widget import TemplateWidget
            config_widget = TemplateWidget(self.vision_engine.template_matcher)
            config_widget.template_updated.connect(lambda info: self._on_module_config_updated(module_id, info))
            
        elif module_type == "roi_detection" or module_type == "roi":
            # 创建ROI配置界面
            from .roi_widget import ROIWidget
            config_widget = ROIWidget()
            config_widget.roi_updated.connect(lambda info: self._on_module_config_updated(module_id, info))
            
        elif module_type == "camera" or module_type == "image_source":
            # 使用现有的相机配置界面
            config_widget = QLabel("相机配置请在左侧相机选项卡中进行")
            config_widget.setStyleSheet("color: #888888; font-size: 14px; padding: 20px;")
            
        elif module_type == "position_correction":
            # 位置修正配置（暂未实现）
            config_widget = QLabel(f"位置修正模块配置界面正在开发中...\n模块ID: {module_id}")
            config_widget.setStyleSheet("color: #888888; font-size: 14px; padding: 20px;")
            
        else:
            # 未知模块类型
            config_widget = QLabel(f"未知模块类型: {module_type}\n模块ID: {module_id}")
            config_widget.setStyleSheet("color: #ff6b6b; font-size: 14px; padding: 20px;")
        
        if config_widget:
            layout.addWidget(config_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(dialog.accept)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(dialog.reject)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
        # 设置对话框样式
        dialog.setStyleSheet("""
            QDialog {
                background-color: #2d2d2d;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
        """)
        
        # 显示对话框
        result = dialog.exec_()
        
        if result == QDialog.Accepted:
            self.status_message.emit(f"模块 {module_id} 配置已保存")
            logger.info(f"模块 {module_id} 配置已保存")
        else:
            self.status_message.emit(f"取消配置模块 {module_id}")
    
    def _on_module_config_updated(self, module_id: str, config_info: Dict[str, Any]):
        """模块配置更新事件处理"""
        logger.info(f"模块 {module_id} 配置已更新: {config_info}")
        # 这里可以将配置信息同步到工作流编辑器中的对应模块
    
    def _on_zoom_changed(self, value: int):
        """缩放变化事件处理"""
        zoom_factor = value / 100.0
        self.zoom_value_label.setText(f"{value}%")
        self.image_view.set_zoom(zoom_factor)

    def _on_roi_selected(self, roi: QRectF):
        """ROI选择事件处理"""
        logger.info(f"ROI已选择: {roi}")
        # 这里可以根据选择的ROI进行相应的处理

    def _on_yolo_image_update(self, image: np.ndarray):
        """YOLO图像更新处理"""
        if self.yolo_widget and image is not None:
            try:
                # 如果YOLO组件可用且有检测器加载，可以进行实时检测
                detection_result = self.yolo_widget.detect_objects(image)
                if detection_result and len(detection_result.boxes) > 0:
                    # 在图像上可视化检测结果
                    annotated_image = self.yolo_widget.detector.visualize_results(image, detection_result)
                    # 这里可以选择是否显示带标注的图像
                    pass
            except Exception as e:
                logger.debug(f"YOLO实时检测失败: {e}")
    
    def _show_yolo_install_help(self):
        """显示YOLO深度学习安装帮助"""
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("YOLO深度学习安装帮助")
        help_dialog.setFixedSize(600, 500)
        help_dialog.setModal(True)
        
        layout = QVBoxLayout(help_dialog)
        
        # 标题
        title_label = QLabel("YOLO深度学习模块安装指南")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #0078d4; padding: 10px;")
        layout.addWidget(title_label)
        
        # 安装说明
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_content = """
YOLO深度学习模块需要安装以下依赖包：

1. PyTorch (深度学习框架)
   pip install torch torchvision

2. Ultralytics (YOLOv8实现)
   pip install ultralytics

3. 其他相关依赖
   pip install supervision
   pip install matplotlib seaborn

完整安装命令：
pip install torch torchvision ultralytics supervision matplotlib seaborn

注意事项：
- 如果您有NVIDIA GPU，建议安装支持CUDA的PyTorch版本以获得更好的性能
- 首次使用时会自动下载YOLOv8模型文件，请确保网络连接正常
- 模型文件较大(约6-200MB不等)，请耐心等待下载完成

GPU版本安装 (推荐)：
# 访问 https://pytorch.org/ 获取适合您CUDA版本的安装命令
# 例如 CUDA 11.8:
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

验证安装：
安装完成后，重启应用程序，YOLO功能选项卡将变为可用状态。

如有问题，请检查：
- Python版本是否为3.8+
- 网络连接是否正常
- 磁盘空间是否充足(至少需要2GB空间)
        """
        help_text.setPlainText(help_content)
        help_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                color: #333333;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(help_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 打开网站按钮
        web_button = QPushButton("访问PyTorch官网")
        web_button.clicked.connect(lambda: self._open_url("https://pytorch.org/"))
        web_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(help_dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        button_layout.addWidget(web_button)
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        
        # 设置对话框样式
        help_dialog.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                color: #333333;
            }
        """)
        
        help_dialog.exec_()
        logger.info("显示YOLO深度学习安装帮助")
    
    def _open_url(self, url: str):
        """打开URL"""
        try:
            import webbrowser
            webbrowser.open(url)
            logger.info(f"打开URL: {url}")
        except Exception as e:
            logger.error(f"打开URL失败: {e}")
            self.status_message.emit(f"无法打开网址: {url}")
    
    def get_yolo_detection_config(self) -> Optional[Dict[str, Any]]:
        """获取YOLO检测配置"""
        if self.yolo_widget:
            return self.yolo_widget.get_detection_config()
        return None
    
    def perform_yolo_detection(self, image: np.ndarray) -> Optional[Any]:
        """执行YOLO检测"""
        if self.yolo_widget and image is not None:
            return self.yolo_widget.detect_objects(image)
        return None
    
    def is_yolo_available(self) -> bool:
        """检查YOLO功能是否可用"""
        return self.yolo_widget is not None and hasattr(self.yolo_widget, 'detector') and self.yolo_widget.detector is not None

    def _on_algorithm_selected(self, category: str, algorithm_name: str):
        """算法选择事件处理"""
        logger.info(f"选择算法: {category}.{algorithm_name}")
        self.status_message.emit(f"已选择算法: {category}.{algorithm_name}")
    
    def _on_algorithm_executed(self, category: str, algorithm_name: str, result: Dict[str, Any]):
        """算法执行事件处理"""
        logger.info(f"算法执行完成: {category}.{algorithm_name}")
        self.status_message.emit(f"算法执行完成: {category}.{algorithm_name}")
        
        # 如果有结果图像，更新图像显示
        if result and 'image' in result:
            self._on_image_captured(result['image'])


class ImageDisplayView(QGraphicsView):
    """
    图像显示视图
    
    支持图像显示、缩放、ROI选择等功能
    """
    
    # 信号定义
    roi_selected = pyqtSignal(QRectF)  # ROI选择信号
    
    def __init__(self):
        """初始化图像显示视图"""
        super().__init__()
        
        # 创建场景
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        # 显示属性
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHint(QPainter.Antialiasing)
        
        # 当前状态
        self.image_item: Optional[QGraphicsPixmapItem] = None
        self.roi_items: List[QGraphicsRectItem] = []
        self.zoom_factor = 1.0
        
        # ROI绘制状态
        self.drawing_roi = False
        self.roi_start_point = None
        self.current_roi_item = None
        
        logger.debug("图像显示视图初始化完成")
    
    def set_image(self, image: np.ndarray):
        """
        设置显示图像
        
        Args:
            image: 图像数组
        """
        if image is None:
            return
        
        try:
            # 转换图像为QPixmap
            pixmap = cv_to_qpixmap(image)
            
            # 清除之前的图像
            if self.image_item:
                self.scene.removeItem(self.image_item)
            
            # 添加新图像
            self.image_item = self.scene.addPixmap(pixmap)
            
            # 调整视图 - 修复QRect到QRectF的转换问题
            scene_rect = QRectF(pixmap.rect())
            self.scene.setSceneRect(scene_rect)
            self.fitInView(self.image_item, Qt.KeepAspectRatio)
            
            logger.debug(f"图像已设置: {image.shape}")
            
        except Exception as e:
            logger.error(f"设置图像失败: {e}")
    
    def set_zoom(self, factor: float):
        """
        设置缩放因子
        
        Args:
            factor: 缩放因子
        """
        if self.image_item is None:
                return
            
        self.zoom_factor = factor
        
        # 重置变换
        self.resetTransform()
        
        # 应用缩放
        self.scale(factor, factor)
        
        logger.debug(f"缩放已设置: {factor}")
    
    def set_roi_regions(self, regions: List[Dict[str, Any]]):
        """
        设置ROI区域
        
        Args:
            regions: ROI区域列表
        """
        # 清除之前的ROI
        for item in self.roi_items:
            self.scene.removeItem(item)
        self.roi_items.clear()
            
        # 添加新的ROI
        for region in regions:
            x = region.get('x', 0)
            y = region.get('y', 0)
            width = region.get('width', 100)
            height = region.get('height', 100)
            name = region.get('name', 'ROI')
            
            # 创建ROI矩形
            rect_item = self.scene.addRect(
                x, y, width, height,
                QPen(QColor(255, 0, 0), 2),
                QBrush(QColor(255, 0, 0, 50))
            )
            
            # 添加文本标签
            text_item = self.scene.addText(name, QFont("Arial", 10))
            text_item.setPos(x, y - 20)
            text_item.setDefaultTextColor(QColor(255, 0, 0))
            
            self.roi_items.extend([rect_item, text_item])
        
        logger.debug(f"ROI区域已设置: {len(regions)}个")
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton and event.modifiers() == Qt.ControlModifier:
            # Ctrl+左键开始绘制ROI
            self.drawing_roi = True
            self.roi_start_point = self.mapToScene(event.pos())
            
            # 创建临时ROI矩形
            self.current_roi_item = self.scene.addRect(
                QRectF(self.roi_start_point, self.roi_start_point),
                QPen(QColor(0, 255, 0), 2),
                QBrush(QColor(0, 255, 0, 30))
            )
        else:
            super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.drawing_roi and self.current_roi_item:
            # 更新ROI矩形
            current_point = self.mapToScene(event.pos())
            rect = QRectF(self.roi_start_point, current_point).normalized()
            self.current_roi_item.setRect(rect)
        else:
            super().mouseMoveEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if self.drawing_roi and event.button() == Qt.LeftButton:
            # 完成ROI绘制
            if self.current_roi_item:
                rect = self.current_roi_item.rect()
                
                # 检查ROI大小
                if rect.width() > 10 and rect.height() > 10:
                    # 发送ROI选择信号
                    self.roi_selected.emit(rect)
                else:
                    # 移除太小的ROI
                    self.scene.removeItem(self.current_roi_item)
                
                self.current_roi_item = None
            
            self.drawing_roi = False
            self.roi_start_point = None
        else:
            super().mouseReleaseEvent(event)
    
    def wheelEvent(self, event):
        """鼠标滚轮事件"""
        # 滚轮缩放
        factor = 1.2 if event.angleDelta().y() > 0 else 1 / 1.2
        self.scale(factor, factor)
        
        # 更新缩放因子
        self.zoom_factor *= factor
