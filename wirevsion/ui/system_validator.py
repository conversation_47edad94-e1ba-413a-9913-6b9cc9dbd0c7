"""
系统验证器模块
用于验证WireVision系统的各个方面
"""

import os
import sys
import time
import platform
import psutil
import json
from typing import Dict, Any, List, Tuple, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QThread
from loguru import logger
import cv2
import numpy as np


class SystemValidator(QObject):
    """系统验证器"""
    
    # 信号定义
    validationStarted = pyqtSignal()
    validationCompleted = pyqtSignal(dict)
    validationFailed = pyqtSignal(str)
    progressUpdated = pyqtSignal(int, str)  # 进度百分比，当前任务
    
    def __init__(self):
        super().__init__()
        self.validation_results = {}
        self.is_validating = False
        
        # 验证权重配置
        self.validation_weights = {
            "ui": 0.25,          # UI验证权重 25%
            "functionality": 0.30,  # 功能验证权重 30%
            "algorithm": 0.25,    # 算法验证权重 25%
            "compatibility": 0.20  # 兼容性验证权重 20%
        }
    
    def start_validation(self, validation_types: List[str] = None):
        """开始系统验证
        
        Args:
            validation_types: 要执行的验证类型列表，None表示执行所有验证
        """
        if self.is_validating:
            logger.warning("验证已在进行中")
            return
        
        if validation_types is None:
            validation_types = ["ui", "functionality", "algorithm", "compatibility"]
        
        self.is_validating = True
        self.validation_results = {}
        self.validationStarted.emit()
        
        # 创建验证线程
        self.validation_thread = ValidationThread(self, validation_types)
        self.validation_thread.progressUpdated.connect(self.progressUpdated.emit)
        self.validation_thread.validationCompleted.connect(self._on_validation_completed)
        self.validation_thread.errorOccurred.connect(self._on_validation_failed)
        self.validation_thread.start()
    
    def validate_ui(self) -> Dict[str, Any]:
        """验证UI组件"""
        logger.info("开始UI验证...")
        results = {
            "passed": True,
            "score": 100,
            "details": {},
            "errors": []
        }
        
        try:
            # 检查PyQt5是否正确安装
            try:
                from PyQt5.QtCore import QT_VERSION_STR
                from PyQt5.QtWidgets import QApplication
                results["details"]["pyqt5_version"] = QT_VERSION_STR
                results["details"]["pyqt5_installed"] = True
            except ImportError as e:
                results["passed"] = False
                results["score"] -= 50
                results["errors"].append(f"PyQt5未正确安装: {e}")
            
            # 检查UI模块
            ui_modules = [
                "wirevsion.ui.main_window",
                "wirevsion.ui.workflow_editor",
                "wirevsion.ui.config_mode",
                "wirevsion.ui.run_mode",
                "wirevsion.ui.camera_widget",
                "wirevsion.ui.image_display",
                "wirevsion.ui.config_widget"
            ]
            
            for module_name in ui_modules:
                try:
                    __import__(module_name)
                    results["details"][f"{module_name}_loaded"] = True
                except ImportError as e:
                    results["passed"] = False
                    results["score"] -= 10
                    results["errors"].append(f"无法加载UI模块 {module_name}: {e}")
            
            # 检查样式和主题
            try:
                from wirevsion.ui.modern_components import ModernStyle
                results["details"]["modern_style_available"] = True
            except:
                results["details"]["modern_style_available"] = False
                results["score"] -= 5
            
        except Exception as e:
            results["passed"] = False
            results["score"] = 0
            results["errors"].append(f"UI验证失败: {e}")
        
        return results
    
    def validate_functionality(self) -> Dict[str, Any]:
        """验证功能模块"""
        logger.info("开始功能验证...")
        results = {
            "passed": True,
            "score": 100,
            "details": {},
            "errors": []
        }
        
        try:
            # 检查核心功能模块
            core_modules = [
                ("wirevsion.core.workflow_manager", "WorkflowManager"),
                ("wirevsion.core.algorithm_manager", "AlgorithmManager"),
                ("wirevsion.camera.camera_manager", "CameraManager"),
                ("wirevsion.config.config_manager", "ConfigManager")
            ]
            
            for module_path, class_name in core_modules:
                try:
                    module = __import__(module_path, fromlist=[class_name])
                    if hasattr(module, class_name):
                        results["details"][f"{class_name}_available"] = True
                    else:
                        results["passed"] = False
                        results["score"] -= 15
                        results["errors"].append(f"类 {class_name} 在模块 {module_path} 中不存在")
                except ImportError as e:
                    results["passed"] = False
                    results["score"] -= 15
                    results["errors"].append(f"无法加载功能模块 {module_path}: {e}")
            
            # 检查工作流功能
            try:
                from wirevsion.core.workflow_manager import WorkflowManager
                wm = WorkflowManager()
                results["details"]["workflow_manager_functional"] = True
            except Exception as e:
                results["score"] -= 10
                results["errors"].append(f"工作流管理器初始化失败: {e}")
            
            # 检查算法注册
            try:
                from wirevsion.algorithms.registry import AlgorithmRegistry
                registry = AlgorithmRegistry()
                algorithm_count = len(registry.get_all_algorithms())
                results["details"]["registered_algorithms"] = algorithm_count
                if algorithm_count < 5:
                    results["score"] -= 5
                    results["errors"].append(f"注册的算法数量过少: {algorithm_count}")
            except Exception as e:
                results["score"] -= 10
                results["errors"].append(f"算法注册表访问失败: {e}")
            
        except Exception as e:
            results["passed"] = False
            results["score"] = 0
            results["errors"].append(f"功能验证失败: {e}")
        
        return results
    
    def validate_algorithms(self) -> Dict[str, Any]:
        """验证算法性能"""
        logger.info("开始算法验证...")
        results = {
            "passed": True,
            "score": 100,
            "details": {},
            "errors": [],
            "performance": {}
        }
        
        try:
            # 创建测试图像
            test_image = np.random.randint(0, 255, (640, 480, 3), dtype=np.uint8)
            
            # 测试基础图像处理算法
            basic_algorithms = [
                ("wirevsion.algorithms.image_processing", "GrayscaleAlgorithm"),
                ("wirevsion.algorithms.image_processing", "ThresholdAlgorithm"),
                ("wirevsion.algorithms.image_processing", "BlurAlgorithm"),
                ("wirevsion.algorithms.feature_detection", "EdgeDetectionAlgorithm"),
                ("wirevsion.algorithms.feature_detection", "ContourDetectionAlgorithm")
            ]
            
            for module_path, class_name in basic_algorithms:
                try:
                    module = __import__(module_path, fromlist=[class_name])
                    algorithm_class = getattr(module, class_name)
                    algorithm = algorithm_class()
                    
                    # 测试算法执行
                    start_time = time.time()
                    config = type('Config', (), {'parameters': algorithm.get_default_parameters()})()
                    result = algorithm.execute({"image": test_image}, config)
                    execution_time = time.time() - start_time
                    
                    results["performance"][class_name] = {
                        "execution_time": execution_time,
                        "success": result.success if hasattr(result, 'success') else False
                    }
                    
                    if execution_time > 1.0:  # 如果执行时间超过1秒
                        results["score"] -= 5
                        results["errors"].append(f"{class_name} 执行时间过长: {execution_time:.2f}秒")
                    
                except Exception as e:
                    results["score"] -= 10
                    results["errors"].append(f"算法 {class_name} 测试失败: {e}")
            
            # 测试深度学习算法（如果可用）
            try:
                from wirevsion.algorithms.deep_learning import YOLODetectionAlgorithm
                yolo = YOLODetectionAlgorithm()
                results["details"]["deep_learning_available"] = True
                
                # 检查是否安装了深度学习依赖
                try:
                    import torch
                    results["details"]["pytorch_installed"] = True
                    results["details"]["pytorch_version"] = torch.__version__
                except ImportError:
                    results["details"]["pytorch_installed"] = False
                    results["score"] -= 5
                
                try:
                    import ultralytics
                    results["details"]["ultralytics_installed"] = True
                except ImportError:
                    results["details"]["ultralytics_installed"] = False
                    results["score"] -= 5
                    
            except ImportError:
                results["details"]["deep_learning_available"] = False
                results["score"] -= 10
            
        except Exception as e:
            results["passed"] = False
            results["score"] = 0
            results["errors"].append(f"算法验证失败: {e}")
        
        return results
    
    def validate_compatibility(self) -> Dict[str, Any]:
        """验证系统兼容性"""
        logger.info("开始兼容性验证...")
        results = {
            "passed": True,
            "score": 100,
            "details": {},
            "errors": [],
            "system_info": {}
        }
        
        try:
            # 获取系统信息
            results["system_info"] = {
                "platform": platform.platform(),
                "python_version": sys.version,
                "opencv_version": cv2.__version__,
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total / (1024**3),  # GB
                "memory_available": psutil.virtual_memory().available / (1024**3)  # GB
            }
            
            # 检查Python版本
            python_version = sys.version_info
            if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
                results["score"] -= 20
                results["errors"].append(f"Python版本过低: {sys.version}")
            
            # 检查OpenCV版本
            cv_version = cv2.__version__.split('.')
            if int(cv_version[0]) < 4:
                results["score"] -= 10
                results["errors"].append(f"OpenCV版本过低: {cv2.__version__}")
            
            # 检查内存
            if results["system_info"]["memory_total"] < 4:
                results["score"] -= 10
                results["errors"].append(f"系统内存不足: {results['system_info']['memory_total']:.1f}GB")
            
            # 检查必要的依赖
            required_packages = [
                ("numpy", "1.19.0"),
                ("PyQt5", "5.12.0"),
                ("opencv-python", "4.0.0"),
                ("loguru", "0.5.0")
            ]
            
            for package_name, min_version in required_packages:
                try:
                    package = __import__(package_name)
                    if hasattr(package, '__version__'):
                        results["details"][f"{package_name}_version"] = package.__version__
                except ImportError:
                    results["score"] -= 10
                    results["errors"].append(f"缺少必要的依赖包: {package_name}")
            
            # 检查相机支持
            try:
                cap = cv2.VideoCapture(0)
                if cap.isOpened():
                    results["details"]["camera_available"] = True
                    cap.release()
                else:
                    results["details"]["camera_available"] = False
            except:
                results["details"]["camera_available"] = False
            
        except Exception as e:
            results["passed"] = False
            results["score"] = 0
            results["errors"].append(f"兼容性验证失败: {e}")
        
        return results
    
    def calculate_overall_score(self) -> Dict[str, Any]:
        """计算总体评分"""
        overall_score = 0
        total_weight = 0
        
        for category, weight in self.validation_weights.items():
            if category in self.validation_results:
                score = self.validation_results[category].get("score", 0)
                overall_score += score * weight
                total_weight += weight
        
        if total_weight > 0:
            overall_score = overall_score / total_weight
        
        # 确定系统状态
        if overall_score >= 90:
            status = "优秀"
            recommendation = "系统运行状态良好，所有功能正常"
        elif overall_score >= 70:
            status = "良好"
            recommendation = "系统基本功能正常，建议修复发现的问题"
        elif overall_score >= 50:
            status = "一般"
            recommendation = "系统存在一些问题，建议尽快修复"
        else:
            status = "差"
            recommendation = "系统存在严重问题，需要立即修复"
        
        return {
            "overall_score": overall_score,
            "status": status,
            "recommendation": recommendation,
            "validation_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "details": self.validation_results
        }
    
    def export_report(self, file_path: str):
        """导出验证报告"""
        try:
            report = self.calculate_overall_score()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"验证报告已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出报告失败: {e}")
            return False
    
    def _on_validation_completed(self, results: Dict[str, Any]):
        """验证完成处理"""
        self.is_validating = False
        self.validation_results = results
        overall = self.calculate_overall_score()
        self.validationCompleted.emit(overall)
    
    def _on_validation_failed(self, error: str):
        """验证失败处理"""
        self.is_validating = False
        self.validationFailed.emit(error)


class ValidationThread(QThread):
    """验证线程"""
    
    progressUpdated = pyqtSignal(int, str)
    validationCompleted = pyqtSignal(dict)
    errorOccurred = pyqtSignal(str)
    
    def __init__(self, validator: SystemValidator, validation_types: List[str]):
        super().__init__()
        self.validator = validator
        self.validation_types = validation_types
    
    def run(self):
        """运行验证"""
        try:
            results = {}
            total_steps = len(self.validation_types)
            
            for i, val_type in enumerate(self.validation_types):
                progress = int((i / total_steps) * 100)
                self.progressUpdated.emit(progress, f"正在验证{val_type}...")
                
                if val_type == "ui":
                    results["ui"] = self.validator.validate_ui()
                elif val_type == "functionality":
                    results["functionality"] = self.validator.validate_functionality()
                elif val_type == "algorithm":
                    results["algorithm"] = self.validator.validate_algorithms()
                elif val_type == "compatibility":
                    results["compatibility"] = self.validator.validate_compatibility()
            
            self.progressUpdated.emit(100, "验证完成")
            self.validationCompleted.emit(results)
            
        except Exception as e:
            self.errorOccurred.emit(str(e)) 