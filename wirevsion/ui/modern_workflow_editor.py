#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化工作流编辑器UI

提供可视化的工作流编辑功能：
- 节点拖放
- 连线编辑
- 属性配置
- 实时预览
- 缩放和平移
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QLabel,
    QGraphicsScene, QGraphicsView, QPushButton, QGraphicsItem,
    QGraphicsPathItem, QGraphicsEllipseItem, QGraphicsTextItem,
    QMenu, QAction, QFrame, QSlider, QListWidget, QListWidgetItem,
    QGraphicsSceneMouseEvent, QGraphicsLineItem, QApplication,
    QSizePolicy, QScrollArea, QToolButton, QDockWidget, QMessageBox,
    QGraphicsDropShadowEffect, QToolBar, QMainWindow, QComboBox
)
from PyQt6.QtCore import (
    Qt, QPointF, QRectF, QSizeF, pyqtSignal, QPropertyAnimation,
    QEasingCurve, QMimeData, QByteArray, pyqtProperty, QLineF, QPoint, QTimer, QDateTime
)
from PyQt6.QtGui import (
    QPainter, QPainterPath, QColor, QBrush, QPen, QFont,
    QLinearGradient, QRadialGradient, QDrag, QPixmap,
    QTransform, QWheelEvent, QPainterPathStroker, QImage
)
from typing import Dict, List, Optional, Tuple, Any
from loguru import logger
import json
import random
import math
import time
import sys

from wirevsion.ui.modern_components import ModernCard, ModernButton, ModernInput, THEME_COLORS
from wirevsion.ui.camera_utils import (
    UIRefresher, ImageDisplayManager, CameraManager, ImageProcessor, ColorHelper
)


class ModernFlowNode(QGraphicsItem):
    """现代化流程节点"""

    def __init__(self, node_id: str, node_type: str, title: str,
                 pos: QPointF = QPointF(0, 0), parent=None):
        super().__init__(parent)

        self.node_id = node_id
        self.node_type = node_type
        self.title = title
        self.width = 180
        self.height = 70
        self.corner_radius = 8
        self.is_selected = False
        self.is_hovered = False
        self._hovered_port = None  # 存储当前悬停的端口名称
        self._port_animation_step = 0  # 端口动画步骤
        self._port_animation_direction = 1  # 动画方向 1:放大 -1:缩小
        self._port_tooltip: Optional[QLabel] = None  # 端口提示标签
        self._port_tooltip_timer = QTimer()  # 提示显示计时器
        self._port_tooltip_timer.setSingleShot(True)
        self._port_tooltip_timer.timeout.connect(self._show_port_tooltip)

        # 输入输出端口
        self.input_ports: List[Dict] = []
        self.output_ports: List[Dict] = []

        # 设置标志
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.ItemSendsGeometryChanges, True)
        self.setAcceptHoverEvents(True)  # 确保接收悬停事件

        # 设置位置
        self.setPos(pos)

        # 添加阴影效果
        self._setup_shadow()

        # 初始化端口
        self._init_ports()

        logger.debug(f"创建节点: {title} ({node_type})")

    def _setup_shadow(self):
        """设置阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(12)
        shadow.setColor(QColor(0, 0, 0, 70))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)

    def _init_ports(self):
        """初始化端口 - 每个节点都有4个端口（上下左右）"""
        # 所有节点都有4个端口
        self.ports = {
            "top": {"type": "universal", "position": "top"},
            "bottom": {"type": "universal", "position": "bottom"},
            "left": {"type": "universal", "position": "left"},
            "right": {"type": "universal", "position": "right"}
        }

        # 为了兼容旧代码，保留 input_ports 和 output_ports
        self.input_ports = [self.ports["left"], self.ports["top"]]
        self.output_ports = [self.ports["right"], self.ports["bottom"]]

    def boundingRect(self) -> QRectF:
        """获取边界矩形"""
        # 添加一些额外的边距以容纳阴影
        margin = 5
        return QRectF(-self.width/2 - margin, -self.height/2 - margin,
                      self.width + 2*margin, self.height + 2*margin)

    def shape(self) -> QPainterPath:
        """定义节点的形状，用于更精确的碰撞检测"""
        path = QPainterPath()
        rect = QRectF(-self.width/2, -self.height/2, self.width, self.height)
        path.addRoundedRect(rect, self.corner_radius, self.corner_radius)
        return path

    def paint(self, painter: QPainter, option, widget=None):
        """绘制节点"""
        painter.setRenderHint(QPainter.Antialiasing)

        # 节点颜色配置
        base_colors = {
            "input":   {"bg": THEME_COLORS["success"], "border": QColor(THEME_COLORS["success"]).darker(115).name()},
            "output":  {"bg": THEME_COLORS["primary"], "border": QColor(THEME_COLORS["primary"]).darker(115).name()},
            "process": {"bg": THEME_COLORS["warning"], "border": QColor(THEME_COLORS["warning"]).darker(115).name()}
        }

        node_style = base_colors.get(self.node_type, base_colors["process"])

        # 获取边界矩形，保留上下圆角，左右去掉圆角
        rect = QRectF(-self.width/2, -self.height/2, self.width, self.height)

        # 创建背景路径 - 四个角都使用圆角
        path = QPainterPath()

        # 左上角圆角
        path.moveTo(-self.width/2, -self.height/2 + self.corner_radius)
        path.arcTo(-self.width/2, -self.height/2, self.corner_radius * 2, self.corner_radius * 2, 180, -90)

        # 右上角圆角
        path.lineTo(self.width/2 - self.corner_radius, -self.height/2)
        path.arcTo(self.width/2 - self.corner_radius * 2, -self.height/2, self.corner_radius * 2, self.corner_radius * 2, 90, -90)

        # 右下角圆角
        path.lineTo(self.width/2, self.height/2 - self.corner_radius)
        path.arcTo(self.width/2 - self.corner_radius * 2, self.height/2 - self.corner_radius * 2,
                  self.corner_radius * 2, self.corner_radius * 2, 0, -90)

        # 左下角圆角
        path.lineTo(-self.width/2 + self.corner_radius, self.height/2)
        path.arcTo(-self.width/2, self.height/2 - self.corner_radius * 2,
                  self.corner_radius * 2, self.corner_radius * 2, 270, -90)

        # 闭合路径
        path.closeSubpath()

        # 扁平化风格背景 - 使用纯色而非渐变
        bg_color = QColor(node_style["bg"])
        if self.is_selected:
            bg_color = bg_color.lighter(110)  # 选中状态略微变亮
        elif self.is_hovered:
            bg_color = bg_color.lighter(105)  # 悬停状态轻微变亮

        painter.fillPath(path, QBrush(bg_color))

        # 绘制边框 - 扁平风格，边框更细
        border_color = QColor(node_style["border"])
        border_width = 1.0  # 统一细边框

        if self.is_selected:
            # 选中状态使用白色虚线边框，增强辨识度
            pen = QPen(QColor("#ffffff"), border_width, Qt.DashLine)
            pen.setDashPattern([3, 2])  # 设置虚线样式
        else:
            pen = QPen(border_color, border_width, Qt.SolidLine)

        painter.setPen(pen)
        painter.drawPath(path)

        # 绘制顶部标题栏
        title_height = 22  # 扁平化设计，标题栏更窄
        title_path = QPainterPath()

        # 左上角圆角
        title_path.moveTo(-self.width/2, -self.height/2 + self.corner_radius)
        title_path.arcTo(-self.width/2, -self.height/2, self.corner_radius * 2, self.corner_radius * 2, 180, -90)

        # 右上角圆角
        title_path.lineTo(self.width/2 - self.corner_radius, -self.height/2)
        title_path.arcTo(self.width/2 - self.corner_radius * 2, -self.height/2, self.corner_radius * 2, self.corner_radius * 2, 90, -90)

        # 右下角圆角
        title_path.lineTo(self.width/2, -self.height/2 + title_height - self.corner_radius)
        title_path.arcTo(self.width/2 - self.corner_radius * 2, -self.height/2 + title_height - self.corner_radius * 2,
                         self.corner_radius * 2, self.corner_radius * 2, 0, -90)

        # 左下角圆角
        title_path.lineTo(-self.width/2 + self.corner_radius, -self.height/2 + title_height)
        title_path.arcTo(-self.width/2, -self.height/2 + title_height - self.corner_radius * 2,
                         self.corner_radius * 2, self.corner_radius * 2, 270, -90)

        # 闭合路径
        title_path.closeSubpath()

        # 标题栏使用纯色，稍微深一点
        title_color = QColor(node_style["border"])
        painter.fillPath(title_path, QBrush(title_color))

        # 绘制标题文字 - 居中对齐
        text_color = THEME_COLORS["text_on_warning_bg"] if self.node_type == "process" else THEME_COLORS["text_on_primary_bg"]
        painter.setPen(QPen(QColor(text_color)))
        font = QFont("Arial", 9, QFont.Medium)
        painter.setFont(font)
        title_text_rect = QRectF(-self.width/2, -self.height/2, self.width, title_height)
        painter.drawText(title_text_rect, Qt.AlignmentFlag.AlignCenter, self.title)

        # 绘制类型指示
        body_rect = rect.adjusted(0, title_height, 0, 0)
        type_text = self.node_type.capitalize()
        painter.setPen(QPen(QColor(text_color).darker(120)))
        type_font = QFont("Arial", 7)
        painter.setFont(type_font)
        type_rect = QRectF(body_rect.left() + 5, body_rect.top() + 2, body_rect.width() - 10, 15)
        painter.drawText(type_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignTop, type_text)

        # 绘制端口
        self._draw_ports(painter)

    def _draw_ports(self, painter: QPainter):
        """绘制端口 - 4个端口（上下左右）"""
        port_radius = 7  # 增大端口半径，使其更容易看到和点击
        port_border_color = QColor(THEME_COLORS["dark_border_primary"])
        port_fill_color = QColor("#FFFFFF")
        highlight_color = QColor(THEME_COLORS["primary"])

        # 端口位置
        ports_positions = {
            "top": QPointF(0, -self.height/2),
            "bottom": QPointF(0, self.height/2),
            "left": QPointF(-self.width/2, 0),
            "right": QPointF(self.width/2, 0)
        }

        # 绘制所有端口 - 只在鼠标悬停时显示
        for port_name, center in ports_positions.items():
            # 如果当前端口是被悬停的端口，使用高亮颜色并有动画效果
            if port_name == self._hovered_port:
                # 计算动画效果的端口尺寸
                animation_scale = 1.0 + (self._port_animation_step / 10.0)  # 动画缩放因子
                animated_radius = port_radius * animation_scale

                # 设置发光效果
                glow = QRadialGradient(center, animated_radius * 2)
                glow_color = QColor(highlight_color)
                glow_color.setAlpha(100)  # 半透明
                glow.setColorAt(0, glow_color)
                glow.setColorAt(1, QColor(0, 0, 0, 0))  # 完全透明
                painter.setBrush(QBrush(glow))
                painter.setPen(Qt.NoPen)
                painter.drawEllipse(center, animated_radius * 1.5, animated_radius * 1.5)

                # 绘制高亮端口
                painter.setPen(QPen(highlight_color, 2.0))
                painter.setBrush(QBrush(highlight_color.lighter(150)))
                painter.drawEllipse(center, animated_radius, animated_radius)

                # 更新动画步骤
                self._port_animation_step += self._port_animation_direction
                if self._port_animation_step >= 5:  # 到达最大放大状态
                    self._port_animation_direction = -1  # 开始缩小
                elif self._port_animation_step <= 0:  # 到达最小状态
                    self._port_animation_direction = 1  # 开始放大

                # 请求重绘以实现动画
                self.update()

    def hoverEnterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.update()

    def hoverMoveEvent(self, event):
        """鼠标在节点上移动事件，用于检测端口悬停"""
        # 检查鼠标是否在某个端口上
        port_info = self.get_port_at_point(event.pos())

        if port_info:
            port_name, _, _ = port_info
            if self._hovered_port != port_name:
                self._hovered_port = port_name
                self.update()  # 更新绘制
                self.setCursor(Qt.PointingHandCursor)  # 更改鼠标样式

                # 开始计时，延迟显示提示
                self._port_tooltip_position = self.mapToScene(event.pos())
                self._port_tooltip_timer.start(600)  # 600毫秒后显示提示
        else:
            if self._hovered_port is not None:
                self._hovered_port = None
                self.update()  # 更新绘制
                self.setCursor(Qt.ArrowCursor)  # 恢复鼠标样式

                # 取消提示显示
                self._port_tooltip_timer.stop()
                self._hide_port_tooltip()

        super().hoverMoveEvent(event)

    def hoverLeaveEvent(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        self._hovered_port = None  # 清除悬停端口
        self._port_animation_step = 0  # 重置动画步骤
        self._port_animation_direction = 1  # 重置动画方向
        self.setCursor(Qt.ArrowCursor)  # 恢复鼠标样式

        # 取消提示显示
        self._port_tooltip_timer.stop()
        self._hide_port_tooltip()

        self.update()
        super().hoverLeaveEvent(event)

    def itemChange(self, change, value):
        """项目变化事件，用于更新连接线"""
        if change == QGraphicsItem.ItemPositionHasChanged and self.scene():
            # 节点位置已改变，更新所有连接到此节点的线
            for item in self.scene().items():
                if isinstance(item, ModernFlowConnection):
                    if item.start_node == self or item.end_node == self:
                        item.update_path()

        if change == QGraphicsItem.ItemSelectedChange:
            self.is_selected = value
            self.update() # 触发重绘以显示选中状态
            # 通知画布节点选择状态改变
            if self.scene() and isinstance(self.scene().views()[0], ModernWorkflowCanvas):
                canvas = self.scene().views()[0]
                if hasattr(canvas, 'node_selected_externally'): # 假设有这样一个方法来处理选择
                    canvas.node_selected_externally(self.node_id, value)

        return super().itemChange(change, value)

    def get_port_pos(self, port_name: str) -> QPointF:
        """获取指定端口的场景坐标"""
        ports_positions = {
            "top": QPointF(0, -self.height/2),
            "bottom": QPointF(0, self.height/2),
            "left": QPointF(-self.width/2, 0),
            "right": QPointF(self.width/2, 0)
        }

        if port_name in ports_positions:
            return self.mapToScene(ports_positions[port_name])

        # 兼容旧代码
        if port_name == "input":
            return self.mapToScene(ports_positions["left"])
        elif port_name == "output":
            return self.mapToScene(ports_positions["right"])

        return self.pos()

    def get_input_port_pos(self, port_index: int = 0) -> QPointF:
        """获取输入端口位置（兼容旧代码）"""
        if port_index == 0:
            return self.get_port_pos("left")
        else:
            return self.get_port_pos("top")

    def get_output_port_pos(self, port_index: int = 0) -> QPointF:
        """获取输出端口位置（兼容旧代码）"""
        if port_index == 0:
            return self.get_port_pos("right")
        else:
            return self.get_port_pos("bottom")

    def get_port_at_point(self, point: QPointF) -> Optional[Tuple[str, int, QPointF]]:
        """检查给定点（节点本地坐标）是否在某个端口上
        Returns: (port_name: str, port_index: int, port_scene_pos: QPointF) or None
        """
        port_radius_check = 20  # 更大的检测半径，方便触控

        # 检查4个端口
        ports_positions = {
            "top": QPointF(0, -self.height/2),
            "bottom": QPointF(0, self.height/2),
            "left": QPointF(-self.width/2, 0),
            "right": QPointF(self.width/2, 0)
        }

        for port_name, port_center in ports_positions.items():
            if QLineF(point, port_center).length() <= port_radius_check:
                return port_name, 0, self.mapToScene(port_center)

        return None

    def mousePressEvent(self, event):
        """处理鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 设置节点被选中
            self.setSelected(True)

            # 保存鼠标相对于节点的位置，用于移动节点
            self.prev_cursor_pos = event.pos()

            # 检查是否点击了端口
            port_info = self.get_port_at_point(event.pos())
            if port_info:
                port_name, port_index, scene_pos = port_info

                # 通知画布开始连线拖拽
                if self.scene() and isinstance(self.scene().views()[0], ModernWorkflowCanvas):
                    canvas = self.scene().views()[0]
                    mouse_scene_pos = self.mapToScene(event.pos())
                    canvas.start_connection_drag(self, port_name, port_index, scene_pos, mouse_scene_pos)
                    event.accept()
                    return

            # 发出信号通知节点被选中
            if self.scene() and isinstance(self.scene().views()[0], ModernWorkflowCanvas):
                canvas = self.scene().views()[0]
                canvas.node_selected.emit(self.node_id)

                # 立即显示当前节点的处理结果（优先级最高）
                parent_editor = canvas.parent()
                if parent_editor and hasattr(parent_editor, 'show_node_result'):
                    logger.info(f"显示节点 {self.node_id} 的处理结果")
                    parent_editor.show_node_result(self.node_id)

                    # 强制刷新确保图像显示
                    if hasattr(parent_editor, 'image_display_manager'):
                        parent_editor.image_display_manager.force_refresh()

            event.accept()
            return

        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 恢复位置变化通知
            self.setFlag(QGraphicsItem.ItemSendsScenePositionChanges, True)

        super().mouseReleaseEvent(event)

    def mouseDoubleClickEvent(self, event):
        """处理鼠标双击事件 - 打开节点配置界面"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 通知场景打开配置界面
            if self.scene() and isinstance(self.scene().views()[0], ModernWorkflowCanvas):
                canvas = self.scene().views()[0]
                canvas.open_node_config_dialog(self)
            event.accept()
            return

        super().mouseDoubleClickEvent(event)

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件，确保节点跟随鼠标移动时保持相对位置"""
        if event.buttons() & Qt.MouseButton.LeftButton and hasattr(self, 'prev_cursor_pos'):
            # 计算鼠标移动的差值
            delta = event.pos() - self.prev_cursor_pos
            self.moveBy(delta.x(), delta.y())
            # 更新连接线
            if self.scene():
                for item in self.scene().items():
                    if isinstance(item, ModernFlowConnection):
                        if item.start_node == self or item.end_node == self:
                            item.update_path()
            event.accept()
            return

        super().mouseMoveEvent(event)

    def _show_port_tooltip(self):
        """显示端口提示"""
        if not self._hovered_port or not self.scene() or not self.scene().views():
            return

        # 获取视图
        view = self.scene().views()[0]
        if not isinstance(view, QGraphicsView):
            return

        # 获取端口位置
        port_position = self.get_port_pos(self._hovered_port)

        # 创建提示标签
        if not self._port_tooltip:
            self._port_tooltip = QLabel(view)
            self._port_tooltip.setStyleSheet("""
                QLabel {
                    background-color: rgba(40, 40, 40, 230);
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-size: 12px;
                    border: 1px solid #555;
                }
            """)
            self._port_tooltip.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 设置提示内容
        port_display_names = {
            "left": "左侧端口",
            "right": "右侧端口",
            "top": "顶部端口",
            "bottom": "底部端口"
        }

        port_descriptions = {
            "left": "用于接收输入连接",
            "right": "用于发送输出连接",
            "top": "多用途连接端口",
            "bottom": "多用途连接端口"
        }

        display_name = port_display_names.get(self._hovered_port, "端口")
        description = port_descriptions.get(self._hovered_port, "可用于连接其他节点")
        tooltip_text = f"<b>{display_name}</b><br>{description}"

        self._port_tooltip.setText(tooltip_text)
        self._port_tooltip.adjustSize()

        # 计算位置
        scene_pos = port_position
        view_pos = view.mapFromScene(scene_pos)

        # 调整位置，确保在视图内
        label_x = view_pos.x() + 15

        # 根据端口位置调整提示框位置
        if self._hovered_port == "left":
            label_x = view_pos.x() - self._port_tooltip.width() - 5
        elif self._hovered_port == "right":
            label_x = view_pos.x() + 5

        label_y = view_pos.y() - self._port_tooltip.height() - 5
        if self._hovered_port == "bottom":
            label_y = view_pos.y() + 5

        self._port_tooltip.move(label_x, label_y)
        self._port_tooltip.show()

    def _hide_port_tooltip(self):
        """隐藏端口提示"""
        if self._port_tooltip:
            self._port_tooltip.hide()


class ModernFlowConnection(QGraphicsPathItem):
    """现代化流程连接线"""

    def __init__(self, start_node: ModernFlowNode, end_node: ModernFlowNode,
                 start_port_name: str = "right", end_port_name: str = "left", parent=None):
        super().__init__(parent)

        self.start_node = start_node
        self.end_node = end_node
        self.start_port_name = start_port_name
        self.end_port_name = end_port_name

        # 设置样式 - 扁平化设计，线条更细更清晰
        line_color = QColor(THEME_COLORS["primary"])
        self._default_pen = QPen(line_color, 2.0, Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin)
        self._selected_pen = QPen(QColor(THEME_COLORS["primary"]).lighter(120), 3.0, Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin)
        self._hover_pen = QPen(QColor(THEME_COLORS["primary"]).lighter(110), 2.5, Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin)

        self.setPen(self._default_pen)
        self.setZValue(-1)  # 在节点下方

        # 设置标志 - 允许选择和悬停
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setAcceptHoverEvents(True)

        # 悬停状态
        self.is_hovered = False

        # 更新路径
        self.update_path()

    def update_path(self):
        """更新连接路径"""
        # 获取起始和结束位置
        start_pos = self.start_node.get_port_pos(self.start_port_name)
        end_pos = self.end_node.get_port_pos(self.end_port_name)

        # 创建贝塞尔曲线路径
        path = QPainterPath()
        path.moveTo(start_pos)

        # 计算控制点
        dx = end_pos.x() - start_pos.x()
        dy = end_pos.y() - start_pos.y()

        # 根据端口方向调整控制点，使曲线更自然
        if self.start_port_name == "right" and self.end_port_name == "left":
            # 标准水平连接
            ctrl1 = QPointF(start_pos.x() + dx * 0.4, start_pos.y())
            ctrl2 = QPointF(end_pos.x() - dx * 0.4, end_pos.y())
        elif self.start_port_name == "bottom" and self.end_port_name == "top":
            # 标准垂直连接
            ctrl1 = QPointF(start_pos.x(), start_pos.y() + dy * 0.4)
            ctrl2 = QPointF(end_pos.x(), end_pos.y() - dy * 0.4)
        else:
            # 混合连接，创建更平滑的曲线
            ctrl1 = QPointF(start_pos.x() + (dx * 0.2), start_pos.y() + (dy * 0.2))
            ctrl2 = QPointF(end_pos.x() - (dx * 0.2), end_pos.y() - (dy * 0.2))

            # 对特殊情况进行调整
            if self.start_port_name == "right" and self.end_port_name == "top":
                ctrl1 = QPointF(start_pos.x() + abs(dx) * 0.5, start_pos.y())
                ctrl2 = QPointF(end_pos.x(), end_pos.y() - abs(dy) * 0.5)
            elif self.start_port_name == "bottom" and self.end_port_name == "left":
                ctrl1 = QPointF(start_pos.x(), start_pos.y() + abs(dy) * 0.5)
                ctrl2 = QPointF(end_pos.x() - abs(dx) * 0.5, end_pos.y())

        path.cubicTo(ctrl1, ctrl2, end_pos)
        self.setPath(path)

    def paint(self, painter, option, widget=None):
        """绘制连接线"""
        painter.setRenderHint(QPainter.Antialiasing)

        if self.isSelected():
            # 选中状态 - 使用更粗更亮的线条
            painter.setPen(self._selected_pen)
        elif self.is_hovered:
            # 悬停状态 - 使用中等粗细的线条
            painter.setPen(self._hover_pen)
        else:
            # 默认状态
            painter.setPen(self._default_pen)

        painter.drawPath(self.path())

        # 在线条两端绘制小圆点，增强视觉效果
        if self.isSelected() or self.is_hovered:
            start_pos = self.path().pointAtPercent(0)
            end_pos = self.path().pointAtPercent(1)

            # 起点圆点
            painter.setBrush(QBrush(self._selected_pen.color() if self.isSelected() else self._hover_pen.color()))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(start_pos, 3, 3)

            # 终点圆点
            painter.drawEllipse(end_pos, 3, 3)

    def hoverEnterEvent(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.update()
        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        self.update()
        super().hoverLeaveEvent(event)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 选中连接线
            self.setSelected(True)
            event.accept()
            return

        super().mousePressEvent(event)

    def shape(self) -> QPainterPath:
        """获取连接线的形状，用于交互检测，增加击中区域的宽度"""
        # 创建一个更宽的路径，便于选择
        stroker = QPainterPathStroker()
        stroker.setWidth(10.0)  # 增加击中区域宽度
        return stroker.createStroke(self.path())


class ModernWorkflowCanvas(QGraphicsView):
    """现代化工作流画布"""

    # 信号
    node_selected = pyqtSignal(str)
    node_deleted = pyqtSignal(str)
    connection_created = pyqtSignal(str, str)
    connection_deleted = pyqtSignal(str, str)  # 新增连接删除信号
    node_config_changed = pyqtSignal(str, dict)  # 节点配置改变信号

    def __init__(self, parent=None):
        """初始化工作流编辑器"""
        super().__init__(parent)

        self.scene = QGraphicsScene()
        self.scene.setSceneRect(-2000, -2000, 4000, 4000)
        self.setScene(self.scene)
        self.nodes: Dict[str, ModernFlowNode] = {}
        self.connections: List[ModernFlowConnection] = []
        self._setup_view()

        # 连接线拖拽相关属性
        self._is_dragging_connection = False
        self._drag_start_node: Optional[ModernFlowNode] = None
        self._drag_start_port_name: Optional[str] = None
        self._drag_start_port_index: Optional[int] = None
        self._drag_start_port_scene_pos: Optional[QPointF] = None
        self._temp_drag_line: Optional[QGraphicsPathItem] = None
        self._connection_hint_label: Optional[QLabel] = None  # 连接提示标签

        # 自动连接相关属性
        self._valid_port_for_auto_connect = False
        self._auto_connect_target = None
        self._auto_connect_port_name = None

        self._middle_mouse_pressed = False
        self._last_middle_mouse_pos = QPointF()
        self._right_mouse_pressed = False
        self._last_right_mouse_pos = QPointF()

        # 当前选择的项目
        self.selected_nodes = []
        self.selected_connections = []

        # 创建连接提示标签
        self._setup_connection_hint()

        # 节点库数据
        self.node_library = {}
        self.node_categories = []

        # 交互状态
        self.is_dragging = False
        self.dragging_node = None
        self.last_pos = QPointF(0, 0)
        self.is_grid_visible = True
        self.grid_size = 20
        self.scale_factor = 1.0
        self.min_scale = 0.1
        self.max_scale = 3.0
        self.camera_offset = QPointF(0, 0)

        # 连接状态
        self.is_connecting = False
        self.connection_start_port = None
        self.connection_start_node = None
        self.connection_end_pos = None
        self.connection_snap_distance = 20

        # 批量选择
        self.is_selecting = False
        self.selection_rect = QRectF()
        self.selection_start = QPointF()

        # 撤销/重做
        from wirevsion.ui.command_system import CommandHistory
        self.command_system = CommandHistory()

        # 渲染优化
        from wirevsion.ui.modern_connection_renderer import ConnectionRenderer
        self.connection_renderer = ConnectionRenderer()

        self.copied_nodes = []

        # 配置节点颜色
        self._setup_node_colors()

        # 设置信号连接
        self._setup_connections()

        # 启用键盘交互
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        logger.info("现代化工作流画布初始化完成")

    def _setup_view(self):
        """设置视图属性"""
        # 渲染设置
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.TextAntialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)

        # 视图设置
        self.setViewportUpdateMode(QGraphicsView.BoundingRectViewportUpdate)
        # 默认不设置拖动模式，让节点可以正常拖动
        self.setDragMode(QGraphicsView.NoDrag)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setFrameShape(QFrame.NoFrame)  # 无边框设计

        # 背景样式 - 确保深色背景
        canvas_bg = THEME_COLORS["dark_bg_app"]
        self.setStyleSheet(f"""
            QGraphicsView {{
                background-color: {canvas_bg};
                border: none;
            }}
            QGraphicsView::viewport {{
                background-color: {canvas_bg};
            }}
        """)

        # 设置场景背景
        self.scene.setBackgroundBrush(QBrush(QColor(canvas_bg)))

        # 绘制网格背景
        self._draw_background_grid()

    def _draw_background_grid(self):
        """绘制网格背景"""
        self.grid_size = 25
        self.grid_color_major = QColor(THEME_COLORS["dark_border_secondary"])
        self.grid_color_minor = QColor(THEME_COLORS["dark_border_secondary"]).lighter(110)
        self.grid_color_minor.setAlpha(100)

    def add_node(self, node_id: str, node_type: str, title: str,
                 pos: QPointF = None) -> ModernFlowNode:
        """添加节点"""
        if pos is None:
            # 在画布中心附近随机放置节点
            x = random.randint(-200, 200)
            y = random.randint(-200, 200)
            pos = QPointF(x, y)

        node = ModernFlowNode(node_id, node_type, title, pos)
        self.scene.addItem(node)
        self.nodes[node_id] = node

        logger.debug(f"添加节点到画布: {title} at {pos}")
        return node

    def remove_node(self, node_id: str):
        """移除节点"""
        if node_id in self.nodes:
            node = self.nodes[node_id]

            # 移除相关连接
            connections_to_remove = []
            for conn in self.connections:
                if conn.start_node == node or conn.end_node == node:
                    connections_to_remove.append(conn)

            for conn in connections_to_remove:
                self.scene.removeItem(conn)
                self.connections.remove(conn)

            # 移除节点
            self.scene.removeItem(node)
            del self.nodes[node_id]

            logger.debug(f"从画布移除节点: {node_id}")

    def create_connection(self, start_node_id: str, end_node_id: str,
                         start_port_name: str = "right", end_port_name: str = "left"):
        """创建连接"""
        if start_node_id in self.nodes and end_node_id in self.nodes:
            start_node = self.nodes[start_node_id]
            end_node = self.nodes[end_node_id]

            connection = ModernFlowConnection(start_node, end_node, start_port_name, end_port_name)
            self.scene.addItem(connection)
            self.connections.append(connection)

            # 添加连接创建动画效果
            self._animate_new_connection(connection)

            self.connection_created.emit(start_node_id, end_node_id)
            logger.debug(f"创建连接: {start_node_id}:{start_port_name} -> {end_node_id}:{end_port_name}")

    def _animate_new_connection(self, connection: ModernFlowConnection):
        """为新创建的连接添加动画效果"""
        # 保存原始颜色和宽度
        original_pen = connection.pen()
        original_color = original_pen.color()
        original_width = original_pen.width()

        # 动画持续时间和步骤
        animation_duration = 700  # 毫秒
        steps = 10  # 动画步骤数
        step_duration = animation_duration / steps

        # 创建脉冲效果 - 高亮颜色
        highlight_color = QColor(THEME_COLORS["primary"])
        highlight_color.setRed(min(255, highlight_color.red() + 60))  # 更亮的蓝色
        highlight_color.setBlue(min(255, highlight_color.blue() + 60))
        highlight_pen = QPen(highlight_color, original_width * 2.0, Qt.SolidLine, Qt.RoundCap)
        connection.setPen(highlight_pen)

        # 进度值，从0到1
        progress = [0.0]

        # 动画回调函数
        def animation_step():
            # 增加进度
            progress[0] += 1.0 / steps

            if progress[0] >= 1.0:
                # 动画结束，恢复原始状态
                connection.setPen(original_pen)
                self.animation_timer.stop()
                return

            # 计算当前状态 - 使用正弦函数产生脉冲效果
            pulse_factor = abs(math.sin(progress[0] * math.pi * 2))  # 0->1->0循环

            # 在原始宽度和高亮宽度之间插值
            current_width = original_width + (original_width * pulse_factor)

            # 颜色渐变 - 从高亮色到原始色
            lerp_factor = progress[0]  # 线性过渡因子
            current_color = QColor(
                int(highlight_color.red() * (1.0 - lerp_factor) + original_color.red() * lerp_factor),
                int(highlight_color.green() * (1.0 - lerp_factor) + original_color.green() * lerp_factor),
                int(highlight_color.blue() * (1.0 - lerp_factor) + original_color.blue() * lerp_factor),
                255
            )

            # 设置当前状态
            current_pen = QPen(current_color, current_width, Qt.SolidLine, Qt.RoundCap)
            connection.setPen(current_pen)

        # 创建定时器进行动画
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(animation_step)
        self.animation_timer.start(int(step_duration))  # 更新间隔

    def wheelEvent(self, event: QWheelEvent):
        """滚轮事件 - 缩放"""
        # 缩放因子
        scale_factor = 1.15

        if event.angleDelta().y() > 0:
            # 放大
            self.scale(scale_factor, scale_factor)
        else:
            # 缩小
            self.scale(1.0 / scale_factor, 1.0 / scale_factor)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 首先重置拖动模式，确保每次点击都从干净状态开始
            self.setDragMode(QGraphicsView.NoDrag)

            # 检查是否点击在节点上
            item = self.itemAt(event.pos())
            if isinstance(item, ModernFlowNode):
                # 如果点击在节点上，让场景处理事件（这样节点可以处理端口点击）
                super().mousePressEvent(event)
            elif not self._is_dragging_connection:
                # 如果点击在空白区域，开始框选
                self.setDragMode(QGraphicsView.RubberBandDrag)
                super().mousePressEvent(event)

        elif event.button() == Qt.MouseButton.MiddleButton:
            # 中键平移
            self.setDragMode(QGraphicsView.NoDrag)
            self._middle_mouse_pressed = True
            self._last_middle_mouse_pos = event.pos()
            self.viewport().setCursor(Qt.ClosedHandCursor)
            event.accept()

        elif event.button() == Qt.MouseButton.RightButton:
            # 右键也可以用于平移
            self.setDragMode(QGraphicsView.NoDrag)
            self._right_mouse_pressed = True
            self._last_right_mouse_pos = event.pos()
            self.viewport().setCursor(Qt.ClosedHandCursor)
            event.accept()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self._is_dragging_connection:
            scene_pos = self.mapToScene(event.pos())
            self._update_drag_line_path(scene_pos)
            event.accept()
            return

        if self._middle_mouse_pressed and event.buttons() & Qt.MouseButton.MiddleButton:
            # 中键平移
            delta = event.pos() - self._last_middle_mouse_pos
            hsbar = self.horizontalScrollBar()
            vsbar = self.verticalScrollBar()
            hsbar.setValue(hsbar.value() - delta.x())
            vsbar.setValue(vsbar.value() - delta.y())
            self._last_middle_mouse_pos = event.pos()
            event.accept()
            return

        if hasattr(self, '_right_mouse_pressed') and self._right_mouse_pressed and event.buttons() & Qt.MouseButton.RightButton:
            # 右键平移
            delta = event.pos() - self._last_right_mouse_pos
            hsbar = self.horizontalScrollBar()
            vsbar = self.verticalScrollBar()
            hsbar.setValue(hsbar.value() - delta.x())
            vsbar.setValue(vsbar.value() - delta.y())
            self._last_right_mouse_pos = event.pos()
            event.accept()
            return

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if self._is_dragging_connection and event.button() == Qt.MouseButton.LeftButton:
            scene_pos = self.mapToScene(event.pos())

            # 检查是否可以自动连接
            if hasattr(self, '_valid_port_for_auto_connect') and self._valid_port_for_auto_connect:
                # 使用找到的目标端口自动完成连接
                if self._auto_connect_target and self._auto_connect_port_name:
                    self._finish_connection_with_target(self._auto_connect_target, self._auto_connect_port_name)
                    event.accept()
                    return

            # 如果没有自动连接，正常处理
            self._finish_connection_drag(scene_pos)
            event.accept()
            return

        if event.button() == Qt.MouseButton.MiddleButton:
            self._middle_mouse_pressed = False
            self.viewport().setCursor(Qt.ArrowCursor)
            self.setDragMode(QGraphicsView.NoDrag)
            event.accept()

        if event.button() == Qt.MouseButton.RightButton and hasattr(self, '_right_mouse_pressed'):
            self._right_mouse_pressed = False
            self.viewport().setCursor(Qt.ArrowCursor)
            self.setDragMode(QGraphicsView.NoDrag)
            event.accept()

        if event.button() == Qt.MouseButton.LeftButton:
            # 左键释放时重置拖动模式
            self.setDragMode(QGraphicsView.NoDrag)

        if not event.isAccepted():
            super().mouseReleaseEvent(event)

    def drawBackground(self, painter: QPainter, rect: QRectF):
        super().drawBackground(painter, rect)
        left = int(rect.left()) - (int(rect.left()) % self.grid_size)
        top = int(rect.top()) - (int(rect.top()) % self.grid_size)
        # 绘制次网格线
        painter.setPen(QPen(self.grid_color_minor, 0.5))
        for x in range(left, int(rect.right()), self.grid_size):
            painter.drawLine(x, int(rect.top()), x, int(rect.bottom()))
        for y in range(top, int(rect.bottom()), self.grid_size):
            painter.drawLine(int(rect.left()), y, int(rect.right()), y)
        # 绘制主网格线 (例如每4条次网格线)
        major_grid_size = self.grid_size * 4
        left_major = int(rect.left()) - (int(rect.left()) % major_grid_size)
        top_major = int(rect.top()) - (int(rect.top()) % major_grid_size)
        painter.setPen(QPen(self.grid_color_major, 0.8))
        for x in range(left_major, int(rect.right()), major_grid_size):
            painter.drawLine(x, int(rect.top()), x, int(rect.bottom()))
        for y in range(top_major, int(rect.bottom()), major_grid_size):
            painter.drawLine(int(rect.left()), y, int(rect.right()), y)

    def start_connection_drag(self, node: ModernFlowNode, port_name: str, port_index: int,
                              port_scene_pos: QPointF, mouse_scene_pos: QPointF):
        """由ModernFlowNode调用，开始拖拽连接线"""
        if self._is_dragging_connection: # 防止重入
            return

        self._is_dragging_connection = True
        self._drag_start_node = node
        self._drag_start_port_name = port_name  # 记录端口名称
        self._drag_start_port_index = port_index
        self._drag_start_port_scene_pos = port_scene_pos

        self._temp_drag_line = QGraphicsPathItem()
        pen_color = QColor(THEME_COLORS["primary"])
        pen_color.setAlpha(180) # 半透明

        # 使用更粗、更明显的虚线，并添加动画效果
        pen = QPen(pen_color, 3.0, Qt.DashLine)
        pen.setDashPattern([6, 3])  # 设置虚线模式
        self._temp_drag_line.setPen(pen)
        self._temp_drag_line.setZValue(1) # 确保临时线在所有其他项上方
        self.scene.addItem(self._temp_drag_line)

        self._update_drag_line_path(mouse_scene_pos)
        logger.debug(f"开始从节点 {node.node_id} 的 {port_name} 端口拖拽连接线")

    def _update_drag_line_path(self, current_mouse_scene_pos: QPointF):
        """更新临时连接线路径"""
        if not self._is_dragging_connection or not self._temp_drag_line or not self._drag_start_port_scene_pos:
            return

        path = QPainterPath(self._drag_start_port_scene_pos)

        start_pos = self._drag_start_port_scene_pos
        end_pos = current_mouse_scene_pos

        # 检查鼠标下方是否有节点和端口
        item_at_pos = self.itemAt(self.mapFromScene(current_mouse_scene_pos))

        # 目标节点和端口信息
        target_node_name = "无"
        target_port_name = "无"
        found_valid_port = False
        target_port_pos = None

        # 重置所有节点的端口高亮状态
        for node_id, node in self.nodes.items():
            if node != self._drag_start_node:  # 跳过起始节点
                node._hovered_port = None

        # 吸附距离 - 当鼠标靠近端口这个距离内时会自动吸附
        snap_distance = 30

        # 检查是否有可吸附的端口
        if isinstance(item_at_pos, ModernFlowNode) and item_at_pos != self._drag_start_node:
            target_node = item_at_pos

            # 检查所有端口
            for port_name in ["left", "right", "top", "bottom"]:
                port_scene_pos = target_node.get_port_pos(port_name)
                distance = QLineF(current_mouse_scene_pos, port_scene_pos).length()

                # 如果鼠标在吸附距离内，高亮显示端口并吸附连接线
                if distance <= snap_distance:
                    target_node._hovered_port = port_name
                    target_node.update()

                    # 使用端口位置作为终点，实现吸附效果
                    end_pos = port_scene_pos
                    target_port_pos = port_scene_pos
                    target_node_name = target_node.title
                    target_port_name = port_name
                    found_valid_port = True
                    break

        # 贝塞尔曲线预览，根据起始端口的方向调整控制点
        dx = end_pos.x() - start_pos.x()
        dy = end_pos.y() - start_pos.y()

        # 根据起始端口方向调整控制点
        if self._drag_start_port_name in ["left", "right"]:
            # 水平端口
            ctrl1 = QPointF(start_pos.x() + dx * 0.5, start_pos.y())
            ctrl2 = QPointF(end_pos.x() - dx * 0.5, end_pos.y())
        else:
            # 垂直端口
            ctrl1 = QPointF(start_pos.x(), start_pos.y() + dy * 0.5)
            ctrl2 = QPointF(end_pos.x(), end_pos.y() - dy * 0.5)

        path.cubicTo(ctrl1, ctrl2, end_pos)

        # 如果找到有效端口，添加吸附点效果
        if found_valid_port and target_port_pos:
            # 设置线条样式 - 找到可连接端口时使用实线
            pen_color = QColor(THEME_COLORS["primary"])
            pen = QPen(pen_color, 3.0, Qt.SolidLine)  # 使用实线表示可连接
            self._temp_drag_line.setPen(pen)

            # 在端口位置添加一个圆形表示吸附点
            snap_indicator = QPainterPath()
            snap_indicator.addEllipse(target_port_pos, 8, 8)
            path.addPath(snap_indicator)
        else:
            # 没有找到可连接端口时使用虚线
            pen_color = QColor(THEME_COLORS["primary"])
            pen_color.setAlpha(180)
            pen = QPen(pen_color, 3.0, Qt.DashLine)
            pen.setDashPattern([6, 3])
            self._temp_drag_line.setPen(pen)

        self._temp_drag_line.setPath(path)

        # 更新连接提示文本
        source_node_name = self._drag_start_node.title if self._drag_start_node else "未知"
        source_port_display = self._get_port_display_name(self._drag_start_port_name)
        target_port_display = self._get_port_display_name(target_port_name) if target_port_name != "无" else "无"

        # 根据是否找到有效端口调整提示文本
        if found_valid_port:
            hint_text = f"连接: {source_node_name} ({source_port_display}) → {target_node_name} ({target_port_display}) [可连接]"
            self._connection_hint_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(0, 120, 0, 180);
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)
        else:
            hint_text = f"连接: {source_node_name} ({source_port_display}) → {target_node_name} ({target_port_display})"
            self._connection_hint_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(40, 40, 40, 180);
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
            """)

        self._connection_hint_label.setText(hint_text)

        # 调整标签位置和大小
        self._connection_hint_label.adjustSize()
        cursor_pos = self.mapFromScene(current_mouse_scene_pos)
        label_x = cursor_pos.x() + 15
        label_y = cursor_pos.y() - self._connection_hint_label.height() - 15
        self._connection_hint_label.move(label_x, label_y)
        self._connection_hint_label.show()

        # 如果找到有效端口并且鼠标没有移动，可以自动完成连接
        self._valid_port_for_auto_connect = found_valid_port
        if found_valid_port:
            self._auto_connect_target = item_at_pos
            self._auto_connect_port_name = target_port_name
        else:
            self._auto_connect_target = None
            self._auto_connect_port_name = None

    def _get_port_display_name(self, port_name):
        """获取端口的显示名称"""
        port_display = {
            "left": "左",
            "right": "右",
            "top": "上",
            "bottom": "下"
        }
        return port_display.get(port_name, "未知")

    def _finish_connection_drag(self, mouse_scene_pos: QPointF):
        """完成连接线拖拽，创建实际连接"""
        if not self._is_dragging_connection or not self._drag_start_node:
            return

        # 清理临时线
        if self._temp_drag_line:
            self.scene.removeItem(self._temp_drag_line)
            self._temp_drag_line = None

        # 隐藏连接提示标签
        if self._connection_hint_label:
            self._connection_hint_label.hide()

        # 查找目标节点和端口
        item_at_release = self.itemAt(self.mapFromScene(mouse_scene_pos))
        target_node: Optional[ModernFlowNode] = None
        target_port_name: Optional[str] = None
        target_port_index: Optional[int] = None

        if isinstance(item_at_release, ModernFlowNode):
            target_node = item_at_release
            # 将鼠标位置转换为目标节点的本地坐标以检测端口
            port_info = target_node.get_port_at_point(target_node.mapFromScene(mouse_scene_pos))
            if port_info:
                target_port_name, target_port_index, _ = port_info

        # 如果找到了目标节点和端口，创建连接
        if target_node and target_node != self._drag_start_node and target_port_name is not None:
            # 创建连接（不再检查端口类型，因为所有端口都是通用的）
            self.create_connection(self._drag_start_node.node_id, target_node.node_id,
                                   self._drag_start_port_name, target_port_name)
            logger.debug(f"创建连接: {self._drag_start_node.node_id} -> {target_node.node_id}")
        else:
            logger.debug("未在有效端口上释放或目标是起始节点")

        # 重置所有节点的端口高亮状态
        for node_id, node in self.nodes.items():
            if node._hovered_port is not None:
                node._hovered_port = None
                node.update()

        # 重置拖拽状态
        self._is_dragging_connection = False
        self._drag_start_node = None
        self._drag_start_port_name = None
        self._drag_start_port_index = None
        self._drag_start_port_scene_pos = None

    def _on_node_selected(self, node_id: str):
        """节点选中事件"""
        logger.debug(f"节点选中: {node_id}")
        # TODO: 更新属性面板

    def _on_node_deleted(self, node_id: str):
        """节点删除事件"""
        logger.debug(f"节点删除: {node_id}")

    def _on_connection_created(self, start_id: str, end_id: str):
        """连接创建事件"""
        logger.debug(f"连接创建: {start_id} -> {end_id}")

    # 工具栏事件
    def _on_new_workflow(self):
        """新建工作流"""
        logger.info("新建工作流")

    def _on_open_workflow(self):
        """打开工作流"""
        logger.info("打开工作流")

    def _on_save_workflow(self):
        """保存工作流"""
        logger.info("保存工作流")

    def _on_run_workflow(self):
        """运行工作流"""
        logger.info("运行工作流")

    def _on_stop_workflow(self):
        """停止工作流"""
        logger.info("停止工作流")

    def _on_zoom_in(self):
        """放大"""
        self.scale(1.2, 1.2)

    def _on_zoom_out(self):
        """缩小"""
        self.scale(0.8, 0.8)

    def _on_zoom_fit(self):
        """适应窗口"""
        self.fitInView(self.scene.itemsBoundingRect(), Qt.KeepAspectRatio)

    def open_node_config_dialog(self, node: ModernFlowNode):
        """打开现代化节点配置对话框

        Args:
            node: 要配置的节点
        """
        logger.debug(f"打开现代化节点配置对话框: {node.node_id} ({node.node_type})")

        try:
            # 创建现代化配置对话框
            from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog

            # 获取主窗口
            parent_widget = self.window()
            logger.info(f"找到主窗口: {parent_widget.objectName()}")

            # 创建对话框
            dialog = ModernNodeConfigDialog(node, parent_widget)

            # 连接配置改变信号
            dialog.config_changed.connect(lambda config: self._on_node_config_changed(node.node_id, config))

            # 显示对话框
            result = dialog.exec_()

            if result == dialog.Accepted:
                logger.info(f"节点配置已保存: {node.node_id}")
            else:
                logger.info(f"节点配置已取消: {node.node_id}")

        except Exception as e:
            logger.error(f"打开节点配置对话框失败: {e}")
            # 回退到旧的对话框
            from wirevsion.ui.node_config_dialog import NodeConfigDialog
            dialog = NodeConfigDialog(node, parent_widget)
            dialog.exec_()

    def _on_node_config_changed(self, node_id: str, config: Dict[str, Any]):
        """节点配置改变事件处理"""
        try:
            # 保存节点配置
            if node_id in self.nodes:
                node = self.nodes[node_id]

                # 将配置保存到节点
                if not hasattr(node, 'config'):
                    node.config = {}

                node.config.update(config)

                logger.info(f"节点配置已更新: {node_id}")
                logger.debug(f"配置内容: {config}")

                # 如果有算法配置，可以在这里触发算法重新初始化
                if config.get("algorithm"):
                    self._update_node_algorithm(node_id, config)

                # 如果有ROI配置，可以在这里更新ROI显示
                if config.get("roi_regions"):
                    self._update_node_roi(node_id, config["roi_regions"])

        except Exception as e:
            logger.error(f"处理节点配置改变失败: {e}")

    def _update_node_algorithm(self, node_id: str, config: Dict[str, Any]):
        """更新节点算法配置"""
        try:
            algorithm_name = config.get("algorithm")
            parameters = config.get("parameters", {})

            if algorithm_name:
                logger.info(f"节点 {node_id} 算法已更新为: {algorithm_name}")
                logger.debug(f"算法参数: {parameters}")

                # 这里可以添加算法实例化和配置的逻辑
                # 例如：创建算法实例，设置参数等

        except Exception as e:
            logger.error(f"更新节点算法失败: {e}")

    def _update_node_roi(self, node_id: str, roi_regions: List[Dict[str, Any]]):
        """更新节点ROI配置"""
        try:
            logger.info(f"节点 {node_id} ROI已更新，共 {len(roi_regions)} 个区域")

            for i, roi in enumerate(roi_regions):
                logger.debug(f"ROI {i}: {roi}")

            # 这里可以添加ROI显示更新的逻辑
            # 例如：在图像上绘制ROI区域等

        except Exception as e:
            logger.error(f"更新节点ROI失败: {e}")

    def _setup_connection_hint(self):
        """设置连接提示标签"""
        self._connection_hint_label = QLabel(self)
        self._connection_hint_label.setStyleSheet("""
            QLabel {
                background-color: rgba(40, 40, 40, 180);
                color: white;
                border-radius: 5px;
                padding: 5px 10px;
                font-size: 12px;
            }
        """)
        self._connection_hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self._connection_hint_label.hide()

    def _finish_connection_with_target(self, target_node: ModernFlowNode, target_port_name: str):
        """使用指定的目标节点和端口完成连接"""
        if not self._is_dragging_connection or not self._drag_start_node:
            return

        # 清理临时线
        if self._temp_drag_line:
            self.scene.removeItem(self._temp_drag_line)
            self._temp_drag_line = None

        # 隐藏连接提示标签
        if self._connection_hint_label:
            self._connection_hint_label.hide()

        # 创建连接
        self.create_connection(self._drag_start_node.node_id, target_node.node_id,
                               self._drag_start_port_name, target_port_name)
        logger.debug(f"自动连接: {self._drag_start_node.node_id} -> {target_node.node_id}")

        # 重置所有节点的端口高亮状态
        for node_id, node in self.nodes.items():
            if node._hovered_port is not None:
                node._hovered_port = None
                node.update()

        # 重置拖拽状态
        self._is_dragging_connection = False
        self._drag_start_node = None
        self._drag_start_port_name = None
        self._drag_start_port_index = None
        self._drag_start_port_scene_pos = None
        self._valid_port_for_auto_connect = False
        self._auto_connect_target = None
        self._auto_connect_port_name = None

    def _setup_node_colors(self):
        """设置节点颜色"""
        from wirevsion.ui.modern_components import THEME_COLORS

        # 更新节点类型颜色映射，使用现代化配色
        self.node_colors = {
            "input": {
                "background": THEME_COLORS["success"],  # 使用成功色作为输入节点
                "border": QColor(THEME_COLORS["success"]).darker(120).name(),
                "title": "#ffffff",
                "text": "#ffffff"
            },
            "process": {
                "background": THEME_COLORS["warning"],  # 使用警告色作为处理节点
                "border": QColor(THEME_COLORS["warning"]).darker(120).name(),
                "title": THEME_COLORS["text_on_warning_bg"],
                "text": THEME_COLORS["text_on_warning_bg"]
            },
            "output": {
                "background": THEME_COLORS["primary"],  # 使用主色作为输出节点
                "border": QColor(THEME_COLORS["primary"]).darker(120).name(),
                "title": "#ffffff",
                "text": "#ffffff"
            },
            "conditional": {
                "background": "#673ab7",  # 紫色
                "border": "#512da8",
                "title": "#ffffff",
                "text": "#ffffff"
            },
            "data": {
                "background": THEME_COLORS["info"],  # 使用信息色作为数据节点
                "border": QColor(THEME_COLORS["info"]).darker(120).name(),
                "title": "#ffffff",
                "text": "#ffffff"
            }
        }

        # 连接线颜色
        self.connection_color = QColor(THEME_COLORS["primary"])

    def _setup_connections(self):
        """设置信号连接"""
        # 连接信号到槽
        self.node_selected.connect(self._on_node_selected)
        self.node_deleted.connect(self._on_node_deleted)
        self.connection_created.connect(self._on_connection_created)

        logger.debug("工作流画布信号连接完成")

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_Delete or event.key() == Qt.Key_Backspace:
            self._delete_selected_items()
            event.accept()
            return
        elif event.key() == Qt.Key_Escape:
            # 取消所有选择
            self._clear_selection()
            event.accept()
            return

        super().keyPressEvent(event)

    def _delete_selected_items(self):
        """删除选中的节点和连接线"""
        # 获取当前选中的项目
        selected_items = self.scene.selectedItems()

        # 删除选中的连接线
        connections_to_remove = []
        for item in selected_items:
            if isinstance(item, ModernFlowConnection):
                connections_to_remove.append(item)

        for conn in connections_to_remove:
            # 发送连接删除信号
            start_node_id = conn.start_node.node_id if conn.start_node else "unknown"
            end_node_id = conn.end_node.node_id if conn.end_node else "unknown"
            self.connection_deleted.emit(start_node_id, end_node_id)

            # 从场景和列表中移除
            self.scene.removeItem(conn)
            if conn in self.connections:
                self.connections.remove(conn)

            logger.debug(f"删除连接: {start_node_id} -> {end_node_id}")

        # 删除选中的节点
        nodes_to_remove = []
        for item in selected_items:
            if isinstance(item, ModernFlowNode):
                nodes_to_remove.append(item)

        for node in nodes_to_remove:
            # 删除连接到此节点的所有连接
            node_connections = []
            for conn in self.connections:
                if conn.start_node == node or conn.end_node == node:
                    node_connections.append(conn)

            for conn in node_connections:
                start_node_id = conn.start_node.node_id if conn.start_node else "unknown"
                end_node_id = conn.end_node.node_id if conn.end_node else "unknown"
                self.connection_deleted.emit(start_node_id, end_node_id)

                self.scene.removeItem(conn)
                if conn in self.connections:
                    self.connections.remove(conn)

            # 发送节点删除信号
            self.node_deleted.emit(node.node_id)

            # 从场景和字典中移除
            self.scene.removeItem(node)
            if node.node_id in self.nodes:
                del self.nodes[node.node_id]

            logger.debug(f"删除节点: {node.node_id}")

    def _clear_selection(self):
        """清除所有选择"""
        for item in self.scene.selectedItems():
            item.setSelected(False)

        self.selected_nodes = []
        self.selected_connections = []

    def _on_node_selected(self, node_id: str):
        """节点选中事件"""
        logger.debug(f"节点选中: {node_id}")
        # TODO: 更新属性面板


class ModernNodeLibrary(QListWidget):
    """节点库，用于拖拽节点到画布"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setDefaultDropAction(Qt.CopyAction)
        self.setMinimumWidth(150)
        self.setMaximumWidth(250)
        self._populate_nodes()
        self.setStyleSheet(f"""
            QListWidget {{
                background-color: {THEME_COLORS["dark_bg_sidebar"]};
                color: {THEME_COLORS["text_primary"]};
                border: none;
                padding: 8px;
                border-radius: 0px;
                outline: none;
            }}
            QListWidget::item {{
                padding: 10px 15px;
                border-radius: 6px;
                margin: 3px 0px;
            }}
            QListWidget::item:hover {{
                background-color: {THEME_COLORS["dark_surface_hover"]};
            }}
            QListWidget::item:selected {{
                background-color: {THEME_COLORS["dark_surface_selected"]};
                color: {THEME_COLORS["text_on_dark_selected_bg"]};
            }}
            QListWidget::item:disabled {{
                color: {THEME_COLORS["text_secondary"]};
                background-color: transparent;
                font-weight: bold;
                padding-top: 15px;
                padding-bottom: 5px;
            }}
        """)
        logger.debug("节点库初始化")

    def _populate_nodes(self):
        """填充预定义的节点类型"""
        default_nodes = [
            # 输入输出节点
            {"title": "相机输入", "node_type": "input", "id_prefix": "camera_input", "category": "输入"},
            {"title": "图像输入", "node_type": "input", "id_prefix": "image_input", "category": "输入"},
            {"title": "视频输入", "node_type": "input", "id_prefix": "video_input", "category": "输入"},
            {"title": "结果输出", "node_type": "output", "id_prefix": "result_output", "category": "输出"},
            {"title": "图像输出", "node_type": "output", "id_prefix": "image_output", "category": "输出"},

            # 图像处理算法
            {"title": "高斯模糊", "node_type": "process", "id_prefix": "gaussian_blur", "category": "滤波"},
            {"title": "中值滤波", "node_type": "process", "id_prefix": "median_filter", "category": "滤波"},
            {"title": "双边滤波", "node_type": "process", "id_prefix": "bilateral_filter", "category": "滤波"},

            # 边缘检测
            {"title": "Canny边缘", "node_type": "process", "id_prefix": "canny_edge", "category": "边缘检测"},
            {"title": "Sobel边缘", "node_type": "process", "id_prefix": "sobel_edge", "category": "边缘检测"},
            {"title": "Laplacian边缘", "node_type": "process", "id_prefix": "laplacian_edge", "category": "边缘检测"},

            # 形态学操作
            {"title": "腐蚀", "node_type": "process", "id_prefix": "erosion", "category": "形态学"},
            {"title": "膨胀", "node_type": "process", "id_prefix": "dilation", "category": "形态学"},
            {"title": "开运算", "node_type": "process", "id_prefix": "opening", "category": "形态学"},
            {"title": "闭运算", "node_type": "process", "id_prefix": "closing", "category": "形态学"},

            # 特征检测
            {"title": "轮廓检测", "node_type": "process", "id_prefix": "contour_detect", "category": "特征检测"},
            {"title": "角点检测", "node_type": "process", "id_prefix": "corner_detect", "category": "特征检测"},
            {"title": "模板匹配", "node_type": "process", "id_prefix": "template_match", "category": "特征检测"},
            {"title": "颜色检测", "node_type": "process", "id_prefix": "color_detect", "category": "特征检测"},

            # 变换操作
            {"title": "缩放", "node_type": "process", "id_prefix": "resize", "category": "变换"},
            {"title": "旋转", "node_type": "process", "id_prefix": "rotate", "category": "变换"},
            {"title": "透视变换", "node_type": "process", "id_prefix": "perspective", "category": "变换"},

            # 深度学习
            {"title": "YOLO检测", "node_type": "process", "id_prefix": "yolo_detect", "category": "深度学习"},
            {"title": "图像分类", "node_type": "process", "id_prefix": "classify", "category": "深度学习"},
            {"title": "语义分割", "node_type": "process", "id_prefix": "segmentation", "category": "深度学习"},
        ]

        # 按类别分组
        categories = {}
        for node_data in default_nodes:
            category = node_data.get("category", "其他")
            if category not in categories:
                categories[category] = []
            categories[category].append(node_data)

        # 添加到列表中
        for category, nodes in categories.items():
            # 添加分类标题
            category_item = QListWidgetItem(f"━━━ {category} ━━━")
            category_item.setFlags(Qt.NoItemFlags)  # 不可选择
            category_item.setData(Qt.UserRole, None)  # 无数据
            self.addItem(category_item)

            # 添加该分类下的节点
            for node_data in nodes:
                item = QListWidgetItem(f"  {node_data['title']}")
                item.setData(Qt.UserRole, node_data)
                self.addItem(item)

        logger.debug(f"节点库填充了 {len(default_nodes)} 个节点类型")

    def startDrag(self, supportedActions):
        """开始拖拽操作"""
        item = self.currentItem()
        if item:
            node_data = item.data(Qt.UserRole)
            # 如果是分类标题，不允许拖拽
            if node_data is None:
                return

            mime_data = QMimeData()
            # 将节点数据序列化为JSON字符串存储
            mime_data.setText(json.dumps(node_data))

            drag = QDrag(self)
            drag.setMimeData(mime_data)

            # 创建节点外观的预览图像
            pixmap = QPixmap(120, 50)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # 绘制节点预览
            rect = QRectF(0, 0, 120, 50)
            path = QPainterPath()
            path.addRoundedRect(rect, 5, 5)

            # 根据节点类型选择颜色
            node_colors = {
                "input": THEME_COLORS["success"],
                "output": THEME_COLORS["primary"],
                "process": THEME_COLORS["warning"]
            }
            node_color = node_colors.get(node_data.get("node_type", "process"), THEME_COLORS["secondary"])
            painter.fillPath(path, QColor(node_color))

            # 绘制文本
            painter.setPen(QPen(QColor(THEME_COLORS["text_on_warning_bg"] if node_data.get("node_type") == "process" else THEME_COLORS["text_on_primary_bg"])))
            painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, node_data["title"])
            painter.end()

            drag.setPixmap(pixmap)
            drag.setHotSpot(QPoint(pixmap.width()//2, pixmap.height()//2))

            logger.debug(f"开始拖拽节点: {node_data['title']}")
            drag.exec_(Qt.CopyAction)


class ModernWorkflowEditor(QWidget):
    """现代化工作流编辑器主控件"""

    def __init__(self, parent=None):
        """初始化工作流编辑器"""
        super().__init__(parent)
        self.setObjectName("ModernWorkflowEditor")  # 设置对象名称，方便查找

        # 添加结果管理系统
        self._node_results = {}  # 存储各节点的处理结果
        self._visible_nodes = set()  # 当前可见节点集合
        self._overlay_mode = False  # 是否开启叠加模式
        self._current_display_node = None  # 当前显示的节点ID
        self._force_camera_refresh = False  # 强制刷新相机标志

        # 初始化定时器和状态变量
        self.continuous_run_timer = None  # 持续执行定时器ID
        self.continuous_run_active = False  # 持续执行状态
        self._continuous_count = 0  # 持续执行计数
        self._last_execution_time = 0  # 上次执行时间

        # 初始化相机管理器（使用单例模式）
        self.camera_manager = CameraManager.get_instance()
        # 强制释放可能被其他组件占用的相机资源
        self.camera_manager.force_release_camera()

        # 尝试初始化相机以便实时显示
        self._init_camera_for_realtime_display()

        # 初始化UI
        self._setup_ui()
        logger.info("现代化工作流编辑器初始化完成")

        # 初始化完成后，确保图像视图可见
        QTimer.singleShot(100, self._ensure_image_view_visible)
        # 同时确保滚动区域可见
        QTimer.singleShot(150, self._ensure_scrollarea_visible)

    def _init_camera_for_realtime_display(self):
        """初始化相机以便实时显示"""
        try:
            logger.info("尝试初始化相机以便实时显示")

            # 尝试初始化相机
            if self.camera_manager.init_camera():
                logger.info("相机初始化成功，可以进行实时显示")

                # 启动一个定时器来定期更新实时画面
                self._start_realtime_display_timer()

                # 立即获取一帧图像并显示
                QTimer.singleShot(500, self._show_initial_camera_frame)
            else:
                logger.warning("相机初始化失败，实时显示将不可用")

        except Exception as e:
            logger.error(f"初始化相机时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _show_initial_camera_frame(self):
        """显示初始相机帧"""
        try:
            logger.info("尝试显示初始相机帧")

            # 获取相机帧
            success, frame = self.camera_manager.get_frame()

            if success and frame is not None:
                # 转换BGR到RGB
                import cv2
                import numpy as np

                if len(frame.shape) == 3 and frame.shape[2] == 3:
                    # 确保数据是连续的
                    if not frame.flags['C_CONTIGUOUS']:
                        frame = np.ascontiguousarray(frame)

                    # BGR转RGB
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                    # 更新图像显示
                    self.image_display_manager.update_display(rgb_frame)
                    logger.info("成功显示初始相机帧")
                else:
                    logger.warning("相机帧格式不正确")
            else:
                logger.warning("无法获取初始相机帧")

        except Exception as e:
            logger.error(f"显示初始相机帧时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _start_realtime_display_timer(self):
        """启动实时显示定时器"""
        try:
            # 创建定时器用于实时显示
            if not hasattr(self, '_realtime_timer'):
                self._realtime_timer = QTimer(self)
                self._realtime_timer.timeout.connect(self._update_realtime_display)

            # 每500毫秒更新一次实时画面（2 FPS）
            self._realtime_timer.start(500)
            logger.info("实时显示定时器已启动")

        except Exception as e:
            logger.error(f"启动实时显示定时器时出错: {e}")

    def _update_realtime_display(self):
        """更新实时显示"""
        try:
            # 只有在没有工作流运行时才更新实时显示
            if self.continuous_run_active:
                return

            # 获取相机帧
            success, frame = self.camera_manager.get_frame()

            if success and frame is not None:
                # 转换BGR到RGB
                import cv2
                import numpy as np

                if len(frame.shape) == 3 and frame.shape[2] == 3:
                    # 确保数据是连续的
                    if not frame.flags['C_CONTIGUOUS']:
                        frame = np.ascontiguousarray(frame)

                    # BGR转RGB
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                    # 更新图像显示
                    self.image_display_manager.update_display(rgb_frame)

        except Exception as e:
            # 静默处理错误，避免日志过多
            pass

    def _ensure_image_view_visible(self):
        """确保图像视图可见"""
        try:
            logger.info("确保图像视图可见")

            # 确保图像视图存在并可见
            if hasattr(self, 'image_view') and self.image_view:
                if self.image_view.isHidden():
                    logger.warning("图像视图不可见，设置为可见")
                    self.image_view.setVisible(True)
                    self.image_view.show()

                # 强制刷新
                UIRefresher.force_refresh_widget(self.image_view)

            # 确保图像滚动区域存在并可见
            if hasattr(self, 'image_scroll_area') and self.image_scroll_area:
                if self.image_scroll_area.isHidden():
                    logger.warning("图像滚动区域不可见，设置为可见")
                    self.image_scroll_area.setVisible(True)
                    self.image_scroll_area.show()

                # 强制刷新
                UIRefresher.force_refresh_widget(self.image_scroll_area)

            # 确保父容器可见
            if hasattr(self, 'right_panel') and self.right_panel:
                if self.right_panel.isHidden():
                    logger.warning("右侧面板不可见，设置为可见")
                    self.right_panel.setVisible(True)
                    self.right_panel.show()

                # 强制刷新
                UIRefresher.force_refresh_widget(self.right_panel)

            logger.info("图像视图可见性设置完成")
        except Exception as e:
            logger.error(f"确保图像视图可见时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _setup_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0,0,0,0) # 编辑器通常不带外边距
        main_layout.setSpacing(0)

        # 设置深色背景
        self.setStyleSheet(f"background-color: {THEME_COLORS['dark_bg_app']};")

        # 主分割器 - 左侧为节点库和画布，右侧为图像显示和结果
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStyleSheet(f"""
            QSplitter {{
                background-color: {THEME_COLORS['dark_bg_app']};
            }}
            QSplitter::handle {{
                background-color: {THEME_COLORS['dark_border_primary']};
                width: 1px;
            }}
        """)

        # 左侧分割器 - 节点库和画布
        left_splitter = QSplitter(Qt.Orientation.Horizontal)
        left_splitter.setStyleSheet(f"""
            QSplitter {{
                background-color: {THEME_COLORS['dark_bg_app']};
            }}
            QSplitter::handle {{
                background-color: {THEME_COLORS['dark_border_primary']};
                width: 1px;
            }}
        """)

        # 节点库
        self.node_library = ModernNodeLibrary()

        # 节点库标题
        node_library_widget = QWidget()
        node_library_layout = QVBoxLayout(node_library_widget)
        node_library_layout.setContentsMargins(0, 0, 0, 0)
        node_library_layout.setSpacing(0)

        node_library_header = QWidget()
        node_library_header.setFixedHeight(40)
        node_library_header.setStyleSheet(f"background-color: {THEME_COLORS['dark_bg_sidebar']};")
        node_library_header_layout = QHBoxLayout(node_library_header)
        node_library_header_layout.setContentsMargins(15, 0, 15, 0)

        node_library_title = QLabel("节点库")
        node_library_title.setStyleSheet(f"""
            color: {THEME_COLORS['text_primary']};
            font-size: 14px;
            font-weight: bold;
        """)
        node_library_header_layout.addWidget(node_library_title)

        node_library_layout.addWidget(node_library_header)
        node_library_layout.addWidget(self.node_library)

        # 画布区域
        self.canvas = ModernWorkflowCanvas()
        # 允许画布接受拖放事件
        self.canvas.setAcceptDrops(True)
        self.canvas.dragEnterEvent = self._canvas_drag_enter_event
        self.canvas.dragMoveEvent = self._canvas_drag_move_event
        self.canvas.dropEvent = self._canvas_drop_event

        # 右侧区域 - 图像显示和检测结果
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)

        # 右侧垂直分割器 - 上方为图像显示，下方为检测结果
        right_splitter = QSplitter(Qt.Orientation.Vertical)
        right_splitter.setStyleSheet(f"""
            QSplitter {{
                background-color: {THEME_COLORS['dark_bg_app']};
            }}
            QSplitter::handle {{
                background-color: {THEME_COLORS['dark_border_primary']};
                height: 1px;
            }}
        """)

        # 图像显示区域
        self.image_display = QWidget()
        self.image_display.setMinimumWidth(400)
        self.image_display.setMinimumHeight(300)
        image_layout = QVBoxLayout(self.image_display)
        image_layout.setContentsMargins(0, 0, 0, 0)
        image_layout.setSpacing(0)

        # 图像显示标题和控制区域
        image_header = QWidget()
        image_header.setFixedHeight(40)
        image_header.setStyleSheet(f"background-color: {THEME_COLORS['dark_bg_card']};")
        image_header_layout = QHBoxLayout(image_header)
        image_header_layout.setContentsMargins(15, 0, 15, 0)

        # 标题
        image_title = QLabel("实时图像")
        image_title.setStyleSheet(f"""
            color: {THEME_COLORS['text_primary']};
            font-size: 14px;
            font-weight: bold;
        """)
        image_header_layout.addWidget(image_title)

        # 创建工具栏
        tools_layout = QHBoxLayout()
        tools_layout.setContentsMargins(0, 0, 0, 0)
        tools_layout.setSpacing(10)

        # 添加操作按钮
        self.run_once_btn = QPushButton("执行一次")
        self.run_once_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['success']};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: {ColorHelper.lighten_color(THEME_COLORS['success'], 10)};
            }}
            QPushButton:pressed {{
                background-color: {ColorHelper.darken_color(THEME_COLORS['success'], 10)};
            }}
        """)
        self.run_once_btn.clicked.connect(self._run_workflow_once)

        self.continuous_run_btn = QPushButton("持续执行")
        self.continuous_run_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['primary']};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: {ColorHelper.lighten_color(THEME_COLORS['primary'], 10)};
            }}
            QPushButton:pressed {{
                background-color: {ColorHelper.darken_color(THEME_COLORS['primary'], 10)};
            }}
        """)
        self.continuous_run_btn.clicked.connect(self._toggle_continuous_run)

        # 添加叠加模式切换按钮
        self.overlay_btn = QPushButton("开启叠加")
        self.overlay_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS['secondary']};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: {ColorHelper.lighten_color(THEME_COLORS['secondary'], 10)};
            }}
            QPushButton:pressed {{
                background-color: {ColorHelper.darken_color(THEME_COLORS['secondary'], 10)};
            }}
        """)
        self.overlay_btn.clicked.connect(self.toggle_overlay_mode)

        # 添加节点显示选择器
        self.display_selector_label = QLabel("显示节点:")
        self.display_selector_label.setStyleSheet("color: white;")

        self.display_selector = QComboBox()
        self.display_selector.setStyleSheet("""
            QComboBox {
                background-color: #333;
                color: white;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px;
            }
            QComboBox::drop-down {
                border: 0px;
            }
            QComboBox::down-arrow {
                image: url(resources/icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                background-color: #333;
                color: white;
                selection-background-color: #555;
                selection-color: white;
            }
        """)
        self.display_selector.addItem("自动 (最近选择)")
        self.display_selector.currentIndexChanged.connect(self._on_display_selector_changed)

        # 添加清除按钮
        self.clear_display_btn = QPushButton("清除显示")
        self.clear_display_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS["danger"]};
                color: {THEME_COLORS["text_on_primary_bg"]};
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background-color: {ColorHelper.lighten_color(THEME_COLORS["danger"], 10)};
            }}
            QPushButton:pressed {{
                background-color: {ColorHelper.darken_color(THEME_COLORS["danger"], 10)};
            }}
        """)
        self.clear_display_btn.clicked.connect(self.clear_visible_nodes)

        # 添加按钮到工具栏
        tools_layout.addWidget(self.run_once_btn)
        tools_layout.addWidget(self.continuous_run_btn)
        tools_layout.addWidget(self.overlay_btn)
        tools_layout.addWidget(self.display_selector_label)
        tools_layout.addWidget(self.display_selector)
        tools_layout.addWidget(self.clear_display_btn)
        tools_layout.addStretch(1)

        # 将工具栏添加到图像头部布局
        image_header_layout.addLayout(tools_layout)
        image_header_layout.addStretch()

        # 初始化执行状态
        self.continuous_run_active = False
        self.continuous_run_timer = None

        # 缩放控制
        zoom_label = QLabel("缩放:")
        zoom_label.setStyleSheet(f"color: {THEME_COLORS['text_secondary']}; font-size: 12px;")
        image_header_layout.addWidget(zoom_label)

        # 缩放滑块
        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        self.zoom_slider.setRange(10, 200)  # 10% 到 200% 缩放
        self.zoom_slider.setValue(100)      # 默认 100%
        self.zoom_slider.setFixedWidth(100)
        self.zoom_slider.setStyleSheet(f"""
            QSlider::groove:horizontal {{
                height: 3px;
                background: {THEME_COLORS['dark_bg_input']};
                border-radius: 1px;
            }}
            QSlider::handle:horizontal {{
                background: {THEME_COLORS['primary']};
                width: 10px;
                height: 10px;
                margin: -4px 0;
                border-radius: 5px;
            }}
            QSlider::sub-page:horizontal {{
                background: {THEME_COLORS['primary']};
                height: 3px;
                border-radius: 1px;
            }}
        """)
        image_header_layout.addWidget(self.zoom_slider)

        # 缩放百分比标签
        self.zoom_percent = QLabel("100%")
        self.zoom_percent.setStyleSheet(f"color: {THEME_COLORS['text_primary']}; font-size: 12px; min-width: 40px;")
        image_header_layout.addWidget(self.zoom_percent)

        # 重置缩放按钮
        reset_zoom = QPushButton("重置")
        reset_zoom.setCursor(Qt.PointingHandCursor)
        reset_zoom.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {THEME_COLORS['text_secondary']};
                border: none;
                padding: 3px 8px;
                font-size: 12px;
            }}
            QPushButton:hover {{
                color: {THEME_COLORS['text_primary']};
            }}
        """)
        reset_zoom.clicked.connect(self.reset_zoom)
        image_header_layout.addWidget(reset_zoom)

        image_layout.addWidget(image_header)

        # 图像显示区域容器（使用QScrollArea实现滚动和缩放）
        self.image_scroll_area = QScrollArea()
        self.image_scroll_area.setWidgetResizable(True)
        self.image_scroll_area.setFrameShape(QFrame.NoFrame)
        self.image_scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: {THEME_COLORS['dark_bg_card']};
                border: none;
            }}
            QScrollBar:vertical {{
                background-color: {THEME_COLORS['dark_bg_app']};
                width: 6px;
                margin: 0px;
                border-radius: 3px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {THEME_COLORS['dark_border_primary']};
                border-radius: 3px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {THEME_COLORS['primary']};
            }}
            QScrollBar:horizontal {{
                background-color: {THEME_COLORS['dark_bg_app']};
                height: 6px;
                margin: 0px;
                border-radius: 3px;
            }}
            QScrollBar::handle:horizontal {{
                background-color: {THEME_COLORS['dark_border_primary']};
                border-radius: 3px;
                min-width: 20px;
            }}
            QScrollBar::handle:horizontal:hover {{
                background-color: {THEME_COLORS['primary']};
            }}
        """)

        # 图像容器
        self.image_container = QWidget()
        self.image_container.setStyleSheet(f"background-color: {THEME_COLORS['dark_bg_card']};")
        image_container_layout = QVBoxLayout(self.image_container)
        image_container_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 图像标签
        self.image_view = QLabel()
        self.image_view.setObjectName("MainImageView")  # 设置对象名称，方便查找
        self.image_view.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_view.setScaledContents(False)  # 不自动拉伸，使用update_image_with_zoom控制
        self.image_view.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)  # 允许随容器调整大小
        self.image_view.setStyleSheet("""
            QLabel {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                border-radius: 4px;
                color: #aaaaaa;  /* 文本颜色 */
            }
        """)
        self.image_view.setMinimumSize(400, 300)  # 设置最小尺寸，确保可见
        self.image_view.setText("等待图像输入...")  # 初始提示文字
        image_container_layout.addWidget(self.image_view)

        # 初始化ImageDisplayManager
        self.image_display_manager = ImageDisplayManager(
            image_view=self.image_view,
            image_container=self.image_container,
            image_scroll_area=self.image_scroll_area
        )

        self.image_scroll_area.setWidget(self.image_container)
        image_layout.addWidget(self.image_scroll_area, 1)  # 添加拉伸

        # 尝试加载一个示例图像
        try:
            # 创建一个模拟的检测图像（黑色背景上的一些彩色矩形）
            import numpy as np
            import cv2

            # 创建黑色背景图像
            sample_image = np.zeros((480, 640, 3), dtype=np.uint8)

            # 绘制网格
            for i in range(0, 640, 40):
                cv2.line(sample_image, (i, 0), (i, 480), (50, 50, 50), 1)
            for i in range(0, 480, 40):
                cv2.line(sample_image, (0, i), (640, i), (50, 50, 50), 1)

            # 绘制一些检测框
            # 绿色框 - 正常对象
            cv2.rectangle(sample_image, (100, 120), (280, 240), (0, 255, 0), 2)
            cv2.putText(sample_image, "正常 (97.5%)", (105, 115), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

            # 红色框 - 缺陷对象1
            cv2.rectangle(sample_image, (350, 150), (550, 250), (0, 0, 255), 2)
            cv2.putText(sample_image, "缺陷 (93.2%)", (355, 145), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

            # 红色框 - 缺陷对象2
            cv2.rectangle(sample_image, (180, 300), (340, 390), (0, 0, 255), 2)
            cv2.putText(sample_image, "缺陷 (89.1%)", (185, 295), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

            # 将BGR转为RGB格式
            sample_image_rgb = cv2.cvtColor(sample_image, cv2.COLOR_BGR2RGB)

            # 设置图像到显示管理器
            self.image_display_manager.update_display(sample_image_rgb)
            logger.debug("已创建模拟检测图像")
        except Exception as e:
            logger.error(f"创建模拟图像失败: {e}")
            self.image_view.setText("图像加载失败")

        # 连接缩放滑块信号
        self.zoom_slider.valueChanged.connect(self.on_zoom_changed)
        reset_zoom.clicked.connect(self.reset_zoom)

        # 添加鼠标滚轮缩放支持
        self.image_view.installEventFilter(self)
        self.image_scroll_area.installEventFilter(self)

        # 检测结果区域
        self.results_widget = QWidget()
        self.results_widget.setMinimumHeight(200)
        results_layout = QVBoxLayout(self.results_widget)
        results_layout.setContentsMargins(0, 0, 0, 0)
        results_layout.setSpacing(0)

        # 检测结果标题和统计
        results_header = QWidget()
        results_header.setFixedHeight(40)
        results_header.setStyleSheet(f"background-color: {THEME_COLORS['dark_bg_card']};")
        results_header_layout = QHBoxLayout(results_header)
        results_header_layout.setContentsMargins(15, 0, 15, 0)

        results_title = QLabel("检测结果")
        results_title.setStyleSheet(f"""
            color: {THEME_COLORS['text_primary']};
            font-size: 14px;
            font-weight: bold;
        """)
        results_header_layout.addWidget(results_title)

        results_header_layout.addStretch()

        results_stats = QLabel("共3项 | 2项缺陷")
        results_stats.setStyleSheet(f"""
            color: {THEME_COLORS['text_secondary']};
            font-size: 12px;
        """)
        results_header_layout.addWidget(results_stats)

        results_layout.addWidget(results_header)

        # 检测结果列表
        self.results_list = QListWidget()
        self.results_list.setStyleSheet(f"""
            QListWidget {{
                background-color: {THEME_COLORS['dark_bg_card']};
                border: none;
                padding: 5px;
                color: {THEME_COLORS['text_primary']};
            }}
            QListWidget::item {{
                padding: 10px;
                border-bottom: 1px solid {THEME_COLORS['dark_border_secondary']};
                margin-bottom: 2px;
            }}
            QListWidget::item:hover {{
                background-color: {THEME_COLORS['dark_surface_hover']};
            }}
            QListWidget::item:selected {{
                background-color: {THEME_COLORS['dark_surface_selected']};
                color: {THEME_COLORS['text_on_dark_selected_bg']};
            }}
        """)

        # 创建自定义检测结果项
        class DetectionResultItem(QListWidgetItem):
            def __init__(self, obj_id, obj_type, confidence, status, parent=None):
                super().__init__(parent)
                self.obj_id = obj_id
                self.obj_type = obj_type
                self.confidence = confidence
                self.status = status

                # 设置项目文本
                self.setText(f"检测对象 #{obj_id}: {obj_type} ({confidence:.1f}%)")

                # 设置项目颜色
                if status == "正常":
                    self.setForeground(QColor(THEME_COLORS["success"]))
                elif status == "缺陷":
                    self.setForeground(QColor(THEME_COLORS["danger"]))
                elif status == "警告":
                    self.setForeground(QColor(THEME_COLORS["warning"]))
                else:
                    self.setForeground(QColor(THEME_COLORS["text_primary"]))

                # 设置工具提示
                self.setToolTip(f"ID: {obj_id}\n类型: {obj_type}\n置信度: {confidence:.1f}%\n状态: {status}")

        # 添加示例检测结果
        self.results_list.addItem(DetectionResultItem(1, "缺陷", 93.2, "缺陷"))
        self.results_list.addItem(DetectionResultItem(2, "正常", 97.5, "正常"))
        self.results_list.addItem(DetectionResultItem(3, "缺陷", 89.1, "缺陷"))

        results_layout.addWidget(self.results_list, 1)

        # 添加操作按钮
        results_actions = QWidget()
        results_actions.setFixedHeight(40)
        results_actions.setStyleSheet(f"background-color: {THEME_COLORS['dark_bg_card']};")
        results_actions_layout = QHBoxLayout(results_actions)
        results_actions_layout.setContentsMargins(15, 0, 15, 0)

        export_btn = QPushButton("导出结果")
        export_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {THEME_COLORS["dark_bg_input"]};
                color: {THEME_COLORS["text_primary"]};
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }}
            QPushButton:hover {{
                background-color: {THEME_COLORS["dark_surface_hover"]};
            }}
        """)
        results_actions_layout.addWidget(export_btn)

        results_actions_layout.addStretch()

        clear_btn = QPushButton("清除")
        clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                color: {THEME_COLORS["text_secondary"]};
                border: none;
                padding: 6px 12px;
            }}
            QPushButton:hover {{
                color: {THEME_COLORS["text_primary"]};
            }}
        """)
        results_actions_layout.addWidget(clear_btn)

        results_layout.addWidget(results_actions)

        # 组装右侧分割器
        right_splitter.addWidget(self.image_display)
        right_splitter.addWidget(self.results_widget)
        right_splitter.setSizes([600, 400])  # 图像区域占比更大
        right_layout.addWidget(right_splitter)

        # 组装左侧分割器
        left_splitter.addWidget(node_library_widget)
        left_splitter.addWidget(self.canvas)
        left_splitter.setStretchFactor(0, 1)  # 节点库
        left_splitter.setStretchFactor(1, 3)  # 画布
        left_splitter.setSizes([200, 800])

        # 组装主分割器
        main_splitter.addWidget(left_splitter)
        main_splitter.addWidget(right_widget)
        main_splitter.setStretchFactor(0, 3)  # 左侧区域
        main_splitter.setStretchFactor(1, 2)  # 右侧区域
        main_splitter.setSizes([800, 400])  # 初始大小

        main_layout.addWidget(main_splitter)
        self.setLayout(main_layout)

    def _canvas_drag_enter_event(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
            logger.debug("拖拽进入画布")
        else:
            event.ignore()

    def _canvas_drag_move_event(self, event):
        if event.mimeData().hasText():
            event.acceptProposedAction()
        else:
            event.ignore()

    def _canvas_drop_event(self, event):
        if event.mimeData().hasText():
            try:
                node_data_str = event.mimeData().text()
                node_data = json.loads(node_data_str)

                # 将视图坐标转换为场景坐标
                drop_pos_view = event.pos() # QPoint
                drop_pos_scene = self.canvas.mapToScene(drop_pos_view)

                node_title = node_data.get("title", "新节点")
                node_type = node_data.get("node_type", "process")
                # 生成唯一节点ID (简单示例，实际应用可能需要更复杂逻辑)
                node_id_prefix = node_data.get("id_prefix", "node")
                new_node_id = f"{node_id_prefix}_{len(self.canvas.nodes) + 1}"
                while new_node_id in self.canvas.nodes:
                    import random
                    new_node_id = f"{node_id_prefix}_{len(self.canvas.nodes) + 1}_{random.randint(1000, 9999)}"

                self.canvas.add_node(new_node_id, node_type, node_title, drop_pos_scene)
                event.acceptProposedAction()
                logger.info(f"节点 '{node_title}' 已拖放到画布: {drop_pos_scene}")
            except json.JSONDecodeError:
                logger.error("拖拽的节点数据无效 (非JSON)")
                event.ignore()
            except Exception as e:
                logger.error(f"处理拖放节点时出错: {e}")
                event.ignore()
        else:
            event.ignore()

    def update_image_with_zoom(self):
        """根据当前缩放因子更新图像显示"""
        if not hasattr(self, 'original_pixmap') or self.original_pixmap is None or self.original_pixmap.isNull():
            logger.warning("尝试更新图像，但original_pixmap不存在或为空")
            return

        # 计算缩放后的大小
        orig_width = self.original_pixmap.width()
        orig_height = self.original_pixmap.height()
        new_width = int(orig_width * self.zoom_factor)
        new_height = int(orig_height * self.zoom_factor)

        logger.info(f"更新图像显示: 原始尺寸={orig_width}x{orig_height}, 缩放因子={self.zoom_factor}, 新尺寸={new_width}x{new_height}")

        # 缩放图像，保持纵横比
        scaled_pixmap = self.original_pixmap.scaled(
            new_width, new_height,
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        # 更新图像显示
        if hasattr(self, 'image_view'):
            from PyQt6.QtWidgets import QApplication

            # 确保图像视图可见
            if self.image_view.isHidden():
                logger.warning("图像视图不可见，设置为可见")
                self.image_view.show()

            # 先清空当前内容
            self.image_view.clear()

            # 设置缩放后的图像
            self.image_view.setPixmap(scaled_pixmap)
            self.image_view.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # 强制重绘
            self.image_view.repaint()
            self.image_view.update()

            # 更新图像容器和滚动区域
            if hasattr(self, 'image_container'):
                self.image_container.update()
                self.image_container.repaint()

            if hasattr(self, 'image_scroll_area'):
                self.image_scroll_area.update()
                self.image_scroll_area.repaint()

            logger.info(f"更新缩放图像显示: 缩放后尺寸={scaled_pixmap.width()}x{scaled_pixmap.height()}")

            # 确保所有事件得到处理
            QApplication.processEvents()

            # 检查是否成功设置图像
            if self.image_view.pixmap() is None or self.image_view.pixmap().isNull():
                logger.warning("缩放后图像未成功显示，尝试再次设置")
                self.image_view.setPixmap(scaled_pixmap)
                self.image_view.repaint()
                QApplication.processEvents()
        else:
            logger.error("图像视图不存在")

        # 确保刷新完成
        from PyQt6.QtWidgets import QApplication
        QApplication.processEvents()

    def on_zoom_changed(self, value):
        """处理缩放滑块值改变事件"""
        zoom_factor = value / 100.0  # 将百分比转换为缩放因子
        self.zoom_percent.setText(f"{value}%")  # 更新显示的百分比

        # 更新ImageDisplayManager的缩放因子
        if hasattr(self, 'image_display_manager'):
            self.image_display_manager.set_zoom_factor(zoom_factor)

    def reset_zoom(self):
        """重置缩放到100%"""
        self.zoom_slider.setValue(100)  # 这会触发on_zoom_changed

    def eventFilter(self, watched, event):
        """事件过滤器，用于处理图像区域的鼠标滚轮事件"""
        if (watched == self.image_view or watched == self.image_scroll_area) and event.type() == event.Wheel:
            # 获取当前缩放值
            current_value = self.zoom_slider.value()

            # 计算新的缩放值
            delta = event.angleDelta().y()

            # 根据滚轮方向和当前缩放值调整缩放步长
            # 缩放值越大，步长越大，反之步长越小
            step = max(1, current_value // 20)  # 确保最小步长为1

            if delta > 0:  # 向上滚动，放大
                new_value = min(current_value + step, self.zoom_slider.maximum())
            else:  # 向下滚动，缩小
                new_value = max(current_value - step, self.zoom_slider.minimum())

            # 设置新的缩放值
            self.zoom_slider.setValue(new_value)

            # 事件已处理
            return True

        # 默认处理
        return super().eventFilter(watched, event)

    def _run_workflow_once(self):
        """单次执行工作流"""
        logger.info("单次执行工作流")

        try:
            # 禁用按钮防止重复点击
            if hasattr(self, 'run_once_btn'):
                self.run_once_btn.setEnabled(False)

            # 设置强制刷新标志，确保每次手动点击都刷新相机帧
            self._force_camera_refresh = True

            # 确保滚动区域和图像视图可见
            self._ensure_scrollarea_visible()
            self._ensure_image_view_visible()

            # 预先强制刷新一次
            if hasattr(self, 'image_display_manager'):
                self.image_display_manager.force_refresh()

            # 强制处理事件队列确保UI响应
            from PyQt6.QtWidgets import QApplication
            QApplication.processEvents()

            # 1. 获取工作流中的所有节点和连接
            workflow_nodes = list(self.canvas.nodes.values()) if hasattr(self, 'canvas') else []
            workflow_connections = list(self.canvas.connections) if hasattr(self, 'canvas') else []

            if not workflow_nodes:
                logger.warning("工作流中没有节点，无法执行")
                if hasattr(self, 'run_once_btn'):
                    self.run_once_btn.setEnabled(True)
                return

            # 2. 查找输入节点（作为起点）
            input_nodes = [node for node in workflow_nodes if node.node_type == "input"]
            if not input_nodes:
                logger.warning("工作流中没有输入节点，无法执行")
                if hasattr(self, 'run_once_btn'):
                    self.run_once_btn.setEnabled(True)
                return

            logger.info(f"找到 {len(input_nodes)} 个输入节点")

            # 强制处理事件队列确保UI响应
            QApplication.processEvents()

            # 创建节点处理结果字典
            node_results = {}
            processed_nodes = set()

            # 获取当前要显示的节点
            current_display_node = getattr(self, '_current_display_node', None)

            # 3. 从每个输入节点开始执行工作流
            for input_node in input_nodes:
                # 检查节点类型
                if "camera_input" in input_node.node_id:
                    logger.info(f"处理相机输入节点: {input_node.node_id}")

                    # 获取相机画面
                    frame = self._get_camera_frame(input_node)

                    # 如果获取到有效帧，应用增强处理
                    if frame is not None and frame.size > 0:
                        # 创建帧的副本以避免引用问题
                        frame = frame.copy()

                        # 应用图像增强 - 保持BGR格式
                        frame = ImageProcessor.enhance_image(frame)

                        # 添加时间戳和相机信息 - 仍然是BGR格式
                        frame = ImageProcessor.add_timestamp(frame, input_node.node_id)

                        # 将BGR转为RGB格式，适合显示
                        import cv2
                        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB).copy()

                        # 确保数据是连续的
                        import numpy as np
                        if not rgb_frame.flags['C_CONTIGUOUS']:
                            rgb_frame = np.ascontiguousarray(rgb_frame)

                        # 更新图像显示 - 只在没有特定显示节点时显示输入节点
                        if not current_display_node:
                            self.image_display_manager.update_display(rgb_frame)

                        # 强制更新UI
                        QApplication.processEvents()

                        # 添加结果
                        node_results[input_node.node_id] = {
                            "node_id": input_node.node_id,
                            "result": "success",
                            "image": rgb_frame,
                            "metadata": {"source": "camera"}
                        }
                        processed_nodes.add(input_node.node_id)

                        # 同时存储到节点结果管理系统
                        self._node_results[input_node.node_id] = {
                            "node_id": input_node.node_id,
                            "result": "success",
                            "image": rgb_frame.copy(),
                            "metadata": {"source": "camera"}
                        }

                        # 强制处理事件队列确保UI响应
                        QApplication.processEvents()

                        # 处理后续节点 - 遍历所有连接
                        for connection in workflow_connections:
                            if connection.start_node.node_id == input_node.node_id:
                                end_node = connection.end_node
                                logger.info(f"处理后续节点: {end_node.node_id}")

                                # 根据节点类型处理图像
                                result = self._process_node_image(end_node, rgb_frame.copy())
                                node_results[end_node.node_id] = result
                                processed_nodes.add(end_node.node_id)

                                # 同时存储到节点结果管理系统
                                self._node_results[end_node.node_id] = result.copy() if isinstance(result, dict) else result

                                # 使用处理后的图像更新显示（仅在结果有效且是当前显示节点或无特定显示节点时）
                                if "image" in result and result["image"] is not None:
                                    # 确保数据是连续的
                                    import numpy as np
                                    if not result["image"].flags['C_CONTIGUOUS']:
                                        result["image"] = np.ascontiguousarray(result["image"])

                                    # 仅当是当前选中的节点或没有特定显示节点时才更新显示
                                    if not current_display_node or end_node.node_id == current_display_node:
                                        self.image_display_manager.update_display(result["image"])
                                        # 强制更新UI
                                        QApplication.processEvents()

                # 其他类型的输入节点可以在这里添加处理逻辑

            # 整理最终结果
            final_results = []
            for node_id, result in node_results.items():
                if node_id not in processed_nodes:
                    continue
                final_results.append(result)

            # 检查是否有当前显示节点，如果有则显示其结果
            if current_display_node and current_display_node in self._node_results:
                result = self._node_results[current_display_node]
                if "image" in result and result["image"] is not None:
                    self.image_display_manager.update_display(result["image"])
                    # 更新可见节点集合
                    if self._overlay_mode:
                        self._visible_nodes.add(current_display_node)
                    else:
                        self._visible_nodes = {current_display_node}
                    logger.info(f"显示当前选中节点 {current_display_node} 的结果")

            # 更新结果面板
            # TODO: 实现结果面板更新

            logger.info(f"工作流执行完成，共处理了 {len(processed_nodes)} 个节点")

            # 强制再次刷新图像 - 确保所有UI元素更新
            self.image_display_manager.force_refresh()

            # 处理事件队列
            QApplication.processEvents()

            # 更新节点选择器
            if hasattr(self, 'display_selector'):
                self._update_display_selector()

        except Exception as e:
            logger.error(f"执行工作流时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # 恢复按钮状态
            if hasattr(self, 'run_once_btn'):
                self.run_once_btn.setEnabled(True)

    def _get_camera_frame(self, input_node):
        """从相机节点获取一帧图像

        Args:
            input_node: 相机输入节点

        Returns:
            numpy数组，BGR格式的图像帧，或None表示失败
        """
        try:
            # 获取当前点击状态 - 检测是否为手动点击触发的刷新
            force_refresh = getattr(self, '_force_camera_refresh', False)

            # 如果是手动点击"执行"按钮，强制刷新相机帧
            if force_refresh:
                logger.info("手动执行按钮点击，强制刷新相机帧")
                # 使用相机管理器获取新帧
                success, frame = self.camera_manager.get_frame()

                # 重置强制刷新标志
                self._force_camera_refresh = False

                # 如果成功获取到了有效帧，保存到节点中
                if success and frame is not None and frame.size > 0:
                    import numpy as np
                    if np.mean(frame) > 15.0:
                        logger.info("成功获取有效相机帧，保存到节点")
                        input_node.camera_frame = frame.copy()  # 保存副本到节点
                return frame

            # 正常流程 - 优先检查节点自身是否有保存的相机帧
            if hasattr(input_node, 'camera_frame') and input_node.camera_frame is not None:
                frame = input_node.camera_frame

                # 检查帧的有效性 - 确保不是空数组并且尺寸合理
                if frame.size > 0 and frame.shape[0] > 10 and frame.shape[1] > 10:
                    import numpy as np
                    avg_value = np.mean(frame)
                    logger.info(f"使用节点保存的相机帧: 形状={frame.shape}, 平均值={avg_value:.2f}")

                    # 只有当帧不是全黑或接近全黑时才使用
                    if avg_value > 15.0:
                        return frame.copy()  # 返回副本避免引用问题
                    else:
                        logger.warning(f"节点保存的帧太暗 (平均值={avg_value:.2f})，将重新获取")

            # 使用相机管理器获取新帧
            success, frame = self.camera_manager.get_frame()

            # 如果成功获取到了有效帧，保存到节点中
            if success and frame is not None and frame.size > 0:
                import numpy as np
                if np.mean(frame) > 15.0:
                    logger.info("成功获取有效相机帧，保存到节点")
                    input_node.camera_frame = frame.copy()  # 保存副本到节点

            return frame

        except Exception as e:
            logger.error(f"获取相机帧出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return self.camera_manager.create_error_frame(f"相机错误: {str(e)}")

    def _process_node_image(self, node, image):
        """处理节点图像

        Args:
            node: 节点对象
            image: 输入图像（RGB格式的numpy数组）

        Returns:
            处理结果
        """
        try:
            # 确保输入图像是RGB格式
            import cv2
            import numpy as np

            # 首先确保图像是连续的并且有效
            import numpy as np
            if not image.flags['C_CONTIGUOUS']:
                image = np.ascontiguousarray(image)

            # 根据节点类型应用不同的算法
            if "gaussian_blur" in node.node_id:
                # 高斯模糊 - 将RGB转换为BGR处理，然后转回RGB
                image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                blurred_bgr = ImageProcessor.apply_gaussian_blur(image_bgr)
                blurred_rgb = cv2.cvtColor(blurred_bgr, cv2.COLOR_BGR2RGB)
                logger.info("应用高斯模糊")

                return {"node_id": node.node_id, "result": "success", "image": blurred_rgb}

            elif "canny_edge" in node.node_id:
                # Canny边缘检测 - 需要转换为BGR处理
                image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                edges_bgr = ImageProcessor.apply_canny_edge(image_bgr)
                edges_rgb = cv2.cvtColor(edges_bgr, cv2.COLOR_BGR2RGB)
                logger.info("应用Canny边缘检测")

                return {"node_id": node.node_id, "result": "success", "image": edges_rgb}

            elif "sobel_edge" in node.node_id:
                # Sobel边缘检测 - 需要转换为BGR处理
                image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                sobel_bgr = ImageProcessor.apply_sobel_edge(image_bgr)
                sobel_rgb = cv2.cvtColor(sobel_bgr, cv2.COLOR_BGR2RGB)
                logger.info("应用Sobel边缘检测")

                return {"node_id": node.node_id, "result": "success", "image": sobel_rgb}

            elif "laplacian_edge" in node.node_id:
                # Laplacian边缘检测 - 需要转换为BGR处理
                image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                laplacian_bgr = ImageProcessor.apply_laplacian_edge(image_bgr)
                laplacian_rgb = cv2.cvtColor(laplacian_bgr, cv2.COLOR_BGR2RGB)
                logger.info("应用Laplacian边缘检测")

                return {"node_id": node.node_id, "result": "success", "image": laplacian_rgb}

            elif "contour_detect" in node.node_id:
                # 轮廓检测 - 需要转换为BGR处理
                image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
                contour_bgr = ImageProcessor.apply_contour_detection(image_bgr)
                contour_rgb = cv2.cvtColor(contour_bgr, cv2.COLOR_BGR2RGB)
                logger.info("应用轮廓检测")

                return {"node_id": node.node_id, "result": "success", "image": contour_rgb}

            else:
                # 默认返回原图 - 确保是RGB格式
                logger.warning(f"未知节点类型 {node.node_id}，无法处理")
                return {"node_id": node.node_id, "result": "unknown_type", "image": image}

        except Exception as e:
            logger.error(f"处理节点 {node.node_id} 出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {"node_id": node.node_id, "result": "error", "error": str(e)}

    def _on_display_selector_changed(self, index):
        """处理显示选择器选项变更

        Args:
            index: 当前选中的索引
        """
        if index == 0:  # "自动 (最近选择)"
            return  # 保持当前选择

        node_id = self.display_selector.currentText()
        if node_id in self._node_results:
            self.show_node_result(node_id)
        else:
            logger.warning(f"找不到节点 {node_id} 的处理结果")

    def _update_display_selector(self):
        """更新显示选择器的选项"""
        # 保存当前选择
        current_selection = self.display_selector.currentText()

        # 清除所有选项，除了第一个"自动"选项
        while self.display_selector.count() > 1:
            self.display_selector.removeItem(1)

        # 添加所有有处理结果的节点
        for node_id in self._node_results.keys():
            if "image" in self._node_results[node_id] and self._node_results[node_id]["image"] is not None:
                self.display_selector.addItem(node_id)

        # 尝试恢复之前的选择
        if current_selection != "自动 (最近选择)":
            index = self.display_selector.findText(current_selection)
            if index >= 0:
                self.display_selector.setCurrentIndex(index)

    def show_node_result(self, node_id):
        """显示指定节点的处理结果

        Args:
            node_id: 要显示的节点ID
        """
        try:
            logger.info(f"显示节点 {node_id} 的处理结果")

            # 确保图像视图可见
            self._ensure_image_view_visible()

            # 更新当前显示的节点
            self._current_display_node = node_id

            # 检查节点结果是否存在
            if node_id in self._node_results:
                result = self._node_results[node_id]

                # 检查结果中是否包含图像
                if "image" in result and result["image"] is not None:
                    # 确保图像数据是连续的
                    import numpy as np
                    if not result["image"].flags['C_CONTIGUOUS']:
                        result["image"] = np.ascontiguousarray(result["image"])

                    # 更新图像显示
                    self.image_display_manager.update_display(result["image"])

                    # 更新可见节点集合
                    if self._overlay_mode:
                        self._visible_nodes.add(node_id)
                    else:
                        self._visible_nodes = {node_id}

                    logger.info(f"显示节点 {node_id} 的图像结果")

                    # 强制刷新确保显示
                    if hasattr(self, 'image_display_manager'):
                        self.image_display_manager.force_refresh()
                else:
                    logger.warning(f"节点 {node_id} 的结果中不包含有效图像")
            else:
                logger.warning(f"找不到节点 {node_id} 的处理结果，尝试运行工作流")
                # 尝试运行工作流以获取结果
                self._run_workflow_once()

                # 再次检查是否有结果
                if node_id in self._node_results:
                    self.show_node_result(node_id)  # 递归调用

        except Exception as e:
            logger.error(f"显示节点 {node_id} 的处理结果时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def _update_composite_image(self):
        """更新叠加模式下的复合图像"""
        try:
            if not self._visible_nodes:
                return

            # 获取第一个可见节点的图像作为基础
            base_node_id = next(iter(self._visible_nodes))
            if base_node_id not in self._node_results:
                return

            base_result = self._node_results[base_node_id]
            if "image" not in base_result or base_result["image"] is None:
                return

            # 创建基础图像的副本
            import numpy as np
            import cv2

            # 确保图像数据是连续的
            import numpy as np
            if not base_result["image"].flags['C_CONTIGUOUS']:
                base_image = np.ascontiguousarray(base_result["image"])
            else:
                base_image = base_result["image"].copy()

            # 叠加其他节点的结果
            for node_id in self._visible_nodes:
                if node_id == base_node_id:
                    continue

                if node_id in self._node_results:
                    node_result = self._node_results[node_id]
                    if "image" in node_result and node_result["image"] is not None:
                        # 确保图像数据是连续的
                        import numpy as np
                        if not node_result["image"].flags['C_CONTIGUOUS']:
                            overlay = np.ascontiguousarray(node_result["image"])
                        else:
                            overlay = node_result["image"].copy()

                        # 使用透明度叠加
                        cv2.addWeighted(base_image, 0.7, overlay, 0.3, 0, base_image)

            # 更新显示
            self.image_display_manager.update_display(base_image)

            # 强制刷新
            self.image_display_manager.force_refresh()

        except Exception as e:
            logger.error(f"更新复合图像时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def toggle_overlay_mode(self):
        """切换叠加模式"""
        self._overlay_mode = not self._overlay_mode
        logger.info(f"叠加模式: {'开启' if self._overlay_mode else '关闭'}")

        # 更新UI状态
        if hasattr(self, 'overlay_btn'):
            self.overlay_btn.setText("关闭叠加" if self._overlay_mode else "开启叠加")

        # 如果叠加模式被关闭，只保留最后选择的节点
        if not self._overlay_mode and self._visible_nodes:
            self._visible_nodes = {list(self._visible_nodes)[-1]}
            self._update_composite_image()

    def clear_visible_nodes(self):
        """清空可见节点集合"""
        self._visible_nodes.clear()
        logger.info("已清空可见节点集合")

        # 清空图像显示
        if hasattr(self, 'image_display_manager'):
            self.image_display_manager.clear_display()

    def _toggle_continuous_run(self):
        """切换持续执行工作流状态"""
        if hasattr(self, 'continuous_run_active') and self.continuous_run_active:
            # 停止持续执行
            if hasattr(self, 'continuous_run_timer') and self.continuous_run_timer is not None:
                self.killTimer(self.continuous_run_timer)
                self.continuous_run_timer = None

            self.continuous_run_active = False
            self._continuous_count = 0  # 重置计数器

            self.continuous_run_btn.setText("持续执行")
            self.continuous_run_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {THEME_COLORS['primary']};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 15px;
                }}
                QPushButton:hover {{
                    background-color: {ColorHelper.lighten_color(THEME_COLORS['primary'], 10)};
                }}
                QPushButton:pressed {{
                    background-color: {ColorHelper.darken_color(THEME_COLORS['primary'], 10)};
                }}
            """)
            logger.info("停止持续执行工作流")

            # 最后再强制刷新一次，确保最终状态
            if hasattr(self, 'image_display_manager'):
                self.image_display_manager.force_refresh()

        else:
            # 开始持续执行
            self.continuous_run_active = True
            self._continuous_count = 0  # 初始化计数器
            self._last_execution_time = time.time()  # 记录上次执行时间用于防抖

            # 确保UI组件可见
            self._ensure_scrollarea_visible()
            self._ensure_image_view_visible()

            # 使用合适的刷新率 (每1秒)，但至少保证0.2秒的最小间隔
            self.continuous_run_timer = self.startTimer(1000)

            self.continuous_run_btn.setText("停止执行")
            self.continuous_run_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {THEME_COLORS['danger']};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 15px;
                }}
                QPushButton:hover {{
                    background-color: {ColorHelper.lighten_color(THEME_COLORS['danger'], 10)};
                }}
                QPushButton:pressed {{
                    background-color: {ColorHelper.darken_color(THEME_COLORS['danger'], 10)};
                }}
            """)
            logger.info("开始持续执行工作流，刷新率=1秒/次")

            # 立即执行一次，不等待定时器
            self._run_workflow_once()

    def timerEvent(self, event):
        """定时器事件处理"""
        if hasattr(self, 'continuous_run_timer') and event.timerId() == self.continuous_run_timer:
            try:
                # 防抖控制：确保执行频率不会过高
                current_time = time.time()
                min_interval = 0.2  # 最小执行间隔（秒）

                if hasattr(self, '_last_execution_time') and current_time - self._last_execution_time < min_interval:
                    logger.debug(f"执行过于频繁，跳过本次执行 (间隔={current_time - self._last_execution_time:.3f}秒)")
                    return

                # 更新执行时间
                self._last_execution_time = current_time

                # 确保UI组件可见
                self._ensure_scrollarea_visible()
                self._ensure_image_view_visible()

                # 执行工作流
                self._run_workflow_once()

                # 强制处理事件队列
                QApplication.processEvents()

                # 更新计数
                self._continuous_count = getattr(self, '_continuous_count', 0) + 1

            except Exception as e:
                logger.error(f"持续执行定时器处理出错: {e}")
                import traceback
                logger.error(traceback.format_exc())

    def _ensure_scrollarea_visible(self):
        """确保滚动区域和其容器可见"""
        try:
            logger.info("强制设置滚动区域和容器可见")

            # 确保滚动区域可见
            if hasattr(self, 'image_scroll_area'):
                self.image_scroll_area.setVisible(True)
                self.image_scroll_area.show()

                # 获取滚动区域的子容器
                viewport = self.image_scroll_area.viewport()
                if viewport:
                    viewport.setVisible(True)
                    viewport.show()

                # 获取滚动区域的垂直和水平容器
                for child in self.image_scroll_area.findChildren(QWidget):
                    if child.objectName() in ["qt_scrollarea_vcontainer", "qt_scrollarea_hcontainer"]:
                        logger.info(f"设置 {child.objectName()} 可见")
                        child.setVisible(True)
                        child.show()

            # 确保图像容器可见
            if hasattr(self, 'image_container'):
                self.image_container.setVisible(True)
                self.image_container.show()

            # 确保图像视图可见
            if hasattr(self, 'image_view'):
                self.image_view.setVisible(True)
                self.image_view.show()

            # 强制处理事件队列，确保可见性变更生效
            from PyQt6.QtWidgets import QApplication
            QApplication.processEvents()

            logger.info("滚动区域和容器可见性设置完成")
        except Exception as e:
            logger.error(f"设置滚动区域可见性时出错: {e}")
            import traceback
            logger.error(traceback.format_exc())