#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ROI配置组件
提供ROI区域配置功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QPushButton, QLabel, QGroupBox, QComboBox,
    QSpinBox, QCheckBox, QListWidget, QListWidgetItem,
    QSlider, QMessageBox, QColorDialog, QLineEdit
)
from PyQt6.QtCore import Qt, pyqtSignal, QRectF
from PyQt6.QtGui import QColor

import cv2
import numpy as np
from typing import List, Dict, Any, Optional
from loguru import logger


class ROIWidget(QWidget):
    """
    ROI配置组件
    
    提供ROI区域选择和颜色检测配置功能
    """
    
    # 信号定义
    roi_updated = pyqtSignal(dict)  # ROI更新信号
    status_message = pyqtSignal(str)  # 状态消息信号
    
    def __init__(self):
        """初始化ROI配置组件"""
        super().__init__()
        
        # ROI列表
        self.roi_regions = []
        self.current_roi_index = -1
        
        # 创建UI
        self._create_ui()
        
        # 连接信号
        self._connect_signals()
        
        logger.info("ROI配置组件初始化完成")
    
    def _create_ui(self):
        """创建UI组件"""
        layout = QVBoxLayout(self)
        
        # ROI列表管理组
        roi_list_group = QGroupBox("ROI区域列表")
        roi_list_layout = QVBoxLayout(roi_list_group)
        
        # ROI列表
        self.roi_list = QListWidget()
        self.roi_list.currentRowChanged.connect(self._on_roi_selected)
        roi_list_layout.addWidget(self.roi_list)
        
        # ROI操作按钮
        btn_layout = QHBoxLayout()
        
        self.add_roi_btn = QPushButton("添加ROI")
        self.add_roi_btn.clicked.connect(self._add_roi)
        btn_layout.addWidget(self.add_roi_btn)
        
        self.remove_roi_btn = QPushButton("删除ROI")
        self.remove_roi_btn.clicked.connect(self._remove_roi)
        btn_layout.addWidget(self.remove_roi_btn)
        
        self.clear_roi_btn = QPushButton("清空所有")
        self.clear_roi_btn.clicked.connect(self._clear_all_roi)
        btn_layout.addWidget(self.clear_roi_btn)
        
        roi_list_layout.addLayout(btn_layout)
        
        layout.addWidget(roi_list_group)
        
        # ROI配置组
        roi_config_group = QGroupBox("ROI配置")
        roi_config_layout = QFormLayout(roi_config_group)
        
        # ROI名称
        self.roi_name_edit = QLineEdit()
        self.roi_name_edit.setPlaceholderText("输入ROI名称")
        self.roi_name_edit.textChanged.connect(self._update_current_roi)
        roi_config_layout.addRow("名称:", self.roi_name_edit)
        
        # ROI位置
        position_layout = QHBoxLayout()
        
        self.roi_x_spin = QSpinBox()
        self.roi_x_spin.setRange(0, 9999)
        self.roi_x_spin.valueChanged.connect(self._update_current_roi)
        position_layout.addWidget(QLabel("X:"))
        position_layout.addWidget(self.roi_x_spin)
        
        self.roi_y_spin = QSpinBox()
        self.roi_y_spin.setRange(0, 9999)
        self.roi_y_spin.valueChanged.connect(self._update_current_roi)
        position_layout.addWidget(QLabel("Y:"))
        position_layout.addWidget(self.roi_y_spin)
        
        roi_config_layout.addRow("位置:", position_layout)
        
        # ROI尺寸
        size_layout = QHBoxLayout()
        
        self.roi_w_spin = QSpinBox()
        self.roi_w_spin.setRange(1, 9999)
        self.roi_w_spin.setValue(100)
        self.roi_w_spin.valueChanged.connect(self._update_current_roi)
        size_layout.addWidget(QLabel("宽:"))
        size_layout.addWidget(self.roi_w_spin)
        
        self.roi_h_spin = QSpinBox()
        self.roi_h_spin.setRange(1, 9999)
        self.roi_h_spin.setValue(100)
        self.roi_h_spin.valueChanged.connect(self._update_current_roi)
        size_layout.addWidget(QLabel("高:"))
        size_layout.addWidget(self.roi_h_spin)
        
        roi_config_layout.addRow("尺寸:", size_layout)
        
        # ROI类型
        self.roi_type_combo = QComboBox()
        self.roi_type_combo.addItem("颜色检测", "color")
        self.roi_type_combo.addItem("图像分析", "image")
        self.roi_type_combo.currentIndexChanged.connect(self._update_current_roi)
        roi_config_layout.addRow("类型:", self.roi_type_combo)
        
        layout.addWidget(roi_config_group)
        
        # 颜色检测配置组
        color_group = QGroupBox("颜色检测配置")
        color_layout = QFormLayout(color_group)
        
        # 颜色空间
        self.color_space_combo = QComboBox()
        self.color_space_combo.addItem("HSV", "HSV")
        self.color_space_combo.addItem("RGB", "RGB")
        self.color_space_combo.addItem("LAB", "LAB")
        self.color_space_combo.currentIndexChanged.connect(self._update_color_config)
        color_layout.addRow("颜色空间:", self.color_space_combo)
        
        # 检测方法
        self.detection_method_combo = QComboBox()
        self.detection_method_combo.addItem("范围检测", "range")
        self.detection_method_combo.addItem("阈值检测", "threshold")
        self.detection_method_combo.addItem("统计检测", "statistical")
        self.detection_method_combo.currentIndexChanged.connect(self._update_color_config)
        color_layout.addRow("检测方法:", self.detection_method_combo)
        
        # 颜色范围配置
        range_group = QGroupBox("颜色范围")
        range_layout = QFormLayout(range_group)
        
        # 下限值
        lower_layout = QHBoxLayout()
        
        self.lower_ch1_slider = QSlider(Qt.Orientation.Horizontal)
        self.lower_ch1_slider.setRange(0, 255)
        self.lower_ch1_slider.valueChanged.connect(self._update_color_config)
        self.lower_ch1_label = QLabel("0")
        lower_layout.addWidget(self.lower_ch1_slider)
        lower_layout.addWidget(self.lower_ch1_label)
        range_layout.addRow("下限通道1:", lower_layout)
        
        lower_layout2 = QHBoxLayout()
        self.lower_ch2_slider = QSlider(Qt.Orientation.Horizontal)
        self.lower_ch2_slider.setRange(0, 255)
        self.lower_ch2_slider.valueChanged.connect(self._update_color_config)
        self.lower_ch2_label = QLabel("0")
        lower_layout2.addWidget(self.lower_ch2_slider)
        lower_layout2.addWidget(self.lower_ch2_label)
        range_layout.addRow("下限通道2:", lower_layout2)
        
        lower_layout3 = QHBoxLayout()
        self.lower_ch3_slider = QSlider(Qt.Orientation.Horizontal)
        self.lower_ch3_slider.setRange(0, 255)
        self.lower_ch3_slider.valueChanged.connect(self._update_color_config)
        self.lower_ch3_label = QLabel("0")
        lower_layout3.addWidget(self.lower_ch3_slider)
        lower_layout3.addWidget(self.lower_ch3_label)
        range_layout.addRow("下限通道3:", lower_layout3)
        
        # 上限值
        upper_layout = QHBoxLayout()
        
        self.upper_ch1_slider = QSlider(Qt.Orientation.Horizontal)
        self.upper_ch1_slider.setRange(0, 255)
        self.upper_ch1_slider.setValue(255)
        self.upper_ch1_slider.valueChanged.connect(self._update_color_config)
        self.upper_ch1_label = QLabel("255")
        upper_layout.addWidget(self.upper_ch1_slider)
        upper_layout.addWidget(self.upper_ch1_label)
        range_layout.addRow("上限通道1:", upper_layout)
        
        upper_layout2 = QHBoxLayout()
        self.upper_ch2_slider = QSlider(Qt.Orientation.Horizontal)
        self.upper_ch2_slider.setRange(0, 255)
        self.upper_ch2_slider.setValue(255)
        self.upper_ch2_slider.valueChanged.connect(self._update_color_config)
        self.upper_ch2_label = QLabel("255")
        upper_layout2.addWidget(self.upper_ch2_slider)
        upper_layout2.addWidget(self.upper_ch2_label)
        range_layout.addRow("上限通道2:", upper_layout2)
        
        upper_layout3 = QHBoxLayout()
        self.upper_ch3_slider = QSlider(Qt.Orientation.Horizontal)
        self.upper_ch3_slider.setRange(0, 255)
        self.upper_ch3_slider.setValue(255)
        self.upper_ch3_slider.valueChanged.connect(self._update_color_config)
        self.upper_ch3_label = QLabel("255")
        upper_layout3.addWidget(self.upper_ch3_slider)
        upper_layout3.addWidget(self.upper_ch3_label)
        range_layout.addRow("上限通道3:", upper_layout3)
        
        color_layout.addRow(range_group)
        
        # 检测参数
        params_layout = QFormLayout()
        
        # 最小面积
        self.min_area_spin = QSpinBox()
        self.min_area_spin.setRange(1, 99999)
        self.min_area_spin.setValue(100)
        self.min_area_spin.valueChanged.connect(self._update_color_config)
        params_layout.addRow("最小面积:", self.min_area_spin)
        
        # 最大面积
        self.max_area_spin = QSpinBox()
        self.max_area_spin.setRange(1, 999999)
        self.max_area_spin.setValue(10000)
        self.max_area_spin.valueChanged.connect(self._update_color_config)
        params_layout.addRow("最大面积:", self.max_area_spin)
        
        # 形态学处理
        self.morphology_check = QCheckBox()
        self.morphology_check.setChecked(True)
        self.morphology_check.stateChanged.connect(self._update_color_config)
        params_layout.addRow("形态学处理:", self.morphology_check)
        
        color_layout.addRow(params_layout)
        
        layout.addWidget(color_group)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        
        self.test_color_btn = QPushButton("测试颜色检测")
        self.test_color_btn.clicked.connect(self._test_color_detection)
        test_layout.addWidget(self.test_color_btn)
        
        self.calibrate_color_btn = QPushButton("颜色校准")
        self.calibrate_color_btn.clicked.connect(self._calibrate_color)
        test_layout.addWidget(self.calibrate_color_btn)
        
        layout.addLayout(test_layout)
        
        layout.addStretch()
        
        # 初始状态
        self._update_ui_state()
    
    def _connect_signals(self):
        """连接信号"""
        # 滑块值更新标签
        self.lower_ch1_slider.valueChanged.connect(lambda v: self.lower_ch1_label.setText(str(v)))
        self.lower_ch2_slider.valueChanged.connect(lambda v: self.lower_ch2_label.setText(str(v)))
        self.lower_ch3_slider.valueChanged.connect(lambda v: self.lower_ch3_label.setText(str(v)))
        self.upper_ch1_slider.valueChanged.connect(lambda v: self.upper_ch1_label.setText(str(v)))
        self.upper_ch2_slider.valueChanged.connect(lambda v: self.upper_ch2_label.setText(str(v)))
        self.upper_ch3_slider.valueChanged.connect(lambda v: self.upper_ch3_label.setText(str(v)))
    
    def _add_roi(self):
        """添加新的ROI区域"""
        roi_count = len(self.roi_regions)
        roi_name = f"ROI_{roi_count + 1}"
        
        roi_data = {
            'name': roi_name,
            'x': 0,
            'y': 0,
            'width': 100,
            'height': 100,
            'type': 'color',
            'color_config': self._get_default_color_config()
        }
        
        self.roi_regions.append(roi_data)
        
        # 添加到列表
        item = QListWidgetItem(roi_name)
        self.roi_list.addItem(item)
        
        # 选中新添加的ROI
        self.roi_list.setCurrentRow(len(self.roi_regions) - 1)
        
        self.status_message.emit(f"已添加ROI: {roi_name}")
        self._emit_roi_updated()
    
    def _remove_roi(self):
        """删除当前选中的ROI"""
        current_row = self.roi_list.currentRow()
        if current_row >= 0:
            roi_name = self.roi_regions[current_row]['name']
            
            # 从列表中删除
            self.roi_regions.pop(current_row)
            self.roi_list.takeItem(current_row)
            
            # 更新UI状态
            self._update_ui_state()
            
            self.status_message.emit(f"已删除ROI: {roi_name}")
            self._emit_roi_updated()
        else:
            QMessageBox.warning(self, "警告", "请先选择要删除的ROI")
    
    def _clear_all_roi(self):
        """清空所有ROI"""
        if self.roi_regions:
            reply = QMessageBox.question(
                self, "确认", "确定要清空所有ROI区域吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.roi_regions.clear()
                self.roi_list.clear()
                self._update_ui_state()
                
                self.status_message.emit("已清空所有ROI")
                self._emit_roi_updated()
    
    def _on_roi_selected(self, row: int):
        """ROI选择事件处理"""
        if 0 <= row < len(self.roi_regions):
            self.current_roi_index = row
            self._load_roi_data(self.roi_regions[row])
        else:
            self.current_roi_index = -1
        
        self._update_ui_state()
    
    def _load_roi_data(self, roi_data: Dict[str, Any]):
        """加载ROI数据到界面"""
        # 加载基本信息
        self.roi_name_edit.setText(roi_data.get('name', ''))
        self.roi_x_spin.setValue(roi_data.get('x', 0))
        self.roi_y_spin.setValue(roi_data.get('y', 0))
        self.roi_w_spin.setValue(roi_data.get('width', 100))
        self.roi_h_spin.setValue(roi_data.get('height', 100))
        
        # 设置ROI类型
        roi_type = roi_data.get('type', 'color')
        for i in range(self.roi_type_combo.count()):
            if self.roi_type_combo.itemData(i) == roi_type:
                self.roi_type_combo.setCurrentIndex(i)
                break
        
        # 加载颜色配置
        color_config = roi_data.get('color_config', {})
        self._load_color_config(color_config)
    
    def _load_color_config(self, color_config: Dict[str, Any]):
        """加载颜色配置"""
        # 设置颜色空间
        color_space = color_config.get('color_space', 'HSV')
        for i in range(self.color_space_combo.count()):
            if self.color_space_combo.itemData(i) == color_space:
                self.color_space_combo.setCurrentIndex(i)
                break
        
        # 设置检测方法
        method = color_config.get('detection_method', 'range')
        for i in range(self.detection_method_combo.count()):
            if self.detection_method_combo.itemData(i) == method:
                self.detection_method_combo.setCurrentIndex(i)
                break
        
        # 设置颜色范围
        color_ranges = color_config.get('color_ranges', [])
        if color_ranges:
            lower = color_ranges[0].get('lower', [0, 0, 0])
            upper = color_ranges[0].get('upper', [255, 255, 255])
            
            self.lower_ch1_slider.setValue(lower[0] if len(lower) > 0 else 0)
            self.lower_ch2_slider.setValue(lower[1] if len(lower) > 1 else 0)
            self.lower_ch3_slider.setValue(lower[2] if len(lower) > 2 else 0)
            
            self.upper_ch1_slider.setValue(upper[0] if len(upper) > 0 else 255)
            self.upper_ch2_slider.setValue(upper[1] if len(upper) > 1 else 255)
            self.upper_ch3_slider.setValue(upper[2] if len(upper) > 2 else 255)
        
        # 设置检测参数
        self.min_area_spin.setValue(color_config.get('min_area', 100))
        self.max_area_spin.setValue(color_config.get('max_area', 10000))
        self.morphology_check.setChecked(color_config.get('morphology_enabled', True))
    
    def _update_current_roi(self):
        """更新当前ROI数据"""
        if self.current_roi_index >= 0:
            roi_data = self.roi_regions[self.current_roi_index]
            
            # 更新基本信息
            roi_data['name'] = self.roi_name_edit.text()
            roi_data['x'] = self.roi_x_spin.value()
            roi_data['y'] = self.roi_y_spin.value()
            roi_data['width'] = self.roi_w_spin.value()
            roi_data['height'] = self.roi_h_spin.value()
            roi_data['type'] = self.roi_type_combo.currentData()
            
            # 更新列表显示
            item = self.roi_list.item(self.current_roi_index)
            if item:
                item.setText(roi_data['name'])
            
            # 更新颜色配置
            self._update_color_config()
            
            self._emit_roi_updated()
    
    def _update_color_config(self):
        """更新颜色配置"""
        if self.current_roi_index >= 0:
            roi_data = self.roi_regions[self.current_roi_index]
            
            color_config = {
                'color_space': self.color_space_combo.currentData(),
                'detection_method': self.detection_method_combo.currentData(),
                'color_ranges': [{
                    'lower': [
                        self.lower_ch1_slider.value(),
                        self.lower_ch2_slider.value(),
                        self.lower_ch3_slider.value()
                    ],
                    'upper': [
                        self.upper_ch1_slider.value(),
                        self.upper_ch2_slider.value(),
                        self.upper_ch3_slider.value()
                    ]
                }],
                'min_area': self.min_area_spin.value(),
                'max_area': self.max_area_spin.value(),
                'morphology_enabled': self.morphology_check.isChecked()
            }
            
            roi_data['color_config'] = color_config
    
    def _get_default_color_config(self) -> Dict[str, Any]:
        """获取默认颜色配置"""
        return {
            'color_space': 'HSV',
            'detection_method': 'range',
            'color_ranges': [{
                'lower': [0, 0, 0],
                'upper': [255, 255, 255]
            }],
            'min_area': 100,
            'max_area': 10000,
            'morphology_enabled': True
        }
    
    def _update_ui_state(self):
        """更新UI状态"""
        has_roi = self.current_roi_index >= 0
        
        # 启用/禁用相关控件
        self.roi_name_edit.setEnabled(has_roi)
        self.roi_x_spin.setEnabled(has_roi)
        self.roi_y_spin.setEnabled(has_roi)
        self.roi_w_spin.setEnabled(has_roi)
        self.roi_h_spin.setEnabled(has_roi)
        self.roi_type_combo.setEnabled(has_roi)
        
        # 颜色配置控件
        color_enabled = has_roi and (self.current_roi_index < len(self.roi_regions) and 
                                   self.roi_regions[self.current_roi_index].get('type') == 'color')
        
        self.color_space_combo.setEnabled(color_enabled)
        self.detection_method_combo.setEnabled(color_enabled)
        self.lower_ch1_slider.setEnabled(color_enabled)
        self.lower_ch2_slider.setEnabled(color_enabled)
        self.lower_ch3_slider.setEnabled(color_enabled)
        self.upper_ch1_slider.setEnabled(color_enabled)
        self.upper_ch2_slider.setEnabled(color_enabled)
        self.upper_ch3_slider.setEnabled(color_enabled)
        self.min_area_spin.setEnabled(color_enabled)
        self.max_area_spin.setEnabled(color_enabled)
        self.morphology_check.setEnabled(color_enabled)
        self.test_color_btn.setEnabled(color_enabled)
        self.calibrate_color_btn.setEnabled(color_enabled)
    
    def _test_color_detection(self):
        """测试颜色检测"""
        if self.current_roi_index >= 0:
            self.status_message.emit("颜色检测测试功能需要图像输入")
        else:
            QMessageBox.warning(self, "警告", "请先选择ROI区域")
    
    def _calibrate_color(self):
        """颜色校准"""
        if self.current_roi_index >= 0:
            self.status_message.emit("颜色校准功能需要图像输入")
        else:
            QMessageBox.warning(self, "警告", "请先选择ROI区域")
    
    def add_roi_from_rect(self, rect: QRectF):
        """从矩形添加ROI"""
        roi_count = len(self.roi_regions)
        roi_name = f"ROI_{roi_count + 1}"
        
        roi_data = {
            'name': roi_name,
            'x': int(rect.x()),
            'y': int(rect.y()),
            'width': int(rect.width()),
            'height': int(rect.height()),
            'type': 'color',
            'color_config': self._get_default_color_config()
        }
        
        self.roi_regions.append(roi_data)
        
        # 添加到列表
        item = QListWidgetItem(roi_name)
        self.roi_list.addItem(item)
        
        # 选中新添加的ROI
        self.roi_list.setCurrentRow(len(self.roi_regions) - 1)
        
        self.status_message.emit(f"从矩形添加ROI: {roi_name}")
        self._emit_roi_updated()
    
    def _emit_roi_updated(self):
        """发送ROI更新信号"""
        self.roi_updated.emit({
            'regions': self.roi_regions.copy(),
            'current_index': self.current_roi_index
        })
    
    def get_roi_regions(self) -> List[Dict[str, Any]]:
        """获取所有ROI区域"""
        return self.roi_regions.copy()
    
    def set_roi_regions(self, regions: List[Dict[str, Any]]):
        """设置ROI区域列表"""
        self.roi_regions = regions.copy()
        
        # 重建列表
        self.roi_list.clear()
        for roi_data in self.roi_regions:
            item = QListWidgetItem(roi_data.get('name', 'ROI'))
            self.roi_list.addItem(item)
        
        # 重置选择
        self.current_roi_index = -1
        self._update_ui_state()
        
        self._emit_roi_updated() 