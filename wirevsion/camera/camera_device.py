#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机设备模块
封装单个相机设备的操作
"""

import cv2
import numpy as np
from loguru import logger
from typing import Any, Tuple, Optional


class CameraDevice:
    """
    相机设备类
    封装单个相机的连接、断开、参数设置和图像获取
    """
    
    def __init__(self, camera_id: int):
        """
        初始化相机设备
        
        Args:
            camera_id: 相机ID
        """
        self.camera_id = camera_id
        self.cap = None  # OpenCV VideoCapture 对象
        self.is_connected_flag = False
    
    def connect(self) -> bool:
        """
        连接相机设备
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 如果已连接，先断开
            if self.is_connected_flag:
                self.disconnect()
            
            # 创建VideoCapture对象
            self.cap = cv2.VideoCapture(self.camera_id)
            
            # 检查相机是否成功打开
            if not self.cap.isOpened():
                logger.error(f"无法连接相机 {self.camera_id}")
                return False
            
            self.is_connected_flag = True
            logger.info(f"成功连接相机 {self.camera_id}")
            return True
        except Exception as e:
            logger.error(f"连接相机 {self.camera_id} 时出错: {str(e)}")
            return False
    
    def disconnect(self) -> bool:
        """
        断开相机连接
        
        Returns:
            bool: 断开连接是否成功
        """
        try:
            if self.cap is not None:
                self.cap.release()
                self.cap = None
            
            self.is_connected_flag = False
            logger.info(f"断开相机 {self.camera_id} 连接")
            return True
        except Exception as e:
            logger.error(f"断开相机 {self.camera_id} 连接时出错: {str(e)}")
            return False
    
    def is_connected(self) -> bool:
        """
        检查相机是否已连接
        
        Returns:
            bool: 相机是否已连接
        """
        if self.cap is None:
            return False
        return self.is_connected_flag and self.cap.isOpened()
    
    def get_frame(self) -> Tuple[bool, Optional[np.ndarray]]:
        """
        获取一帧图像
        
        Returns:
            Tuple[bool, Optional[np.ndarray]]: (成功标志, 图像数据)
        """
        if not self.is_connected():
            logger.warning(f"相机 {self.camera_id} 未连接")
            return False, None
        
        try:
            ret, frame = self.cap.read()
            if not ret:
                logger.warning(f"无法从相机 {self.camera_id} 获取图像")
                return False, None
            
            return True, frame
        except Exception as e:
            logger.error(f"获取相机 {self.camera_id} 图像时出错: {str(e)}")
            return False, None
    
    def set_property(self, property_id: int, value: Any) -> bool:
        """
        设置相机属性
        
        Args:
            property_id: OpenCV 相机属性ID
            value: 属性值
            
        Returns:
            bool: 设置是否成功
        """
        if not self.is_connected():
            logger.warning(f"相机 {self.camera_id} 未连接")
            return False
        
        try:
            result = self.cap.set(property_id, value)
            
            # 检查设置是否成功
            if result:
                logger.debug(f"设置相机 {self.camera_id} 属性 {property_id} 为 {value}")
            else:
                logger.warning(f"设置相机 {self.camera_id} 属性 {property_id} 为 {value} 失败")
            
            return result
        except Exception as e:
            logger.error(f"设置相机 {self.camera_id} 属性时出错: {str(e)}")
            return False
    
    def get_property(self, property_id: int) -> Optional[Any]:
        """
        获取相机属性
        
        Args:
            property_id: OpenCV 相机属性ID
            
        Returns:
            Optional[Any]: 属性值，如果获取失败则返回 None
        """
        if not self.is_connected():
            logger.warning(f"相机 {self.camera_id} 未连接")
            return None
        
        try:
            value = self.cap.get(property_id)
            return value
        except Exception as e:
            logger.error(f"获取相机 {self.camera_id} 属性时出错: {str(e)}")
            return None
