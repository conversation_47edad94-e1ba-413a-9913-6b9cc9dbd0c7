#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机管理器模块
负责管理相机设备的连接、断开和参数设置
"""

import cv2
from loguru import logger
from typing import Dict, List, Optional, Any, Tuple

from wirevsion.camera.camera_device import CameraDevice


class CameraManager:
    """
    相机管理器类
    管理相机设备的连接、断开和参数设置
    """
    
    def __init__(self):
        """初始化相机管理器"""
        self.cameras = {}  # 存储已连接的相机设备 {camera_id: CameraDevice}
        self.current_camera_id = None  # 当前使用的相机ID
    
    def get_available_cameras(self) -> List[Dict[str, Any]]:
        """
        获取系统中可用的相机列表
        
        Returns:
            List[Dict[str, Any]]: 相机信息列表，每个字典包含 id 和 name
        """
        available_cameras = []
        # 尝试打开系统中的相机设备
        # OpenCV 通常使用索引 0, 1, 2... 来访问相机设备
        # 这里尝试打开前 5 个索引，可以根据实际情况调整
        for i in range(5):
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                # 相机可用
                cap.release()  # 立即释放相机，避免占用
                available_cameras.append({
                    'id': i,
                    'name': f'相机 {i}'
                })
        
        logger.info(f"找到 {len(available_cameras)} 个可用相机")
        return available_cameras
    
    def connect_camera(self, camera_id: int) -> bool:
        """
        连接指定相机
        
        Args:
            camera_id: 相机ID
        
        Returns:
            bool: 连接是否成功
        """
        if camera_id in self.cameras and self.cameras[camera_id].is_connected():
            logger.info(f"相机 {camera_id} 已经连接")
            return True
        
        try:
            # 创建相机设备实例并连接
            camera = CameraDevice(camera_id)
            success = camera.connect()
            
            if success:
                self.cameras[camera_id] = camera
                self.current_camera_id = camera_id
                logger.info(f"成功连接相机 {camera_id}")
                return True
            else:
                logger.error(f"无法连接相机 {camera_id}")
                return False
        except Exception as e:
            logger.error(f"连接相机 {camera_id} 时出错: {str(e)}")
            return False
    
    def disconnect_camera(self, camera_id: Optional[int] = None) -> bool:
        """
        断开指定相机的连接
        如果未指定相机ID，则断开当前相机
        
        Args:
            camera_id: 相机ID，默认为当前相机
        
        Returns:
            bool: 断开连接是否成功
        """
        camera_id = camera_id if camera_id is not None else self.current_camera_id
        
        if camera_id is None:
            logger.warning("没有指定相机ID，且没有当前相机")
            return False
        
        if camera_id not in self.cameras:
            logger.warning(f"相机 {camera_id} 未连接")
            return False
        
        try:
            success = self.cameras[camera_id].disconnect()
            if success:
                if camera_id == self.current_camera_id:
                    self.current_camera_id = None
                del self.cameras[camera_id]
                logger.info(f"成功断开相机 {camera_id}")
                return True
            else:
                logger.error(f"断开相机 {camera_id} 失败")
                return False
        except Exception as e:
            logger.error(f"断开相机 {camera_id} 时出错: {str(e)}")
            return False
    
    def get_frame(self, camera_id: Optional[int] = None) -> Tuple[bool, Optional[Any]]:
        """
        从指定相机获取一帧图像
        如果未指定相机ID，则使用当前相机
        
        Args:
            camera_id: 相机ID，默认为当前相机
            
        Returns:
            Tuple[bool, Optional[Any]]: (成功标志, 图像数据)
        """
        camera_id = camera_id if camera_id is not None else self.current_camera_id
        
        if camera_id is None:
            logger.warning("没有指定相机ID，且没有当前相机")
            return False, None
        
        if camera_id not in self.cameras:
            logger.warning(f"相机 {camera_id} 未连接")
            return False, None
        
        return self.cameras[camera_id].get_frame()
    
    def set_camera_property(self, property_id: int, value: Any, camera_id: Optional[int] = None) -> bool:
        """
        设置相机属性
        如果未指定相机ID，则使用当前相机
        
        Args:
            property_id: OpenCV 相机属性ID
            value: 属性值
            camera_id: 相机ID，默认为当前相机
            
        Returns:
            bool: 设置是否成功
        """
        camera_id = camera_id if camera_id is not None else self.current_camera_id
        
        if camera_id is None:
            logger.warning("没有指定相机ID，且没有当前相机")
            return False
        
        if camera_id not in self.cameras:
            logger.warning(f"相机 {camera_id} 未连接")
            return False
        
        return self.cameras[camera_id].set_property(property_id, value)
    
    def apply_camera_settings(self, settings: Dict[str, Any], camera_id: Optional[int] = None) -> bool:
        """
        应用相机设置
        
        Args:
            settings: 相机设置参数字典
            camera_id: 相机ID，默认为当前相机
            
        Returns:
            bool: 设置是否成功
        """
        camera_id = camera_id if camera_id is not None else self.current_camera_id
        
        if camera_id is None:
            logger.warning("没有指定相机ID，且没有当前相机")
            return False
        
        if camera_id not in self.cameras:
            logger.warning(f"相机 {camera_id} 未连接")
            return False
        
        try:
            camera = self.cameras[camera_id]
            
            # 设置分辨率
            if 'resolution' in settings:
                width, height = map(int, settings['resolution'].split('x'))
                camera.set_property(cv2.CAP_PROP_FRAME_WIDTH, width)
                camera.set_property(cv2.CAP_PROP_FRAME_HEIGHT, height)
            
            # 设置帧率
            if 'fps' in settings:
                camera.set_property(cv2.CAP_PROP_FPS, settings['fps'])
            
            # 设置自动曝光
            if 'auto_exposure' in settings:
                if settings['auto_exposure']:
                    # 自动曝光模式
                    camera.set_property(cv2.CAP_PROP_AUTO_EXPOSURE, 3)  # 3 = 自动曝光
                else:
                    # 手动曝光模式
                    camera.set_property(cv2.CAP_PROP_AUTO_EXPOSURE, 1)  # 1 = 手动曝光
                    if 'exposure' in settings:
                        camera.set_property(cv2.CAP_PROP_EXPOSURE, settings['exposure'])
            
            # 设置对比度
            if 'contrast' in settings:
                camera.set_property(cv2.CAP_PROP_CONTRAST, settings['contrast'])
            
            # 设置亮度
            if 'brightness' in settings:
                camera.set_property(cv2.CAP_PROP_BRIGHTNESS, settings['brightness'])
            
            logger.info(f"成功应用相机 {camera_id} 设置")
            return True
        except Exception as e:
            logger.error(f"应用相机 {camera_id} 设置时出错: {str(e)}")
            return False
    
    def close_all_cameras(self):
        """关闭所有已连接的相机"""
        for camera_id in list(self.cameras.keys()):
            self.disconnect_camera(camera_id)
        
        self.current_camera_id = None
        logger.info("已关闭所有相机")
    
    # 以下方法为兼容性方法，与原有方法保持一致
    def open_camera(self, camera_id) -> bool:
        """
        打开相机（兼容性方法）
        
        Args:
            camera_id: 相机ID，可以是字符串或整数
            
        Returns:
            bool: 是否成功打开
        """
        # 处理camera_id格式，可能是字符串形式的
        if isinstance(camera_id, str):
            # 如果是字符串，尝试提取数字ID
            try:
                # 处理 "相机 0 (0)" 这样的格式
                if '(' in camera_id and ')' in camera_id:
                    camera_id = int(camera_id.split('(')[1].split(')')[0])
                else:
                    # 尝试直接转换为整数
                    camera_id = int(camera_id)
            except (ValueError, IndexError):
                logger.error(f"无效的相机ID格式: {camera_id}")
                return False
        
        return self.connect_camera(camera_id)
    
    def close_camera(self, camera_id=None) -> bool:
        """
        关闭相机（兼容性方法）
        
        Args:
            camera_id: 相机ID，默认为当前相机
            
        Returns:
            bool: 是否成功关闭
        """
        return self.disconnect_camera(camera_id)
    
    def capture(self, camera_id=None) -> tuple:
        """
        捕获图像（兼容性方法）
        
        Args:
            camera_id: 相机ID，默认为当前相机
            
        Returns:
            tuple: (是否成功, 图像数据)
        """
        return self.get_frame(camera_id)
    
    def set_format(self, pixel_format: str, camera_id=None) -> bool:
        """
        设置像素格式（兼容性方法）
        
        Args:
            pixel_format: 像素格式字符串
            camera_id: 相机ID，默认为当前相机
            
        Returns:
            bool: 是否成功设置
        """
        # 注意：OpenCV通常不直接支持设置像素格式
        # 这里主要是为了兼容性，实际的格式转换需要在图像获取后进行
        logger.info(f"设置像素格式: {pixel_format}")
        
        # 对于OpenCV相机，我们可以设置一些相关属性
        camera_id = camera_id if camera_id is not None else self.current_camera_id
        
        if camera_id is None or camera_id not in self.cameras:
            logger.warning("相机未连接，无法设置像素格式")
            return False
        
        # 根据像素格式设置相应的相机属性
        try:
            camera = self.cameras[camera_id]
            
            if pixel_format in ["MONO8", "MONO12"]:
                # 对于单色格式，可以尝试设置为灰度模式
                # 注意：大多数USB相机不支持直接设置为单色模式
                pass
            elif pixel_format in ["RGB8", "BGR8"]:
                # 对于彩色格式，确保相机处于彩色模式
                pass
            
            logger.info(f"像素格式设置完成: {pixel_format}")
            return True
            
        except Exception as e:
            logger.error(f"设置像素格式失败: {str(e)}")
            return False
    
    def is_opened(self, camera_id=None) -> bool:
        """
        检查相机是否已打开（兼容性方法）
        
        Args:
            camera_id: 相机ID，默认为当前相机
            
        Returns:
            bool: 相机是否已打开
        """
        camera_id = camera_id if camera_id is not None else self.current_camera_id
        
        if camera_id is None:
            return False
        
        return camera_id in self.cameras and self.cameras[camera_id].is_connected()