#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主程序入口模块
启动应用并显示现代化UI主窗口
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon, QPixmap, QFontDatabase, QFont, QFontInfo
from PyQt5.QtCore import QDir, Qt
from loguru import logger
from pathlib import Path

from wirevsion.ui.modern_main_application import ModernMainApplication, THEME_COLORS
from wirevsion.config.config_manager import ConfigManager
from wirevsion.utils.memory_manager import initialize_memory_management, cleanup_memory_management


def setup_environment():
    """设置环境变量和资源路径"""
    # 获取应用程序所在目录 (假设main.py在wirevsion目录下)
    app_path = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(app_path) # 项目根目录是wirevsion的上级目录
    
    # 设置资源目录
    resources_path = os.path.join(project_root, "resources")
    icons_path = os.path.join(resources_path, "icons")
    styles_path = os.path.join(resources_path, "styles") # 旧样式表目录，可能不再主要使用
    
    QDir.addSearchPath("icons", icons_path)
    QDir.addSearchPath("styles", styles_path)
    os.environ["WIREVSION_HOME"] = project_root
    
    logger.info(f"项目根目录: {project_root}")
    logger.info(f"资源路径: {resources_path}")


def setup_logging():
    """设置日志系统"""
    project_root = os.environ.get("WIREVSION_HOME", os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    log_dir = Path(project_root) / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logger.remove() 
    logger.add(
        sys.stderr, level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    log_file = log_dir / "wirevision_main.log"
    logger.add(
        log_file, level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB", retention="7 days", compression="zip"
    )
    logger.info("日志系统初始化完成")


def setup_modern_styles(app):
    """设置应用程序的现代化样式 (基于THEME_COLORS)"""
    app_bg_color = THEME_COLORS["dark_bg_app"]
    text_color_primary = THEME_COLORS["text_primary"]
    tooltip_bg = THEME_COLORS["dark_bg_input"]
    tooltip_border = THEME_COLORS["dark_border_primary"]

    app.setStyleSheet(f"""
        QWidget {{
            color: {text_color_primary};
            /* font-family: "Segoe UI", "Arial", sans-serif; */ /* 字体在main中设置 */
        }}
        QMainWindow, QDialog {{
            /* background-color: {app_bg_color}; */ /* 主窗口和对话框背景由其自身控制 */
        }}
        QToolTip {{
            background-color: {tooltip_bg};
            color: {text_color_primary};
            border: 1px solid {tooltip_border};
            padding: 5px;
            border-radius: 4px;
            font-size: 12px;
        }}
        QMessageBox {{
            background-color: {tooltip_bg};
        }}
        QMessageBox QLabel {{
            color: {text_color_primary};
            font-size: 13px;
        }}
        QMenu {{
            background-color: {THEME_COLORS["dark_bg_content"]};
            color: {THEME_COLORS["text_primary"]};
            border: 1px solid {THEME_COLORS["dark_border_primary"]};
            padding: 5px;
            border-radius: 6px;
        }}
        QMenu::item {{
            padding: 8px 24px;
            border-radius: 4px;
        }}
        QMenu::item:selected {{
            background-color: {THEME_COLORS["dark_surface_selected"]};
            color: {THEME_COLORS["text_on_dark_selected_bg"]};
        }}
        QMenu::separator {{
            height: 1px;
            background: {THEME_COLORS["dark_border_primary"]};
            margin: 4px 8px;
        }}
        QScrollBar:vertical {{
            background: {THEME_COLORS["dark_bg_sidebar"]};
            width: 10px; margin: 0px; border-radius: 5px;
        }}
        QScrollBar::handle:vertical {{
            background: {THEME_COLORS["dark_border_primary"]};
            min-height: 25px; border-radius: 5px;
        }}
        QScrollBar::handle:vertical:hover {{ background: {THEME_COLORS["secondary"]}; }}
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{ border: none; background: none; height: 0px; }}
        QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{ background: none; }}
        QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{ background: none; }}
        QScrollBar:horizontal {{
            background: {THEME_COLORS["dark_bg_sidebar"]};
            height: 10px; margin: 0px; border-radius: 5px;
        }}
        QScrollBar::handle:horizontal {{
            background: {THEME_COLORS["dark_border_primary"]};
            min-width: 25px; border-radius: 5px;
        }}
        QScrollBar::handle:horizontal:hover {{ background: {THEME_COLORS["secondary"]}; }}
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{ border: none; background: none; width: 0px; }}
        QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{ background: none; }}
        QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{ background: none; }}
    """)
    logger.info("已应用现代化全局样式")


def setup_application_properties(app):
    """设置应用程序属性"""
    app.setApplicationName("WireVision")
    app.setApplicationDisplayName("WireVision - 智能视觉检测系统")
    app.setApplicationVersion("1.0.0-modern") # 更新版本号
    app.setOrganizationName("WireVisionTech")
    app.setOrganizationDomain("wirevision.tech") # 示例域名
    
    icon_path = QDir("icons:").filePath("app_icon.png") # 确保你有这个图标或替换
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
        logger.info(f"已设置应用程序图标: {icon_path}")
    else:
        logger.warning(f"应用程序图标未找到: {icon_path}")


def handle_exception(exc_type, exc_value, exc_traceback):
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    logger.critical("未捕获的全局异常", exc_info=(exc_type, exc_value, exc_traceback))
    # 可以选择在这里显示一个简单的错误消息框，但ModernMainApplication内部已有自己的错误处理


def main():
    """主函数"""
    try:
        sys.excepthook = handle_exception
        setup_environment()
        setup_logging()
        
        logger.info("=" * 50)
        logger.info("WireVision Modern UI 启动")
        logger.info("=" * 50)
        
        app = QApplication(sys.argv)
        
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        default_font = QFont("Segoe UI", 9)
        if not QFontInfo(default_font).exactMatch(): default_font = QFont("Arial", 10)
        app.setFont(default_font)
        
        setup_application_properties(app)
        setup_modern_styles(app) # 应用新的样式函数
        
        # 配置管理器现在由 ModernMainApplication 内部处理
        # logger.info("初始化配置管理器...")
        # config_manager = ConfigManager()
        
        logger.info("创建 ModernMainApplication 实例...")
        window = ModernMainApplication() # ModernMainApplication 会处理其内部依赖
        
        window.show()
        logger.info("ModernMainApplication 已显示")
        
        logger.info("应用程序事件循环开始")
        exit_code = app.exec_()
        
        logger.info(f"应用程序退出，退出代码: {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.critical(f"应用程序启动失败: {str(e)}", exc_info=True)
        return 1
    finally:
        try:
            cleanup_memory_management() # 确保这个函数仍然相关和存在
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {str(e)}")


if __name__ == "__main__":
    sys.exit(main()) 