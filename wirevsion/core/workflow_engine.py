"""
工作流执行引擎

支持：
- 多线程并行执行
- 数据流传递
- 错误处理和状态管理
- 实时状态更新
- 结果收集和分析
"""

import time
import uuid
import threading
from queue import Queue, Empty
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future

import numpy as np
from loguru import logger
from PyQt6.QtCore import QObject, pyqtSignal, QTimer

# 导入算法注册表
try:
    from ..algorithms import algorithm_registry
    from ..algorithms.base_algorithm import BaseAlgorithm, AlgorithmConfig, AlgorithmResult
except ImportError:
    logger.warning("无法导入算法模块")
    algorithm_registry = None
    BaseAlgorithm = None
    AlgorithmConfig = None
    AlgorithmResult = None


class NodeStatus(Enum):
    """节点状态枚举"""
    PENDING = "pending"       # 等待执行
    READY = "ready"          # 准备就绪
    RUNNING = "running"      # 正在执行
    SUCCESS = "success"      # 执行成功
    ERROR = "error"          # 执行错误
    SKIPPED = "skipped"      # 跳过执行


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    IDLE = "idle"           # 空闲状态
    RUNNING = "running"     # 运行中
    COMPLETED = "completed" # 完成
    FAILED = "failed"       # 失败
    STOPPED = "stopped"     # 停止


@dataclass
class NodeExecutionResult:
    """节点执行结果"""
    node_id: str
    status: NodeStatus
    result: Optional[AlgorithmResult] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    memory_usage: Optional[float] = None


@dataclass
class WorkflowExecutionContext:
    """工作流执行上下文"""
    workflow_id: str
    start_time: float
    current_step: int = 0
    total_steps: int = 0
    node_results: Dict[str, NodeExecutionResult] = field(default_factory=dict)
    node_data: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # 节点输出数据
    global_data: Dict[str, Any] = field(default_factory=dict)  # 全局共享数据
    error_log: List[str] = field(default_factory=list)


class WorkflowNode:
    """工作流节点"""
    
    def __init__(self, config: Dict[str, Any]):
        self.node_id = config["node_id"]
        self.category = config["category"]
        self.algorithm = config["algorithm"]
        self.metadata = config["metadata"]
        self.parameters = config.get("parameters", {})
        self.position = config.get("position", {"x": 0, "y": 0})
        
        # 连接信息
        self.input_connections = {}  # connection_name -> source_node_info
        self.output_connections = {}  # connection_name -> target_node_info list
        
        # 执行状态
        self.status = NodeStatus.PENDING
        self.result = None
        self.error = None
        
        # 算法实例
        self.algorithm_instance = None
        self._init_algorithm()
    
    def _init_algorithm(self):
        """初始化算法实例"""
        if not algorithm_registry:
            return
        
        try:
            algorithm_class = algorithm_registry.get_algorithm_class(self.category, self.algorithm)
            if algorithm_class:
                self.algorithm_instance = algorithm_class()
                logger.debug(f"算法实例化成功: {self.node_id}")
            else:
                logger.error(f"找不到算法类: {self.category}.{self.algorithm}")
        except Exception as e:
            logger.error(f"算法实例化失败: {e}")
    
    def add_input_connection(self, connection_name: str, source_node_id: str, 
                           source_output: str, data_type: str):
        """添加输入连接"""
        self.input_connections[connection_name] = {
            "source_node_id": source_node_id,
            "source_output": source_output,
            "data_type": data_type
        }
    
    def add_output_connection(self, connection_name: str, target_node_id: str,
                            target_input: str, data_type: str):
        """添加输出连接"""
        if connection_name not in self.output_connections:
            self.output_connections[connection_name] = []
        
        self.output_connections[connection_name].append({
            "target_node_id": target_node_id,
            "target_input": target_input,
            "data_type": data_type
        })
    
    def is_ready_to_execute(self, context: WorkflowExecutionContext) -> bool:
        """检查是否准备好执行"""
        # 检查所有必需的输入是否已就绪
        for conn_name, conn_info in self.input_connections.items():
            source_node_id = conn_info["source_node_id"]
            
            # 检查源节点是否已成功执行
            if source_node_id not in context.node_results:
                return False
            
            source_result = context.node_results[source_node_id]
            if source_result.status != NodeStatus.SUCCESS:
                return False
            
            # 检查输出数据是否存在
            if source_node_id not in context.node_data:
                return False
        
        return True
    
    def prepare_inputs(self, context: WorkflowExecutionContext) -> Dict[str, Any]:
        """准备输入数据"""
        inputs = {}
        
        for conn_name, conn_info in self.input_connections.items():
            source_node_id = conn_info["source_node_id"]
            source_output = conn_info["source_output"]
            
            if source_node_id in context.node_data:
                source_data = context.node_data[source_node_id]
                if source_output in source_data:
                    inputs[conn_name] = source_data[source_output]
                else:
                    logger.warning(f"源节点 {source_node_id} 缺少输出 {source_output}")
        
        return inputs
    
    def execute(self, context: WorkflowExecutionContext) -> NodeExecutionResult:
        """执行节点"""
        start_time = time.time()
        
        try:
            self.status = NodeStatus.RUNNING
            
            # 检查算法实例
            if not self.algorithm_instance:
                raise Exception(f"算法实例未初始化: {self.algorithm}")
            
            # 准备输入数据
            inputs = self.prepare_inputs(context)
            
            # 创建算法配置
            config = AlgorithmConfig(
                algorithm_id=self.node_id,
                parameters=self.parameters,
                roi_regions=[],  # TODO: 支持ROI
                metadata={}
            )
            
            # 执行算法
            logger.info(f"开始执行节点: {self.node_id} ({self.metadata.get('display_name')})")
            result = self.algorithm_instance.execute(inputs, config)
            
            if result.success:
                self.status = NodeStatus.SUCCESS
                self.result = result
                
                # 保存输出数据
                output_data = {}
                if result.image is not None:
                    output_data["image"] = result.image
                if result.data:
                    output_data.update(result.data)
                
                context.node_data[self.node_id] = output_data
                
                logger.info(f"节点执行成功: {self.node_id}, 耗时: {result.execution_time:.3f}s")
                
            else:
                self.status = NodeStatus.ERROR
                self.error = result.error_message
                logger.error(f"节点执行失败: {self.node_id}, 错误: {result.error_message}")
            
            execution_time = time.time() - start_time
            
            return NodeExecutionResult(
                node_id=self.node_id,
                status=self.status,
                result=result if result.success else None,
                error=self.error,
                execution_time=execution_time,
                start_time=start_time,
                end_time=time.time()
            )
            
        except Exception as e:
            self.status = NodeStatus.ERROR
            self.error = str(e)
            execution_time = time.time() - start_time
            
            logger.error(f"节点执行异常: {self.node_id}, 错误: {e}")
            
            return NodeExecutionResult(
                node_id=self.node_id,
                status=NodeStatus.ERROR,
                error=str(e),
                execution_time=execution_time,
                start_time=start_time,
                end_time=time.time()
            )


class WorkflowEngine(QObject):
    """工作流执行引擎"""
    
    # 信号定义
    workflow_started = pyqtSignal(str)  # workflow_id
    workflow_completed = pyqtSignal(str, dict)  # workflow_id, results
    workflow_failed = pyqtSignal(str, str)  # workflow_id, error
    workflow_stopped = pyqtSignal(str)  # workflow_id
    
    node_started = pyqtSignal(str, str)  # workflow_id, node_id
    node_completed = pyqtSignal(str, str, object)  # workflow_id, node_id, result
    node_failed = pyqtSignal(str, str, str)  # workflow_id, node_id, error
    
    progress_updated = pyqtSignal(str, int, int)  # workflow_id, current, total
    status_updated = pyqtSignal(str, str)  # workflow_id, status
    
    def __init__(self, max_workers: int = 4):
        super().__init__()
        
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 执行状态
        self.running_workflows: Dict[str, WorkflowExecutionContext] = {}
        self.workflow_nodes: Dict[str, Dict[str, WorkflowNode]] = {}  # workflow_id -> nodes
        self.workflow_status: Dict[str, WorkflowStatus] = {}
        
        # 执行队列和线程
        self.execution_queue = Queue()
        self.stop_flags: Dict[str, bool] = {}  # workflow_id -> stop_flag
        
        logger.info(f"工作流引擎初始化完成，最大工作线程数: {max_workers}")
    
    def load_workflow(self, workflow_config: Dict[str, Any]) -> str:
        """加载工作流配置"""
        workflow_id = str(uuid.uuid4())
        
        try:
            # 解析节点
            nodes = {}
            for node_config in workflow_config.get("nodes", []):
                node = WorkflowNode(node_config)
                nodes[node.node_id] = node
            
            # 解析连接
            connections = workflow_config.get("connections", [])
            for conn in connections:
                start_node_id = conn["start_node"]
                end_node_id = conn["end_node"]
                start_output = conn["start_connection"]
                end_input = conn["end_connection"]
                data_type = conn.get("data_type", "any")
                
                if start_node_id in nodes and end_node_id in nodes:
                    # 添加输出连接到源节点
                    nodes[start_node_id].add_output_connection(
                        start_output, end_node_id, end_input, data_type
                    )
                    
                    # 添加输入连接到目标节点
                    nodes[end_node_id].add_input_connection(
                        end_input, start_node_id, start_output, data_type
                    )
            
            # 保存节点信息
            self.workflow_nodes[workflow_id] = nodes
            self.workflow_status[workflow_id] = WorkflowStatus.IDLE
            
            logger.info(f"工作流加载成功: {workflow_id}, 节点数: {len(nodes)}, 连接数: {len(connections)}")
            return workflow_id
            
        except Exception as e:
            logger.error(f"工作流加载失败: {e}")
            raise e
    
    def execute_workflow(self, workflow_id: str, 
                        input_data: Optional[Dict[str, Any]] = None) -> bool:
        """执行工作流"""
        if workflow_id not in self.workflow_nodes:
            logger.error(f"工作流不存在: {workflow_id}")
            return False
        
        if workflow_id in self.running_workflows:
            logger.warning(f"工作流正在运行: {workflow_id}")
            return False
        
        try:
            # 创建执行上下文
            context = WorkflowExecutionContext(
                workflow_id=workflow_id,
                start_time=time.time(),
                total_steps=len(self.workflow_nodes[workflow_id])
            )
            
            # 设置全局输入数据
            if input_data:
                context.global_data.update(input_data)
            
            self.running_workflows[workflow_id] = context
            self.workflow_status[workflow_id] = WorkflowStatus.RUNNING
            self.stop_flags[workflow_id] = False
            
            # 发送开始信号
            self.workflow_started.emit(workflow_id)
            
            # 启动执行线程
            execution_thread = threading.Thread(
                target=self._execute_workflow_thread,
                args=(workflow_id,),
                daemon=True
            )
            execution_thread.start()
            
            logger.info(f"工作流开始执行: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"启动工作流失败: {e}")
            self.workflow_failed.emit(workflow_id, str(e))
            return False
    
    def stop_workflow(self, workflow_id: str):
        """停止工作流执行"""
        if workflow_id in self.stop_flags:
            self.stop_flags[workflow_id] = True
            logger.info(f"请求停止工作流: {workflow_id}")
    
    def _execute_workflow_thread(self, workflow_id: str):
        """工作流执行线程"""
        try:
            context = self.running_workflows[workflow_id]
            nodes = self.workflow_nodes[workflow_id]
            
            # 拓扑排序：找到执行顺序
            execution_order = self._topological_sort(nodes)
            
            if not execution_order:
                raise Exception("工作流存在循环依赖，无法执行")
            
            context.total_steps = len(execution_order)
            
            # 逐个执行节点
            for i, node_id in enumerate(execution_order):
                # 检查停止标志
                if self.stop_flags.get(workflow_id, False):
                    self.workflow_status[workflow_id] = WorkflowStatus.STOPPED
                    self.workflow_stopped.emit(workflow_id)
                    return
                
                node = nodes[node_id]
                context.current_step = i + 1
                
                # 更新进度
                self.progress_updated.emit(workflow_id, context.current_step, context.total_steps)
                
                # 等待节点准备就绪
                max_wait_time = 30.0  # 最大等待时间
                wait_start = time.time()
                
                while not node.is_ready_to_execute(context):
                    if time.time() - wait_start > max_wait_time:
                        raise Exception(f"节点 {node_id} 等待输入数据超时")
                    
                    if self.stop_flags.get(workflow_id, False):
                        return
                    
                    time.sleep(0.1)
                
                # 发送节点开始信号
                self.node_started.emit(workflow_id, node_id)
                
                # 执行节点
                result = node.execute(context)
                context.node_results[node_id] = result
                
                # 发送节点完成信号
                if result.status == NodeStatus.SUCCESS:
                    self.node_completed.emit(workflow_id, node_id, result)
                else:
                    self.node_failed.emit(workflow_id, node_id, result.error or "未知错误")
                    
                    # 如果节点失败，整个工作流失败
                    self.workflow_status[workflow_id] = WorkflowStatus.FAILED
                    self.workflow_failed.emit(workflow_id, f"节点 {node_id} 执行失败: {result.error}")
                    return
            
            # 工作流完成
            self.workflow_status[workflow_id] = WorkflowStatus.COMPLETED
            
            # 收集结果
            workflow_results = {
                "execution_time": time.time() - context.start_time,
                "total_steps": context.total_steps,
                "node_results": {nid: result for nid, result in context.node_results.items()},
                "final_outputs": context.node_data,
                "status": "completed"
            }
            
            self.workflow_completed.emit(workflow_id, workflow_results)
            logger.info(f"工作流执行完成: {workflow_id}")
            
        except Exception as e:
            logger.error(f"工作流执行失败: {workflow_id}, 错误: {e}")
            self.workflow_status[workflow_id] = WorkflowStatus.FAILED
            self.workflow_failed.emit(workflow_id, str(e))
        
        finally:
            # 清理资源
            if workflow_id in self.running_workflows:
                del self.running_workflows[workflow_id]
            if workflow_id in self.stop_flags:
                del self.stop_flags[workflow_id]
    
    def _topological_sort(self, nodes: Dict[str, WorkflowNode]) -> List[str]:
        """拓扑排序，确定节点执行顺序"""
        # 计算每个节点的入度
        in_degree = {node_id: 0 for node_id in nodes}
        
        for node_id, node in nodes.items():
            for conn_info in node.input_connections.values():
                source_node_id = conn_info["source_node_id"]
                if source_node_id in in_degree:
                    in_degree[node_id] += 1
        
        # 找到入度为0的节点
        queue = [node_id for node_id, degree in in_degree.items() if degree == 0]
        result = []
        
        while queue:
            current = queue.pop(0)
            result.append(current)
            
            # 更新相邻节点的入度
            for conn_name, conn_targets in nodes[current].output_connections.items():
                for target_info in conn_targets:
                    target_node_id = target_info["target_node_id"]
                    if target_node_id in in_degree:
                        in_degree[target_node_id] -= 1
                        if in_degree[target_node_id] == 0:
                            queue.append(target_node_id)
        
        # 检查是否存在循环依赖
        if len(result) != len(nodes):
            logger.error("工作流存在循环依赖")
            return []
        
        return result
    
    def get_workflow_status(self, workflow_id: str) -> Optional[WorkflowStatus]:
        """获取工作流状态"""
        return self.workflow_status.get(workflow_id)
    
    def get_workflow_progress(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流进度"""
        if workflow_id not in self.running_workflows:
            return None
        
        context = self.running_workflows[workflow_id]
        return {
            "current_step": context.current_step,
            "total_steps": context.total_steps,
            "progress": context.current_step / context.total_steps if context.total_steps > 0 else 0,
            "execution_time": time.time() - context.start_time,
            "completed_nodes": len([r for r in context.node_results.values() if r.status == NodeStatus.SUCCESS]),
            "failed_nodes": len([r for r in context.node_results.values() if r.status == NodeStatus.ERROR])
        }
    
    def get_node_status(self, workflow_id: str, node_id: str) -> Optional[NodeStatus]:
        """获取节点状态"""
        if workflow_id not in self.workflow_nodes:
            return None
        
        if node_id not in self.workflow_nodes[workflow_id]:
            return None
        
        return self.workflow_nodes[workflow_id][node_id].status
    
    def get_node_result(self, workflow_id: str, node_id: str) -> Optional[NodeExecutionResult]:
        """获取节点执行结果"""
        if workflow_id not in self.running_workflows:
            return None
        
        context = self.running_workflows[workflow_id]
        return context.node_results.get(node_id)
    
    def get_workflow_results(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """获取工作流结果"""
        if workflow_id not in self.running_workflows:
            return None
        
        context = self.running_workflows[workflow_id]
        return {
            "node_data": context.node_data,
            "node_results": context.node_results,
            "global_data": context.global_data,
            "error_log": context.error_log
        }
    
    def cleanup_workflow(self, workflow_id: str):
        """清理工作流资源"""
        if workflow_id in self.workflow_nodes:
            del self.workflow_nodes[workflow_id]
        if workflow_id in self.workflow_status:
            del self.workflow_status[workflow_id]
        if workflow_id in self.running_workflows:
            del self.running_workflows[workflow_id]
        if workflow_id in self.stop_flags:
            del self.stop_flags[workflow_id]
        
        logger.info(f"工作流资源已清理: {workflow_id}")
    
    def shutdown(self):
        """关闭引擎"""
        # 停止所有运行中的工作流
        for workflow_id in list(self.stop_flags.keys()):
            self.stop_workflow(workflow_id)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info("工作流引擎已关闭") 