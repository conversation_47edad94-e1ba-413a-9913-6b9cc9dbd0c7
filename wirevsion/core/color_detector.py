#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
颜色检测器
提供精确的颜色检测和分析功能
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from loguru import logger


class ColorDetector:
    """
    颜色检测器
    
    支持多种颜色空间和检测方法
    """
    
    def __init__(self):
        """初始化颜色检测器"""
        # 默认配置
        self.config = {
            'color_space': 'HSV',        # 颜色空间: HSV, RGB, LAB
            'detection_method': 'range',  # 检测方法: range, threshold, statistical
            'morphology_enabled': True,   # 形态学处理
            'noise_reduction': True,      # 噪声降低
            'min_area': 100,             # 最小区域面积
            'max_area': 10000            # 最大区域面积
        }
        
        logger.info("颜色检测器初始化完成")
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置检测配置
        
        Args:
            config: 配置字典
        """
        self.config.update(config)
        logger.info(f"更新颜色检测配置: {config}")
    
    def detect(self, image: np.ndarray, color_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行颜色检测
        
        Args:
            image: 输入图像
            color_config: 颜色检测配置
            
        Returns:
            Dict[str, Any]: 检测结果
        """
        if image is None or image.size == 0:
            logger.error("输入图像为空")
            return {'detected': False, 'error': '输入图像为空'}
        
        try:
            # 合并配置
            combined_config = {**self.config, **color_config}
            
            # 根据检测方法执行检测
            method = combined_config.get('detection_method', 'range')
            
            if method == 'range':
                result = self._range_detection(image, combined_config)
            elif method == 'threshold':
                result = self._threshold_detection(image, combined_config)
            elif method == 'statistical':
                result = self._statistical_detection(image, combined_config)
            else:
                logger.error(f"未知的检测方法: {method}")
                return {'detected': False, 'error': f'未知的检测方法: {method}'}
            
            logger.debug(f"颜色检测完成: 方法={method}, 结果={result.get('detected', False)}")
            return result
            
        except Exception as e:
            error_msg = f"颜色检测出错: {str(e)}"
            logger.error(error_msg)
            return {'detected': False, 'error': error_msg}
    
    def _range_detection(self, image: np.ndarray, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于颜色范围的检测
        
        Args:
            image: 输入图像
            config: 检测配置
            
        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            # 获取颜色范围
            color_ranges = config.get('color_ranges', [])
            if not color_ranges:
                return {'detected': False, 'error': '未设置颜色范围'}
            
            # 转换颜色空间
            color_space = config.get('color_space', 'HSV')
            converted_image = self._convert_color_space(image, color_space)
            
            # 创建组合掩码
            combined_mask = np.zeros(converted_image.shape[:2], dtype=np.uint8)
            
            for color_range in color_ranges:
                lower = np.array(color_range['lower'])
                upper = np.array(color_range['upper'])
                
                # 创建单个颜色的掩码
                mask = cv2.inRange(converted_image, lower, upper)
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # 后处理
            processed_mask = self._post_process_mask(combined_mask, config)
            
            # 分析结果
            result = self._analyze_detection_result(processed_mask, config)
            result['mask'] = processed_mask
            result['color_space'] = color_space
            
            return result
            
        except Exception as e:
            logger.error(f"范围检测出错: {e}")
            return {'detected': False, 'error': str(e)}
    
    def _threshold_detection(self, image: np.ndarray, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于阈值的检测
        
        Args:
            image: 输入图像
            config: 检测配置
            
        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            # 获取阈值参数
            threshold_value = config.get('threshold_value', 127)
            threshold_type = config.get('threshold_type', cv2.THRESH_BINARY)
            
            # 应用阈值
            _, mask = cv2.threshold(gray, threshold_value, 255, threshold_type)
            
            # 后处理
            processed_mask = self._post_process_mask(mask, config)
            
            # 分析结果
            result = self._analyze_detection_result(processed_mask, config)
            result['mask'] = processed_mask
            result['threshold_value'] = threshold_value
            
            return result
            
        except Exception as e:
            logger.error(f"阈值检测出错: {e}")
            return {'detected': False, 'error': str(e)}
    
    def _statistical_detection(self, image: np.ndarray, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于统计特性的检测
        
        Args:
            image: 输入图像
            config: 检测配置
            
        Returns:
            Dict[str, Any]: 检测结果
        """
        try:
            # 转换颜色空间
            color_space = config.get('color_space', 'HSV')
            converted_image = self._convert_color_space(image, color_space)
            
            # 计算统计特征
            mean_values = np.mean(converted_image, axis=(0, 1))
            std_values = np.std(converted_image, axis=(0, 1))
            
            # 获取期望的统计特征
            expected_mean = config.get('expected_mean', mean_values)
            expected_std = config.get('expected_std', std_values)
            tolerance = config.get('tolerance', 0.1)
            
            # 检查是否在期望范围内
            mean_diff = np.abs(mean_values - expected_mean) / (expected_mean + 1e-6)
            std_diff = np.abs(std_values - expected_std) / (expected_std + 1e-6)
            
            # 判断检测结果
            detected = np.all(mean_diff < tolerance) and np.all(std_diff < tolerance)
            
            result = {
                'detected': detected,
                'mean_values': mean_values.tolist(),
                'std_values': std_values.tolist(),
                'expected_mean': expected_mean.tolist() if hasattr(expected_mean, 'tolist') else expected_mean,
                'expected_std': expected_std.tolist() if hasattr(expected_std, 'tolist') else expected_std,
                'mean_diff': mean_diff.tolist(),
                'std_diff': std_diff.tolist(),
                'tolerance': tolerance
            }
            
            return result
            
        except Exception as e:
            logger.error(f"统计检测出错: {e}")
            return {'detected': False, 'error': str(e)}
    
    def _convert_color_space(self, image: np.ndarray, color_space: str) -> np.ndarray:
        """
        转换颜色空间
        
        Args:
            image: 输入图像
            color_space: 目标颜色空间
            
        Returns:
            np.ndarray: 转换后的图像
        """
        if len(image.shape) == 2:  # 灰度图像
            if color_space in ['HSV', 'LAB']:
                # 转换为BGR然后再转换到目标颜色空间
                bgr_image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
                return self._convert_color_space(bgr_image, color_space)
            else:
                return image
        
        if color_space == 'HSV':
            return cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        elif color_space == 'LAB':
            return cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        elif color_space == 'RGB':
            return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        elif color_space == 'GRAY':
            return cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:  # BGR 或未知
            return image
    
    def _post_process_mask(self, mask: np.ndarray, config: Dict[str, Any]) -> np.ndarray:
        """
        后处理掩码
        
        Args:
            mask: 原始掩码
            config: 处理配置
            
        Returns:
            np.ndarray: 处理后的掩码
        """
        processed_mask = mask.copy()
        
        try:
            # 噪声降低
            if config.get('noise_reduction', True):
                # 高斯模糊
                processed_mask = cv2.GaussianBlur(processed_mask, (3, 3), 0)
                # 重新二值化
                _, processed_mask = cv2.threshold(processed_mask, 127, 255, cv2.THRESH_BINARY)
            
            # 形态学处理
            if config.get('morphology_enabled', True):
                kernel_size = config.get('morphology_kernel_size', 3)
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
                
                # 开运算：去除噪声
                processed_mask = cv2.morphologyEx(processed_mask, cv2.MORPH_OPEN, kernel)
                
                # 闭运算：填充空洞
                processed_mask = cv2.morphologyEx(processed_mask, cv2.MORPH_CLOSE, kernel)
            
            return processed_mask
            
        except Exception as e:
            logger.error(f"掩码后处理出错: {e}")
            return mask
    
    def _analyze_detection_result(self, mask: np.ndarray, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析检测结果
        
        Args:
            mask: 检测掩码
            config: 分析配置
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            # 计算基本统计信息
            total_pixels = mask.shape[0] * mask.shape[1]
            detected_pixels = np.sum(mask > 0)
            detection_ratio = detected_pixels / total_pixels if total_pixels > 0 else 0
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 过滤轮廓
            min_area = config.get('min_area', 100)
            max_area = config.get('max_area', 10000)
            
            valid_contours = []
            total_area = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area <= area <= max_area:
                    valid_contours.append(contour)
                    total_area += area
            
            # 判断检测结果
            min_detection_ratio = config.get('min_detection_ratio', 0.01)
            min_contour_count = config.get('min_contour_count', 1)
            
            detected = (detection_ratio >= min_detection_ratio and 
                       len(valid_contours) >= min_contour_count)
            
            # 计算轮廓特征
            contour_features = []
            for contour in valid_contours:
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)
                
                # 边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                
                # 最小外接矩形
                rect = cv2.minAreaRect(contour)
                
                feature = {
                    'area': float(area),
                    'perimeter': float(perimeter),
                    'bounding_rect': [int(x), int(y), int(w), int(h)],
                    'center': [float(rect[0][0]), float(rect[0][1])],
                    'angle': float(rect[2])
                }
                contour_features.append(feature)
            
            result = {
                'detected': detected,
                'detection_ratio': float(detection_ratio),
                'detected_pixels': int(detected_pixels),
                'total_pixels': int(total_pixels),
                'contour_count': len(valid_contours),
                'total_area': float(total_area),
                'contour_features': contour_features,
                'config_used': {
                    'min_area': min_area,
                    'max_area': max_area,
                    'min_detection_ratio': min_detection_ratio,
                    'min_contour_count': min_contour_count
                }
            }
            
            return result
            
        except Exception as e:
            logger.error(f"结果分析出错: {e}")
            return {'detected': False, 'error': str(e)}
    
    def visualize_detection(self, image: np.ndarray, result: Dict[str, Any]) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            result: 检测结果
            
        Returns:
            np.ndarray: 可视化图像
        """
        if not result or not result.get('detected', False):
            return image.copy()
        
        try:
            vis_image = image.copy()
            
            # 绘制掩码覆盖
            if 'mask' in result:
                mask = result['mask']
                # 创建彩色掩码
                colored_mask = np.zeros_like(vis_image)
                colored_mask[mask > 0] = [0, 255, 0]  # 绿色
                
                # 混合图像
                vis_image = cv2.addWeighted(vis_image, 0.7, colored_mask, 0.3, 0)
            
            # 绘制轮廓
            contour_features = result.get('contour_features', [])
            for i, feature in enumerate(contour_features):
                # 绘制边界矩形
                x, y, w, h = feature['bounding_rect']
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), (255, 0, 0), 2)
                
                # 绘制中心点
                center_x, center_y = feature['center']
                cv2.circle(vis_image, (int(center_x), int(center_y)), 3, (255, 0, 0), -1)
                
                # 添加文本信息
                text = f"Area: {feature['area']:.0f}"
                cv2.putText(vis_image, text, (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            
            # 添加总体信息
            detection_ratio = result.get('detection_ratio', 0)
            status_text = f"Detected: {result.get('detected', False)}, Ratio: {detection_ratio:.3f}"
            cv2.putText(vis_image, status_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            return vis_image
            
        except Exception as e:
            logger.error(f"可视化检测结果出错: {e}")
            return image.copy()
    
    def calibrate_color_range(self, image: np.ndarray, roi: Optional[Tuple[int, int, int, int]] = None) -> Dict[str, Any]:
        """
        校准颜色范围
        
        Args:
            image: 校准图像
            roi: ROI区域 (x, y, w, h)，如果为None则使用整个图像
            
        Returns:
            Dict[str, Any]: 校准结果，包含建议的颜色范围
        """
        try:
            # 提取ROI区域
            if roi:
                x, y, w, h = roi
                roi_image = image[y:y+h, x:x+w]
            else:
                roi_image = image
            
            # 转换到HSV颜色空间
            hsv_image = cv2.cvtColor(roi_image, cv2.COLOR_BGR2HSV)
            
            # 计算每个通道的统计信息
            h_values = hsv_image[:, :, 0].flatten()
            s_values = hsv_image[:, :, 1].flatten()
            v_values = hsv_image[:, :, 2].flatten()
            
            # 计算统计量
            h_mean, h_std = np.mean(h_values), np.std(h_values)
            s_mean, s_std = np.mean(s_values), np.std(s_values)
            v_mean, v_std = np.mean(v_values), np.std(v_values)
            
            # 生成建议的颜色范围（均值 ± 2倍标准差）
            h_range = [max(0, h_mean - 2*h_std), min(179, h_mean + 2*h_std)]
            s_range = [max(0, s_mean - 2*s_std), min(255, s_mean + 2*s_std)]
            v_range = [max(0, v_mean - 2*v_std), min(255, v_mean + 2*v_std)]
            
            result = {
                'color_ranges': [{
                    'lower': [int(h_range[0]), int(s_range[0]), int(v_range[0])],
                    'upper': [int(h_range[1]), int(s_range[1]), int(v_range[1])]
                }],
                'statistics': {
                    'h': {'mean': h_mean, 'std': h_std, 'range': h_range},
                    's': {'mean': s_mean, 'std': s_std, 'range': s_range},
                    'v': {'mean': v_mean, 'std': v_std, 'range': v_range}
                },
                'color_space': 'HSV',
                'roi': roi
            }
            
            logger.info(f"颜色范围校准完成: H={h_range}, S={s_range}, V={v_range}")
            return result
            
        except Exception as e:
            logger.error(f"颜色校准出错: {e}")
            return {'error': str(e)}
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取检测器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            'config': self.config.copy(),
            'available_color_spaces': ['HSV', 'RGB', 'LAB', 'GRAY'],
            'available_methods': ['range', 'threshold', 'statistical']
        } 