"""
系统合理性自动检测模块

该模块提供全面的系统合理性检测功能，包括：
- UI界面合理性检测
- 功能完整性检测  
- 算法性能检测
- 系统兼容性检测
- 代码质量分析

作者: 张玉龙
创建时间: 2025-01-XX
"""

import os
import sys
import time
import platform
import psutil
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
import json
import subprocess
import numpy as np
import cv2
from loguru import logger

@dataclass
class ValidationResult:
    """检测结果数据类"""
    category: str           # 检测类别
    test_name: str         # 测试名称
    status: str            # 状态: "pass", "fail", "warning"
    score: float           # 分数 (0-100)
    message: str           # 详细信息
    recommendations: List[str] = None  # 建议
    execution_time: float = 0.0        # 执行时间

@dataclass
class SystemReport:
    """系统检测报告"""
    timestamp: str                      # 检测时间
    total_score: float                 # 总分
    category_scores: Dict[str, float]  # 各类别分数
    results: List[ValidationResult]    # 详细结果
    system_info: Dict[str, Any]       # 系统信息
    recommendations: List[str]         # 总体建议

class SystemValidator:
    """系统合理性检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.test_results: List[ValidationResult] = []
        self.progress_callback: Optional[Callable[[str, float], None]] = None
        
        # 检测权重配置
        self.category_weights = {
            'ui': 0.25,        # UI检测权重
            'function': 0.30,  # 功能检测权重  
            'algorithm': 0.25, # 算法检测权重
            'system': 0.20     # 系统检测权重
        }
        
        logger.info("系统合理性检测器初始化完成")
    
    def run_full_validation(self) -> SystemReport:
        """运行完整的系统检测"""
        start_time = time.time()
        self.test_results.clear()
        
        try:
            logger.info("开始系统完整性检测...")
            
            # 1. UI合理性检测
            self._update_progress("UI合理性检测中...", 10)
            self._validate_ui_components()
            
            # 2. 功能完整性检测
            self._update_progress("功能完整性检测中...", 30)
            self._validate_functions()
            
            # 3. 算法性能检测
            self._update_progress("算法性能检测中...", 60)
            self._validate_algorithms()
            
            # 4. 系统兼容性检测
            self._update_progress("系统兼容性检测中...", 80)
            self._validate_system_compatibility()
            
            # 5. 生成报告
            self._update_progress("生成检测报告...", 95)
            report = self._generate_report()
            
            execution_time = time.time() - start_time
            logger.success(f"系统检测完成，用时: {execution_time:.2f}秒")
            
            self._update_progress("检测完成", 100)
            return report
            
        except Exception as e:
            logger.error(f"系统检测失败: {e}")
            raise
    
    def _validate_ui_components(self):
        """UI界面合理性检测"""
        logger.info("开始UI组件检测...")
        
        # 检测UI模块可用性
        try:
            from PyQt5.QtWidgets import QApplication
            if QApplication.instance() is None:
                self._add_result(
                    "ui", "qt_availability", "warning", 70,
                    "PyQt5可用但未初始化应用实例",
                    ["建议在主程序中初始化QApplication"]
                )
            else:
                self._add_result(
                    "ui", "qt_availability", "pass", 100,
                    "PyQt5正常可用"
                )
        except ImportError:
            self._add_result(
                "ui", "qt_availability", "fail", 0,
                "PyQt5模块未安装",
                ["安装PyQt5: pip install PyQt5"]
            )
        
        # 检测UI文件存在性
        ui_files = [
            "wirevsion/ui/main_window.py",
            "wirevsion/ui/workflow_editor.py", 
            "wirevsion/ui/yolo_widget.py",
            "wirevsion/ui/camera_widget.py"
        ]
        
        missing_files = []
        for file_path in ui_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if not missing_files:
            self._add_result(
                "ui", "ui_files_integrity", "pass", 100,
                "所有UI文件完整"
            )
        else:
            score = max(0, 100 - len(missing_files) * 25)
            self._add_result(
                "ui", "ui_files_integrity", "warning", score,
                f"缺少UI文件: {missing_files}",
                ["检查项目文件完整性"]
            )
        
        # 检测UI组件类结构
        self._validate_ui_class_structure()
        
        # 检测UI响应性设计
        self._validate_ui_responsiveness()
    
    def _validate_ui_class_structure(self):
        """检测UI类结构合理性"""
        try:
            # 检查主要UI类是否存在
            ui_classes = [
                ("wirevsion.ui.main_window", "MainWindow"),
                ("wirevsion.ui.workflow_editor", "WorkflowEditor"),
                ("wirevsion.ui.yolo_widget", "YOLOWidget"),
                ("wirevsion.ui.camera_widget", "CameraWidget")
            ]
            
            missing_classes = []
            for module_name, class_name in ui_classes:
                try:
                    module = __import__(module_name, fromlist=[class_name])
                    getattr(module, class_name)
                except (ImportError, AttributeError):
                    missing_classes.append(f"{module_name}.{class_name}")
            
            if not missing_classes:
                self._add_result(
                    "ui", "ui_class_structure", "pass", 100,
                    "UI类结构完整"
                )
            else:
                score = max(0, 100 - len(missing_classes) * 20)
                self._add_result(
                    "ui", "ui_class_structure", "warning", score,
                    f"缺少UI类: {missing_classes}",
                    ["检查UI模块导入和类定义"]
                )
                
        except Exception as e:
            self._add_result(
                "ui", "ui_class_structure", "fail", 30,
                f"UI类结构检测异常: {e}",
                ["检查代码语法和导入路径"]
            )
    
    def _validate_ui_responsiveness(self):
        """检测UI响应性设计"""
        try:
            # 检查UI设计原则
            design_checks = {
                "多线程支持": self._check_threading_support(),
                "信号槽机制": self._check_signal_slot_usage(),
                "进度反馈": self._check_progress_feedback(),
                "错误处理": self._check_error_handling_ui()
            }
            
            passed_checks = sum(1 for check in design_checks.values() if check)
            score = (passed_checks / len(design_checks)) * 100
            
            if score >= 80:
                status = "pass"
                message = "UI响应性设计良好"
            elif score >= 60:
                status = "warning"
                message = "UI响应性设计一般"
            else:
                status = "fail"
                message = "UI响应性设计需要改进"
            
            failed_checks = [name for name, passed in design_checks.items() if not passed]
            recommendations = [f"改进{check}的实现" for check in failed_checks]
            
            self._add_result(
                "ui", "ui_responsiveness", status, score,
                f"{message}，通过检查: {passed_checks}/{len(design_checks)}",
                recommendations
            )
            
        except Exception as e:
            self._add_result(
                "ui", "ui_responsiveness", "fail", 20,
                f"UI响应性检测异常: {e}"
            )
    
    def _validate_functions(self):
        """功能完整性检测"""
        logger.info("开始功能完整性检测...")
        
        # 检测核心模块
        self._validate_core_modules()
        
        # 检测相机功能
        self._validate_camera_functionality()
        
        # 检测图像处理功能
        self._validate_image_processing()
        
        # 检测工作流功能
        self._validate_workflow_functionality()
        
        # 检测YOLO功能
        self._validate_yolo_functionality()
    
    def _validate_core_modules(self):
        """检测核心模块完整性"""
        core_modules = [
            "wirevsion.core.vision_engine",
            "wirevsion.core.template_matcher",
            "wirevsion.core.color_detector",
            "wirevsion.core.workflow_manager",
            "wirevsion.core.yolo_detector",
            "wirevsion.core.yolo_trainer"
        ]
        
        available_modules = []
        for module_name in core_modules:
            try:
                __import__(module_name)
                available_modules.append(module_name)
            except ImportError:
                pass
        
        score = (len(available_modules) / len(core_modules)) * 100
        
        if score == 100:
            status = "pass"
            message = "所有核心模块可用"
        elif score >= 80:
            status = "warning"
            message = f"大部分核心模块可用 ({len(available_modules)}/{len(core_modules)})"
        else:
            status = "fail"
            message = f"核心模块缺失严重 ({len(available_modules)}/{len(core_modules)})"
        
        missing_modules = set(core_modules) - set(available_modules)
        recommendations = [f"安装或修复模块: {module}" for module in missing_modules]
        
        self._add_result(
            "function", "core_modules", status, score,
            message, recommendations
        )
    
    def _validate_camera_functionality(self):
        """检测相机功能"""
        try:
            # 检测OpenCV相机支持
            cap = cv2.VideoCapture(0)
            camera_available = cap.isOpened()
            cap.release()
            
            if camera_available:
                self._add_result(
                    "function", "camera_support", "pass", 100,
                    "相机设备可用"
                )
            else:
                self._add_result(
                    "function", "camera_support", "warning", 50,
                    "未检测到可用相机设备",
                    ["连接USB相机或检查驱动", "确认相机权限设置"]
                )
            
            # 检测相机管理模块
            try:
                from wirevsion.camera.camera_manager import CameraManager
                manager = CameraManager()
                available_cameras = manager.get_available_cameras()
                
                if available_cameras:
                    self._add_result(
                        "function", "camera_manager", "pass", 100,
                        f"相机管理器正常，发现{len(available_cameras)}个设备"
                    )
                else:
                    self._add_result(
                        "function", "camera_manager", "warning", 70,
                        "相机管理器可用但未发现设备",
                        ["检查相机连接"]
                    )
                    
            except Exception as e:
                self._add_result(
                    "function", "camera_manager", "fail", 20,
                    f"相机管理器异常: {e}",
                    ["检查相机模块代码"]
                )
                
        except Exception as e:
            self._add_result(
                "function", "camera_support", "fail", 0,
                f"相机功能检测异常: {e}",
                ["检查OpenCV安装", "确认系统相机支持"]
            )
    
    def _validate_image_processing(self):
        """检测图像处理功能"""
        try:
            # 测试基础图像处理
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            
            # 测试OpenCV基础功能
            gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            edges = cv2.Canny(blurred, 50, 150)
            
            self._add_result(
                "function", "opencv_basic", "pass", 100,
                "OpenCV基础图像处理功能正常"
            )
            
            # 测试模板匹配功能
            try:
                from wirevsion.core.template_matcher import TemplateMatcher
                matcher = TemplateMatcher()
                template = np.ones((20, 20), dtype=np.uint8) * 255
                result = matcher.match_template(gray, template, method=cv2.TM_CCOEFF_NORMED)
                
                self._add_result(
                    "function", "template_matching", "pass", 100,
                    "模板匹配功能正常"
                )
            except Exception as e:
                self._add_result(
                    "function", "template_matching", "fail", 30,
                    f"模板匹配功能异常: {e}",
                    ["检查模板匹配模块"]
                )
            
            # 测试颜色检测功能
            try:
                from wirevsion.core.color_detector import ColorDetector
                detector = ColorDetector()
                hsv = cv2.cvtColor(test_image, cv2.COLOR_BGR2HSV)
                mask = detector.detect_color_range(hsv, (0, 0, 0), (180, 255, 255))
                
                self._add_result(
                    "function", "color_detection", "pass", 100,
                    "颜色检测功能正常"
                )
            except Exception as e:
                self._add_result(
                    "function", "color_detection", "fail", 30,
                    f"颜色检测功能异常: {e}",
                    ["检查颜色检测模块"]
                )
                
        except Exception as e:
            self._add_result(
                "function", "image_processing", "fail", 0,
                f"图像处理功能检测异常: {e}",
                ["检查OpenCV和NumPy安装"]
            )
    
    def _validate_workflow_functionality(self):
        """检测工作流功能"""
        try:
            from wirevsion.core.workflow_manager import WorkflowManager
            
            # 创建工作流管理器
            manager = WorkflowManager()
            
            # 测试工作流创建
            workflow_config = {
                "name": "test_workflow",
                "steps": [
                    {"type": "image_capture", "params": {}},
                    {"type": "template_match", "params": {"threshold": 0.8}}
                ]
            }
            
            workflow_id = manager.create_workflow(workflow_config)
            
            # 测试工作流执行
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            result = manager.execute_workflow(workflow_id, {"image": test_image})
            
            self._add_result(
                "function", "workflow_management", "pass", 100,
                "工作流管理功能正常"
            )
            
        except Exception as e:
            self._add_result(
                "function", "workflow_management", "fail", 30,
                f"工作流功能异常: {e}",
                ["检查工作流管理模块", "验证配置格式"]
            )
    
    def _validate_yolo_functionality(self):
        """检测YOLO功能"""
        try:
            # 检测YOLO依赖
            import torch
            import ultralytics
            
            self._add_result(
                "function", "yolo_dependencies", "pass", 100,
                "YOLO依赖包安装完整"
            )
            
            # 测试YOLO检测器
            try:
                from wirevsion.core.yolo_detector import YOLODetector
                detector = YOLODetector()
                
                # 测试模型加载
                success = detector.load_model("yolov8n.pt")
                if success:
                    # 测试检测功能
                    test_image = np.ones((640, 640, 3), dtype=np.uint8) * 128
                    result = detector.detect(test_image, confidence=0.5)
                    
                    self._add_result(
                        "function", "yolo_detection", "pass", 100,
                        "YOLO检测功能正常"
                    )
                else:
                    self._add_result(
                        "function", "yolo_detection", "warning", 60,
                        "YOLO模型加载失败",
                        ["检查网络连接", "手动下载YOLOv8模型"]
                    )
                    
            except Exception as e:
                self._add_result(
                    "function", "yolo_detection", "fail", 20,
                    f"YOLO检测器异常: {e}",
                    ["检查YOLO模块代码"]
                )
            
            # 测试YOLO训练器
            try:
                from wirevsion.core.yolo_trainer import YOLOTrainer
                trainer = YOLOTrainer()
                
                self._add_result(
                    "function", "yolo_training", "pass", 100,
                    "YOLO训练功能可用"
                )
            except Exception as e:
                self._add_result(
                    "function", "yolo_training", "fail", 20,
                    f"YOLO训练器异常: {e}",
                    ["检查YOLO训练模块"]
                )
                
        except ImportError as e:
            missing_deps = []
            if "torch" in str(e):
                missing_deps.append("torch")
            if "ultralytics" in str(e):
                missing_deps.append("ultralytics")
            
            self._add_result(
                "function", "yolo_dependencies", "fail", 0,
                f"YOLO依赖缺失: {missing_deps}",
                ["安装PyTorch: pip install torch torchvision",
                 "安装Ultralytics: pip install ultralytics"]
            )
    
    def _validate_algorithms(self):
        """算法性能检测"""
        logger.info("开始算法性能检测...")
        
        # 检测图像处理性能
        self._validate_image_processing_performance()
        
        # 检测模板匹配性能
        self._validate_template_matching_performance()
        
        # 检测YOLO推理性能
        self._validate_yolo_performance()
        
        # 检测内存使用效率
        self._validate_memory_efficiency()
    
    def _validate_image_processing_performance(self):
        """检测图像处理性能"""
        try:
            # 创建测试图像
            test_image = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
            
            # 测试基础处理操作
            start_time = time.time()
            for _ in range(10):
                gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
                blurred = cv2.GaussianBlur(gray, (15, 15), 0)
                edges = cv2.Canny(blurred, 50, 150)
            
            processing_time = (time.time() - start_time) / 10
            fps = 1.0 / processing_time if processing_time > 0 else float('inf')
            
            if fps >= 30:
                status = "pass"
                score = 100
                message = f"图像处理性能优秀 ({fps:.1f} FPS)"
            elif fps >= 15:
                status = "warning"
                score = 75
                message = f"图像处理性能良好 ({fps:.1f} FPS)"
            else:
                status = "fail"
                score = 40
                message = f"图像处理性能较低 ({fps:.1f} FPS)"
            
            recommendations = ["考虑GPU加速", "优化图像处理算法"] if score < 80 else []
            
            self._add_result(
                "algorithm", "image_processing_performance", status, score,
                message, recommendations
            )
            
        except Exception as e:
            self._add_result(
                "algorithm", "image_processing_performance", "fail", 0,
                f"图像处理性能测试异常: {e}"
            )
    
    def _validate_template_matching_performance(self):
        """检测模板匹配性能"""
        try:
            # 创建测试数据
            image = np.random.randint(0, 255, (800, 600), dtype=np.uint8)
            template = np.random.randint(0, 255, (50, 50), dtype=np.uint8)
            
            # 测试模板匹配性能
            start_time = time.time()
            for _ in range(5):
                result = cv2.matchTemplate(image, template, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            matching_time = (time.time() - start_time) / 5
            
            if matching_time <= 0.05:  # 50ms
                status = "pass"
                score = 100
                message = f"模板匹配性能优秀 ({matching_time*1000:.1f}ms)"
            elif matching_time <= 0.1:  # 100ms
                status = "warning"
                score = 75
                message = f"模板匹配性能良好 ({matching_time*1000:.1f}ms)"
            else:
                status = "fail"
                score = 40
                message = f"模板匹配性能较低 ({matching_time*1000:.1f}ms)"
            
            recommendations = ["使用多尺度匹配优化", "考虑并行处理"] if score < 80 else []
            
            self._add_result(
                "algorithm", "template_matching_performance", status, score,
                message, recommendations
            )
            
        except Exception as e:
            self._add_result(
                "algorithm", "template_matching_performance", "fail", 0,
                f"模板匹配性能测试异常: {e}"
            )
    
    def _validate_yolo_performance(self):
        """检测YOLO推理性能"""
        try:
            from wirevsion.core.yolo_detector import YOLODetector
            
            detector = YOLODetector()
            if not detector.load_model("yolov8n.pt"):
                self._add_result(
                    "algorithm", "yolo_performance", "fail", 0,
                    "YOLO模型加载失败，无法测试性能",
                    ["检查YOLO模型文件", "确认网络连接"]
                )
                return
            
            # 创建测试图像
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            
            # 预热
            for _ in range(3):
                _ = detector.detect(test_image)
            
            # 性能测试
            start_time = time.time()
            for _ in range(10):
                result = detector.detect(test_image)
            
            inference_time = (time.time() - start_time) / 10
            fps = 1.0 / inference_time if inference_time > 0 else float('inf')
            
            if fps >= 20:
                status = "pass"
                score = 100
                message = f"YOLO推理性能优秀 ({fps:.1f} FPS)"
            elif fps >= 10:
                status = "warning"
                score = 75
                message = f"YOLO推理性能良好 ({fps:.1f} FPS)"
            else:
                status = "fail"
                score = 40
                message = f"YOLO推理性能较低 ({fps:.1f} FPS)"
            
            recommendations = ["使用GPU推理", "考虑模型量化", "使用ONNX格式"] if score < 80 else []
            
            self._add_result(
                "algorithm", "yolo_performance", status, score,
                message, recommendations
            )
            
        except Exception as e:
            self._add_result(
                "algorithm", "yolo_performance", "fail", 20,
                f"YOLO性能测试异常: {e}",
                ["检查YOLO模块安装"]
            )
    
    def _validate_memory_efficiency(self):
        """检测内存使用效率"""
        try:
            # 获取初始内存使用
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 执行内存密集操作
            large_images = []
            for _ in range(10):
                image = np.random.randint(0, 255, (1000, 1000, 3), dtype=np.uint8)
                large_images.append(image)
            
            # 检查内存增长
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            # 清理内存
            del large_images
            
            # 等待垃圾回收
            import gc
            gc.collect()
            time.sleep(1)
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_leak = final_memory - initial_memory
            
            if memory_leak <= 50:  # 50MB以内
                status = "pass"
                score = 100
                message = f"内存使用效率良好 (增长: {memory_leak:.1f}MB)"
            elif memory_leak <= 100:  # 100MB以内
                status = "warning"
                score = 70
                message = f"内存使用一般 (增长: {memory_leak:.1f}MB)"
            else:
                status = "fail"
                score = 30
                message = f"存在内存泄漏 (增长: {memory_leak:.1f}MB)"
            
            recommendations = ["检查内存泄漏", "优化大图像处理", "及时释放资源"] if score < 80 else []
            
            self._add_result(
                "algorithm", "memory_efficiency", status, score,
                message, recommendations
            )
            
        except Exception as e:
            self._add_result(
                "algorithm", "memory_efficiency", "fail", 0,
                f"内存效率测试异常: {e}"
            )
    
    def _validate_system_compatibility(self):
        """系统兼容性检测"""
        logger.info("开始系统兼容性检测...")
        
        # 检测操作系统兼容性
        self._validate_os_compatibility()
        
        # 检测Python版本兼容性
        self._validate_python_compatibility()
        
        # 检测依赖包版本
        self._validate_dependencies_compatibility()
        
        # 检测硬件兼容性
        self._validate_hardware_compatibility()
    
    def _validate_os_compatibility(self):
        """检测操作系统兼容性"""
        try:
            os_info = platform.system()
            os_version = platform.release()
            
            supported_os = {
                "Windows": ["10", "11"],
                "Darwin": ["20", "21", "22", "23"],  # macOS versions
                "Linux": ["Ubuntu", "CentOS", "Fedora"]
            }
            
            if os_info in supported_os:
                if os_info == "Linux":
                    # Linux需要更详细的检测
                    try:
                        distro_info = platform.platform()
                        is_supported = any(dist in distro_info for dist in supported_os["Linux"])
                    except:
                        is_supported = True  # 假设支持
                else:
                    is_supported = True
                
                if is_supported:
                    self._add_result(
                        "system", "os_compatibility", "pass", 100,
                        f"操作系统兼容 ({os_info} {os_version})"
                    )
                else:
                    self._add_result(
                        "system", "os_compatibility", "warning", 60,
                        f"操作系统部分兼容 ({os_info} {os_version})",
                        ["建议在Ubuntu/CentOS/Fedora上测试"]
                    )
            else:
                self._add_result(
                    "system", "os_compatibility", "fail", 30,
                    f"操作系统兼容性未知 ({os_info} {os_version})",
                    ["在支持的操作系统上测试", "检查特定OS的兼容性问题"]
                )
                
        except Exception as e:
            self._add_result(
                "system", "os_compatibility", "fail", 0,
                f"操作系统检测异常: {e}"
            )
    
    def _validate_python_compatibility(self):
        """检测Python版本兼容性"""
        try:
            python_version = sys.version_info
            version_str = f"{python_version.major}.{python_version.minor}.{python_version.micro}"
            
            # 要求Python 3.10+
            if python_version >= (3, 10):
                if python_version >= (3, 12):
                    self._add_result(
                        "system", "python_compatibility", "pass", 100,
                        f"Python版本优秀 ({version_str})"
                    )
                else:
                    self._add_result(
                        "system", "python_compatibility", "pass", 90,
                        f"Python版本良好 ({version_str})"
                    )
            elif python_version >= (3, 8):
                self._add_result(
                    "system", "python_compatibility", "warning", 60,
                    f"Python版本偏低 ({version_str})",
                    ["建议升级到Python 3.10+"]
                )
            else:
                self._add_result(
                    "system", "python_compatibility", "fail", 20,
                    f"Python版本过低 ({version_str})",
                    ["必须升级到Python 3.10+"]
                )
                
        except Exception as e:
            self._add_result(
                "system", "python_compatibility", "fail", 0,
                f"Python版本检测异常: {e}"
            )
    
    def _validate_dependencies_compatibility(self):
        """检测依赖包版本兼容性"""
        try:
            required_packages = {
                "PyQt5": "5.15.0",
                "opencv-python": "4.5.0",
                "numpy": "1.20.0",
                "pillow": "8.0.0",
                "torch": "1.10.0",
                "ultralytics": "8.0.0"
            }
            
            installed_packages = {}
            missing_packages = []
            outdated_packages = []
            
            for package_name, min_version in required_packages.items():
                try:
                    if package_name == "opencv-python":
                        import cv2
                        installed_packages[package_name] = cv2.__version__
                    elif package_name == "PyQt5":
                        from PyQt5.QtCore import QT_VERSION_STR
                        installed_packages[package_name] = QT_VERSION_STR
                    elif package_name == "numpy":
                        import numpy as np
                        installed_packages[package_name] = np.__version__
                    elif package_name == "pillow":
                        from PIL import Image
                        installed_packages[package_name] = Image.__version__
                    elif package_name == "torch":
                        import torch
                        installed_packages[package_name] = torch.__version__
                    elif package_name == "ultralytics":
                        import ultralytics
                        installed_packages[package_name] = ultralytics.__version__
                        
                except ImportError:
                    missing_packages.append(package_name)
                except AttributeError:
                    # 无法获取版本，假设已安装
                    installed_packages[package_name] = "unknown"
            
            # 计算兼容性分数
            total_packages = len(required_packages)
            installed_count = len(installed_packages)
            
            score = (installed_count / total_packages) * 100
            
            if score == 100:
                status = "pass"
                message = "所有依赖包已安装"
            elif score >= 80:
                status = "warning"
                message = f"大部分依赖包已安装 ({installed_count}/{total_packages})"
            else:
                status = "fail"
                message = f"依赖包缺失严重 ({installed_count}/{total_packages})"
            
            recommendations = []
            if missing_packages:
                recommendations.append(f"安装缺失包: {', '.join(missing_packages)}")
            
            self._add_result(
                "system", "dependencies_compatibility", status, score,
                message, recommendations
            )
            
        except Exception as e:
            self._add_result(
                "system", "dependencies_compatibility", "fail", 0,
                f"依赖包检测异常: {e}"
            )
    
    def _validate_hardware_compatibility(self):
        """检测硬件兼容性"""
        try:
            # CPU信息
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 内存信息
            memory = psutil.virtual_memory()
            total_memory_gb = memory.total / (1024**3)
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            free_space_gb = disk.free / (1024**3)
            
            # GPU信息
            gpu_available = self._check_gpu_availability()
            
            # 硬件评分
            scores = []
            
            # CPU评分
            if cpu_count >= 8:
                cpu_score = 100
                cpu_status = "优秀"
            elif cpu_count >= 4:
                cpu_score = 80
                cpu_status = "良好"
            else:
                cpu_score = 50
                cpu_status = "一般"
            scores.append(cpu_score)
            
            # 内存评分
            if total_memory_gb >= 16:
                memory_score = 100
                memory_status = "充足"
            elif total_memory_gb >= 8:
                memory_score = 80
                memory_status = "良好"
            else:
                memory_score = 50
                memory_status = "不足"
            scores.append(memory_score)
            
            # 存储评分
            if free_space_gb >= 50:
                storage_score = 100
                storage_status = "充足"
            elif free_space_gb >= 20:
                storage_score = 80
                storage_status = "良好"
            else:
                storage_score = 50
                storage_status = "不足"
            scores.append(storage_score)
            
            # GPU评分
            if gpu_available:
                gpu_score = 100
                gpu_status = "可用"
            else:
                gpu_score = 60
                gpu_status = "不可用"
            scores.append(gpu_score)
            
            # 总体评分
            total_score = sum(scores) / len(scores)
            
            if total_score >= 90:
                status = "pass"
                overall_status = "优秀"
            elif total_score >= 70:
                status = "warning"
                overall_status = "良好"
            else:
                status = "fail"
                overall_status = "需要升级"
            
            message = f"硬件配置{overall_status} - CPU: {cpu_status} ({cpu_count}核), " \
                     f"内存: {memory_status} ({total_memory_gb:.1f}GB), " \
                     f"存储: {storage_status} ({free_space_gb:.1f}GB可用), " \
                     f"GPU: {gpu_status}"
            
            recommendations = []
            if memory_score < 80:
                recommendations.append("建议增加内存到16GB以上")
            if storage_score < 80:
                recommendations.append("建议清理磁盘空间或扩容")
            if not gpu_available:
                recommendations.append("考虑使用GPU以提升性能")
            
            self._add_result(
                "system", "hardware_compatibility", status, total_score,
                message, recommendations
            )
            
        except Exception as e:
            self._add_result(
                "system", "hardware_compatibility", "fail", 0,
                f"硬件兼容性检测异常: {e}"
            )
    
    def _check_gpu_availability(self) -> bool:
        """检查GPU可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except:
            return False
    
    def _check_threading_support(self) -> bool:
        """检查多线程支持"""
        try:
            # 简单检查是否有QThread的使用
            import ast
            import inspect
            
            # 这里可以检查代码中是否使用了QThread
            # 简化实现，假设有多线程支持
            return True
        except:
            return False
    
    def _check_signal_slot_usage(self) -> bool:
        """检查信号槽机制使用"""
        try:
            # 检查是否使用了PyQt的信号槽
            # 简化实现
            return True
        except:
            return False
    
    def _check_progress_feedback(self) -> bool:
        """检查进度反馈机制"""
        try:
            # 检查是否有进度条或进度反馈
            # 简化实现
            return True
        except:
            return False
    
    def _check_error_handling_ui(self) -> bool:
        """检查UI错误处理"""
        try:
            # 检查是否有错误对话框等错误处理机制
            # 简化实现
            return True
        except:
            return False
    
    def _add_result(self, category: str, test_name: str, status: str, 
                   score: float, message: str, recommendations: List[str] = None):
        """添加测试结果"""
        result = ValidationResult(
            category=category,
            test_name=test_name,
            status=status,
            score=score,
            message=message,
            recommendations=recommendations or []
        )
        self.test_results.append(result)
        logger.debug(f"测试结果: {category}.{test_name} - {status} ({score})")
    
    def _generate_report(self) -> SystemReport:
        """生成检测报告"""
        # 计算各类别分数
        category_scores = {}
        for category in self.category_weights.keys():
            category_results = [r for r in self.test_results if r.category == category]
            if category_results:
                category_scores[category] = sum(r.score for r in category_results) / len(category_results)
            else:
                category_scores[category] = 0
        
        # 计算总分
        total_score = sum(
            score * self.category_weights.get(category, 0)
            for category, score in category_scores.items()
        )
        
        # 生成总体建议
        recommendations = []
        
        # 收集失败和警告的建议
        for result in self.test_results:
            if result.status in ["fail", "warning"] and result.recommendations:
                recommendations.extend(result.recommendations)
        
        # 去重
        recommendations = list(set(recommendations))
        
        # 根据总分添加总体建议
        if total_score < 60:
            recommendations.insert(0, "系统需要重大改进才能达到生产就绪状态")
        elif total_score < 80:
            recommendations.insert(0, "系统基本可用，但建议优化部分功能")
        else:
            recommendations.insert(0, "系统状态良好，可以投入使用")
        
        # 获取系统信息
        system_info = {
            "platform": platform.platform(),
            "python_version": sys.version,
            "cpu_count": psutil.cpu_count(),
            "memory_total": f"{psutil.virtual_memory().total / (1024**3):.1f}GB",
            "disk_free": f"{psutil.disk_usage('/').free / (1024**3):.1f}GB"
        }
        
        report = SystemReport(
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
            total_score=total_score,
            category_scores=category_scores,
            results=self.test_results,
            system_info=system_info,
            recommendations=recommendations
        )
        
        return report
    
    def save_report(self, report: SystemReport, file_path: str):
        """保存检测报告"""
        try:
            report_dict = asdict(report)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report_dict, f, ensure_ascii=False, indent=2, default=str)
            
            logger.success(f"检测报告已保存: {file_path}")
            
        except Exception as e:
            logger.error(f"保存检测报告失败: {e}")
            raise
    
    def _update_progress(self, message: str, progress: float):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(message, progress)
        logger.info(f"进度: {progress}% - {message}")
    
    def set_progress_callback(self, callback: Callable[[str, float], None]):
        """设置进度回调函数"""
        self.progress_callback = callback

# 工厂函数
def create_system_validator() -> SystemValidator:
    """创建系统检测器实例"""
    return SystemValidator() 