"""
YOLO深度学习检测器模块

该模块提供基于YOLOv8的目标检测功能，包括：
- 模型加载和推理
- 自定义模型训练  
- 模型管理和评估
- 检测结果可视化

作者: 张玉龙
创建时间: 2025-01-XX
"""

import os
import cv2
import numpy as np
import torch
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from ultralytics import YOLO
import supervision as sv
from loguru import logger

@dataclass
class DetectionResult:
    """检测结果数据类"""
    boxes: np.ndarray           # 边界框坐标 [x1, y1, x2, y2]
    confidences: np.ndarray     # 置信度
    class_ids: np.ndarray       # 类别ID
    class_names: List[str]      # 类别名称
    masks: Optional[np.ndarray] = None  # 分割掩码(如果有)

@dataclass  
class TrainingConfig:
    """训练配置数据类"""
    data_path: str              # 数据集路径
    epochs: int = 100          # 训练轮数
    batch_size: int = 16       # 批次大小
    image_size: int = 640      # 图像尺寸
    lr0: float = 0.01          # 初始学习率
    weight_decay: float = 0.0005  # 权重衰减
    warmup_epochs: int = 3     # 预热轮数
    patience: int = 50         # 早停耐心值
    device: str = "auto"       # 设备选择
    workers: int = 8           # 数据加载工作进程数
    save_period: int = 10      # 模型保存间隔

class YOLODetector:
    """YOLO深度学习检测器"""
    
    def __init__(self, model_path: str = None, device: str = "auto"):
        """
        初始化YOLO检测器
        
        Args:
            model_path: 模型文件路径，None时使用预训练模型
            device: 计算设备 ("cpu", "cuda", "auto")
        """
        self.model_path = model_path
        self.device = self._setup_device(device)
        self.model: Optional[YOLO] = None
        self.class_names: List[str] = []
        self.is_loaded = False
        
        # 初始化可视化工具
        self.box_annotator = sv.BoxAnnotator()
        self.label_annotator = sv.LabelAnnotator()
        
        logger.info(f"YOLO检测器初始化完成，设备: {self.device}")
    
    def _setup_device(self, device: str) -> str:
        """设置计算设备"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                return "mps"  # Apple Silicon
            else:
                return "cpu"
        return device
    
    def load_model(self, model_path: str = None) -> bool:
        """
        加载YOLO模型
        
        Args:
            model_path: 模型路径，支持本地文件或预训练模型名
            
        Returns:
            是否加载成功
        """
        try:
            if model_path:
                self.model_path = model_path
            
            # 如果没有指定路径，使用预训练模型
            if not self.model_path:
                self.model_path = "yolov8n.pt"  # 默认使用nano模型
                
            logger.info(f"正在加载YOLO模型: {self.model_path}")
            self.model = YOLO(self.model_path)
            
            # 获取类别名称
            if hasattr(self.model, 'names') and self.model.names:
                self.class_names = list(self.model.names.values())
            else:
                # 默认COCO类别
                self.class_names = self._get_coco_classes()
                
            self.is_loaded = True
            logger.success(f"YOLO模型加载成功，类别数: {len(self.class_names)}")
            return True
            
        except Exception as e:
            logger.error(f"YOLO模型加载失败: {e}")
            self.is_loaded = False
            return False
    
    def detect(self, image: np.ndarray, confidence: float = 0.5, 
              iou_threshold: float = 0.45) -> DetectionResult:
        """
        执行目标检测
        
        Args:
            image: 输入图像 (BGR格式)
            confidence: 置信度阈值
            iou_threshold: NMS IoU阈值
            
        Returns:
            检测结果
        """
        if not self.is_loaded:
            logger.warning("模型未加载，尝试加载默认模型")
            if not self.load_model():
                raise RuntimeError("无法加载YOLO模型")
        
        try:
            # 执行推理
            results = self.model(
                image, 
                conf=confidence,
                iou=iou_threshold,
                device=self.device,
                verbose=False
            )
            
            # 解析结果
            result = results[0]
            
            if len(result.boxes) == 0:
                # 没有检测到目标
                return DetectionResult(
                    boxes=np.array([]),
                    confidences=np.array([]),
                    class_ids=np.array([]),
                    class_names=[]
                )
            
            # 提取检测数据
            boxes = result.boxes.xyxy.cpu().numpy()  # [x1, y1, x2, y2]
            confidences = result.boxes.conf.cpu().numpy()
            class_ids = result.boxes.cls.cpu().numpy().astype(int)
            
            # 获取类别名称
            detected_classes = [self.class_names[cls_id] for cls_id in class_ids]
            
            # 处理分割掩码(如果有)
            masks = None
            if hasattr(result, 'masks') and result.masks is not None:
                masks = result.masks.data.cpu().numpy()
            
            return DetectionResult(
                boxes=boxes,
                confidences=confidences,
                class_ids=class_ids,
                class_names=detected_classes,
                masks=masks
            )
            
        except Exception as e:
            logger.error(f"YOLO检测失败: {e}")
            raise
    
    def visualize_results(self, image: np.ndarray, 
                         detection_result: DetectionResult) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            detection_result: 检测结果
            
        Returns:
            带有标注的图像
        """
        if len(detection_result.boxes) == 0:
            return image.copy()
        
        try:
            # 转换为supervision格式
            detections = sv.Detections(
                xyxy=detection_result.boxes,
                confidence=detection_result.confidences,
                class_id=detection_result.class_ids
            )
            
            # 绘制边界框
            annotated_image = self.box_annotator.annotate(
                scene=image.copy(),
                detections=detections
            )
            
            # 生成标签
            labels = [
                f"{class_name} {confidence:.2f}"
                for class_name, confidence 
                in zip(detection_result.class_names, detection_result.confidences)
            ]
            
            # 绘制标签
            annotated_image = self.label_annotator.annotate(
                scene=annotated_image,
                detections=detections,
                labels=labels
            )
            
            return annotated_image
            
        except Exception as e:
            logger.error(f"结果可视化失败: {e}")
            return image.copy()
    
    def train_model(self, config: TrainingConfig, 
                   model_name: str = "custom_yolo") -> bool:
        """
        训练自定义YOLO模型
        
        Args:
            config: 训练配置
            model_name: 模型保存名称
            
        Returns:
            训练是否成功
        """
        try:
            logger.info(f"开始训练YOLO模型: {model_name}")
            
            # 创建模型(从预训练模型开始)
            base_model = "yolov8n.pt"  # 使用nano模型作为基础
            model = YOLO(base_model)
            
            # 训练参数
            train_args = {
                'data': config.data_path,
                'epochs': config.epochs,
                'batch': config.batch_size,
                'imgsz': config.image_size,
                'lr0': config.lr0,
                'weight_decay': config.weight_decay,
                'warmup_epochs': config.warmup_epochs,
                'patience': config.patience,
                'device': self.device,
                'workers': config.workers,
                'save_period': config.save_period,
                'name': model_name,
                'project': 'wirevsion_models'
            }
            
            # 开始训练
            results = model.train(**train_args)
            
            # 保存最佳模型路径
            best_model_path = results.save_dir / "weights" / "best.pt"
            self.model_path = str(best_model_path)
            
            logger.success(f"模型训练完成，最佳模型: {self.model_path}")
            return True
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return False
    
    def evaluate_model(self, test_data_path: str) -> Dict[str, float]:
        """
        评估模型性能
        
        Args:
            test_data_path: 测试数据路径
            
        Returns:
            评估指标字典
        """
        if not self.is_loaded:
            logger.error("模型未加载，无法评估")
            return {}
        
        try:
            logger.info("开始模型评估...")
            
            # 执行验证
            results = self.model.val(data=test_data_path, device=self.device)
            
            # 提取关键指标
            metrics = {
                'mAP50': float(results.box.map50),      # mAP@0.5
                'mAP50-95': float(results.box.map),     # mAP@0.5:0.95
                'precision': float(results.box.mp),     # 精确率
                'recall': float(results.box.mr),        # 召回率
                'fitness': float(results.fitness)       # 综合指标
            }
            
            logger.success(f"模型评估完成: {metrics}")
            return metrics
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            return {}
    
    def export_model(self, format: str = "onnx", 
                    output_path: str = None) -> str:
        """
        导出模型到指定格式
        
        Args:
            format: 导出格式 ("onnx", "engine", "coreml", etc.)
            output_path: 输出路径
            
        Returns:
            导出文件路径
        """
        if not self.is_loaded:
            logger.error("模型未加载，无法导出")
            return ""
        
        try:
            logger.info(f"开始导出模型为 {format} 格式...")
            
            # 执行导出
            export_path = self.model.export(
                format=format,
                device=self.device
            )
            
            logger.success(f"模型导出成功: {export_path}")
            return str(export_path)
            
        except Exception as e:
            logger.error(f"模型导出失败: {e}")
            return ""
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if not self.is_loaded:
            return {"status": "未加载"}
        
        try:
            info = {
                "model_path": self.model_path,
                "device": self.device,
                "class_count": len(self.class_names),
                "class_names": self.class_names[:10],  # 只显示前10个类别
                "model_type": str(type(self.model).__name__),
                "is_loaded": self.is_loaded
            }
            
            # 如果可能，添加模型参数信息
            if hasattr(self.model, 'model'):
                try:
                    total_params = sum(p.numel() for p in self.model.model.parameters())
                    info["total_parameters"] = total_params
                except:
                    pass
            
            return info
            
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return {"status": "错误", "error": str(e)}
    
    def _get_coco_classes(self) -> List[str]:
        """获取COCO数据集的80个类别名称"""
        return [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]

# 工厂函数
def create_yolo_detector(model_path: str = None, device: str = "auto") -> YOLODetector:
    """
    创建YOLO检测器实例
    
    Args:
        model_path: 模型路径
        device: 计算设备
        
    Returns:
        YOLO检测器实例
    """
    detector = YOLODetector(model_path, device)
    
    # 如果指定了模型路径，尝试加载
    if model_path:
        detector.load_model(model_path)
    
    return detector 