#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVision 核心模块

包含所有核心检测算法和管理功能
"""

from .vision_engine import VisionEngine
from .template_matcher import TemplateMatcher
from .color_detector import ColorDetector
from .workflow_manager import WorkflowManager

# YOLO深度学习模块
try:
    from .yolo_detector import YOLODetector, DetectionResult, create_yolo_detector
    from .yolo_trainer import YOLOTrainer, TrainingConfig, DatasetInfo, create_yolo_trainer
except ImportError:
    # YOLO依赖未安装时的优雅处理
    YOLODetector = None
    DetectionResult = None
    YOLOTrainer = None
    TrainingConfig = None
    DatasetInfo = None
    create_yolo_detector = None
    create_yolo_trainer = None

# 系统检测模块
from .system_validator import SystemValidator, ValidationResult, SystemReport, create_system_validator

__all__ = [
    # 传统视觉检测
    'VisionEngine',
    'TemplateMatcher', 
    'ColorDetector',
    'WorkflowManager',
    
    # YOLO深度学习
    'YOLODetector',
    'DetectionResult',
    'YOLOTrainer',
    'TrainingConfig',
    'DatasetInfo',
    'create_yolo_detector',
    'create_yolo_trainer',
    
    # 系统检测
    'SystemValidator',
    'ValidationResult',
    'SystemReport',
    'create_system_validator'
] 