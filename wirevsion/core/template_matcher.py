#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模板匹配器
提供高精度的模板匹配功能
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from loguru import logger


class TemplateMatcher:
    """
    模板匹配器
    
    支持多种匹配方法和多模板匹配
    """
    
    def __init__(self):
        """初始化模板匹配器"""
        self.templates = {}  # 存储加载的模板
        
        # 匹配配置
        self.config = {
            'method': cv2.TM_CCOEFF_NORMED,  # 匹配方法
            'threshold': 0.8,                # 匹配阈值
            'scale_range': (0.8, 1.2),      # 缩放范围
            'scale_steps': 5,                # 缩放步数
            'multi_scale': False,            # 多尺度匹配
            'roi_enabled': False,            # ROI匹配
            'roi_region': None               # ROI区域
        }
        
        logger.info("模板匹配器初始化完成")
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置匹配配置
        
        Args:
            config: 配置字典
        """
        self.config.update(config)
        logger.info(f"更新模板匹配配置: {config}")
    
    def load_template(self, template_path: str, name: str = "default") -> bool:
        """
        加载模板图像
        
        Args:
            template_path: 模板图像路径
            name: 模板名称
            
        Returns:
            bool: 是否加载成功
        """
        try:
            # 检查文件是否存在
            if not Path(template_path).exists():
                logger.error(f"模板文件不存在: {template_path}")
                return False
            
            # 加载模板图像
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                logger.error(f"无法读取模板图像: {template_path}")
                return False
            
            # 存储模板信息
            self.templates[name] = {
                'image': template,
                'gray': cv2.cvtColor(template, cv2.COLOR_BGR2GRAY),
                'path': template_path,
                'size': (template.shape[1], template.shape[0]),  # (width, height)
                'created_time': Path(template_path).stat().st_mtime
            }
            
            logger.info(f"模板加载成功: {name} ({template.shape[1]}x{template.shape[0]})")
            return True
            
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            return False
    
    def load_template_from_image(self, image: np.ndarray, name: str = "default") -> bool:
        """
        从图像数组加载模板
        
        Args:
            image: 模板图像数组
            name: 模板名称
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if image is None or image.size == 0:
                logger.error("输入图像为空")
                return False
            
            # 确保图像是彩色的
            if len(image.shape) == 2:
                template = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
            else:
                template = image.copy()
            
            # 存储模板信息
            self.templates[name] = {
                'image': template,
                'gray': cv2.cvtColor(template, cv2.COLOR_BGR2GRAY),
                'path': None,
                'size': (template.shape[1], template.shape[0]),
                'created_time': None
            }
            
            logger.info(f"从图像加载模板成功: {name} ({template.shape[1]}x{template.shape[0]})")
            return True
            
        except Exception as e:
            logger.error(f"从图像加载模板失败: {e}")
            return False
    
    def remove_template(self, name: str) -> bool:
        """
        移除模板
        
        Args:
            name: 模板名称
            
        Returns:
            bool: 是否移除成功
        """
        if name in self.templates:
            del self.templates[name]
            logger.info(f"模板已移除: {name}")
            return True
        else:
            logger.warning(f"模板不存在: {name}")
            return False
    
    def has_templates(self) -> bool:
        """
        检查是否有加载的模板
        
        Returns:
            bool: 是否有模板
        """
        return len(self.templates) > 0
    
    def get_template_names(self) -> List[str]:
        """
        获取所有模板名称
        
        Returns:
            List[str]: 模板名称列表
        """
        return list(self.templates.keys())
    
    def match(self, image: np.ndarray, template_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        执行模板匹配
        
        Args:
            image: 待匹配的图像
            template_name: 指定的模板名称，如果为None则使用最佳匹配
            
        Returns:
            Dict[str, Any]: 匹配结果，包含位置、置信度等信息
        """
        if not self.has_templates():
            logger.warning("没有加载的模板")
            return None
        
        if image is None or image.size == 0:
            logger.error("输入图像为空")
            return None
        
        try:
            # 转换为灰度图像
            if len(image.shape) == 3:
                gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray_image = image.copy()
            
            best_result = None
            best_confidence = 0.0
            
            # 选择要匹配的模板
            templates_to_match = {}
            if template_name and template_name in self.templates:
                templates_to_match[template_name] = self.templates[template_name]
            else:
                templates_to_match = self.templates
            
            # 对每个模板进行匹配
            for name, template_info in templates_to_match.items():
                template_gray = template_info['gray']
                
                # 执行匹配
                if self.config['multi_scale']:
                    result = self._multi_scale_match(gray_image, template_gray, name)
                else:
                    result = self._single_scale_match(gray_image, template_gray, name)
                
                # 更新最佳结果
                if result and result['confidence'] > best_confidence:
                    best_confidence = result['confidence']
                    best_result = result
            
            return best_result
            
        except Exception as e:
            logger.error(f"模板匹配过程出错: {e}")
            return None
    
    def _single_scale_match(self, image: np.ndarray, template: np.ndarray, 
                           template_name: str) -> Optional[Dict[str, Any]]:
        """
        单尺度模板匹配
        
        Args:
            image: 待匹配图像
            template: 模板图像
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 匹配结果
        """
        try:
            # 应用ROI限制
            if self.config['roi_enabled'] and self.config['roi_region']:
                x, y, w, h = self.config['roi_region']
                roi_image = image[y:y+h, x:x+w]
                roi_offset = (x, y)
            else:
                roi_image = image
                roi_offset = (0, 0)
            
            # 检查模板尺寸
            if template.shape[0] > roi_image.shape[0] or template.shape[1] > roi_image.shape[1]:
                logger.warning(f"模板 {template_name} 尺寸大于图像，跳过匹配")
                return None
            
            # 执行模板匹配
            result = cv2.matchTemplate(roi_image, template, self.config['method'])
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 根据匹配方法选择最佳位置
            if self.config['method'] in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                best_loc = min_loc
                confidence = 1.0 - min_val  # 转换为置信度
            else:
                best_loc = max_loc
                confidence = max_val
            
            # 计算实际位置（考虑ROI偏移）
            actual_loc = (best_loc[0] + roi_offset[0], best_loc[1] + roi_offset[1])
            
            match_result = {
                'template_name': template_name,
                'confidence': float(confidence),
                'location': actual_loc,
                'size': (template.shape[1], template.shape[0]),
                'method': self.config['method'],
                'scale': 1.0
            }
            
            logger.debug(f"单尺度匹配完成: {template_name}, 置信度={confidence:.3f}")
            return match_result
            
        except Exception as e:
            logger.error(f"单尺度匹配出错: {e}")
            return None
    
    def _multi_scale_match(self, image: np.ndarray, template: np.ndarray, 
                          template_name: str) -> Optional[Dict[str, Any]]:
        """
        多尺度模板匹配
        
        Args:
            image: 待匹配图像
            template: 模板图像
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 匹配结果
        """
        try:
            scale_min, scale_max = self.config['scale_range']
            scale_steps = self.config['scale_steps']
            
            best_result = None
            best_confidence = 0.0
            
            # 生成缩放比例
            scales = np.linspace(scale_min, scale_max, scale_steps)
            
            for scale in scales:
                # 缩放模板
                new_width = int(template.shape[1] * scale)
                new_height = int(template.shape[0] * scale)
                
                if new_width <= 0 or new_height <= 0:
                    continue
                
                scaled_template = cv2.resize(template, (new_width, new_height))
                
                # 检查缩放后的模板是否适合图像
                if scaled_template.shape[0] > image.shape[0] or scaled_template.shape[1] > image.shape[1]:
                    continue
                
                # 执行单尺度匹配
                result = self._single_scale_match(image, scaled_template, template_name)
                
                if result and result['confidence'] > best_confidence:
                    best_confidence = result['confidence']
                    best_result = result
                    best_result['scale'] = scale  # 记录缩放比例
            
            if best_result:
                logger.debug(f"多尺度匹配完成: {template_name}, 最佳缩放={best_result['scale']:.2f}, 置信度={best_confidence:.3f}")
            
            return best_result
            
        except Exception as e:
            logger.error(f"多尺度匹配出错: {e}")
            return None
    
    def visualize_match(self, image: np.ndarray, result: Dict[str, Any]) -> np.ndarray:
        """
        可视化匹配结果
        
        Args:
            image: 原始图像
            result: 匹配结果
            
        Returns:
            np.ndarray: 标注后的图像
        """
        if not result:
            return image.copy()
        
        try:
            vis_image = image.copy()
            
            # 获取匹配位置和尺寸
            x, y = result['location']
            w, h = result['size']
            confidence = result['confidence']
            
            # 如果有缩放，调整尺寸
            if 'scale' in result:
                w = int(w * result['scale'])
                h = int(h * result['scale'])
            
            # 绘制矩形框
            color = (0, 255, 0) if confidence >= self.config['threshold'] else (0, 0, 255)
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 2)
            
            # 添加文本标注
            text = f"{result['template_name']}: {confidence:.3f}"
            cv2.putText(vis_image, text, (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            return vis_image
            
        except Exception as e:
            logger.error(f"可视化匹配结果出错: {e}")
            return image.copy()
    
    def get_template_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息
        
        Args:
            name: 模板名称
            
        Returns:
            Dict[str, Any]: 模板信息
        """
        if name not in self.templates:
            return None
        
        template_info = self.templates[name].copy()
        # 移除图像数据以减少内存占用
        template_info.pop('image', None)
        template_info.pop('gray', None)
        
        return template_info
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取匹配器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        template_info = {}
        for name, info in self.templates.items():
            template_info[name] = {
                'size': info['size'],
                'path': info['path']
            }
        
        return {
            'template_count': len(self.templates),
            'templates': template_info,
            'config': self.config.copy()
        } 