#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工作流管理器
管理检测流程的创建、保存、加载和执行
"""

import json
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from loguru import logger

from .vision_engine import VisionEngine, DetectionResult


@dataclass
class WorkflowStep:
    """工作流步骤"""
    step_id: str
    step_type: str  # template_match, roi_detection, color_detection
    name: str
    enabled: bool = True
    config: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.config is None:
            self.config = {}


@dataclass
class WorkflowConfig:
    """工作流配置"""
    name: str
    description: str = ""
    version: str = "1.0"
    steps: List[WorkflowStep] = None
    global_config: Dict[str, Any] = None
    created_time: Optional[str] = None
    modified_time: Optional[str] = None
    
    def __post_init__(self):
        if self.steps is None:
            self.steps = []
        if self.global_config is None:
            self.global_config = {}


class WorkflowManager:
    """
    工作流管理器
    
    负责管理检测流程的完整生命周期
    """
    
    def __init__(self, config_manager_or_dir = None):
        """
        初始化工作流管理器
        
        Args:
            config_manager_or_dir: ConfigManager实例或工作流目录字符串
        """
        # 兼容不同的初始化参数
        if config_manager_or_dir is None:
            workflow_dir = "configs/workflows"
        elif isinstance(config_manager_or_dir, str):
            workflow_dir = config_manager_or_dir
        else:
            # 假设是ConfigManager实例
            workflow_dir = "configs/workflows"
        
        self.workflow_dir = Path(workflow_dir)
        self.workflow_dir.mkdir(parents=True, exist_ok=True)
        
        self.vision_engine = VisionEngine()
        self.current_workflow: Optional[WorkflowConfig] = None
        
        # 支持的步骤类型
        self.supported_step_types = {
            'template_match': self._execute_template_match,
            'roi_detection': self._execute_roi_detection,
            'color_detection': self._execute_color_detection,
            'custom': self._execute_custom_step
        }
        
        logger.info(f"工作流管理器初始化完成，工作流目录: {self.workflow_dir}")
    
    def create_workflow(self, name: str, description: str = "") -> WorkflowConfig:
        """
        创建新的工作流
        
        Args:
            name: 工作流名称
            description: 工作流描述
            
        Returns:
            WorkflowConfig: 新创建的工作流配置
        """
        import datetime
        
        workflow = WorkflowConfig(
            name=name,
            description=description,
            created_time=datetime.datetime.now().isoformat(),
            modified_time=datetime.datetime.now().isoformat()
        )
        
        self.current_workflow = workflow
        logger.info(f"创建新工作流: {name}")
        
        return workflow
    
    def add_step(self, step_type: str, name: str, config: Dict[str, Any], 
                 step_id: Optional[str] = None) -> bool:
        """
        添加工作流步骤
        
        Args:
            step_type: 步骤类型
            name: 步骤名称
            config: 步骤配置
            step_id: 步骤ID，如果为None则自动生成
            
        Returns:
            bool: 是否添加成功
        """
        if not self.current_workflow:
            logger.error("当前没有活动的工作流")
            return False
        
        if step_type not in self.supported_step_types:
            logger.error(f"不支持的步骤类型: {step_type}")
            return False
        
        if step_id is None:
            step_id = f"{step_type}_{len(self.current_workflow.steps) + 1}"
        
        # 检查步骤ID是否已存在
        existing_ids = [step.step_id for step in self.current_workflow.steps]
        if step_id in existing_ids:
            logger.error(f"步骤ID已存在: {step_id}")
            return False
        
        step = WorkflowStep(
            step_id=step_id,
            step_type=step_type,
            name=name,
            config=config
        )
        
        self.current_workflow.steps.append(step)
        self._update_modified_time()
        
        logger.info(f"添加工作流步骤: {name} ({step_type})")
        return True
    
    def remove_step(self, step_id: str) -> bool:
        """
        移除工作流步骤
        
        Args:
            step_id: 步骤ID
            
        Returns:
            bool: 是否移除成功
        """
        if not self.current_workflow:
            logger.error("当前没有活动的工作流")
            return False
        
        for i, step in enumerate(self.current_workflow.steps):
            if step.step_id == step_id:
                removed_step = self.current_workflow.steps.pop(i)
                self._update_modified_time()
                logger.info(f"移除工作流步骤: {removed_step.name}")
                return True
        
        logger.warning(f"步骤不存在: {step_id}")
        return False
    
    def update_step(self, step_id: str, config: Dict[str, Any]) -> bool:
        """
        更新工作流步骤配置
        
        Args:
            step_id: 步骤ID
            config: 新的配置
            
        Returns:
            bool: 是否更新成功
        """
        if not self.current_workflow:
            logger.error("当前没有活动的工作流")
            return False
        
        for step in self.current_workflow.steps:
            if step.step_id == step_id:
                step.config.update(config)
                self._update_modified_time()
                logger.info(f"更新工作流步骤: {step.name}")
                return True
        
        logger.warning(f"步骤不存在: {step_id}")
        return False
    
    def save_workflow(self, filename: Optional[str] = None) -> bool:
        """
        保存工作流配置
        
        Args:
            filename: 文件名，如果为None则使用工作流名称
            
        Returns:
            bool: 是否保存成功
        """
        if not self.current_workflow:
            logger.error("当前没有活动的工作流")
            return False
        
        if filename is None:
            filename = f"{self.current_workflow.name}.yaml"
        
        if not filename.endswith(('.yaml', '.yml')):
            filename += '.yaml'
        
        filepath = self.workflow_dir / filename
        
        try:
            # 转换为字典格式
            workflow_dict = asdict(self.current_workflow)
            
            # 保存为YAML文件
            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump(workflow_dict, f, default_flow_style=False, 
                         allow_unicode=True, sort_keys=False)
            
            logger.info(f"工作流已保存: {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"保存工作流失败: {e}")
            return False
    
    def load_workflow(self, filename: str) -> bool:
        """
        加载工作流配置
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否加载成功
        """
        filepath = self.workflow_dir / filename
        
        if not filepath.exists():
            logger.error(f"工作流文件不存在: {filepath}")
            return False
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                workflow_dict = yaml.safe_load(f)
            
            # 重建WorkflowStep对象
            steps = []
            for step_dict in workflow_dict.get('steps', []):
                step = WorkflowStep(**step_dict)
                steps.append(step)
            
            # 创建WorkflowConfig对象
            workflow_dict['steps'] = steps
            self.current_workflow = WorkflowConfig(**workflow_dict)
            
            # 配置视觉引擎
            self._configure_vision_engine()
            
            logger.info(f"工作流已加载: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"加载工作流失败: {e}")
            return False
    
    def execute_workflow(self, image, progress_callback=None) -> Dict[str, Any]:
        """
        执行工作流
        
        Args:
            image: 输入图像
            progress_callback: 进度回调函数
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        if not self.current_workflow:
            logger.error("当前没有活动的工作流")
            return {'success': False, 'error': '没有加载工作流'}
        
        if image is None:
            logger.error("输入图像为空")
            return {'success': False, 'error': '输入图像为空'}
        
        try:
            results = []
            total_steps = len([s for s in self.current_workflow.steps if s.enabled])
            
            logger.info(f"开始执行工作流: {self.current_workflow.name}")
            
            for i, step in enumerate(self.current_workflow.steps):
                if not step.enabled:
                    continue
                
                # 更新进度
                if progress_callback:
                    progress = (i + 1) / total_steps * 100
                    progress_callback(progress, f"执行步骤: {step.name}")
                
                # 执行步骤
                step_result = self._execute_step(step, image, results)
                step_result['step_info'] = {
                    'step_id': step.step_id,
                    'step_type': step.step_type,
                    'name': step.name
                }
                
                results.append(step_result)
                
                # 如果步骤失败且配置为关键步骤，停止执行
                if not step_result.get('success', False) and step.config.get('critical', False):
                    logger.warning(f"关键步骤失败，停止执行: {step.name}")
                    break
                
                logger.debug(f"步骤完成: {step.name}")
            
            # 汇总结果
            execution_result = {
                'success': True,
                'workflow_name': self.current_workflow.name,
                'total_steps': total_steps,
                'completed_steps': len(results),
                'step_results': results,
                'overall_success': all(r.get('success', False) for r in results)
            }
            
            logger.info(f"工作流执行完成: {self.current_workflow.name}")
            return execution_result
            
        except Exception as e:
            logger.error(f"工作流执行出错: {e}")
            return {'success': False, 'error': str(e)}
    
    def _execute_step(self, step: WorkflowStep, image, previous_results: List[Dict]) -> Dict[str, Any]:
        """
        执行单个工作流步骤
        
        Args:
            step: 工作流步骤
            image: 输入图像
            previous_results: 前面步骤的结果
            
        Returns:
            Dict[str, Any]: 步骤执行结果
        """
        try:
            executor = self.supported_step_types.get(step.step_type)
            if not executor:
                return {'success': False, 'error': f'不支持的步骤类型: {step.step_type}'}
            
            result = executor(step, image, previous_results)
            return result
            
        except Exception as e:
            logger.error(f"执行步骤 {step.name} 时出错: {e}")
            return {'success': False, 'error': str(e)}
    
    def _execute_template_match(self, step: WorkflowStep, image, previous_results: List[Dict]) -> Dict[str, Any]:
        """执行模板匹配步骤"""
        try:
            config = step.config
            template_path = config.get('template_path')
            
            if not template_path:
                return {'success': False, 'error': '未指定模板路径'}
            
            # 加载模板
            if not self.vision_engine.template_matcher.load_template(template_path, step.step_id):
                return {'success': False, 'error': '加载模板失败'}
            
            # 设置匹配配置
            match_config = config.get('match_config', {})
            self.vision_engine.template_matcher.set_config(match_config)
            
            # 执行匹配
            match_result = self.vision_engine.template_matcher.match(image, step.step_id)
            
            if match_result:
                return {
                    'success': True,
                    'match_result': match_result,
                    'template_found': match_result['confidence'] >= match_config.get('threshold', 0.8)
                }
            else:
                return {'success': False, 'error': '模板匹配失败'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_roi_detection(self, step: WorkflowStep, image, previous_results: List[Dict]) -> Dict[str, Any]:
        """执行ROI检测步骤"""
        try:
            config = step.config
            
            # 从前面的模板匹配结果中获取位置信息
            template_result = None
            for result in previous_results:
                if result.get('match_result'):
                    template_result = result['match_result']
                    break
            
            if not template_result:
                return {'success': False, 'error': '未找到模板匹配结果'}
            
            # 计算ROI区域
            template_location = template_result['location']
            roi_config = config.get('roi_config', {})
            
            roi_x = template_location[0] + roi_config.get('offset_x', 0)
            roi_y = template_location[1] + roi_config.get('offset_y', 0)
            roi_w = roi_config.get('width', 100)
            roi_h = roi_config.get('height', 100)
            
            # 确保ROI在图像范围内
            roi_x = max(0, min(roi_x, image.shape[1] - 1))
            roi_y = max(0, min(roi_y, image.shape[0] - 1))
            roi_w = min(roi_w, image.shape[1] - roi_x)
            roi_h = min(roi_h, image.shape[0] - roi_y)
            
            if roi_w <= 0 or roi_h <= 0:
                return {'success': False, 'error': 'ROI区域无效'}
            
            # 提取ROI图像
            roi_image = image[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
            
            return {
                'success': True,
                'roi_location': (roi_x, roi_y, roi_w, roi_h),
                'roi_image': roi_image
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_color_detection(self, step: WorkflowStep, image, previous_results: List[Dict]) -> Dict[str, Any]:
        """执行颜色检测步骤"""
        try:
            config = step.config
            
            # 获取ROI图像
            roi_image = image
            roi_location = None
            
            for result in previous_results:
                if result.get('roi_image') is not None:
                    roi_image = result['roi_image']
                    roi_location = result.get('roi_location')
                    break
            
            # 执行颜色检测
            color_config = config.get('color_config', {})
            detection_result = self.vision_engine.color_detector.detect(roi_image, color_config)
            
            return {
                'success': True,
                'detection_result': detection_result,
                'roi_location': roi_location,
                'color_detected': detection_result.get('detected', False)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_custom_step(self, step: WorkflowStep, image, previous_results: List[Dict]) -> Dict[str, Any]:
        """执行自定义步骤"""
        # 留给用户扩展
        return {'success': True, 'message': '自定义步骤执行完成'}
    
    def _configure_vision_engine(self):
        """根据当前工作流配置视觉引擎"""
        if not self.current_workflow:
            return
        
        # 应用全局配置
        global_config = self.current_workflow.global_config
        if global_config:
            self.vision_engine.set_config(global_config)
    
    def _update_modified_time(self):
        """更新修改时间"""
        if self.current_workflow:
            import datetime
            self.current_workflow.modified_time = datetime.datetime.now().isoformat()
    
    def get_workflow_list(self) -> List[str]:
        """
        获取可用的工作流列表
        
        Returns:
            List[str]: 工作流文件名列表
        """
        workflow_files = []
        for file_path in self.workflow_dir.glob("*.yaml"):
            workflow_files.append(file_path.name)
        
        for file_path in self.workflow_dir.glob("*.yml"):
            workflow_files.append(file_path.name)
        
        return sorted(workflow_files)
    
    def get_current_workflow(self) -> Optional[WorkflowConfig]:
        """
        获取当前工作流
        
        Returns:
            WorkflowConfig: 当前工作流配置
        """
        return self.current_workflow
    
    def get_workflow_info(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        获取工作流信息（不加载完整配置）
        
        Args:
            filename: 工作流文件名
            
        Returns:
            Dict[str, Any]: 工作流基本信息
        """
        filepath = self.workflow_dir / filename
        
        if not filepath.exists():
            return None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                workflow_dict = yaml.safe_load(f)
            
            return {
                'name': workflow_dict.get('name', ''),
                'description': workflow_dict.get('description', ''),
                'version': workflow_dict.get('version', ''),
                'step_count': len(workflow_dict.get('steps', [])),
                'created_time': workflow_dict.get('created_time', ''),
                'modified_time': workflow_dict.get('modified_time', '')
            }
            
        except Exception as e:
            logger.error(f"读取工作流信息失败: {e}")
            return None 