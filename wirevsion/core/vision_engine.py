#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视觉检测引擎
负责协调各个检测组件，执行完整的检测流程
"""

import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from loguru import logger

from .template_matcher import TemplateMatcher
from .color_detector import ColorDetector


@dataclass
class DetectionResult:
    """检测结果数据类"""
    success: bool = False
    template_found: bool = False
    template_location: Optional[Tuple[int, int, int, int]] = None  # x, y, w, h
    template_confidence: float = 0.0
    color_detection_results: List[Dict] = None
    roi_areas: List[Dict] = None
    processing_time: float = 0.0
    error_message: str = ""
    
    def __post_init__(self):
        if self.color_detection_results is None:
            self.color_detection_results = []
        if self.roi_areas is None:
            self.roi_areas = []


class VisionEngine:
    """
    视觉检测引擎
    
    负责执行完整的视觉检测流程：
    1. 模板匹配定位
    2. ROI区域提取
    3. 颜色检测
    4. 结果汇总
    """
    
    def __init__(self):
        """初始化视觉检测引擎"""
        self.template_matcher = TemplateMatcher()
        self.color_detector = ColorDetector()
        
        # 检测配置
        self.config = {
            'template_threshold': 0.8,  # 模板匹配阈值
            'roi_enabled': True,        # 是否启用ROI检测
            'color_enabled': True,      # 是否启用颜色检测
            'debug_mode': False         # 调试模式
        }
        
        logger.info("视觉检测引擎初始化完成")
    
    def set_config(self, config: Dict[str, Any]) -> None:
        """
        设置检测配置
        
        Args:
            config: 配置字典
        """
        self.config.update(config)
        logger.info(f"更新检测配置: {config}")
    
    def load_template(self, template_path: str, name: str = "default") -> bool:
        """
        加载检测模板
        
        Args:
            template_path: 模板图像路径
            name: 模板名称
            
        Returns:
            bool: 是否加载成功
        """
        try:
            return self.template_matcher.load_template(template_path, name)
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
            return False
    
    def add_roi_area(self, name: str, x: int, y: int, w: int, h: int, 
                     color_config: Optional[Dict] = None) -> None:
        """
        添加ROI检测区域
        
        Args:
            name: ROI区域名称
            x, y, w, h: ROI区域坐标和尺寸（相对于模板）
            color_config: 颜色检测配置
        """
        roi_config = {
            'name': name,
            'x': x, 'y': y, 'w': w, 'h': h,
            'color_config': color_config or {}
        }
        
        # 存储ROI配置
        if not hasattr(self, 'roi_configs'):
            self.roi_configs = []
        self.roi_configs.append(roi_config)
        
        logger.info(f"添加ROI区域: {name} ({x}, {y}, {w}, {h})")
    
    def detect(self, image: np.ndarray) -> DetectionResult:
        """
        执行完整的视觉检测
        
        Args:
            image: 输入图像
            
        Returns:
            DetectionResult: 检测结果
        """
        import time
        start_time = time.time()
        
        result = DetectionResult()
        
        try:
            # 1. 模板匹配
            if self.template_matcher.has_templates():
                template_result = self.template_matcher.match(image)
                
                if template_result and template_result['confidence'] >= self.config['template_threshold']:
                    result.template_found = True
                    result.template_location = (
                        template_result['location'][0],
                        template_result['location'][1],
                        template_result['size'][0],
                        template_result['size'][1]
                    )
                    result.template_confidence = template_result['confidence']
                    
                    logger.debug(f"模板匹配成功: 置信度={result.template_confidence:.3f}")
                    
                    # 2. ROI区域检测
                    if self.config['roi_enabled'] and hasattr(self, 'roi_configs'):
                        roi_results = self._process_roi_areas(image, result.template_location)
                        result.roi_areas = roi_results
                        
                        # 3. 颜色检测
                        if self.config['color_enabled']:
                            color_results = self._process_color_detection(image, roi_results)
                            result.color_detection_results = color_results
                    
                    result.success = True
                else:
                    result.error_message = "模板匹配失败或置信度不足"
                    logger.warning(f"模板匹配失败: 置信度={template_result['confidence'] if template_result else 0:.3f}")
            else:
                result.error_message = "未加载检测模板"
                logger.warning("未加载检测模板")
        
        except Exception as e:
            result.error_message = f"检测过程出错: {str(e)}"
            logger.error(f"检测过程出错: {e}")
        
        finally:
            result.processing_time = time.time() - start_time
            logger.debug(f"检测完成，耗时: {result.processing_time:.3f}s")
        
        return result
    
    def _process_roi_areas(self, image: np.ndarray, template_location: Tuple[int, int, int, int]) -> List[Dict]:
        """
        处理ROI区域
        
        Args:
            image: 输入图像
            template_location: 模板位置 (x, y, w, h)
            
        Returns:
            List[Dict]: ROI区域信息列表
        """
        roi_results = []
        
        if not hasattr(self, 'roi_configs'):
            return roi_results
        
        template_x, template_y, template_w, template_h = template_location
        
        for roi_config in self.roi_configs:
            try:
                # 计算ROI在图像中的绝对位置
                roi_x = template_x + roi_config['x']
                roi_y = template_y + roi_config['y']
                roi_w = roi_config['w']
                roi_h = roi_config['h']
                
                # 确保ROI在图像范围内
                roi_x = max(0, min(roi_x, image.shape[1] - 1))
                roi_y = max(0, min(roi_y, image.shape[0] - 1))
                roi_w = min(roi_w, image.shape[1] - roi_x)
                roi_h = min(roi_h, image.shape[0] - roi_y)
                
                if roi_w > 0 and roi_h > 0:
                    # 提取ROI区域
                    roi_image = image[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
                    
                    roi_result = {
                        'name': roi_config['name'],
                        'location': (roi_x, roi_y, roi_w, roi_h),
                        'image': roi_image,
                        'color_config': roi_config['color_config']
                    }
                    
                    roi_results.append(roi_result)
                    logger.debug(f"处理ROI区域: {roi_config['name']}")
                else:
                    logger.warning(f"ROI区域超出图像范围: {roi_config['name']}")
                    
            except Exception as e:
                logger.error(f"处理ROI区域 {roi_config['name']} 时出错: {e}")
        
        return roi_results
    
    def _process_color_detection(self, image: np.ndarray, roi_areas: List[Dict]) -> List[Dict]:
        """
        处理颜色检测
        
        Args:
            image: 输入图像
            roi_areas: ROI区域列表
            
        Returns:
            List[Dict]: 颜色检测结果列表
        """
        color_results = []
        
        for roi_area in roi_areas:
            try:
                color_config = roi_area.get('color_config', {})
                if not color_config:
                    continue
                
                roi_image = roi_area['image']
                
                # 执行颜色检测
                detection_result = self.color_detector.detect(roi_image, color_config)
                
                color_result = {
                    'roi_name': roi_area['name'],
                    'roi_location': roi_area['location'],
                    'detection_result': detection_result,
                    'color_config': color_config
                }
                
                color_results.append(color_result)
                logger.debug(f"颜色检测完成: {roi_area['name']}")
                
            except Exception as e:
                logger.error(f"颜色检测出错 {roi_area['name']}: {e}")
        
        return color_results
    
    def visualize_result(self, image: np.ndarray, result: DetectionResult) -> np.ndarray:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            result: 检测结果
            
        Returns:
            np.ndarray: 标注后的图像
        """
        vis_image = image.copy()
        
        try:
            # 绘制模板匹配结果
            if result.template_found and result.template_location:
                x, y, w, h = result.template_location
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(vis_image, f"Template: {result.template_confidence:.3f}", 
                           (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 绘制ROI区域
            for roi_area in result.roi_areas:
                x, y, w, h = roi_area['location']
                cv2.rectangle(vis_image, (x, y), (x + w, y + h), (255, 0, 0), 1)
                cv2.putText(vis_image, roi_area['name'], 
                           (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            
            # 绘制颜色检测结果
            for color_result in result.color_detection_results:
                detection = color_result['detection_result']
                roi_name = color_result['roi_name']
                x, y, w, h = color_result['roi_location']
                
                # 根据检测结果选择颜色
                color = (0, 255, 0) if detection.get('detected', False) else (0, 0, 255)
                
                # 显示检测状态
                status = "PASS" if detection.get('detected', False) else "FAIL"
                cv2.putText(vis_image, f"{roi_name}: {status}", 
                           (x, y + h + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        except Exception as e:
            logger.error(f"可视化结果时出错: {e}")
        
        return vis_image
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取引擎状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            'templates_loaded': self.template_matcher.has_templates(),
            'template_count': len(self.template_matcher.templates) if hasattr(self.template_matcher, 'templates') else 0,
            'roi_count': len(self.roi_configs) if hasattr(self, 'roi_configs') else 0,
            'config': self.config.copy()
        } 