"""
YOLO模型训练管理器

该模块提供完整的YOLO模型训练流程管理，包括：
- 数据集准备和验证
- 训练过程监控
- 模型评估和比较
- 训练结果可视化

作者: 张玉龙
创建时间: 2025-01-XX
"""

import os
import yaml
import shutil
import json
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
import numpy as np
from PIL import Image
import cv2
from loguru import logger

from .yolo_detector import YOLODetector, TrainingConfig

@dataclass
class DatasetInfo:
    """数据集信息"""
    name: str                    # 数据集名称
    path: str                   # 数据集路径
    train_images: int           # 训练图像数量
    val_images: int             # 验证图像数量
    test_images: int = 0        # 测试图像数量
    classes: List[str] = None   # 类别列表
    class_count: int = 0        # 类别数量

@dataclass
class TrainingProgress:
    """训练进度信息"""
    epoch: int                  # 当前轮次
    total_epochs: int          # 总轮次
    train_loss: float          # 训练损失
    val_loss: float            # 验证损失
    mAP50: float               # mAP@0.5
    mAP50_95: float            # mAP@0.5:0.95
    best_fitness: float        # 最佳适应度
    elapsed_time: float        # 已用时间
    eta: float                 # 预计剩余时间

@dataclass
class TrainingResult:
    """训练结果"""
    model_path: str            # 模型路径
    config: TrainingConfig     # 训练配置
    final_metrics: Dict[str, float]  # 最终指标
    training_history: List[TrainingProgress]  # 训练历史
    dataset_info: DatasetInfo  # 数据集信息
    status: str                # 训练状态
    error_message: str = ""    # 错误信息

class YOLOTrainer:
    """YOLO模型训练管理器"""
    
    def __init__(self, workspace_dir: str = "wirevsion_workspace"):
        """
        初始化训练管理器
        
        Args:
            workspace_dir: 工作空间目录
        """
        self.workspace_dir = Path(workspace_dir)
        self.datasets_dir = self.workspace_dir / "datasets"
        self.models_dir = self.workspace_dir / "models"
        self.results_dir = self.workspace_dir / "results"
        
        # 创建目录结构
        self._create_directories()
        
        # 训练回调
        self.progress_callback: Optional[Callable[[TrainingProgress], None]] = None
        self.current_training: Optional[TrainingResult] = None
        
        logger.info(f"YOLO训练管理器初始化完成，工作空间: {self.workspace_dir}")
    
    def _create_directories(self):
        """创建必要的目录结构"""
        directories = [
            self.workspace_dir,
            self.datasets_dir,
            self.models_dir,
            self.results_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"创建目录: {directory}")
    
    def prepare_dataset(self, dataset_name: str, images_dir: str, 
                       annotations_dir: str, class_names: List[str],
                       train_ratio: float = 0.8, val_ratio: float = 0.2) -> DatasetInfo:
        """
        准备YOLO训练数据集
        
        Args:
            dataset_name: 数据集名称
            images_dir: 图像目录
            annotations_dir: 标注目录（YOLO格式）
            class_names: 类别名称列表
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            
        Returns:
            数据集信息
        """
        try:
            logger.info(f"开始准备数据集: {dataset_name}")
            
            # 创建数据集目录
            dataset_path = self.datasets_dir / dataset_name
            dataset_path.mkdir(exist_ok=True)
            
            # 创建子目录
            for split in ['train', 'val']:
                (dataset_path / 'images' / split).mkdir(parents=True, exist_ok=True)
                (dataset_path / 'labels' / split).mkdir(parents=True, exist_ok=True)
            
            # 获取所有图像文件
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                image_files.extend(Path(images_dir).glob(ext))
            
            if not image_files:
                raise ValueError(f"在 {images_dir} 中未找到图像文件")
            
            # 随机分割数据集
            np.random.shuffle(image_files)
            total_images = len(image_files)
            train_count = int(total_images * train_ratio)
            
            train_files = image_files[:train_count]
            val_files = image_files[train_count:]
            
            # 复制文件到相应目录
            def copy_files(files, split):
                copied_count = 0
                for img_file in files:
                    # 复制图像
                    dst_img = dataset_path / 'images' / split / img_file.name
                    shutil.copy2(img_file, dst_img)
                    
                    # 查找对应的标注文件
                    label_file = Path(annotations_dir) / f"{img_file.stem}.txt"
                    if label_file.exists():
                        dst_label = dataset_path / 'labels' / split / f"{img_file.stem}.txt"
                        shutil.copy2(label_file, dst_label)
                        copied_count += 1
                    else:
                        logger.warning(f"未找到标注文件: {label_file}")
                
                return copied_count
            
            train_count = copy_files(train_files, 'train')
            val_count = copy_files(val_files, 'val')
            
            # 创建数据集配置文件
            dataset_config = {
                'path': str(dataset_path),
                'train': 'images/train',
                'val': 'images/val',
                'nc': len(class_names),
                'names': class_names
            }
            
            config_file = dataset_path / 'dataset.yaml'
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
            
            # 创建数据集信息
            dataset_info = DatasetInfo(
                name=dataset_name,
                path=str(config_file),
                train_images=train_count,
                val_images=val_count,
                classes=class_names,
                class_count=len(class_names)
            )
            
            # 保存数据集信息
            info_file = dataset_path / 'info.json'
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(dataset_info), f, ensure_ascii=False, indent=2)
            
            logger.success(f"数据集准备完成: {dataset_info}")
            return dataset_info
            
        except Exception as e:
            logger.error(f"数据集准备失败: {e}")
            raise
    
    def validate_dataset(self, dataset_path: str) -> Dict[str, Any]:
        """
        验证数据集有效性
        
        Args:
            dataset_path: 数据集配置文件路径
            
        Returns:
            验证结果
        """
        try:
            logger.info(f"开始验证数据集: {dataset_path}")
            
            # 加载配置
            with open(dataset_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            dataset_root = Path(dataset_path).parent
            
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'statistics': {}
            }
            
            # 检查必要字段
            required_fields = ['path', 'train', 'val', 'nc', 'names']
            for field in required_fields:
                if field not in config:
                    validation_result['errors'].append(f"缺少必要字段: {field}")
                    validation_result['valid'] = False
            
            if not validation_result['valid']:
                return validation_result
            
            # 检查目录和文件
            for split in ['train', 'val']:
                images_dir = dataset_root / config[split]
                labels_dir = dataset_root / config[split].replace('images', 'labels')
                
                if not images_dir.exists():
                    validation_result['errors'].append(f"图像目录不存在: {images_dir}")
                    validation_result['valid'] = False
                    continue
                
                if not labels_dir.exists():
                    validation_result['errors'].append(f"标注目录不存在: {labels_dir}")
                    validation_result['valid'] = False
                    continue
                
                # 统计文件数量
                image_files = list(images_dir.glob('*'))
                label_files = list(labels_dir.glob('*.txt'))
                
                validation_result['statistics'][f'{split}_images'] = len(image_files)
                validation_result['statistics'][f'{split}_labels'] = len(label_files)
                
                # 检查图像和标注对应关系
                unmatched_images = 0
                for img_file in image_files:
                    label_file = labels_dir / f"{img_file.stem}.txt"
                    if not label_file.exists():
                        unmatched_images += 1
                
                if unmatched_images > 0:
                    warning_msg = f"{split}集中有 {unmatched_images} 个图像缺少标注文件"
                    validation_result['warnings'].append(warning_msg)
            
            # 检查类别数量一致性
            nc = config.get('nc', 0)
            names_count = len(config.get('names', []))
            if nc != names_count:
                validation_result['errors'].append(f"类别数量不匹配: nc={nc}, names数量={names_count}")
                validation_result['valid'] = False
            
            validation_result['statistics']['total_classes'] = nc
            validation_result['statistics']['class_names'] = config.get('names', [])
            
            logger.info(f"数据集验证完成: {validation_result}")
            return validation_result
            
        except Exception as e:
            logger.error(f"数据集验证失败: {e}")
            return {
                'valid': False,
                'errors': [str(e)],
                'warnings': [],
                'statistics': {}
            }
    
    def train_model(self, dataset_path: str, config: TrainingConfig, 
                   model_name: str = "custom_model") -> TrainingResult:
        """
        训练YOLO模型
        
        Args:
            dataset_path: 数据集配置文件路径
            config: 训练配置
            model_name: 模型名称
            
        Returns:
            训练结果
        """
        try:
            logger.info(f"开始训练模型: {model_name}")
            
            # 验证数据集
            validation = self.validate_dataset(dataset_path)
            if not validation['valid']:
                raise ValueError(f"数据集验证失败: {validation['errors']}")
            
            # 加载数据集信息
            dataset_info = self._load_dataset_info(dataset_path)
            
            # 创建YOLO检测器进行训练
            detector = YOLODetector()
            
            # 更新训练配置中的数据路径
            config.data_path = dataset_path
            
            # 初始化训练结果
            self.current_training = TrainingResult(
                model_path="",
                config=config,
                final_metrics={},
                training_history=[],
                dataset_info=dataset_info,
                status="training"
            )
            
            # 执行训练
            success = detector.train_model(config, model_name)
            
            if success:
                # 训练成功，更新结果
                self.current_training.model_path = detector.model_path
                self.current_training.status = "completed"
                
                # 评估模型
                try:
                    metrics = detector.evaluate_model(dataset_path)
                    self.current_training.final_metrics = metrics
                    logger.success(f"模型训练和评估完成: {metrics}")
                except Exception as e:
                    logger.warning(f"模型评估失败: {e}")
                    self.current_training.final_metrics = {}
                
                # 保存训练结果
                self._save_training_result(self.current_training, model_name)
                
            else:
                self.current_training.status = "failed"
                self.current_training.error_message = "训练过程失败"
            
            return self.current_training
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            if self.current_training:
                self.current_training.status = "failed"
                self.current_training.error_message = str(e)
                return self.current_training
            else:
                # 创建失败结果
                return TrainingResult(
                    model_path="",
                    config=config,
                    final_metrics={},
                    training_history=[],
                    dataset_info=DatasetInfo("", "", 0, 0),
                    status="failed",
                    error_message=str(e)
                )
    
    def _load_dataset_info(self, dataset_path: str) -> DatasetInfo:
        """加载数据集信息"""
        try:
            dataset_dir = Path(dataset_path).parent
            info_file = dataset_dir / 'info.json'
            
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    info_data = json.load(f)
                return DatasetInfo(**info_data)
            else:
                # 从配置文件推断信息
                with open(dataset_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                return DatasetInfo(
                    name=Path(dataset_path).parent.name,
                    path=dataset_path,
                    train_images=0,  # 需要实际计算
                    val_images=0,    # 需要实际计算
                    classes=config.get('names', []),
                    class_count=config.get('nc', 0)
                )
                
        except Exception as e:
            logger.error(f"加载数据集信息失败: {e}")
            return DatasetInfo("unknown", dataset_path, 0, 0)
    
    def _save_training_result(self, result: TrainingResult, model_name: str):
        """保存训练结果"""
        try:
            result_dir = self.results_dir / model_name
            result_dir.mkdir(exist_ok=True)
            
            # 保存训练结果
            result_file = result_dir / 'training_result.json'
            with open(result_file, 'w', encoding='utf-8') as f:
                # 转换为可序列化格式
                result_dict = asdict(result)
                json.dump(result_dict, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"训练结果已保存: {result_file}")
            
        except Exception as e:
            logger.error(f"保存训练结果失败: {e}")
    
    def list_datasets(self) -> List[DatasetInfo]:
        """列出所有可用数据集"""
        datasets = []
        
        try:
            for dataset_dir in self.datasets_dir.iterdir():
                if dataset_dir.is_dir():
                    config_file = dataset_dir / 'dataset.yaml'
                    if config_file.exists():
                        dataset_info = self._load_dataset_info(str(config_file))
                        datasets.append(dataset_info)
            
            logger.info(f"找到 {len(datasets)} 个数据集")
            return datasets
            
        except Exception as e:
            logger.error(f"列出数据集失败: {e}")
            return []
    
    def list_trained_models(self) -> List[Dict[str, Any]]:
        """列出所有训练好的模型"""
        models = []
        
        try:
            for result_dir in self.results_dir.iterdir():
                if result_dir.is_dir():
                    result_file = result_dir / 'training_result.json'
                    if result_file.exists():
                        with open(result_file, 'r', encoding='utf-8') as f:
                            result_data = json.load(f)
                        
                        model_info = {
                            'name': result_dir.name,
                            'model_path': result_data.get('model_path', ''),
                            'status': result_data.get('status', 'unknown'),
                            'final_metrics': result_data.get('final_metrics', {}),
                            'dataset_name': result_data.get('dataset_info', {}).get('name', 'unknown')
                        }
                        models.append(model_info)
            
            logger.info(f"找到 {len(models)} 个训练模型")
            return models
            
        except Exception as e:
            logger.error(f"列出训练模型失败: {e}")
            return []
    
    def delete_dataset(self, dataset_name: str) -> bool:
        """删除数据集"""
        try:
            dataset_path = self.datasets_dir / dataset_name
            if dataset_path.exists():
                shutil.rmtree(dataset_path)
                logger.info(f"数据集已删除: {dataset_name}")
                return True
            else:
                logger.warning(f"数据集不存在: {dataset_name}")
                return False
                
        except Exception as e:
            logger.error(f"删除数据集失败: {e}")
            return False
    
    def delete_model(self, model_name: str) -> bool:
        """删除训练模型"""
        try:
            result_path = self.results_dir / model_name
            if result_path.exists():
                shutil.rmtree(result_path)
                logger.info(f"训练模型已删除: {model_name}")
                return True
            else:
                logger.warning(f"训练模型不存在: {model_name}")
                return False
                
        except Exception as e:
            logger.error(f"删除训练模型失败: {e}")
            return False
    
    def set_progress_callback(self, callback: Callable[[TrainingProgress], None]):
        """设置训练进度回调函数"""
        self.progress_callback = callback
        logger.info("训练进度回调函数已设置")

# 工厂函数
def create_yolo_trainer(workspace_dir: str = "wirevsion_workspace") -> YOLOTrainer:
    """
    创建YOLO训练管理器实例
    
    Args:
        workspace_dir: 工作空间目录
        
    Returns:
        YOLO训练管理器实例
    """
    return YOLOTrainer(workspace_dir) 