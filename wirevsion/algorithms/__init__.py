"""
WireVision 算法模块

统一的算法接口和实现，按功能分类组织：
- 图像处理算法 (Image Processing)
- 特征检测算法 (Feature Detection) 
- 目标检测算法 (Object Detection)
- 测量算法 (Measurement)
- 深度学习算法 (Deep Learning)

每个算法都继承自BaseAlgorithm，实现标准接口
"""

from .base_algorithm import BaseAlgorithm, AlgorithmResult, AlgorithmConfig
from .registry import AlgorithmRegistry, register_algorithm

# 图像处理算法
from .image_processing import (
    GaussianBlurAlgorithm,
    MedianBlurAlgorithm,
    BilateralFilterAlgorithm,
    MorphologyAlgorithm,
    ThresholdAlgorithm,
    EdgeDetectionAlgorithm,
    ColorSpaceAlgorithm,
    HistogramAlgorithm,
    ContrastAlgorithm,
    NoiseReductionAlgorithm
)

# 特征检测算法
from .feature_detection import (
    TemplateMatchingAlgorithm,
    CornerDetectionAlgorithm,
    BlobDetectionAlgorithm,
    LineDetectionAlgorithm,
    CircleDetectionAlgorithm,
    ContourDetectionAlgorithm,
    KeyPointDetectionAlgorithm
)

# 目标检测算法
from .object_detection import (
    ColorDetectionAlgorithm,
    ShapeDetectionAlgorithm,
    TextDetectionAlgorithm,
    BarcodeDetectionAlgorithm,
    FaceDetectionAlgorithm
)

# 测量算法
from .measurement import (
    DistanceMeasurementAlgorithm,
    AngleMeasurementAlgorithm,
    AreaMeasurementAlgorithm,
    GeometryAnalysisAlgorithm,
    DimensionMeasurementAlgorithm
)

# 深度学习算法
from .deep_learning import (
    YOLODetectionAlgorithm,
    ClassificationAlgorithm,
    SegmentationAlgorithm,
    PoseEstimationAlgorithm
)

# 图像源算法
from .image_source import (
    CameraSourceAlgorithm,
    FileSourceAlgorithm,
    NetworkSourceAlgorithm,
    VideoSourceAlgorithm
)

# 位置修正算法
from .position_correction import (
    AffineTransformAlgorithm,
    PerspectiveTransformAlgorithm,
    RotationCorrectionAlgorithm,
    TranslationCorrectionAlgorithm,
    ScaleCorrectionAlgorithm
)

# 算法注册表实例
algorithm_registry = AlgorithmRegistry()

def initialize_algorithms():
    """初始化并注册所有算法"""
    
    # 注册图像源算法
    algorithm_registry.register("image_source", "camera", CameraSourceAlgorithm)
    algorithm_registry.register("image_source", "file", FileSourceAlgorithm)
    algorithm_registry.register("image_source", "network", NetworkSourceAlgorithm)
    algorithm_registry.register("image_source", "video", VideoSourceAlgorithm)
    
    # 注册图像处理算法
    algorithm_registry.register("image_processing", "gaussian_blur", GaussianBlurAlgorithm)
    algorithm_registry.register("image_processing", "median_blur", MedianBlurAlgorithm)
    algorithm_registry.register("image_processing", "bilateral_filter", BilateralFilterAlgorithm)
    algorithm_registry.register("image_processing", "morphology", MorphologyAlgorithm)
    algorithm_registry.register("image_processing", "threshold", ThresholdAlgorithm)
    algorithm_registry.register("image_processing", "edge_detection", EdgeDetectionAlgorithm)
    algorithm_registry.register("image_processing", "color_space", ColorSpaceAlgorithm)
    algorithm_registry.register("image_processing", "histogram", HistogramAlgorithm)
    algorithm_registry.register("image_processing", "contrast", ContrastAlgorithm)
    algorithm_registry.register("image_processing", "noise_reduction", NoiseReductionAlgorithm)
    
    # 注册特征检测算法
    algorithm_registry.register("feature_detection", "template_matching", TemplateMatchingAlgorithm)
    algorithm_registry.register("feature_detection", "corner_detection", CornerDetectionAlgorithm)
    algorithm_registry.register("feature_detection", "blob_detection", BlobDetectionAlgorithm)
    algorithm_registry.register("feature_detection", "line_detection", LineDetectionAlgorithm)
    algorithm_registry.register("feature_detection", "circle_detection", CircleDetectionAlgorithm)
    algorithm_registry.register("feature_detection", "contour_detection", ContourDetectionAlgorithm)
    algorithm_registry.register("feature_detection", "keypoint_detection", KeyPointDetectionAlgorithm)
    
    # 注册目标检测算法
    algorithm_registry.register("object_detection", "color_detection", ColorDetectionAlgorithm)
    algorithm_registry.register("object_detection", "shape_detection", ShapeDetectionAlgorithm)
    algorithm_registry.register("object_detection", "text_detection", TextDetectionAlgorithm)
    algorithm_registry.register("object_detection", "barcode_detection", BarcodeDetectionAlgorithm)
    algorithm_registry.register("object_detection", "face_detection", FaceDetectionAlgorithm)
    
    # 注册测量算法
    algorithm_registry.register("measurement", "distance_measurement", DistanceMeasurementAlgorithm)
    algorithm_registry.register("measurement", "angle_measurement", AngleMeasurementAlgorithm)
    algorithm_registry.register("measurement", "area_measurement", AreaMeasurementAlgorithm)
    algorithm_registry.register("measurement", "geometry_analysis", GeometryAnalysisAlgorithm)
    algorithm_registry.register("measurement", "dimension_measurement", DimensionMeasurementAlgorithm)
    
    # 注册深度学习算法
    algorithm_registry.register("deep_learning", "yolo_detection", YOLODetectionAlgorithm)
    algorithm_registry.register("deep_learning", "classification", ClassificationAlgorithm)
    algorithm_registry.register("deep_learning", "segmentation", SegmentationAlgorithm)
    algorithm_registry.register("deep_learning", "pose_estimation", PoseEstimationAlgorithm)
    
    # 注册位置修正算法
    algorithm_registry.register("position_correction", "affine_transform", AffineTransformAlgorithm)
    algorithm_registry.register("position_correction", "perspective_transform", PerspectiveTransformAlgorithm)
    algorithm_registry.register("position_correction", "rotation_correction", RotationCorrectionAlgorithm)
    algorithm_registry.register("position_correction", "translation_correction", TranslationCorrectionAlgorithm)
    algorithm_registry.register("position_correction", "scale_correction", ScaleCorrectionAlgorithm)

# 初始化算法
initialize_algorithms()

__all__ = [
    'BaseAlgorithm',
    'AlgorithmResult', 
    'AlgorithmConfig',
    'AlgorithmRegistry',
    'algorithm_registry',
    'initialize_algorithms'
] 