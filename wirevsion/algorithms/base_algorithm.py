"""
算法基类模块

定义所有算法的基础接口和数据结构
"""

import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
import cv2
from loguru import logger


class AlgorithmType(Enum):
    """算法类型枚举"""
    IMAGE_SOURCE = "image_source"           # 图像源
    IMAGE_PROCESSING = "image_processing"   # 图像处理
    FEATURE_DETECTION = "feature_detection" # 特征检测
    OBJECT_DETECTION = "object_detection"   # 目标检测
    MEASUREMENT = "measurement"             # 测量算法
    DEEP_LEARNING = "deep_learning"         # 深度学习
    POSITION_CORRECTION = "position_correction"  # 位置修正


class ConnectionType(Enum):
    """连接点类型枚举"""
    INPUT = "input"     # 输入连接点
    OUTPUT = "output"   # 输出连接点


class DataType(Enum):
    """数据类型枚举"""
    IMAGE = "image"         # 图像数据
    ROI = "roi"            # ROI区域数据
    POINTS = "points"       # 点数据
    CONTOURS = "contours"   # 轮廓数据
    FEATURES = "features"   # 特征数据
    RESULTS = "results"     # 结果数据
    NUMBERS = "numbers"     # 数值数据
    TEXT = "text"          # 文本数据
    ANY = "any"            # 任意数据


@dataclass
class ConnectionPoint:
    """连接点定义"""
    id: str                          # 连接点ID
    name: str                        # 连接点名称
    connection_type: ConnectionType  # 连接点类型
    data_type: DataType             # 数据类型
    required: bool = True           # 是否必需
    description: str = ""           # 描述


@dataclass
class AlgorithmConfig:
    """算法配置"""
    algorithm_id: str               # 算法ID
    algorithm_type: AlgorithmType   # 算法类型
    algorithm_name: str             # 算法名称
    parameters: Dict[str, Any] = field(default_factory=dict)  # 参数配置
    roi_regions: List[Dict[str, Any]] = field(default_factory=list)  # ROI区域
    enabled: bool = True            # 是否启用


@dataclass
class AlgorithmResult:
    """算法执行结果"""
    success: bool                   # 是否成功
    data: Dict[str, Any] = field(default_factory=dict)  # 结果数据
    image: Optional[np.ndarray] = None  # 处理后的图像
    message: str = ""               # 消息
    execution_time: float = 0.0     # 执行时间
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据


@dataclass
class ROI:
    """ROI区域定义"""
    x: int                          # X坐标
    y: int                          # Y坐标
    width: int                      # 宽度
    height: int                     # 高度
    name: str = ""                  # ROI名称
    type: str = "rectangle"         # ROI类型 (rectangle, circle, polygon)
    points: List[Tuple[int, int]] = field(default_factory=list)  # 多边形点


class BaseAlgorithm(ABC):
    """
    算法基类
    
    所有算法都必须继承此类，实现标准接口
    """
    
    def __init__(self):
        """初始化算法"""
        self.id = str(uuid.uuid4())
        self.config: Optional[AlgorithmConfig] = None
        self._input_connections: List[ConnectionPoint] = []
        self._output_connections: List[ConnectionPoint] = []
        self._setup_connections()
        
        logger.debug(f"算法 {self.get_display_name()} 初始化完成")
    
    @abstractmethod
    def get_algorithm_type(self) -> AlgorithmType:
        """获取算法类型"""
        pass
    
    @abstractmethod
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        pass
    
    @abstractmethod
    def get_display_name(self) -> str:
        """获取显示名称"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取算法描述"""
        pass
    
    @abstractmethod
    def get_icon_path(self) -> str:
        """获取图标路径"""
        pass
    
    @abstractmethod
    def get_default_parameters(self) -> Dict[str, Any]:
        """获取默认参数"""
        pass
    
    @abstractmethod
    def get_parameter_schema(self) -> Dict[str, Any]:
        """获取参数模式定义"""
        pass
    
    @abstractmethod
    def _setup_connections(self):
        """设置连接点"""
        pass
    
    @abstractmethod
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行算法"""
        pass
    
    def validate_inputs(self, inputs: Dict[str, Any]) -> bool:
        """
        验证输入数据
        
        Args:
            inputs: 输入数据
            
        Returns:
            是否有效
        """
        for connection in self._input_connections:
            if connection.required and connection.name not in inputs:
                logger.error(f"缺少必需输入: {connection.name}")
                return False
        return True
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        验证参数
        
        Args:
            parameters: 参数
            
        Returns:
            是否有效
        """
        schema = self.get_parameter_schema()
        # 这里可以实现详细的参数验证逻辑
        return True
    
    def get_input_connections(self) -> List[ConnectionPoint]:
        """获取输入连接点"""
        return self._input_connections
    
    def get_output_connections(self) -> List[ConnectionPoint]:
        """获取输出连接点"""
        return self._output_connections
    
    def add_input_connection(self, name: str, data_type: DataType, 
                           required: bool = True, description: str = ""):
        """添加输入连接点"""
        connection = ConnectionPoint(
            id=f"input_{name}",
            name=name,
            connection_type=ConnectionType.INPUT,
            data_type=data_type,
            required=required,
            description=description
        )
        self._input_connections.append(connection)
    
    def add_output_connection(self, name: str, data_type: DataType, 
                            description: str = ""):
        """添加输出连接点"""
        connection = ConnectionPoint(
            id=f"output_{name}",
            name=name,
            connection_type=ConnectionType.OUTPUT,
            data_type=data_type,
            required=False,
            description=description
        )
        self._output_connections.append(connection)
    
    def create_roi_mask(self, image_shape: Tuple[int, int], roi: ROI) -> np.ndarray:
        """
        根据ROI创建掩码
        
        Args:
            image_shape: 图像形状 (height, width)
            roi: ROI区域
            
        Returns:
            掩码图像
        """
        mask = np.zeros(image_shape, dtype=np.uint8)
        
        if roi.type == "rectangle":
            cv2.rectangle(mask, (roi.x, roi.y), 
                         (roi.x + roi.width, roi.y + roi.height), 255, -1)
        elif roi.type == "circle":
            center = (roi.x + roi.width // 2, roi.y + roi.height // 2)
            radius = min(roi.width, roi.height) // 2
            cv2.circle(mask, center, radius, 255, -1)
        elif roi.type == "polygon" and roi.points:
            points = np.array(roi.points, np.int32)
            cv2.fillPoly(mask, [points], 255)
        
        return mask
    
    def apply_roi_mask(self, image: np.ndarray, roi: ROI) -> np.ndarray:
        """
        应用ROI掩码到图像
        
        Args:
            image: 输入图像
            roi: ROI区域
            
        Returns:
            应用掩码后的图像
        """
        mask = self.create_roi_mask(image.shape[:2], roi)
        
        # 创建三通道掩码
        if len(image.shape) == 3:
            mask = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR)
        
        # 应用掩码
        result = cv2.bitwise_and(image, mask)
        return result
    
    def get_roi_image(self, image: np.ndarray, roi: ROI) -> np.ndarray:
        """
        提取ROI区域图像
        
        Args:
            image: 输入图像
            roi: ROI区域
            
        Returns:
            ROI区域图像
        """
        if roi.type == "rectangle":
            x, y = roi.x, roi.y
            w, h = roi.width, roi.height
            
            # 确保坐标在图像范围内
            x = max(0, min(x, image.shape[1] - 1))
            y = max(0, min(y, image.shape[0] - 1))
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)
            
            return image[y:y+h, x:x+w]
        else:
            # 对于其他类型，使用掩码提取
            masked = self.apply_roi_mask(image, roi)
            return masked
    
    def draw_roi_on_image(self, image: np.ndarray, roi: ROI, 
                         color: Tuple[int, int, int] = (0, 255, 0),
                         thickness: int = 2) -> np.ndarray:
        """
        在图像上绘制ROI
        
        Args:
            image: 输入图像
            roi: ROI区域
            color: 绘制颜色
            thickness: 线条粗细
            
        Returns:
            绘制ROI后的图像
        """
        result = image.copy()
        
        if roi.type == "rectangle":
            cv2.rectangle(result, (roi.x, roi.y), 
                         (roi.x + roi.width, roi.y + roi.height), 
                         color, thickness)
        elif roi.type == "circle":
            center = (roi.x + roi.width // 2, roi.y + roi.height // 2)
            radius = min(roi.width, roi.height) // 2
            cv2.circle(result, center, radius, color, thickness)
        elif roi.type == "polygon" and roi.points:
            points = np.array(roi.points, np.int32)
            cv2.polylines(result, [points], True, color, thickness)
        
        # 绘制ROI名称
        if roi.name:
            text_pos = (roi.x + 5, roi.y + 20)
            cv2.putText(result, roi.name, text_pos, cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, color, 1)
        
        return result
    
    def create_config(self, parameters: Dict[str, Any] = None, 
                     roi_regions: List[ROI] = None) -> AlgorithmConfig:
        """
        创建算法配置
        
        Args:
            parameters: 算法参数
            roi_regions: ROI区域列表
            
        Returns:
            算法配置
        """
        if parameters is None:
            parameters = self.get_default_parameters()
        
        if roi_regions is None:
            roi_regions = []
        
        # 将ROI对象转换为字典
        roi_dicts = []
        for roi in roi_regions:
            if isinstance(roi, ROI):
                roi_dict = {
                    'x': roi.x,
                    'y': roi.y,
                    'width': roi.width,
                    'height': roi.height,
                    'name': roi.name,
                    'type': roi.type,
                    'points': roi.points
                }
                roi_dicts.append(roi_dict)
            else:
                roi_dicts.append(roi)
        
        return AlgorithmConfig(
            algorithm_id=self.id,
            algorithm_type=self.get_algorithm_type(),
            algorithm_name=self.get_algorithm_name(),
            parameters=parameters,
            roi_regions=roi_dicts
        )
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.get_display_name()} ({self.get_algorithm_name()})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"<{self.__class__.__name__}: {self.get_display_name()}>"


def create_error_result(message: str, execution_time: float = 0.0) -> AlgorithmResult:
    """
    创建错误结果
    
    Args:
        message: 错误消息
        execution_time: 执行时间
        
    Returns:
        错误结果
    """
    return AlgorithmResult(
        success=False,
        message=message,
        execution_time=execution_time
    )


def create_success_result(data: Dict[str, Any] = None, 
                         image: np.ndarray = None,
                         message: str = "执行成功",
                         execution_time: float = 0.0,
                         metadata: Dict[str, Any] = None) -> AlgorithmResult:
    """
    创建成功结果
    
    Args:
        data: 结果数据
        image: 处理后的图像
        message: 消息
        execution_time: 执行时间
        metadata: 元数据
        
    Returns:
        成功结果
    """
    return AlgorithmResult(
        success=True,
        data=data or {},
        image=image,
        message=message,
        execution_time=execution_time,
        metadata=metadata or {}
    ) 