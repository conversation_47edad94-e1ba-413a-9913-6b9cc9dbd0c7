"""
目标检测算法模块

提供各种目标检测算法：颜色检测、形状检测、文本检测等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper


class ColorDetectionAlgorithm(BaseAlgorithm):
    """颜色检测算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION

    def get_algorithm_name(self) -> str:
        return "color_detection"

    def get_display_name(self) -> str:
        return "颜色检测"

    def get_description(self) -> str:
        return "基于HSV颜色空间进行颜色检测"

    def get_icon_path(self) -> str:
        return "resources/icons/color_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "color_space": "HSV",
            "lower_bound": [0, 50, 50],
            "upper_bound": [10, 255, 255],
            "morphology_enabled": True,
            "morphology_kernel_size": 5,
            "min_area": 100,
            "max_area": 10000
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "color_space": {
                "type": "string",
                "enum": ["HSV", "RGB", "LAB"],
                "description": "颜色空间"
            },
            "lower_bound": {
                "type": "array",
                "items": {"type": "integer", "minimum": 0, "maximum": 255},
                "minItems": 3,
                "maxItems": 3,
                "description": "颜色下限值"
            },
            "upper_bound": {
                "type": "array",
                "items": {"type": "integer", "minimum": 0, "maximum": 255},
                "minItems": 3,
                "maxItems": 3,
                "description": "颜色上限值"
            },
            "morphology_enabled": {
                "type": "boolean",
                "description": "启用形态学处理"
            },
            "min_area": {
                "type": "number",
                "minimum": 0,
                "description": "最小面积"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("roi", DataType.ROI, False, "检测区域")

        self.add_output_connection("mask", DataType.IMAGE, "颜色掩码")
        self.add_output_connection("contours", DataType.CONTOURS, "检测到的轮廓")
        self.add_output_connection("results", DataType.RESULTS, "检测结果")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")

    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行颜色检测"""
        start_time = time.time()

        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)

            image = inputs["image"]
            parameters = config.parameters

            # 颜色空间转换
            color_space = parameters.get("color_space", "HSV")
            if color_space == "HSV":
                converted = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            elif color_space == "LAB":
                converted = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            else:
                converted = image  # RGB/BGR

            # 设置颜色范围
            lower_bound = np.array(parameters.get("lower_bound", [0, 50, 50]))
            upper_bound = np.array(parameters.get("upper_bound", [10, 255, 255]))

            # 创建颜色掩码
            mask = cv2.inRange(converted, lower_bound, upper_bound)

            # 形态学处理（可选）
            if parameters.get("morphology_enabled", True):
                kernel_size = parameters.get("morphology_kernel_size", 5)
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
                mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
                mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 过滤轮廓
            min_area = parameters.get("min_area", 100)
            max_area = parameters.get("max_area", 10000)

            filtered_contours = []
            detection_results = []

            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area <= area <= max_area:
                    filtered_contours.append(contour)

                    # 计算边界矩形
                    x, y, w, h = cv2.boundingRect(contour)

                    # 计算中心点
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                    else:
                        cx, cy = x + w // 2, y + h // 2

                    detection_results.append({
                        "area": float(area),
                        "center": {"x": cx, "y": cy},
                        "bounding_rect": {"x": x, "y": y, "width": w, "height": h},
                        "perimeter": float(cv2.arcLength(contour, True))
                    })

            # 创建标注图像
            annotated_image = image.copy()
            for i, contour in enumerate(filtered_contours):
                # 绘制轮廓
                cv2.drawContours(annotated_image, [contour], -1, (0, 255, 0), 2)

                # 绘制边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (255, 0, 0), 2)

                # 标注序号
                cv2.putText(annotated_image, str(i), (x, y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            execution_time = time.time() - start_time

            return create_success_result(
                data={
                    "mask": mask,
                    "contours": [contour.tolist() for contour in filtered_contours],
                    "results": detection_results,
                    "detection_count": len(filtered_contours),
                    "color_space": color_space,
                    "bounds": {"lower": lower_bound.tolist(), "upper": upper_bound.tolist()}
                },
                image=annotated_image,
                message=f"检测到 {len(filtered_contours)} 个颜色目标",
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"颜色检测失败: {e}")
            return create_error_result(f"颜色检测失败: {e}", time.time() - start_time)


class ShapeDetectionAlgorithm(BaseAlgorithm):
    """形状检测算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION

    def get_algorithm_name(self) -> str:
        return "shape_detection"

    def get_display_name(self) -> str:
        return "形状检测"

    def get_description(self) -> str:
        return "检测基本几何形状：圆形、矩形、三角形等"

    def get_icon_path(self) -> str:
        return "resources/icons/shape_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "shapes_to_detect": ["circle", "rectangle", "triangle"],
            "min_area": 100,
            "max_area": 10000,
            "approximation_accuracy": 0.02,
            "circularity_threshold": 0.7,
            "rectangularity_threshold": 0.85
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "shapes_to_detect": {
                "type": "array",
                "items": {
                    "type": "string",
                    "enum": ["circle", "rectangle", "triangle", "polygon"]
                },
                "description": "要检测的形状类型"
            },
            "min_area": {
                "type": "number",
                "minimum": 0,
                "description": "最小面积"
            },
            "approximation_accuracy": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 0.1,
                "description": "轮廓近似精度"
            },
            "circularity_threshold": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 1.0,
                "description": "圆形判定阈值"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, False, "输入轮廓")

        self.add_output_connection("shapes", DataType.RESULTS, "检测到的形状")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")

    def _classify_shape(self, contour: np.ndarray, parameters: Dict[str, Any]) -> str:
        """分类形状"""
        # 计算轮廓近似
        epsilon = parameters.get("approximation_accuracy", 0.02) * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 根据顶点数判断形状
        vertex_count = len(approx)

        if vertex_count == 3:
            return "triangle"
        elif vertex_count == 4:
            # 进一步判断是否为矩形
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h

            # 计算轮廓面积与边界矩形面积的比例
            contour_area = cv2.contourArea(contour)
            rect_area = w * h
            rectangularity = contour_area / rect_area if rect_area > 0 else 0

            if rectangularity > parameters.get("rectangularity_threshold", 0.85):
                if 0.8 <= aspect_ratio <= 1.2:
                    return "square"
                else:
                    return "rectangle"
            else:
                return "quadrilateral"
        else:
            # 判断是否为圆形
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                if circularity > parameters.get("circularity_threshold", 0.7):
                    return "circle"

            if vertex_count > 4:
                return "polygon"
            else:
                return "unknown"

    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行形状检测"""
        start_time = time.time()

        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)

            image = inputs["image"]
            parameters = config.parameters

            # 获取轮廓
            if "contours" in inputs and inputs["contours"]:
                # 使用提供的轮廓
                contours = [np.array(contour, dtype=np.int32) for contour in inputs["contours"]]
            else:
                # 从图像中检测轮廓
                if len(image.shape) == 3:
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = image

                # 二值化
                _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
                contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 过滤轮廓
            min_area = parameters.get("min_area", 100)
            max_area = parameters.get("max_area", 10000)
            shapes_to_detect = parameters.get("shapes_to_detect", ["circle", "rectangle", "triangle"])

            detected_shapes = []
            annotated_image = image.copy()

            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                if area < min_area or area > max_area:
                    continue

                # 分类形状
                shape_type = self._classify_shape(contour, parameters)

                if shape_type in shapes_to_detect or "all" in shapes_to_detect:
                    # 计算几何属性
                    x, y, w, h = cv2.boundingRect(contour)

                    # 计算中心点
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                    else:
                        cx, cy = x + w // 2, y + h // 2

                    shape_info = {
                        "id": i,
                        "type": shape_type,
                        "area": float(area),
                        "perimeter": float(cv2.arcLength(contour, True)),
                        "center": {"x": cx, "y": cy},
                        "bounding_rect": {"x": x, "y": y, "width": w, "height": h}
                    }

                    # 添加特定形状的属性
                    if shape_type == "circle":
                        (center_x, center_y), radius = cv2.minEnclosingCircle(contour)
                        shape_info["circle"] = {
                            "center": {"x": float(center_x), "y": float(center_y)},
                            "radius": float(radius)
                        }

                    detected_shapes.append(shape_info)

                    # 绘制形状
                    color = self._get_shape_color(shape_type)
                    cv2.drawContours(annotated_image, [contour], -1, color, 2)

                    # 标注形状类型
                    cv2.putText(annotated_image, f"{shape_type}_{i}", (x, y - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

            execution_time = time.time() - start_time

            return create_success_result(
                data={
                    "shapes": detected_shapes,
                    "shape_count": len(detected_shapes),
                    "shape_types": list(set(shape["type"] for shape in detected_shapes))
                },
                image=annotated_image,
                message=f"检测到 {len(detected_shapes)} 个形状",
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"形状检测失败: {e}")
            return create_error_result(f"形状检测失败: {e}", time.time() - start_time)

    def _get_shape_color(self, shape_type: str) -> Tuple[int, int, int]:
        """获取形状对应的颜色"""
        color_map = {
            "circle": (0, 255, 0),      # 绿色
            "rectangle": (255, 0, 0),   # 蓝色
            "square": (0, 0, 255),      # 红色
            "triangle": (255, 255, 0),  # 青色
            "polygon": (255, 0, 255),   # 紫色
            "unknown": (128, 128, 128)  # 灰色
        }
        return color_map.get(shape_type, (255, 255, 255))


# 其他目标检测算法的占位符实现
class TextDetectionAlgorithm(BaseAlgorithm):
    """文本检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION

    def get_algorithm_name(self) -> str:
        return "text_detection"

    def get_display_name(self) -> str:
        return "文本检测"

    def get_description(self) -> str:
        return "使用EAST和MSER算法检测图像中的文本区域"

    def get_icon_path(self) -> str:
        return "resources/icons/text_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "method": "mser",  # mser, contour_based
            "min_area": 50,
            "max_area": 10000,
            "aspect_ratio_min": 0.1,
            "aspect_ratio_max": 10.0,
            "solidity_threshold": 0.3,
            "extent_threshold": 0.2
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "method": {
                "type": "string",
                "enum": ["mser", "contour_based"],
                "description": "文本检测方法"
            },
            "min_area": {
                "type": "integer",
                "minimum": 10,
                "maximum": 1000,
                "description": "最小文本区域面积"
            },
            "max_area": {
                "type": "integer",
                "minimum": 100,
                "maximum": 50000,
                "description": "最大文本区域面积"
            },
            "aspect_ratio_min": {
                "type": "number",
                "minimum": 0.01,
                "maximum": 1.0,
                "description": "最小宽高比"
            },
            "aspect_ratio_max": {
                "type": "number",
                "minimum": 1.0,
                "maximum": 20.0,
                "description": "最大宽高比"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("text_regions", DataType.RESULTS, "检测到的文本区域")
        self.add_output_connection("image", DataType.IMAGE, "标记文本区域的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行文本检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        method = parameters.get("method", "mser")
        min_area = parameters.get("min_area", 50)
        max_area = parameters.get("max_area", 10000)
        aspect_ratio_min = parameters.get("aspect_ratio_min", 0.1)
        aspect_ratio_max = parameters.get("aspect_ratio_max", 10.0)
        solidity_threshold = parameters.get("solidity_threshold", 0.3)
        extent_threshold = parameters.get("extent_threshold", 0.2)

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        text_regions = []
        result_image = image.copy()

        if method == "mser":
            # 使用MSER检测文本区域
            mser = cv2.MSER_create()
            regions, _ = mser.detectRegions(gray)

            for i, region in enumerate(regions):
                # 计算区域属性
                area = len(region)
                if area < min_area or area > max_area:
                    continue

                # 获取边界矩形
                hull = cv2.convexHull(region.reshape(-1, 1, 2))
                x, y, w, h = cv2.boundingRect(hull)

                # 检查宽高比
                aspect_ratio = w / h if h > 0 else 0
                if aspect_ratio < aspect_ratio_min or aspect_ratio > aspect_ratio_max:
                    continue

                text_region = {
                    "id": i,
                    "method": "mser",
                    "bbox": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "area": int(area),
                    "aspect_ratio": float(aspect_ratio),
                    "center": {"x": int(x + w/2), "y": int(y + h/2)}
                }
                text_regions.append(text_region)

                # 绘制文本区域
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(result_image, f"T{i}", (x, y - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        elif method == "contour_based":
            # 基于轮廓的文本检测
            # 预处理：形态学操作增强文本区域
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            processed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)

            # 二值化
            _, binary = cv2.threshold(processed, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                if area < min_area or area > max_area:
                    continue

                # 获取边界矩形
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0

                if aspect_ratio < aspect_ratio_min or aspect_ratio > aspect_ratio_max:
                    continue

                # 计算形状特征
                hull = cv2.convexHull(contour)
                hull_area = cv2.contourArea(hull)
                solidity = area / hull_area if hull_area > 0 else 0

                rect_area = w * h
                extent = area / rect_area if rect_area > 0 else 0

                # 过滤非文本区域
                if solidity < solidity_threshold or extent < extent_threshold:
                    continue

                text_region = {
                    "id": i,
                    "method": "contour_based",
                    "bbox": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "area": int(area),
                    "aspect_ratio": float(aspect_ratio),
                    "solidity": float(solidity),
                    "extent": float(extent),
                    "center": {"x": int(x + w/2), "y": int(y + h/2)}
                }
                text_regions.append(text_region)

                # 绘制文本区域
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 0), 2)
                cv2.putText(result_image, f"T{i}", (x, y - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

        return create_success_result(
            data={
                "text_regions": text_regions,
                "region_count": len(text_regions),
                "method": method,
                "detection_params": {
                    "min_area": min_area,
                    "max_area": max_area,
                    "aspect_ratio_range": [aspect_ratio_min, aspect_ratio_max]
                }
            },
            image=result_image,
            message=f"文本检测完成，找到 {len(text_regions)} 个文本区域"
        )


class BarcodeDetectionAlgorithm(BaseAlgorithm):
    """条码检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION

    def get_algorithm_name(self) -> str:
        return "barcode_detection"

    def get_display_name(self) -> str:
        return "条码检测"

    def get_description(self) -> str:
        return "检测和识别一维/二维条码（需要pyzbar库）"

    def get_icon_path(self) -> str:
        return "resources/icons/barcode_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "barcode_types": ["all"],  # all, qr, code128, ean13, etc.
            "enhance_image": True,
            "min_area": 100,
            "max_area": 50000
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "barcode_types": {
                "type": "array",
                "items": {
                    "type": "string",
                    "enum": ["all", "qr", "code128", "ean13", "ean8", "code39", "code93", "datamatrix"]
                },
                "description": "要检测的条码类型"
            },
            "enhance_image": {
                "type": "boolean",
                "description": "是否增强图像"
            },
            "min_area": {
                "type": "integer",
                "minimum": 10,
                "maximum": 1000,
                "description": "最小条码区域面积"
            },
            "max_area": {
                "type": "integer",
                "minimum": 100,
                "maximum": 100000,
                "description": "最大条码区域面积"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("barcodes", DataType.RESULTS, "检测到的条码信息")
        self.add_output_connection("image", DataType.IMAGE, "标记条码的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行条码检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        try:
            # 尝试导入pyzbar库
            from pyzbar import pyzbar
        except ImportError:
            return create_error_result("条码检测需要安装pyzbar库: pip install pyzbar")

        enhance_image = parameters.get("enhance_image", True)
        barcode_types = parameters.get("barcode_types", ["all"])
        min_area = parameters.get("min_area", 100)
        max_area = parameters.get("max_area", 50000)

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 图像增强
        if enhance_image:
            # 直方图均衡化
            gray = cv2.equalizeHist(gray)

            # 高斯模糊去噪
            gray = cv2.GaussianBlur(gray, (3, 3), 0)

        # 检测条码
        detected_barcodes = pyzbar.decode(gray)

        barcodes_data = []
        result_image = image.copy()

        for i, barcode in enumerate(detected_barcodes):
            # 获取条码信息
            barcode_data = barcode.data.decode('utf-8')
            barcode_type = barcode.type

            # 过滤条码类型
            if "all" not in barcode_types and barcode_type.lower() not in [bt.lower() for bt in barcode_types]:
                continue

            # 获取边界框
            points = barcode.polygon
            if len(points) == 4:
                # 计算边界矩形
                x_coords = [p.x for p in points]
                y_coords = [p.y for p in points]
                x, y = min(x_coords), min(y_coords)
                w, h = max(x_coords) - x, max(y_coords) - y

                # 检查面积
                area = w * h
                if area < min_area or area > max_area:
                    continue

                barcode_info = {
                    "id": i,
                    "data": barcode_data,
                    "type": barcode_type,
                    "bbox": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "area": int(area),
                    "center": {"x": int(x + w/2), "y": int(y + h/2)},
                    "polygon": [{"x": int(p.x), "y": int(p.y)} for p in points],
                    "quality": barcode.quality if hasattr(barcode, 'quality') else None
                }
                barcodes_data.append(barcode_info)

                # 绘制条码边界
                pts = np.array([[p.x, p.y] for p in points], dtype=np.int32)
                cv2.polylines(result_image, [pts], True, (0, 255, 0), 2)

                # 标注条码信息
                cv2.putText(result_image, f"{barcode_type}", (x, y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(result_image, barcode_data[:20] + "..." if len(barcode_data) > 20 else barcode_data,
                           (x, y + h + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

        return create_success_result(
            data={
                "barcodes": barcodes_data,
                "barcode_count": len(barcodes_data),
                "detected_types": list(set(bc["type"] for bc in barcodes_data)),
                "detection_params": {
                    "enhance_image": enhance_image,
                    "barcode_types": barcode_types,
                    "area_range": [min_area, max_area]
                }
            },
            image=result_image,
            message=f"条码检测完成，找到 {len(barcodes_data)} 个条码"
        )


class FaceDetectionAlgorithm(BaseAlgorithm):
    """人脸检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.OBJECT_DETECTION

    def get_algorithm_name(self) -> str:
        return "face_detection"

    def get_display_name(self) -> str:
        return "人脸检测"

    def get_description(self) -> str:
        return "使用Haar级联分类器检测图像中的人脸"

    def get_icon_path(self) -> str:
        return "resources/icons/face_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "scale_factor": 1.1,
            "min_neighbors": 5,
            "min_size": 30,
            "max_size": 300,
            "cascade_file": "haarcascade_frontalface_default.xml"
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "scale_factor": {
                "type": "number",
                "minimum": 1.01,
                "maximum": 2.0,
                "description": "图像缩放因子"
            },
            "min_neighbors": {
                "type": "integer",
                "minimum": 1,
                "maximum": 20,
                "description": "最小邻居数"
            },
            "min_size": {
                "type": "integer",
                "minimum": 10,
                "maximum": 200,
                "description": "最小人脸尺寸"
            },
            "max_size": {
                "type": "integer",
                "minimum": 50,
                "maximum": 1000,
                "description": "最大人脸尺寸"
            },
            "cascade_file": {
                "type": "string",
                "enum": [
                    "haarcascade_frontalface_default.xml",
                    "haarcascade_frontalface_alt.xml",
                    "haarcascade_frontalface_alt2.xml",
                    "haarcascade_profileface.xml"
                ],
                "description": "级联分类器文件"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("faces", DataType.RESULTS, "检测到的人脸信息")
        self.add_output_connection("image", DataType.IMAGE, "标记人脸的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行人脸检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        scale_factor = parameters.get("scale_factor", 1.1)
        min_neighbors = parameters.get("min_neighbors", 5)
        min_size = parameters.get("min_size", 30)
        max_size = parameters.get("max_size", 300)
        cascade_file = parameters.get("cascade_file", "haarcascade_frontalface_default.xml")

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        try:
            # 加载级联分类器
            cascade_path = cv2.data.haarcascades + cascade_file
            face_cascade = cv2.CascadeClassifier(cascade_path)

            if face_cascade.empty():
                return create_error_result(f"无法加载级联分类器: {cascade_file}")

            # 检测人脸
            faces = face_cascade.detectMultiScale(
                gray,
                scaleFactor=scale_factor,
                minNeighbors=min_neighbors,
                minSize=(min_size, min_size),
                maxSize=(max_size, max_size)
            )

            faces_data = []
            result_image = image.copy()

            for i, (x, y, w, h) in enumerate(faces):
                # 计算人脸属性
                center_x = x + w // 2
                center_y = y + h // 2
                area = w * h
                aspect_ratio = w / h if h > 0 else 0

                face_info = {
                    "id": i,
                    "bbox": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "center": {"x": int(center_x), "y": int(center_y)},
                    "area": int(area),
                    "aspect_ratio": float(aspect_ratio),
                    "confidence": 1.0  # Haar级联不提供置信度，设为1.0
                }
                faces_data.append(face_info)

                # 绘制人脸边界框
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 0), 2)

                # 标注人脸ID
                cv2.putText(result_image, f"Face {i}", (x, y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

                # 绘制中心点
                cv2.circle(result_image, (center_x, center_y), 3, (0, 255, 0), -1)

            return create_success_result(
                data={
                    "faces": faces_data,
                    "face_count": len(faces_data),
                    "detection_params": {
                        "scale_factor": scale_factor,
                        "min_neighbors": min_neighbors,
                        "size_range": [min_size, max_size],
                        "cascade_file": cascade_file
                    }
                },
                image=result_image,
                message=f"人脸检测完成，找到 {len(faces_data)} 张人脸"
            )

        except Exception as e:
            return create_error_result(f"人脸检测失败: {e}")