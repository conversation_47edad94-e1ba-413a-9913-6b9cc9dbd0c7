#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
算法装饰器模块

提供算法执行的通用装饰器，减少重复代码
"""

import time
import functools
from typing import Dict, Any, Callable
from loguru import logger

from .base_algorithm import AlgorithmConfig, AlgorithmResult, create_error_result, create_success_result


def algorithm_executor(func: Callable) -> Callable:
    """
    算法执行装饰器
    
    统一处理：
    - 执行时间记录
    - 输入验证
    - 异常处理
    - 日志记录
    
    Args:
        func: 要装饰的算法执行函数
        
    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    def wrapper(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        start_time = time.time()
        algorithm_name = self.get_display_name()
        
        try:
            logger.debug(f"开始执行算法: {algorithm_name}")
            
            # 输入验证
            if not self.validate_inputs(inputs):
                error_msg = f"算法 {algorithm_name} 输入验证失败"
                logger.error(error_msg)
                return create_error_result(error_msg, time.time() - start_time)
            
            # 参数验证
            if not self.validate_parameters(config.parameters):
                error_msg = f"算法 {algorithm_name} 参数验证失败"
                logger.error(error_msg)
                return create_error_result(error_msg, time.time() - start_time)
            
            # 执行算法
            result = func(self, inputs, config)
            
            # 确保返回结果包含执行时间
            if isinstance(result, AlgorithmResult):
                result.execution_time = time.time() - start_time
            
            # 记录成功日志
            if result.success:
                logger.info(f"算法 {algorithm_name} 执行成功，耗时: {result.execution_time:.3f}s")
            else:
                logger.warning(f"算法 {algorithm_name} 执行失败: {result.message}")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"算法 {algorithm_name} 执行异常: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return create_error_result(error_msg, execution_time)
    
    return wrapper


def performance_monitor(func: Callable) -> Callable:
    """
    性能监控装饰器
    
    监控算法执行的性能指标
    """
    @functools.wraps(func)
    def wrapper(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        import psutil
        import threading
        
        # 记录开始时的系统状态
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        start_cpu_time = process.cpu_times()
        
        # 执行原函数
        result = func(self, inputs, config)
        
        # 记录结束时的系统状态
        end_memory = process.memory_info().rss / 1024 / 1024  # MB
        end_cpu_time = process.cpu_times()
        
        # 计算性能指标
        memory_delta = end_memory - start_memory
        cpu_time_delta = (end_cpu_time.user - start_cpu_time.user) + \
                        (end_cpu_time.system - start_cpu_time.system)
        
        # 添加性能元数据
        if isinstance(result, AlgorithmResult):
            result.metadata.update({
                'performance': {
                    'memory_start_mb': start_memory,
                    'memory_end_mb': end_memory,
                    'memory_delta_mb': memory_delta,
                    'cpu_time_delta_s': cpu_time_delta,
                    'thread_id': threading.get_ident()
                }
            })
            
            # 性能警告
            if memory_delta > 100:  # 内存增长超过100MB
                logger.warning(f"算法 {self.get_display_name()} 内存使用较高: +{memory_delta:.1f}MB")
        
        return result
    
    return wrapper


def cache_result(cache_key_func: Callable = None):
    """
    结果缓存装饰器
    
    Args:
        cache_key_func: 生成缓存键的函数
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @functools.wraps(func)
        def wrapper(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
            # 生成缓存键
            if cache_key_func:
                cache_key = cache_key_func(self, inputs, config)
            else:
                # 默认缓存键生成策略
                image_hash = hash(inputs.get('image', b'').tobytes()) if 'image' in inputs else 0
                params_str = str(sorted(config.parameters.items()))
                cache_key = f"{self.get_algorithm_name()}_{image_hash}_{hash(params_str)}"
            
            # 检查缓存
            if cache_key in cache:
                logger.debug(f"算法 {self.get_display_name()} 使用缓存结果")
                cached_result = cache[cache_key]
                # 创建新的结果对象，避免修改缓存
                return AlgorithmResult(
                    success=cached_result.success,
                    data=cached_result.data.copy(),
                    image=cached_result.image.copy() if cached_result.image is not None else None,
                    message=cached_result.message,
                    execution_time=0.0,  # 缓存命中时间为0
                    metadata=cached_result.metadata.copy()
                )
            
            # 执行算法并缓存结果
            result = func(self, inputs, config)
            
            if result.success:
                cache[cache_key] = result
                # 限制缓存大小
                if len(cache) > 100:
                    # 删除最旧的缓存项
                    oldest_key = next(iter(cache))
                    del cache[oldest_key]
            
            return result
        
        return wrapper
    return decorator


def roi_processor(func: Callable) -> Callable:
    """
    ROI处理装饰器
    
    自动处理ROI区域的提取和结果合并
    """
    @functools.wraps(func)
    def wrapper(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        # 检查是否有ROI配置
        roi_regions = config.roi_regions
        
        if not roi_regions or not inputs.get('image') is not None:
            # 没有ROI或没有图像，直接执行
            return func(self, inputs, config)
        
        # 处理多个ROI区域
        image = inputs['image']
        roi_results = []
        combined_image = image.copy()
        
        for i, roi_config in enumerate(roi_regions):
            try:
                # 创建ROI对象
                from .base_algorithm import ROI
                roi = ROI(**roi_config)
                
                # 提取ROI图像
                roi_image = self.get_roi_image(image, roi)
                
                # 创建ROI专用的输入
                roi_inputs = inputs.copy()
                roi_inputs['image'] = roi_image
                
                # 执行算法
                roi_result = func(self, roi_inputs, config)
                
                if roi_result.success:
                    # 将结果映射回原图像坐标
                    roi_results.append({
                        'roi_index': i,
                        'roi_name': roi.name,
                        'roi_location': (roi.x, roi.y, roi.width, roi.height),
                        'result': roi_result
                    })
                    
                    # 在组合图像上绘制ROI
                    combined_image = self.draw_roi_on_image(combined_image, roi)
                
            except Exception as e:
                logger.error(f"处理ROI {i} 时出错: {e}")
        
        # 合并结果
        if roi_results:
            return create_success_result(
                data={'roi_results': roi_results},
                image=combined_image,
                message=f"处理了 {len(roi_results)} 个ROI区域"
            )
        else:
            return create_error_result("所有ROI处理失败")
    
    return wrapper


def input_transformer(transform_func: Callable):
    """
    输入变换装饰器
    
    在算法执行前对输入进行变换处理
    
    Args:
        transform_func: 输入变换函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
            try:
                # 变换输入
                transformed_inputs = transform_func(inputs, config)
                # 执行算法
                return func(self, transformed_inputs, config)
            except Exception as e:
                logger.error(f"输入变换失败: {e}")
                return create_error_result(f"输入变换失败: {e}")
        
        return wrapper
    return decorator


def output_transformer(transform_func: Callable):
    """
    输出变换装饰器
    
    在算法执行后对输出进行变换处理
    
    Args:
        transform_func: 输出变换函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
            # 执行算法
            result = func(self, inputs, config)
            
            if result.success:
                try:
                    # 变换输出
                    transformed_result = transform_func(result, inputs, config)
                    return transformed_result
                except Exception as e:
                    logger.error(f"输出变换失败: {e}")
                    result.success = False
                    result.message = f"输出变换失败: {e}"
            
            return result
        
        return wrapper
    return decorator


# 组合装饰器
def standard_algorithm_wrapper(
    enable_performance_monitor: bool = True,
    enable_cache: bool = False,
    enable_roi_processing: bool = False
):
    """
    标准算法包装器
    
    组合多个装饰器，提供完整的算法执行包装
    
    Args:
        enable_performance_monitor: 是否启用性能监控
        enable_cache: 是否启用结果缓存
        enable_roi_processing: 是否启用ROI处理
    """
    def decorator(func: Callable) -> Callable:
        # 从内到外应用装饰器
        wrapped_func = func
        
        if enable_roi_processing:
            wrapped_func = roi_processor(wrapped_func)
        
        if enable_cache:
            wrapped_func = cache_result()(wrapped_func)
        
        if enable_performance_monitor:
            wrapped_func = performance_monitor(wrapped_func)
        
        # 算法执行器必须在最外层
        wrapped_func = algorithm_executor(wrapped_func)
        
        return wrapped_func
    
    return decorator 