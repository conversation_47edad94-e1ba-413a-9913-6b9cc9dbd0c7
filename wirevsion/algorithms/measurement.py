#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测量算法模块

提供各种测量算法：距离测量、角度测量、面积测量、几何分析等
"""

import time
import cv2
import numpy as np
import math
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper
from ..utils.image_utils import ImageUtils


class DistanceMeasurementAlgorithm(BaseAlgorithm):
    """距离测量算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT

    def get_algorithm_name(self) -> str:
        return "distance_measurement"

    def get_display_name(self) -> str:
        return "距离测量"

    def get_description(self) -> str:
        return "测量图像中两点之间的距离"

    def get_icon_path(self) -> str:
        return "resources/icons/distance_measurement.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit": 1.0,  # 每单位的像素数
            "unit": "pixel",         # 测量单位
            "line_thickness": 2,     # 绘制线条粗细
            "point_radius": 5,       # 点标记半径
            "text_size": 0.7,       # 文字大小
            "precision": 2          # 小数点精度
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 1000.0,
                "description": "每单位对应的像素数"
            },
            "unit": {
                "type": "string",
                "enum": ["pixel", "mm", "cm", "inch"],
                "description": "测量单位"
            },
            "line_thickness": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "绘制线条粗细"
            },
            "precision": {
                "type": "integer",
                "minimum": 0,
                "maximum": 6,
                "description": "小数点精度"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("points", DataType.POINTS, True, "测量点对")
        self.add_output_connection("distances", DataType.RESULTS, "距离测量结果")
        self.add_output_connection("image", DataType.IMAGE, "标记测量的图像")

    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False,
        enable_roi_processing=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行距离测量 - 使用装饰器优化版本"""
        # 装饰器已处理输入验证、时间记录、异常处理等

        image = inputs["image"]
        points = inputs["points"]
        parameters = config.parameters

        # 参数提取
        pixels_per_unit = parameters.get("pixels_per_unit", 1.0)
        unit = parameters.get("unit", "pixel")
        line_thickness = parameters.get("line_thickness", 2)
        point_radius = parameters.get("point_radius", 5)
        text_size = parameters.get("text_size", 0.7)
        precision = parameters.get("precision", 2)

        # 处理点数据
        if isinstance(points, list) and len(points) >= 2:
            point_pairs = []
            # 假设points是成对的点 [p1, p2, p3, p4, ...] -> [(p1,p2), (p3,p4), ...]
            for i in range(0, len(points) - 1, 2):
                if i + 1 < len(points):
                    point_pairs.append((points[i], points[i + 1]))
        else:
            # 如果只有两个点，直接使用
            if len(points) >= 2:
                point_pairs = [(points[0], points[1])]
            else:
                point_pairs = []

        # 计算距离
        distances = []
        result_image = image.copy()

        for idx, (point1, point2) in enumerate(point_pairs):
            # 提取坐标
            if isinstance(point1, dict):
                x1, y1 = point1.get("x", 0), point1.get("y", 0)
            else:
                x1, y1 = point1[0], point1[1]

            if isinstance(point2, dict):
                x2, y2 = point2.get("x", 0), point2.get("y", 0)
            else:
                x2, y2 = point2[0], point2[1]

            # 计算像素距离
            pixel_distance = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

            # 转换为实际单位
            actual_distance = pixel_distance / pixels_per_unit

            # 记录结果
            distance_info = {
                "point1": {"x": x1, "y": y1},
                "point2": {"x": x2, "y": y2},
                "pixel_distance": round(pixel_distance, precision),
                "actual_distance": round(actual_distance, precision),
                "unit": unit
            }
            distances.append(distance_info)

            # 绘制测量线和点
            pt1 = (int(x1), int(y1))
            pt2 = (int(x2), int(y2))

            # 绘制线条
            cv2.line(result_image, pt1, pt2, (0, 255, 0), line_thickness)

            # 绘制端点
            cv2.circle(result_image, pt1, point_radius, (255, 0, 0), -1)
            cv2.circle(result_image, pt2, point_radius, (255, 0, 0), -1)

            # 绘制距离文本
            mid_x = int((x1 + x2) / 2)
            mid_y = int((y1 + y2) / 2) - 10

            distance_text = f"{actual_distance:.{precision}f} {unit}"
            cv2.putText(result_image, distance_text, (mid_x, mid_y),
                       cv2.FONT_HERSHEY_SIMPLEX, text_size, (0, 255, 0), 2)

        return create_success_result(
            data={
                "distances": distances,
                "measurement_count": len(distances),
                "unit": unit,
                "pixels_per_unit": pixels_per_unit
            },
            image=result_image,
            message=f"距离测量完成，测量了 {len(distances)} 段距离"
        )


class AngleMeasurementAlgorithm(BaseAlgorithm):
    """角度测量算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT

    def get_algorithm_name(self) -> str:
        return "angle_measurement"

    def get_display_name(self) -> str:
        return "角度测量"

    def get_description(self) -> str:
        return "测量图像中三点组成的角度"

    def get_icon_path(self) -> str:
        return "resources/icons/angle_measurement.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "angle_unit": "degree",     # degree 或 radian
            "line_thickness": 2,        # 绘制线条粗细
            "arc_radius": 30,          # 角度弧半径
            "text_size": 0.7,          # 文字大小
            "precision": 1             # 小数点精度
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "angle_unit": {
                "type": "string",
                "enum": ["degree", "radian"],
                "description": "角度单位"
            },
            "line_thickness": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "绘制线条粗细"
            },
            "arc_radius": {
                "type": "integer",
                "minimum": 10,
                "maximum": 100,
                "description": "角度弧显示半径"
            },
            "precision": {
                "type": "integer",
                "minimum": 0,
                "maximum": 6,
                "description": "小数点精度"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("points", DataType.POINTS, True, "角度点(顶点,点1,点2)")
        self.add_output_connection("angles", DataType.RESULTS, "角度测量结果")
        self.add_output_connection("image", DataType.IMAGE, "标记角度的图像")

    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行角度测量 - 使用装饰器优化版本"""
        image = inputs["image"]
        points = inputs["points"]
        parameters = config.parameters

        # 参数提取
        angle_unit = parameters.get("angle_unit", "degree")
        line_thickness = parameters.get("line_thickness", 2)
        arc_radius = parameters.get("arc_radius", 30)
        text_size = parameters.get("text_size", 0.7)
        precision = parameters.get("precision", 1)

        # 处理点数据 - 每三个点组成一个角度 (顶点, 点1, 点2)
        if isinstance(points, list) and len(points) >= 3:
            angle_groups = []
            for i in range(0, len(points) - 2, 3):
                if i + 2 < len(points):
                    angle_groups.append((points[i], points[i + 1], points[i + 2]))
        else:
            angle_groups = []

        # 计算角度
        angles = []
        result_image = image.copy()

        for idx, (vertex, point1, point2) in enumerate(angle_groups):
            # 提取坐标
            if isinstance(vertex, dict):
                vx, vy = vertex.get("x", 0), vertex.get("y", 0)
            else:
                vx, vy = vertex[0], vertex[1]

            if isinstance(point1, dict):
                x1, y1 = point1.get("x", 0), point1.get("y", 0)
            else:
                x1, y1 = point1[0], point1[1]

            if isinstance(point2, dict):
                x2, y2 = point2.get("x", 0), point2.get("y", 0)
            else:
                x2, y2 = point2[0], point2[1]

            # 计算向量
            vector1 = (x1 - vx, y1 - vy)
            vector2 = (x2 - vx, y2 - vy)

            # 计算角度 (弧度)
            dot_product = vector1[0] * vector2[0] + vector1[1] * vector2[1]
            magnitude1 = math.sqrt(vector1[0] ** 2 + vector1[1] ** 2)
            magnitude2 = math.sqrt(vector2[0] ** 2 + vector2[1] ** 2)

            if magnitude1 == 0 or magnitude2 == 0:
                angle_rad = 0
            else:
                cos_angle = dot_product / (magnitude1 * magnitude2)
                # 防止数值误差导致的域错误
                cos_angle = max(-1, min(1, cos_angle))
                angle_rad = math.acos(cos_angle)

            # 转换单位
            if angle_unit == "degree":
                angle_value = math.degrees(angle_rad)
                unit_symbol = "°"
            else:
                angle_value = angle_rad
                unit_symbol = " rad"

            # 记录结果
            angle_info = {
                "vertex": {"x": vx, "y": vy},
                "point1": {"x": x1, "y": y1},
                "point2": {"x": x2, "y": y2},
                "angle_radian": round(angle_rad, precision + 2),
                "angle_value": round(angle_value, precision),
                "unit": angle_unit
            }
            angles.append(angle_info)

            # 绘制角度线
            vertex_pt = (int(vx), int(vy))
            pt1 = (int(x1), int(y1))
            pt2 = (int(x2), int(y2))

            # 绘制两条边
            cv2.line(result_image, vertex_pt, pt1, (0, 255, 255), line_thickness)
            cv2.line(result_image, vertex_pt, pt2, (0, 255, 255), line_thickness)

            # 绘制角度弧（简化版）
            start_angle = math.degrees(math.atan2(y1 - vy, x1 - vx))
            end_angle = math.degrees(math.atan2(y2 - vy, x2 - vx))

            # 确保角度为正值且按逆时针方向
            if end_angle < start_angle:
                end_angle += 360

            cv2.ellipse(result_image, vertex_pt, (arc_radius, arc_radius),
                       0, start_angle, end_angle, (255, 0, 255), 2)

            # 绘制顶点
            cv2.circle(result_image, vertex_pt, 5, (255, 0, 0), -1)
            cv2.circle(result_image, pt1, 3, (0, 255, 0), -1)
            cv2.circle(result_image, pt2, 3, (0, 255, 0), -1)

            # 绘制角度文本
            text_x = int(vx + arc_radius * 1.2)
            text_y = int(vy)

            angle_text = f"{angle_value:.{precision}f}{unit_symbol}"
            cv2.putText(result_image, angle_text, (text_x, text_y),
                       cv2.FONT_HERSHEY_SIMPLEX, text_size, (255, 0, 255), 2)

        return create_success_result(
            data={
                "angles": angles,
                "measurement_count": len(angles),
                "unit": angle_unit
            },
            image=result_image,
            message=f"角度测量完成，测量了 {len(angles)} 个角度"
        )


class AreaMeasurementAlgorithm(BaseAlgorithm):
    """面积测量算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT

    def get_algorithm_name(self) -> str:
        return "area_measurement"

    def get_display_name(self) -> str:
        return "面积测量"

    def get_description(self) -> str:
        return "测量图像中轮廓或多边形的面积"

    def get_icon_path(self) -> str:
        return "resources/icons/area_measurement.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit_squared": 1.0,  # 每单位面积的像素数
            "unit": "pixel²",                # 面积单位
            "precision": 2,                  # 小数点精度
            "fill_alpha": 0.3               # 填充透明度
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit_squared": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 1000000.0,
                "description": "每单位面积对应的像素数"
            },
            "unit": {
                "type": "string",
                "enum": ["pixel²", "mm²", "cm²", "inch²"],
                "description": "面积单位"
            },
            "precision": {
                "type": "integer",
                "minimum": 0,
                "maximum": 6,
                "description": "小数点精度"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, True, "轮廓数据")
        self.add_output_connection("areas", DataType.RESULTS, "面积测量结果")
        self.add_output_connection("image", DataType.IMAGE, "标记面积的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行面积测量 - 使用装饰器优化版本"""
        image = inputs["image"]
        contours = inputs["contours"]
        parameters = config.parameters

        # 参数提取
        pixels_per_unit_squared = parameters.get("pixels_per_unit_squared", 1.0)
        unit = parameters.get("unit", "pixel²")
        precision = parameters.get("precision", 2)
        fill_alpha = parameters.get("fill_alpha", 0.3)

        # 计算面积
        areas = []
        result_image = image.copy()
        overlay = image.copy()

        for idx, contour in enumerate(contours):
            # 转换轮廓格式
            if isinstance(contour, list):
                # 如果是点列表，转换为numpy数组
                contour_points = np.array([[pt["x"], pt["y"]] for pt in contour], dtype=np.int32)
            else:
                contour_points = contour

            # 计算像素面积
            pixel_area = cv2.contourArea(contour_points)

            # 转换为实际单位
            actual_area = pixel_area / pixels_per_unit_squared

            # 记录结果
            area_info = {
                "contour_index": idx,
                "pixel_area": round(pixel_area, precision),
                "actual_area": round(actual_area, precision),
                "unit": unit,
                "contour_points": len(contour_points)
            }
            areas.append(area_info)

            # 绘制轮廓
            cv2.drawContours(overlay, [contour_points], -1, (0, 255, 0), -1)
            cv2.drawContours(result_image, [contour_points], -1, (0, 255, 0), 2)

            # 绘制面积文本
            M = cv2.moments(contour_points)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])

                area_text = f"{actual_area:.{precision}f} {unit}"
                cv2.putText(result_image, area_text, (cx - 50, cy),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 应用透明度混合
        cv2.addWeighted(overlay, fill_alpha, result_image, 1 - fill_alpha, 0, result_image)

        return create_success_result(
            data={
                "areas": areas,
                "measurement_count": len(areas),
                "total_area": sum(area["actual_area"] for area in areas),
                "unit": unit
            },
            image=result_image,
            message=f"面积测量完成，测量了 {len(areas)} 个区域"
        )


# 其他测量算法的占位符实现
class GeometryAnalysisAlgorithm(BaseAlgorithm):
    """几何分析算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT

    def get_algorithm_name(self) -> str:
        return "geometry_analysis"

    def get_display_name(self) -> str:
        return "几何分析"

    def get_description(self) -> str:
        return "分析几何形状的各种属性：面积、周长、圆度、矩形度等"

    def get_icon_path(self) -> str:
        return "resources/icons/geometry_analysis.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "min_contour_area": 100,        # 最小轮廓面积
            "analysis_types": ["all"],      # 分析类型
            "show_bounding_rect": True,     # 显示边界矩形
            "show_min_enclosing_circle": True,  # 显示最小外接圆
            "show_convex_hull": True,       # 显示凸包
            "precision": 3                  # 计算精度
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "min_contour_area": {
                "type": "number",
                "minimum": 1,
                "maximum": 10000,
                "description": "最小轮廓面积阈值"
            },
            "analysis_types": {
                "type": "array",
                "items": {
                    "type": "string",
                    "enum": ["all", "area", "perimeter", "circularity", "rectangularity", "solidity", "extent", "aspect_ratio"]
                },
                "description": "要进行的分析类型"
            },
            "show_bounding_rect": {
                "type": "boolean",
                "description": "是否显示边界矩形"
            },
            "show_min_enclosing_circle": {
                "type": "boolean",
                "description": "是否显示最小外接圆"
            },
            "show_convex_hull": {
                "type": "boolean",
                "description": "是否显示凸包"
            },
            "precision": {
                "type": "integer",
                "minimum": 1,
                "maximum": 6,
                "description": "计算结果精度"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, False, "轮廓数据（可选）")
        self.add_output_connection("analysis", DataType.RESULTS, "几何分析结果")
        self.add_output_connection("image", DataType.IMAGE, "标记分析结果的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行几何分析 - 使用装饰器优化版本"""
        image = inputs["image"]
        contours = inputs.get("contours", None)
        parameters = config.parameters

        min_contour_area = parameters.get("min_contour_area", 100)
        analysis_types = parameters.get("analysis_types", ["all"])
        show_bounding_rect = parameters.get("show_bounding_rect", True)
        show_min_enclosing_circle = parameters.get("show_min_enclosing_circle", True)
        show_convex_hull = parameters.get("show_convex_hull", True)
        precision = parameters.get("precision", 3)

        # 如果没有提供轮廓，自动检测
        if contours is None:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤轮廓
        filtered_contours = [c for c in contours if cv2.contourArea(c) >= min_contour_area]

        # 分析结果
        analysis_results = []
        result_image = image.copy()

        for idx, contour in enumerate(filtered_contours):
            # 基本几何属性
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            # 边界矩形
            x, y, w, h = cv2.boundingRect(contour)
            rect_area = w * h

            # 最小外接圆
            (center_x, center_y), radius = cv2.minEnclosingCircle(contour)
            circle_area = np.pi * radius * radius

            # 凸包
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)

            # 计算各种几何特征
            analysis = {
                "contour_id": idx,
                "basic_properties": {
                    "area": round(area, precision),
                    "perimeter": round(perimeter, precision),
                    "centroid": self._calculate_centroid(contour, precision)
                },
                "shape_descriptors": {
                    "circularity": round(4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0, precision),
                    "rectangularity": round(area / rect_area if rect_area > 0 else 0, precision),
                    "solidity": round(area / hull_area if hull_area > 0 else 0, precision),
                    "extent": round(area / rect_area if rect_area > 0 else 0, precision),
                    "aspect_ratio": round(w / h if h > 0 else 0, precision)
                },
                "bounding_shapes": {
                    "bounding_rect": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "min_enclosing_circle": {
                        "center": {"x": round(center_x, precision), "y": round(center_y, precision)},
                        "radius": round(radius, precision)
                    },
                    "convex_hull_area": round(hull_area, precision)
                },
                "advanced_features": self._calculate_advanced_features(contour, precision)
            }

            # 过滤分析类型
            if "all" not in analysis_types:
                filtered_analysis = {"contour_id": idx}
                for analysis_type in analysis_types:
                    if analysis_type in ["area", "perimeter"]:
                        if "basic_properties" not in filtered_analysis:
                            filtered_analysis["basic_properties"] = {}
                        if analysis_type in analysis["basic_properties"]:
                            filtered_analysis["basic_properties"][analysis_type] = analysis["basic_properties"][analysis_type]
                    elif analysis_type in analysis["shape_descriptors"]:
                        if "shape_descriptors" not in filtered_analysis:
                            filtered_analysis["shape_descriptors"] = {}
                        filtered_analysis["shape_descriptors"][analysis_type] = analysis["shape_descriptors"][analysis_type]
                analysis = filtered_analysis

            analysis_results.append(analysis)

            # 绘制轮廓
            cv2.drawContours(result_image, [contour], -1, (0, 255, 0), 2)

            # 绘制边界矩形
            if show_bounding_rect:
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (255, 0, 0), 2)

            # 绘制最小外接圆
            if show_min_enclosing_circle:
                cv2.circle(result_image, (int(center_x), int(center_y)), int(radius), (0, 0, 255), 2)

            # 绘制凸包
            if show_convex_hull:
                cv2.drawContours(result_image, [hull], -1, (255, 255, 0), 2)

            # 标注ID
            cv2.putText(result_image, f"#{idx}", (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return create_success_result(
            data={
                "analysis_results": analysis_results,
                "contour_count": len(analysis_results),
                "total_area": sum(result["basic_properties"]["area"] for result in analysis_results if "basic_properties" in result),
                "analysis_summary": self._generate_analysis_summary(analysis_results)
            },
            image=result_image,
            message=f"几何分析完成，分析了 {len(analysis_results)} 个轮廓"
        )

    def _calculate_centroid(self, contour: np.ndarray, precision: int) -> Dict[str, float]:
        """计算质心"""
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cx = M["m10"] / M["m00"]
            cy = M["m01"] / M["m00"]
        else:
            cx, cy = 0, 0

        return {"x": round(cx, precision), "y": round(cy, precision)}

    def _calculate_advanced_features(self, contour: np.ndarray, precision: int) -> Dict[str, Any]:
        """计算高级几何特征"""
        # 椭圆拟合
        if len(contour) >= 5:
            try:
                ellipse = cv2.fitEllipse(contour)
                ellipse_info = {
                    "center": {"x": round(ellipse[0][0], precision), "y": round(ellipse[0][1], precision)},
                    "axes": {"major": round(max(ellipse[1]), precision), "minor": round(min(ellipse[1]), precision)},
                    "angle": round(ellipse[2], precision)
                }
            except:
                ellipse_info = None
        else:
            ellipse_info = None

        # 最小面积矩形
        try:
            min_rect = cv2.minAreaRect(contour)
            min_rect_info = {
                "center": {"x": round(min_rect[0][0], precision), "y": round(min_rect[0][1], precision)},
                "size": {"width": round(min_rect[1][0], precision), "height": round(min_rect[1][1], precision)},
                "angle": round(min_rect[2], precision)
            }
        except:
            min_rect_info = None

        # 计算形状复杂度（基于轮廓点数）
        complexity = len(contour)

        return {
            "fitted_ellipse": ellipse_info,
            "min_area_rect": min_rect_info,
            "complexity": complexity,
            "contour_points": len(contour)
        }

    def _generate_analysis_summary(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成分析摘要"""
        if not analysis_results:
            return {}

        # 统计信息
        areas = [result["basic_properties"]["area"] for result in analysis_results if "basic_properties" in result]
        circularities = [result["shape_descriptors"]["circularity"] for result in analysis_results if "shape_descriptors" in result and "circularity" in result["shape_descriptors"]]

        summary = {
            "total_objects": len(analysis_results),
            "area_statistics": {
                "total": round(sum(areas), 3) if areas else 0,
                "average": round(np.mean(areas), 3) if areas else 0,
                "min": round(min(areas), 3) if areas else 0,
                "max": round(max(areas), 3) if areas else 0
            }
        }

        if circularities:
            summary["circularity_statistics"] = {
                "average": round(np.mean(circularities), 3),
                "min": round(min(circularities), 3),
                "max": round(max(circularities), 3)
            }

        return summary


class DimensionMeasurementAlgorithm(BaseAlgorithm):
    """尺寸测量算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.MEASUREMENT

    def get_algorithm_name(self) -> str:
        return "dimension_measurement"

    def get_display_name(self) -> str:
        return "尺寸测量"

    def get_description(self) -> str:
        return "测量物体的长、宽、高等尺寸，支持多种测量方法"

    def get_icon_path(self) -> str:
        return "resources/icons/dimension_measurement.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit": 1.0,         # 每单位长度的像素数
            "unit": "pixel",                # 长度单位
            "measurement_method": "bounding_rect",  # 测量方法
            "min_contour_area": 100,        # 最小轮廓面积
            "show_dimensions": True,        # 显示尺寸标注
            "precision": 2,                 # 精度
            "line_thickness": 2,            # 线条粗细
            "text_size": 0.6               # 文字大小
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "pixels_per_unit": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 1000.0,
                "description": "每单位长度对应的像素数"
            },
            "unit": {
                "type": "string",
                "enum": ["pixel", "mm", "cm", "inch"],
                "description": "长度单位"
            },
            "measurement_method": {
                "type": "string",
                "enum": ["bounding_rect", "min_area_rect", "caliper", "feret_diameter"],
                "description": "测量方法"
            },
            "min_contour_area": {
                "type": "number",
                "minimum": 1,
                "maximum": 10000,
                "description": "最小轮廓面积阈值"
            },
            "show_dimensions": {
                "type": "boolean",
                "description": "是否显示尺寸标注"
            },
            "precision": {
                "type": "integer",
                "minimum": 0,
                "maximum": 6,
                "description": "测量精度"
            },
            "line_thickness": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "标注线条粗细"
            },
            "text_size": {
                "type": "number",
                "minimum": 0.3,
                "maximum": 2.0,
                "description": "标注文字大小"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, False, "轮廓数据（可选）")
        self.add_output_connection("dimensions", DataType.RESULTS, "尺寸测量结果")
        self.add_output_connection("image", DataType.IMAGE, "标记尺寸的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行尺寸测量 - 使用装饰器优化版本"""
        image = inputs["image"]
        contours = inputs.get("contours", None)
        parameters = config.parameters

        pixels_per_unit = parameters.get("pixels_per_unit", 1.0)
        unit = parameters.get("unit", "pixel")
        measurement_method = parameters.get("measurement_method", "bounding_rect")
        min_contour_area = parameters.get("min_contour_area", 100)
        show_dimensions = parameters.get("show_dimensions", True)
        precision = parameters.get("precision", 2)
        line_thickness = parameters.get("line_thickness", 2)
        text_size = parameters.get("text_size", 0.6)

        # 如果没有提供轮廓，自动检测
        if contours is None:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
            _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤轮廓
        filtered_contours = [c for c in contours if cv2.contourArea(c) >= min_contour_area]

        # 测量结果
        dimension_results = []
        result_image = image.copy()

        for idx, contour in enumerate(filtered_contours):
            # 根据测量方法计算尺寸
            if measurement_method == "bounding_rect":
                dimensions = self._measure_bounding_rect(contour, pixels_per_unit, unit, precision)
            elif measurement_method == "min_area_rect":
                dimensions = self._measure_min_area_rect(contour, pixels_per_unit, unit, precision)
            elif measurement_method == "caliper":
                dimensions = self._measure_caliper(contour, pixels_per_unit, unit, precision)
            elif measurement_method == "feret_diameter":
                dimensions = self._measure_feret_diameter(contour, pixels_per_unit, unit, precision)
            else:
                dimensions = self._measure_bounding_rect(contour, pixels_per_unit, unit, precision)

            dimensions["contour_id"] = idx
            dimensions["method"] = measurement_method
            dimension_results.append(dimensions)

            # 绘制轮廓
            cv2.drawContours(result_image, [contour], -1, (0, 255, 0), 2)

            # 绘制尺寸标注
            if show_dimensions:
                self._draw_dimensions(result_image, dimensions, line_thickness, text_size)

        return create_success_result(
            data={
                "dimensions": dimension_results,
                "object_count": len(dimension_results),
                "measurement_method": measurement_method,
                "unit": unit,
                "summary": self._generate_dimension_summary(dimension_results)
            },
            image=result_image,
            message=f"尺寸测量完成，测量了 {len(dimension_results)} 个物体"
        )

    def _measure_bounding_rect(self, contour: np.ndarray, pixels_per_unit: float,
                              unit: str, precision: int) -> Dict[str, Any]:
        """使用边界矩形测量尺寸"""
        x, y, w, h = cv2.boundingRect(contour)

        # 转换为实际单位
        width = round(w / pixels_per_unit, precision)
        height = round(h / pixels_per_unit, precision)

        return {
            "width": width,
            "height": height,
            "length": max(width, height),  # 长度为较大值
            "width_pixels": w,
            "height_pixels": h,
            "bounding_rect": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
            "aspect_ratio": round(w / h if h > 0 else 0, precision),
            "unit": unit
        }

    def _measure_min_area_rect(self, contour: np.ndarray, pixels_per_unit: float,
                              unit: str, precision: int) -> Dict[str, Any]:
        """使用最小面积矩形测量尺寸"""
        min_rect = cv2.minAreaRect(contour)
        box = cv2.boxPoints(min_rect)
        box = np.int0(box)

        # 获取矩形的宽度和高度
        width_pixels = min_rect[1][0]
        height_pixels = min_rect[1][1]

        # 转换为实际单位
        width = round(width_pixels / pixels_per_unit, precision)
        height = round(height_pixels / pixels_per_unit, precision)

        return {
            "width": width,
            "height": height,
            "length": max(width, height),
            "width_pixels": round(width_pixels, precision),
            "height_pixels": round(height_pixels, precision),
            "min_area_rect": {
                "center": {"x": round(min_rect[0][0], precision), "y": round(min_rect[0][1], precision)},
                "size": {"width": round(width_pixels, precision), "height": round(height_pixels, precision)},
                "angle": round(min_rect[2], precision)
            },
            "box_points": box.tolist(),
            "aspect_ratio": round(width_pixels / height_pixels if height_pixels > 0 else 0, precision),
            "unit": unit
        }

    def _measure_caliper(self, contour: np.ndarray, pixels_per_unit: float,
                        unit: str, precision: int) -> Dict[str, Any]:
        """使用卡尺测量法（最大和最小宽度）"""
        # 计算所有可能的距离
        points = contour.reshape(-1, 2)

        max_distance = 0
        min_distance = float('inf')
        max_points = None
        min_points = None

        # 计算凸包以减少计算量
        hull = cv2.convexHull(contour)
        hull_points = hull.reshape(-1, 2)

        for i in range(len(hull_points)):
            for j in range(i + 1, len(hull_points)):
                dist = np.linalg.norm(hull_points[i] - hull_points[j])
                if dist > max_distance:
                    max_distance = dist
                    max_points = (hull_points[i], hull_points[j])
                if dist < min_distance and dist > 0:
                    min_distance = dist
                    min_points = (hull_points[i], hull_points[j])

        # 转换为实际单位
        max_width = round(max_distance / pixels_per_unit, precision)
        min_width = round(min_distance / pixels_per_unit, precision)

        return {
            "max_width": max_width,
            "min_width": min_width,
            "length": max_width,
            "width": min_width,
            "max_width_pixels": round(max_distance, precision),
            "min_width_pixels": round(min_distance, precision),
            "max_width_points": max_points.tolist() if max_points is not None else None,
            "min_width_points": min_points.tolist() if min_points is not None else None,
            "unit": unit
        }

    def _measure_feret_diameter(self, contour: np.ndarray, pixels_per_unit: float,
                               unit: str, precision: int) -> Dict[str, Any]:
        """测量Feret直径（最大和最小Feret直径）"""
        # 获取凸包
        hull = cv2.convexHull(contour)
        hull_points = hull.reshape(-1, 2)

        max_feret = 0
        min_feret = float('inf')

        # 计算所有方向的Feret直径
        for angle in range(0, 180, 5):  # 每5度计算一次
            # 旋转点
            rad = np.radians(angle)
            cos_a, sin_a = np.cos(rad), np.sin(rad)

            rotated_points = []
            for point in hull_points:
                x_rot = point[0] * cos_a - point[1] * sin_a
                y_rot = point[0] * sin_a + point[1] * cos_a
                rotated_points.append([x_rot, y_rot])

            rotated_points = np.array(rotated_points)

            # 计算该方向的Feret直径
            x_min, x_max = np.min(rotated_points[:, 0]), np.max(rotated_points[:, 0])
            y_min, y_max = np.min(rotated_points[:, 1]), np.max(rotated_points[:, 1])

            feret_x = x_max - x_min
            feret_y = y_max - y_min

            max_feret = max(max_feret, feret_x, feret_y)
            min_feret = min(min_feret, feret_x, feret_y)

        # 转换为实际单位
        max_feret_actual = round(max_feret / pixels_per_unit, precision)
        min_feret_actual = round(min_feret / pixels_per_unit, precision)

        return {
            "max_feret_diameter": max_feret_actual,
            "min_feret_diameter": min_feret_actual,
            "length": max_feret_actual,
            "width": min_feret_actual,
            "max_feret_pixels": round(max_feret, precision),
            "min_feret_pixels": round(min_feret, precision),
            "feret_ratio": round(max_feret / min_feret if min_feret > 0 else 0, precision),
            "unit": unit
        }

    def _draw_dimensions(self, image: np.ndarray, dimensions: Dict[str, Any],
                        line_thickness: int, text_size: float):
        """绘制尺寸标注"""
        if "bounding_rect" in dimensions:
            # 绘制边界矩形的尺寸
            rect = dimensions["bounding_rect"]
            x, y, w, h = rect["x"], rect["y"], rect["width"], rect["height"]

            # 绘制宽度标注
            cv2.line(image, (x, y - 20), (x + w, y - 20), (255, 0, 255), line_thickness)
            cv2.line(image, (x, y - 25), (x, y - 15), (255, 0, 255), line_thickness)
            cv2.line(image, (x + w, y - 25), (x + w, y - 15), (255, 0, 255), line_thickness)

            width_text = f"W: {dimensions['width']}{dimensions['unit']}"
            cv2.putText(image, width_text, (x + w//2 - 30, y - 30),
                       cv2.FONT_HERSHEY_SIMPLEX, text_size, (255, 0, 255), 2)

            # 绘制高度标注
            cv2.line(image, (x - 20, y), (x - 20, y + h), (255, 0, 255), line_thickness)
            cv2.line(image, (x - 25, y), (x - 15, y), (255, 0, 255), line_thickness)
            cv2.line(image, (x - 25, y + h), (x - 15, y + h), (255, 0, 255), line_thickness)

            height_text = f"H: {dimensions['height']}{dimensions['unit']}"
            cv2.putText(image, height_text, (x - 80, y + h//2),
                       cv2.FONT_HERSHEY_SIMPLEX, text_size, (255, 0, 255), 2)

        elif "box_points" in dimensions:
            # 绘制最小面积矩形
            box = np.array(dimensions["box_points"], dtype=np.int32)
            cv2.drawContours(image, [box], 0, (255, 0, 255), line_thickness)

            # 标注尺寸
            center = dimensions["min_area_rect"]["center"]
            dimension_text = f"L: {dimensions['length']}, W: {dimensions['width']}{dimensions['unit']}"
            cv2.putText(image, dimension_text, (int(center["x"]) - 50, int(center["y"])),
                       cv2.FONT_HERSHEY_SIMPLEX, text_size, (255, 0, 255), 2)

    def _generate_dimension_summary(self, dimension_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成尺寸测量摘要"""
        if not dimension_results:
            return {}

        lengths = [d["length"] for d in dimension_results if "length" in d]
        widths = [d["width"] for d in dimension_results if "width" in d]

        summary = {
            "total_objects": len(dimension_results),
            "length_statistics": {
                "average": round(np.mean(lengths), 3) if lengths else 0,
                "min": round(min(lengths), 3) if lengths else 0,
                "max": round(max(lengths), 3) if lengths else 0,
                "std": round(np.std(lengths), 3) if lengths else 0
            },
            "width_statistics": {
                "average": round(np.mean(widths), 3) if widths else 0,
                "min": round(min(widths), 3) if widths else 0,
                "max": round(max(widths), 3) if widths else 0,
                "std": round(np.std(widths), 3) if widths else 0
            }
        }

        return summary