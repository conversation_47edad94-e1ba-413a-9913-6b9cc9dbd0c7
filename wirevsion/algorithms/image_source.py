"""
图像源算法模块

提供各种图像输入源：相机、文件、网络、视频等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)


class CameraSourceAlgorithm(BaseAlgorithm):
    """相机图像源算法"""
    
    def __init__(self):
        """初始化相机源"""
        super().__init__()
        self.camera = None
        self.is_opened = False
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_SOURCE
    
    def get_algorithm_name(self) -> str:
        return "camera"
    
    def get_display_name(self) -> str:
        return "相机源"
    
    def get_description(self) -> str:
        return "从相机设备获取图像数据"
    
    def get_icon_path(self) -> str:
        return "resources/icons/camera.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "camera_id": 0,
            "width": 640,
            "height": 480,
            "fps": 60,
            "auto_exposure": True,
            "exposure": -6,
            "brightness": 0,
            "contrast": 32,
            "saturation": 64,
            "hue": 0,
            "gain": 0
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "camera_id": {
                "type": "integer",
                "minimum": 0,
                "maximum": 10,
                "description": "相机设备ID"
            },
            "width": {
                "type": "integer",
                "minimum": 320,
                "maximum": 4096,
                "description": "图像宽度"
            },
            "height": {
                "type": "integer", 
                "minimum": 240,
                "maximum": 3072,
                "description": "图像高度"
            },
            "fps": {
                "type": "integer",
                "minimum": 1,
                "maximum": 120,
                "default": 60,
                "description": "帧率"
            },
            "auto_exposure": {
                "type": "boolean",
                "description": "自动曝光"
            },
            "exposure": {
                "type": "integer",
                "minimum": -13,
                "maximum": -1,
                "description": "曝光值"
            },
            "brightness": {
                "type": "integer",
                "minimum": -100,
                "maximum": 100,
                "description": "亮度"
            },
            "contrast": {
                "type": "integer",
                "minimum": 0,
                "maximum": 100,
                "description": "对比度"
            },
            "saturation": {
                "type": "integer",
                "minimum": 0,
                "maximum": 200,
                "description": "饱和度"
            },
            "hue": {
                "type": "integer",
                "minimum": -100,
                "maximum": 100,
                "description": "色调"
            },
            "gain": {
                "type": "integer",
                "minimum": 0,
                "maximum": 100,
                "description": "增益"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        # 相机源没有输入
        # 输出图像
        self.add_output_connection("image", DataType.IMAGE, "捕获的图像")
        self.add_output_connection("camera_info", DataType.ANY, "相机信息")
    
    def _open_camera(self, camera_id: int, parameters: Dict[str, Any]) -> bool:
        """
        打开相机
        
        Args:
            camera_id: 相机ID
            parameters: 相机参数
            
        Returns:
            是否成功
        """
        try:
            self.camera = cv2.VideoCapture(camera_id)
            
            if not self.camera.isOpened():
                logger.error(f"无法打开相机 {camera_id}")
                return False
            
            # 设置相机参数
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, parameters.get("width", 640))
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, parameters.get("height", 480))
            self.camera.set(cv2.CAP_PROP_FPS, parameters.get("fps", 60))
            
            # 设置曝光参数
            if not parameters.get("auto_exposure", True):
                self.camera.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 手动曝光
                self.camera.set(cv2.CAP_PROP_EXPOSURE, parameters.get("exposure", -6))
            else:
                self.camera.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.75)  # 自动曝光
            
            # 设置其他参数
            self.camera.set(cv2.CAP_PROP_BRIGHTNESS, parameters.get("brightness", 0))
            self.camera.set(cv2.CAP_PROP_CONTRAST, parameters.get("contrast", 32))
            self.camera.set(cv2.CAP_PROP_SATURATION, parameters.get("saturation", 64))
            self.camera.set(cv2.CAP_PROP_HUE, parameters.get("hue", 0))
            self.camera.set(cv2.CAP_PROP_GAIN, parameters.get("gain", 0))
            
            self.is_opened = True
            logger.info(f"相机 {camera_id} 打开成功")
            return True
            
        except Exception as e:
            logger.error(f"打开相机失败: {e}")
            return False
    
    def _close_camera(self):
        """关闭相机"""
        if self.camera is not None:
            self.camera.release()
            self.camera = None
            self.is_opened = False
            logger.info("相机已关闭")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行相机图像获取"""
        start_time = time.time()
        
        try:
            parameters = config.parameters
            camera_id = parameters.get("camera_id", 0)
            
            # 如果相机未打开或ID不匹配，重新打开
            if not self.is_opened or (hasattr(self, 'current_camera_id') and 
                                    self.current_camera_id != camera_id):
                self._close_camera()
                if not self._open_camera(camera_id, parameters):
                    return create_error_result("无法打开相机", time.time() - start_time)
                self.current_camera_id = camera_id
            
            # 捕获图像
            ret, frame = self.camera.read()
            if not ret or frame is None:
                return create_error_result("无法捕获图像", time.time() - start_time)
            
            # 获取相机信息
            camera_info = {
                "camera_id": camera_id,
                "width": int(self.camera.get(cv2.CAP_PROP_FRAME_WIDTH)),
                "height": int(self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                "fps": self.camera.get(cv2.CAP_PROP_FPS),
                "format": "BGR",
                "channels": frame.shape[2] if len(frame.shape) > 2 else 1
            }
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "camera_info": camera_info,
                    "frame_count": 1,
                    "capture_time": execution_time
                },
                image=frame,
                message=f"成功捕获图像 ({camera_info['width']}x{camera_info['height']})",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"相机图像获取失败: {e}")
            return create_error_result(f"相机图像获取失败: {e}", time.time() - start_time)
    
    def __del__(self):
        """析构函数"""
        self._close_camera()


class FileSourceAlgorithm(BaseAlgorithm):
    """文件图像源算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_SOURCE
    
    def get_algorithm_name(self) -> str:
        return "file"
    
    def get_display_name(self) -> str:
        return "文件源"
    
    def get_description(self) -> str:
        return "从图像文件获取图像数据"
    
    def get_icon_path(self) -> str:
        return "resources/icons/file.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "file_path": "",
            "color_mode": "BGR",  # BGR, RGB, GRAY
            "resize_enabled": False,
            "resize_width": 640,
            "resize_height": 480,
            "keep_aspect_ratio": True
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "file_path": {
                "type": "string",
                "description": "图像文件路径"
            },
            "color_mode": {
                "type": "string",
                "enum": ["BGR", "RGB", "GRAY"],
                "description": "颜色模式"
            },
            "resize_enabled": {
                "type": "boolean",
                "description": "是否调整大小"
            },
            "resize_width": {
                "type": "integer",
                "minimum": 1,
                "maximum": 4096,
                "description": "调整后宽度"
            },
            "resize_height": {
                "type": "integer",
                "minimum": 1,
                "maximum": 3072,
                "description": "调整后高度"
            },
            "keep_aspect_ratio": {
                "type": "boolean",
                "description": "保持宽高比"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        # 文件源没有输入
        # 输出图像
        self.add_output_connection("image", DataType.IMAGE, "加载的图像")
        self.add_output_connection("file_info", DataType.ANY, "文件信息")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行文件图像加载"""
        start_time = time.time()
        
        try:
            parameters = config.parameters
            file_path = parameters.get("file_path", "")
            
            if not file_path:
                return create_error_result("未指定文件路径", time.time() - start_time)
            
            # 检查文件是否存在
            if not Path(file_path).exists():
                return create_error_result(f"文件不存在: {file_path}", time.time() - start_time)
            
            # 读取图像
            color_mode = parameters.get("color_mode", "BGR")
            if color_mode == "GRAY":
                image = cv2.imread(file_path, cv2.IMREAD_GRAYSCALE)
            else:
                image = cv2.imread(file_path, cv2.IMREAD_COLOR)
                if color_mode == "RGB":
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            if image is None:
                return create_error_result(f"无法读取图像文件: {file_path}", time.time() - start_time)
            
            # 调整大小
            if parameters.get("resize_enabled", False):
                target_width = parameters.get("resize_width", 640)
                target_height = parameters.get("resize_height", 480)
                keep_aspect_ratio = parameters.get("keep_aspect_ratio", True)
                
                if keep_aspect_ratio:
                    # 保持宽高比
                    h, w = image.shape[:2]
                    aspect_ratio = w / h
                    
                    if target_width / target_height > aspect_ratio:
                        # 高度优先
                        new_height = target_height
                        new_width = int(target_height * aspect_ratio)
                    else:
                        # 宽度优先
                        new_width = target_width
                        new_height = int(target_width / aspect_ratio)
                    
                    image = cv2.resize(image, (new_width, new_height))
                else:
                    image = cv2.resize(image, (target_width, target_height))
            
            # 获取文件信息
            file_info = {
                "file_path": file_path,
                "file_name": Path(file_path).name,
                "file_size": Path(file_path).stat().st_size,
                "width": image.shape[1],
                "height": image.shape[0],
                "channels": image.shape[2] if len(image.shape) > 2 else 1,
                "color_mode": color_mode
            }
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "file_info": file_info,
                    "resize_applied": parameters.get("resize_enabled", False)
                },
                image=image,
                message=f"成功加载图像 ({file_info['width']}x{file_info['height']})",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"文件图像加载失败: {e}")
            return create_error_result(f"文件图像加载失败: {e}", time.time() - start_time)


class NetworkSourceAlgorithm(BaseAlgorithm):
    """网络图像源算法"""
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_SOURCE
    
    def get_algorithm_name(self) -> str:
        return "network"
    
    def get_display_name(self) -> str:
        return "网络源"
    
    def get_description(self) -> str:
        return "从网络摄像头或RTSP流获取图像"
    
    def get_icon_path(self) -> str:
        return "resources/icons/network.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "stream_url": "",
            "timeout": 10,
            "retry_count": 3,
            "buffer_size": 1,
            "username": "",
            "password": ""
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "stream_url": {
                "type": "string",
                "description": "网络流URL (RTSP/HTTP)"
            },
            "timeout": {
                "type": "integer",
                "minimum": 1,
                "maximum": 60,
                "description": "连接超时(秒)"
            },
            "retry_count": {
                "type": "integer",
                "minimum": 0,
                "maximum": 10,
                "description": "重试次数"
            },
            "buffer_size": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "缓冲区大小"
            },
            "username": {
                "type": "string",
                "description": "用户名"
            },
            "password": {
                "type": "string",
                "description": "密码"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        # 网络源没有输入
        # 输出图像
        self.add_output_connection("image", DataType.IMAGE, "网络图像")
        self.add_output_connection("stream_info", DataType.ANY, "流信息")
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行网络图像获取"""
        start_time = time.time()
        
        try:
            parameters = config.parameters
            stream_url = parameters.get("stream_url", "")
            
            if not stream_url:
                return create_error_result("未指定流URL", time.time() - start_time)
            
            # 构建完整URL（包含认证信息）
            username = parameters.get("username", "")
            password = parameters.get("password", "")
            
            if username and password:
                # 插入认证信息到URL
                if "://" in stream_url:
                    protocol, rest = stream_url.split("://", 1)
                    stream_url = f"{protocol}://{username}:{password}@{rest}"
            
            # 打开网络流
            cap = cv2.VideoCapture(stream_url)
            
            # 设置缓冲区大小
            buffer_size = parameters.get("buffer_size", 1)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, buffer_size)
            
            if not cap.isOpened():
                return create_error_result(f"无法连接到网络流: {stream_url}", time.time() - start_time)
            
            # 读取图像
            ret, frame = cap.read()
            cap.release()
            
            if not ret or frame is None:
                return create_error_result("无法从网络流获取图像", time.time() - start_time)
            
            # 获取流信息
            stream_info = {
                "stream_url": stream_url,
                "width": frame.shape[1],
                "height": frame.shape[0],
                "channels": frame.shape[2] if len(frame.shape) > 2 else 1,
                "format": "BGR"
            }
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "stream_info": stream_info,
                    "connection_time": execution_time
                },
                image=frame,
                message=f"成功获取网络图像 ({stream_info['width']}x{stream_info['height']})",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"网络图像获取失败: {e}")
            return create_error_result(f"网络图像获取失败: {e}", time.time() - start_time)


class VideoSourceAlgorithm(BaseAlgorithm):
    """视频文件源算法"""
    
    def __init__(self):
        super().__init__()
        self.video_cap = None
        self.current_frame = 0
        self.total_frames = 0
    
    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_SOURCE
    
    def get_algorithm_name(self) -> str:
        return "video"
    
    def get_display_name(self) -> str:
        return "视频源"
    
    def get_description(self) -> str:
        return "从视频文件获取帧图像"
    
    def get_icon_path(self) -> str:
        return "resources/icons/video.png"
    
    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "video_path": "",
            "frame_number": 0,
            "auto_advance": False,
            "loop": False,
            "skip_frames": 1
        }
    
    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "video_path": {
                "type": "string",
                "description": "视频文件路径"
            },
            "frame_number": {
                "type": "integer",
                "minimum": 0,
                "description": "帧号"
            },
            "auto_advance": {
                "type": "boolean",
                "description": "自动前进到下一帧"
            },
            "loop": {
                "type": "boolean",
                "description": "循环播放"
            },
            "skip_frames": {
                "type": "integer",
                "minimum": 1,
                "description": "跳帧间隔"
            }
        }
    
    def _setup_connections(self):
        """设置连接点"""
        # 视频源没有输入
        # 输出图像
        self.add_output_connection("image", DataType.IMAGE, "视频帧")
        self.add_output_connection("video_info", DataType.ANY, "视频信息")
    
    def _open_video(self, video_path: str) -> bool:
        """打开视频文件"""
        try:
            if self.video_cap is not None:
                self.video_cap.release()
            
            self.video_cap = cv2.VideoCapture(video_path)
            
            if not self.video_cap.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return False
            
            self.total_frames = int(self.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.current_frame = 0
            
            logger.info(f"视频文件打开成功: {video_path}, 总帧数: {self.total_frames}")
            return True
            
        except Exception as e:
            logger.error(f"打开视频文件失败: {e}")
            return False
    
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行视频帧获取"""
        start_time = time.time()
        
        try:
            parameters = config.parameters
            video_path = parameters.get("video_path", "")
            
            if not video_path:
                return create_error_result("未指定视频文件路径", time.time() - start_time)
            
            # 检查文件是否存在
            if not Path(video_path).exists():
                return create_error_result(f"视频文件不存在: {video_path}", time.time() - start_time)
            
            # 打开视频文件
            if (self.video_cap is None or 
                not hasattr(self, 'current_video_path') or 
                self.current_video_path != video_path):
                
                if not self._open_video(video_path):
                    return create_error_result("无法打开视频文件", time.time() - start_time)
                self.current_video_path = video_path
            
            # 获取帧号
            frame_number = parameters.get("frame_number", self.current_frame)
            auto_advance = parameters.get("auto_advance", False)
            loop = parameters.get("loop", False)
            skip_frames = parameters.get("skip_frames", 1)
            
            # 设置帧位置
            if frame_number != self.current_frame:
                self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                self.current_frame = frame_number
            
            # 读取帧
            ret, frame = self.video_cap.read()
            
            if not ret or frame is None:
                if loop and self.total_frames > 0:
                    # 循环播放，回到开头
                    self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    self.current_frame = 0
                    ret, frame = self.video_cap.read()
                
                if not ret or frame is None:
                    return create_error_result("无法读取视频帧", time.time() - start_time)
            
            # 自动前进
            if auto_advance:
                self.current_frame += skip_frames
                if self.current_frame >= self.total_frames:
                    if loop:
                        self.current_frame = 0
                    else:
                        self.current_frame = self.total_frames - 1
            
            # 获取视频信息
            video_info = {
                "video_path": video_path,
                "current_frame": self.current_frame,
                "total_frames": self.total_frames,
                "fps": self.video_cap.get(cv2.CAP_PROP_FPS),
                "width": frame.shape[1],
                "height": frame.shape[0],
                "channels": frame.shape[2] if len(frame.shape) > 2 else 1
            }
            
            execution_time = time.time() - start_time
            
            return create_success_result(
                data={
                    "video_info": video_info,
                    "frame_position": self.current_frame / self.total_frames if self.total_frames > 0 else 0
                },
                image=frame,
                message=f"成功读取视频帧 {self.current_frame}/{self.total_frames}",
                execution_time=execution_time
            )
            
        except Exception as e:
            logger.error(f"视频帧获取失败: {e}")
            return create_error_result(f"视频帧获取失败: {e}", time.time() - start_time)
    
    def __del__(self):
        """析构函数"""
        if self.video_cap is not None:
            self.video_cap.release() 