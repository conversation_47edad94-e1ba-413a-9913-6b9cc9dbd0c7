"""
位置修正算法模块

提供各种位置修正算法：仿射变换、透视变换、旋转修正、平移修正、缩放修正等
"""

import time
import cv2
import numpy as np
import math
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper


class AffineTransformAlgorithm(BaseAlgorithm):
    """仿射变换算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION

    def get_algorithm_name(self) -> str:
        return "affine_transform"

    def get_display_name(self) -> str:
        return "仿射变换"

    def get_description(self) -> str:
        return "使用三点对应关系进行仿射变换校正"

    def get_icon_path(self) -> str:
        return "resources/icons/affine_transform.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "src_points": [[0, 0], [100, 0], [0, 100]],  # 源点
            "dst_points": [[0, 0], [100, 0], [0, 100]],  # 目标点
            "interpolation": "linear",  # linear, cubic, nearest
            "border_mode": "constant",  # constant, reflect, wrap, replicate
            "border_value": 0
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "src_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 3,
                "maxItems": 3,
                "description": "源点坐标 (3个点)"
            },
            "dst_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 3,
                "maxItems": 3,
                "description": "目标点坐标 (3个点)"
            },
            "interpolation": {
                "type": "string",
                "enum": ["linear", "cubic", "nearest"],
                "description": "插值方法"
            },
            "border_mode": {
                "type": "string",
                "enum": ["constant", "reflect", "wrap", "replicate"],
                "description": "边界处理模式"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("src_points", DataType.POINTS, False, "源点")
        self.add_input_connection("dst_points", DataType.POINTS, False, "目标点")

        self.add_output_connection("transformed_image", DataType.IMAGE, "变换后图像")
        self.add_output_connection("transform_matrix", DataType.RESULTS, "变换矩阵")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行仿射变换 - 使用装饰器优化版本"""
        try:

            image = inputs["image"]
            parameters = config.parameters

            # 获取变换点
            if "src_points" in inputs and "dst_points" in inputs:
                src_points = inputs["src_points"]
                dst_points = inputs["dst_points"]
            else:
                src_points = parameters.get("src_points", [[0, 0], [100, 0], [0, 100]])
                dst_points = parameters.get("dst_points", [[0, 0], [100, 0], [0, 100]])

            # 转换为numpy数组
            src_pts = np.float32(src_points[:3])  # 仿射变换需要3个点
            dst_pts = np.float32(dst_points[:3])

            # 计算仿射变换矩阵
            transform_matrix = cv2.getAffineTransform(src_pts, dst_pts)

            # 设置插值和边界模式
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST
            }

            border_map = {
                "constant": cv2.BORDER_CONSTANT,
                "reflect": cv2.BORDER_REFLECT,
                "wrap": cv2.BORDER_WRAP,
                "replicate": cv2.BORDER_REPLICATE
            }

            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)
            border_mode = border_map.get(parameters.get("border_mode", "constant"), cv2.BORDER_CONSTANT)
            border_value = parameters.get("border_value", 0)

            # 执行仿射变换
            h, w = image.shape[:2]
            transformed = cv2.warpAffine(
                image, transform_matrix, (w, h),
                flags=interpolation,
                borderMode=border_mode,
                borderValue=border_value
            )

            return create_success_result(
                data={
                    "transform_matrix": transform_matrix.tolist(),
                    "src_points": src_points,
                    "dst_points": dst_points,
                    "interpolation": parameters.get("interpolation", "linear"),
                    "border_mode": parameters.get("border_mode", "constant")
                },
                image=transformed,
                message="仿射变换完成"
            )

        except Exception as e:
            return create_error_result(f"仿射变换失败: {e}")


class PerspectiveTransformAlgorithm(BaseAlgorithm):
    """透视变换算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION

    def get_algorithm_name(self) -> str:
        return "perspective_transform"

    def get_display_name(self) -> str:
        return "透视变换"

    def get_description(self) -> str:
        return "使用四点对应关系进行透视变换校正"

    def get_icon_path(self) -> str:
        return "resources/icons/perspective_transform.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "src_points": [[0, 0], [100, 0], [100, 100], [0, 100]],  # 源点（四边形）
            "dst_points": [[0, 0], [100, 0], [100, 100], [0, 100]],  # 目标点
            "output_size": None,  # 输出图像尺寸，None表示与输入相同
            "interpolation": "linear",
            "border_mode": "constant",
            "border_value": 0
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "src_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 4,
                "maxItems": 4,
                "description": "源点坐标 (4个点)"
            },
            "dst_points": {
                "type": "array",
                "items": {
                    "type": "array",
                    "items": {"type": "number"},
                    "minItems": 2,
                    "maxItems": 2
                },
                "minItems": 4,
                "maxItems": 4,
                "description": "目标点坐标 (4个点)"
            },
            "output_size": {
                "type": "array",
                "items": {"type": "integer"},
                "minItems": 2,
                "maxItems": 2,
                "description": "输出图像尺寸 [width, height]"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("src_points", DataType.POINTS, False, "源点")
        self.add_input_connection("dst_points", DataType.POINTS, False, "目标点")

        self.add_output_connection("transformed_image", DataType.IMAGE, "变换后图像")
        self.add_output_connection("transform_matrix", DataType.RESULTS, "变换矩阵")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行透视变换 - 使用装饰器优化版本"""
        try:

            image = inputs["image"]
            parameters = config.parameters

            # 获取变换点
            if "src_points" in inputs and "dst_points" in inputs:
                src_points = inputs["src_points"]
                dst_points = inputs["dst_points"]
            else:
                src_points = parameters.get("src_points", [[0, 0], [100, 0], [100, 100], [0, 100]])
                dst_points = parameters.get("dst_points", [[0, 0], [100, 0], [100, 100], [0, 100]])

            # 转换为numpy数组
            src_pts = np.float32(src_points[:4])  # 透视变换需要4个点
            dst_pts = np.float32(dst_points[:4])

            # 计算透视变换矩阵
            transform_matrix = cv2.getPerspectiveTransform(src_pts, dst_pts)

            # 确定输出尺寸
            output_size = parameters.get("output_size")
            if output_size:
                w, h = output_size
            else:
                h, w = image.shape[:2]

            # 设置插值和边界模式
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST
            }

            border_map = {
                "constant": cv2.BORDER_CONSTANT,
                "reflect": cv2.BORDER_REFLECT,
                "wrap": cv2.BORDER_WRAP,
                "replicate": cv2.BORDER_REPLICATE
            }

            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)
            border_mode = border_map.get(parameters.get("border_mode", "constant"), cv2.BORDER_CONSTANT)
            border_value = parameters.get("border_value", 0)

            # 执行透视变换
            transformed = cv2.warpPerspective(
                image, transform_matrix, (w, h),
                flags=interpolation,
                borderMode=border_mode,
                borderValue=border_value
            )

            return create_success_result(
                data={
                    "transform_matrix": transform_matrix.tolist(),
                    "src_points": src_points,
                    "dst_points": dst_points,
                    "output_size": [w, h]
                },
                image=transformed,
                message="透视变换完成"
            )

        except Exception as e:
            return create_error_result(f"透视变换失败: {e}")


class RotationCorrectionAlgorithm(BaseAlgorithm):
    """旋转修正算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION

    def get_algorithm_name(self) -> str:
        return "rotation_correction"

    def get_display_name(self) -> str:
        return "旋转修正"

    def get_description(self) -> str:
        return "对图像进行旋转修正"

    def get_icon_path(self) -> str:
        return "resources/icons/rotation_correction.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "angle": 0.0,  # 旋转角度（度）
            "center": None,  # 旋转中心，None表示图像中心
            "scale": 1.0,  # 缩放比例
            "auto_detect_angle": False,  # 自动检测旋转角度
            "detection_method": "hough_lines",  # hough_lines, minAreaRect
            "interpolation": "linear",
            "border_mode": "constant",
            "border_value": 0
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "angle": {
                "type": "number",
                "minimum": -360,
                "maximum": 360,
                "description": "旋转角度（度）"
            },
            "center": {
                "type": "array",
                "items": {"type": "number"},
                "minItems": 2,
                "maxItems": 2,
                "description": "旋转中心 [x, y]"
            },
            "scale": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10,
                "description": "缩放比例"
            },
            "auto_detect_angle": {
                "type": "boolean",
                "description": "自动检测旋转角度"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("contours", DataType.CONTOURS, False, "轮廓数据")

        self.add_output_connection("corrected_image", DataType.IMAGE, "修正后图像")
        self.add_output_connection("rotation_info", DataType.RESULTS, "旋转信息")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行旋转修正 - 使用装饰器优化版本"""
        try:

            image = inputs["image"]
            parameters = config.parameters

            h, w = image.shape[:2]

            # 确定旋转角度
            angle = parameters.get("angle", 0.0)

            if parameters.get("auto_detect_angle", False):
                detected_angle = self._auto_detect_rotation_angle(image, parameters)
                if detected_angle is not None:
                    angle = detected_angle

            # 确定旋转中心
            center = parameters.get("center")
            if center is None:
                center = (w // 2, h // 2)
            else:
                center = tuple(center)

            scale = parameters.get("scale", 1.0)

            # 获取旋转矩阵
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, scale)

            # 设置插值和边界模式
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST
            }

            border_map = {
                "constant": cv2.BORDER_CONSTANT,
                "reflect": cv2.BORDER_REFLECT,
                "wrap": cv2.BORDER_WRAP,
                "replicate": cv2.BORDER_REPLICATE
            }

            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)
            border_mode = border_map.get(parameters.get("border_mode", "constant"), cv2.BORDER_CONSTANT)
            border_value = parameters.get("border_value", 0)

            # 执行旋转
            rotated = cv2.warpAffine(
                image, rotation_matrix, (w, h),
                flags=interpolation,
                borderMode=border_mode,
                borderValue=border_value
            )

            return create_success_result(
                data={
                    "rotation_info": {
                        "angle": angle,
                        "center": center,
                        "scale": scale,
                        "rotation_matrix": rotation_matrix.tolist()
                    }
                },
                image=rotated,
                message=f"旋转修正完成，角度: {angle:.2f}°"
            )

        except Exception as e:
            return create_error_result(f"旋转修正失败: {e}")

    def _auto_detect_rotation_angle(self, image: np.ndarray, parameters: Dict[str, Any]) -> Optional[float]:
        """自动检测旋转角度"""
        try:
            method = parameters.get("detection_method", "hough_lines")

            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            if method == "hough_lines":
                # 使用霍夫直线检测
                edges = cv2.Canny(gray, 50, 150)
                lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

                if lines is not None:
                    angles = []
                    for line in lines:
                        _, theta = line[0]
                        angle = math.degrees(theta) - 90
                        if -45 <= angle <= 45:
                            angles.append(angle)

                    if angles:
                        return np.median(angles)

            elif method == "minAreaRect":
                # 使用最小外接矩形
                contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if contours:
                    # 找到最大轮廓
                    largest_contour = max(contours, key=cv2.contourArea)
                    rect = cv2.minAreaRect(largest_contour)
                    return rect[2]  # 返回角度

            return None

        except Exception as e:
            logger.warning(f"自动检测旋转角度失败: {e}")
            return None


# 其他位置修正算法的占位符实现
class TranslationCorrectionAlgorithm(BaseAlgorithm):
    """平移修正算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION

    def get_algorithm_name(self) -> str:
        return "translation_correction"

    def get_display_name(self) -> str:
        return "平移修正"

    def get_description(self) -> str:
        return "对图像进行平移修正，支持手动和自动检测"

    def get_icon_path(self) -> str:
        return "resources/icons/translation_correction.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "dx": 0,  # X方向平移量（像素）
            "dy": 0,  # Y方向平移量（像素）
            "auto_detect": False,  # 自动检测平移量
            "detection_method": "template_matching",  # template_matching, feature_matching
            "reference_image": None,  # 参考图像路径
            "interpolation": "linear",
            "border_mode": "constant",
            "border_value": 0
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "dx": {
                "type": "integer",
                "minimum": -1000,
                "maximum": 1000,
                "description": "X方向平移量（像素）"
            },
            "dy": {
                "type": "integer",
                "minimum": -1000,
                "maximum": 1000,
                "description": "Y方向平移量（像素）"
            },
            "auto_detect": {
                "type": "boolean",
                "description": "自动检测平移量"
            },
            "detection_method": {
                "type": "string",
                "enum": ["template_matching", "feature_matching"],
                "description": "检测方法"
            },
            "reference_image": {
                "type": "string",
                "description": "参考图像路径"
            },
            "interpolation": {
                "type": "string",
                "enum": ["linear", "cubic", "nearest"],
                "description": "插值方法"
            },
            "border_mode": {
                "type": "string",
                "enum": ["constant", "reflect", "wrap", "replicate"],
                "description": "边界处理模式"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("reference_image", DataType.IMAGE, False, "参考图像")
        self.add_output_connection("corrected_image", DataType.IMAGE, "修正后图像")
        self.add_output_connection("translation_info", DataType.RESULTS, "平移信息")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行平移修正 - 使用装饰器优化版本"""
        try:
            image = inputs["image"]
            parameters = config.parameters

            # 获取平移量
            dx = parameters.get("dx", 0)
            dy = parameters.get("dy", 0)

            # 自动检测平移量
            if parameters.get("auto_detect", False):
                reference_image = inputs.get("reference_image")
                if reference_image is None:
                    # 尝试从参数中加载参考图像
                    ref_path = parameters.get("reference_image")
                    if ref_path:
                        try:
                            reference_image = cv2.imread(ref_path)
                        except:
                            pass

                if reference_image is not None:
                    detected_translation = self._auto_detect_translation(image, reference_image, parameters)
                    if detected_translation is not None:
                        dx, dy = detected_translation

            # 创建平移矩阵
            translation_matrix = np.float32([[1, 0, dx], [0, 1, dy]])

            # 设置插值和边界模式
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST
            }

            border_map = {
                "constant": cv2.BORDER_CONSTANT,
                "reflect": cv2.BORDER_REFLECT,
                "wrap": cv2.BORDER_WRAP,
                "replicate": cv2.BORDER_REPLICATE
            }

            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)
            border_mode = border_map.get(parameters.get("border_mode", "constant"), cv2.BORDER_CONSTANT)
            border_value = parameters.get("border_value", 0)

            # 执行平移变换
            h, w = image.shape[:2]
            translated = cv2.warpAffine(
                image, translation_matrix, (w, h),
                flags=interpolation,
                borderMode=border_mode,
                borderValue=border_value
            )

            return create_success_result(
                data={
                    "translation_info": {
                        "dx": dx,
                        "dy": dy,
                        "translation_matrix": translation_matrix.tolist(),
                        "auto_detected": parameters.get("auto_detect", False)
                    }
                },
                image=translated,
                message=f"平移修正完成，偏移: ({dx}, {dy})"
            )

        except Exception as e:
            return create_error_result(f"平移修正失败: {e}")

    def _auto_detect_translation(self, image: np.ndarray, reference_image: np.ndarray,
                                parameters: Dict[str, Any]) -> Optional[Tuple[int, int]]:
        """自动检测平移量"""
        try:
            method = parameters.get("detection_method", "template_matching")

            if method == "template_matching":
                # 使用模板匹配
                return self._detect_translation_template_matching(image, reference_image)
            elif method == "feature_matching":
                # 使用特征匹配
                return self._detect_translation_feature_matching(image, reference_image)

            return None

        except Exception as e:
            logger.warning(f"自动检测平移量失败: {e}")
            return None

    def _detect_translation_template_matching(self, image: np.ndarray,
                                            reference_image: np.ndarray) -> Optional[Tuple[int, int]]:
        """使用模板匹配检测平移量"""
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray_img = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray_img = image

            if len(reference_image.shape) == 3:
                gray_ref = cv2.cvtColor(reference_image, cv2.COLOR_BGR2GRAY)
            else:
                gray_ref = reference_image

            # 调整尺寸
            if gray_img.shape != gray_ref.shape:
                gray_ref = cv2.resize(gray_ref, (gray_img.shape[1], gray_img.shape[0]))

            # 模板匹配
            result = cv2.matchTemplate(gray_img, gray_ref, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            if max_val > 0.5:  # 置信度阈值
                # 计算平移量
                h, w = gray_ref.shape
                dx = max_loc[0] - w // 2
                dy = max_loc[1] - h // 2
                return (dx, dy)

            return None

        except Exception as e:
            logger.warning(f"模板匹配检测失败: {e}")
            return None

    def _detect_translation_feature_matching(self, image: np.ndarray,
                                           reference_image: np.ndarray) -> Optional[Tuple[int, int]]:
        """使用特征匹配检测平移量"""
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray_img = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray_img = image

            if len(reference_image.shape) == 3:
                gray_ref = cv2.cvtColor(reference_image, cv2.COLOR_BGR2GRAY)
            else:
                gray_ref = reference_image

            # 创建ORB检测器
            orb = cv2.ORB_create()

            # 检测关键点和描述符
            kp1, des1 = orb.detectAndCompute(gray_ref, None)
            kp2, des2 = orb.detectAndCompute(gray_img, None)

            if des1 is None or des2 is None:
                return None

            # 匹配特征点
            bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
            matches = bf.match(des1, des2)

            if len(matches) < 10:  # 需要足够的匹配点
                return None

            # 排序匹配点
            matches = sorted(matches, key=lambda x: x.distance)

            # 计算平移量
            src_pts = np.float32([kp1[m.queryIdx].pt for m in matches[:10]]).reshape(-1, 1, 2)
            dst_pts = np.float32([kp2[m.trainIdx].pt for m in matches[:10]]).reshape(-1, 1, 2)

            # 计算平均偏移
            offsets = dst_pts - src_pts
            dx = int(np.mean(offsets[:, 0, 0]))
            dy = int(np.mean(offsets[:, 0, 1]))

            return (dx, dy)

        except Exception as e:
            logger.warning(f"特征匹配检测失败: {e}")
            return None


class ScaleCorrectionAlgorithm(BaseAlgorithm):
    """缩放修正算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.POSITION_CORRECTION

    def get_algorithm_name(self) -> str:
        return "scale_correction"

    def get_display_name(self) -> str:
        return "缩放修正"

    def get_description(self) -> str:
        return "对图像进行缩放修正，支持等比和非等比缩放"

    def get_icon_path(self) -> str:
        return "resources/icons/scale_correction.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "scale_x": 1.0,  # X方向缩放比例
            "scale_y": 1.0,  # Y方向缩放比例
            "maintain_aspect_ratio": True,  # 保持宽高比
            "output_size": None,  # 输出尺寸 [width, height]
            "center": None,  # 缩放中心，None表示图像中心
            "interpolation": "linear",
            "auto_calculate_scale": False,  # 自动计算缩放比例
            "target_width": None,  # 目标宽度
            "target_height": None  # 目标高度
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "scale_x": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10.0,
                "description": "X方向缩放比例"
            },
            "scale_y": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10.0,
                "description": "Y方向缩放比例"
            },
            "maintain_aspect_ratio": {
                "type": "boolean",
                "description": "保持宽高比"
            },
            "output_size": {
                "type": "array",
                "items": {"type": "integer"},
                "minItems": 2,
                "maxItems": 2,
                "description": "输出图像尺寸 [width, height]"
            },
            "center": {
                "type": "array",
                "items": {"type": "number"},
                "minItems": 2,
                "maxItems": 2,
                "description": "缩放中心 [x, y]"
            },
            "interpolation": {
                "type": "string",
                "enum": ["linear", "cubic", "nearest", "area", "lanczos"],
                "description": "插值方法"
            },
            "auto_calculate_scale": {
                "type": "boolean",
                "description": "自动计算缩放比例"
            },
            "target_width": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10000,
                "description": "目标宽度"
            },
            "target_height": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10000,
                "description": "目标高度"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("corrected_image", DataType.IMAGE, "修正后图像")
        self.add_output_connection("scale_info", DataType.RESULTS, "缩放信息")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行缩放修正 - 使用装饰器优化版本"""
        try:
            image = inputs["image"]
            parameters = config.parameters

            h, w = image.shape[:2]

            # 获取缩放参数
            scale_x = parameters.get("scale_x", 1.0)
            scale_y = parameters.get("scale_y", 1.0)
            maintain_aspect_ratio = parameters.get("maintain_aspect_ratio", True)

            # 自动计算缩放比例
            if parameters.get("auto_calculate_scale", False):
                target_width = parameters.get("target_width")
                target_height = parameters.get("target_height")

                if target_width or target_height:
                    if maintain_aspect_ratio:
                        # 保持宽高比的缩放
                        if target_width and target_height:
                            scale_x = min(target_width / w, target_height / h)
                            scale_y = scale_x
                        elif target_width:
                            scale_x = target_width / w
                            scale_y = scale_x
                        elif target_height:
                            scale_y = target_height / h
                            scale_x = scale_y
                    else:
                        # 非等比缩放
                        if target_width:
                            scale_x = target_width / w
                        if target_height:
                            scale_y = target_height / h

            # 如果保持宽高比，使用相同的缩放比例
            if maintain_aspect_ratio and scale_x != scale_y:
                scale = min(scale_x, scale_y)
                scale_x = scale_y = scale

            # 确定输出尺寸
            output_size = parameters.get("output_size")
            if output_size:
                new_w, new_h = output_size
            else:
                new_w = int(w * scale_x)
                new_h = int(h * scale_y)

            # 设置插值方法
            interpolation_map = {
                "linear": cv2.INTER_LINEAR,
                "cubic": cv2.INTER_CUBIC,
                "nearest": cv2.INTER_NEAREST,
                "area": cv2.INTER_AREA,
                "lanczos": cv2.INTER_LANCZOS4
            }

            interpolation = interpolation_map.get(parameters.get("interpolation", "linear"), cv2.INTER_LINEAR)

            # 执行缩放
            if parameters.get("center") is not None:
                # 使用指定中心点的缩放
                center = tuple(parameters["center"])

                # 创建缩放矩阵
                scale_matrix = cv2.getRotationMatrix2D(center, 0, scale_x)

                # 执行仿射变换
                scaled = cv2.warpAffine(image, scale_matrix, (new_w, new_h), flags=interpolation)
            else:
                # 简单的整体缩放
                scaled = cv2.resize(image, (new_w, new_h), interpolation=interpolation)

            # 计算实际缩放比例
            actual_scale_x = new_w / w
            actual_scale_y = new_h / h

            return create_success_result(
                data={
                    "scale_info": {
                        "original_size": {"width": w, "height": h},
                        "new_size": {"width": new_w, "height": new_h},
                        "scale_x": actual_scale_x,
                        "scale_y": actual_scale_y,
                        "maintain_aspect_ratio": maintain_aspect_ratio,
                        "interpolation": parameters.get("interpolation", "linear")
                    }
                },
                image=scaled,
                message=f"缩放修正完成，比例: {actual_scale_x:.2f}x{actual_scale_y:.2f}"
            )

        except Exception as e:
            return create_error_result(f"缩放修正失败: {e}")