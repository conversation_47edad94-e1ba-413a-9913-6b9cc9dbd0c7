#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像处理算法模块

提供各种基础图像处理算法：高斯模糊、形态学操作、阈值处理等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper
from ..utils.image_utils import ImageUtils


class GaussianBlurAlgorithm(BaseAlgorithm):
    """高斯模糊算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "gaussian_blur"

    def get_display_name(self) -> str:
        return "高斯模糊"

    def get_description(self) -> str:
        return "对图像进行高斯模糊处理"

    def get_icon_path(self) -> str:
        return "resources/icons/gaussian_blur.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "kernel_size_x": 15,
            "kernel_size_y": 15,
            "sigma_x": 0,
            "sigma_y": 0
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "kernel_size_x": {
                "type": "integer",
                "minimum": 1,
                "maximum": 99,
                "description": "X方向核大小（必须为奇数）"
            },
            "kernel_size_y": {
                "type": "integer",
                "minimum": 1,
                "maximum": 99,
                "description": "Y方向核大小（必须为奇数）"
            },
            "sigma_x": {
                "type": "number",
                "minimum": 0,
                "maximum": 100,
                "description": "X方向标准差"
            },
            "sigma_y": {
                "type": "number",
                "minimum": 0,
                "maximum": 100,
                "description": "Y方向标准差"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "模糊处理后的图像")

    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False,
        enable_roi_processing=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行高斯模糊 - 使用装饰器优化版本"""
        # 装饰器已经处理了输入验证、时间记录、异常处理等
        # 这里只需要专注于核心算法逻辑

        image = inputs["image"]
        parameters = config.parameters

        # 使用工具类确保核大小为奇数
        ksize_x = parameters.get("kernel_size_x", 15)
        ksize_y = parameters.get("kernel_size_y", 15)
        if ksize_x % 2 == 0:
            ksize_x += 1
        if ksize_y % 2 == 0:
            ksize_y += 1

        sigma_x = parameters.get("sigma_x", 0)
        sigma_y = parameters.get("sigma_y", 0)

        # 执行高斯模糊
        blurred = cv2.GaussianBlur(image, (ksize_x, ksize_y), sigma_x, sigmaY=sigma_y)

        return create_success_result(
            data={
                "kernel_size": (ksize_x, ksize_y),
                "sigma": (sigma_x, sigma_y)
            },
            image=blurred,
            message=f"高斯模糊完成，核大小: ({ksize_x}, {ksize_y})"
        )


class MedianBlurAlgorithm(BaseAlgorithm):
    """中值滤波算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "median_blur"

    def get_display_name(self) -> str:
        return "中值滤波"

    def get_description(self) -> str:
        return "对图像进行中值滤波处理，用于去除椒盐噪声"

    def get_icon_path(self) -> str:
        return "resources/icons/median_blur.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "kernel_size": 5
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "kernel_size": {
                "type": "integer",
                "minimum": 3,
                "maximum": 99,
                "description": "核大小（必须为奇数）"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "滤波处理后的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行中值滤波 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        ksize = parameters.get("kernel_size", 5)
        if ksize % 2 == 0:
            ksize += 1

        # 执行中值滤波
        filtered = cv2.medianBlur(image, ksize)

        return create_success_result(
            data={"kernel_size": ksize},
            image=filtered,
            message=f"中值滤波完成，核大小: {ksize}"
        )


class ThresholdAlgorithm(BaseAlgorithm):
    """阈值处理算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "threshold"

    def get_display_name(self) -> str:
        return "阈值处理"

    def get_description(self) -> str:
        return "对图像进行二值化阈值处理"

    def get_icon_path(self) -> str:
        return "resources/icons/threshold.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "threshold_value": 127,
            "max_value": 255,
            "threshold_type": "binary",
            "use_otsu": False,
            "use_triangle": False
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "threshold_value": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "阈值"
            },
            "max_value": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "最大值"
            },
            "threshold_type": {
                "type": "string",
                "enum": ["binary", "binary_inv", "trunc", "tozero", "tozero_inv"],
                "description": "阈值类型"
            },
            "use_otsu": {
                "type": "boolean",
                "description": "使用Otsu自动阈值"
            },
            "use_triangle": {
                "type": "boolean",
                "description": "使用Triangle自动阈值"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "二值化图像")

    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行阈值处理"""
        start_time = time.time()

        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)

            image = inputs["image"]
            parameters = config.parameters

            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            threshold_value = parameters.get("threshold_value", 127)
            max_value = parameters.get("max_value", 255)
            threshold_type = parameters.get("threshold_type", "binary")

            # 设置阈值类型
            type_map = {
                "binary": cv2.THRESH_BINARY,
                "binary_inv": cv2.THRESH_BINARY_INV,
                "trunc": cv2.THRESH_TRUNC,
                "tozero": cv2.THRESH_TOZERO,
                "tozero_inv": cv2.THRESH_TOZERO_INV
            }

            thresh_type = type_map.get(threshold_type, cv2.THRESH_BINARY)

            # 添加自动阈值选择
            if parameters.get("use_otsu", False):
                thresh_type |= cv2.THRESH_OTSU
            elif parameters.get("use_triangle", False):
                thresh_type |= cv2.THRESH_TRIANGLE

            # 执行阈值处理
            ret_val, thresholded = cv2.threshold(gray, threshold_value, max_value, thresh_type)

            execution_time = time.time() - start_time

            return create_success_result(
                data={
                    "threshold_value": ret_val,
                    "actual_threshold": ret_val,
                    "threshold_type": threshold_type
                },
                image=thresholded,
                message=f"阈值处理完成，阈值: {ret_val:.1f}",
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"阈值处理失败: {e}")
            return create_error_result(f"阈值处理失败: {e}", time.time() - start_time)


class EdgeDetectionAlgorithm(BaseAlgorithm):
    """边缘检测算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "edge_detection"

    def get_display_name(self) -> str:
        return "边缘检测"

    def get_description(self) -> str:
        return "使用Canny算法检测图像边缘"

    def get_icon_path(self) -> str:
        return "resources/icons/edge_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "low_threshold": 50,
            "high_threshold": 150,
            "aperture_size": 3,
            "l2_gradient": False
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "low_threshold": {
                "type": "integer",
                "minimum": 0,
                "maximum": 500,
                "description": "低阈值"
            },
            "high_threshold": {
                "type": "integer",
                "minimum": 0,
                "maximum": 500,
                "description": "高阈值"
            },
            "aperture_size": {
                "type": "integer",
                "enum": [3, 5, 7],
                "description": "Sobel核大小"
            },
            "l2_gradient": {
                "type": "boolean",
                "description": "使用L2梯度"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("edges", DataType.IMAGE, "边缘检测结果")

    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行边缘检测"""
        start_time = time.time()

        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)

            image = inputs["image"]
            parameters = config.parameters

            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            low_threshold = parameters.get("low_threshold", 50)
            high_threshold = parameters.get("high_threshold", 150)
            aperture_size = parameters.get("aperture_size", 3)
            l2_gradient = parameters.get("l2_gradient", False)

            # 执行Canny边缘检测
            edges = cv2.Canny(gray, low_threshold, high_threshold,
                             apertureSize=aperture_size, L2gradient=l2_gradient)

            execution_time = time.time() - start_time

            return create_success_result(
                data={
                    "low_threshold": low_threshold,
                    "high_threshold": high_threshold,
                    "edge_count": np.count_nonzero(edges)
                },
                image=edges,
                message=f"边缘检测完成，检测到 {np.count_nonzero(edges)} 个边缘像素",
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"边缘检测失败: {e}")
            return create_error_result(f"边缘检测失败: {e}", time.time() - start_time)


class MorphologyAlgorithm(BaseAlgorithm):
    """形态学操作算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "morphology"

    def get_display_name(self) -> str:
        return "形态学操作"

    def get_description(self) -> str:
        return "对图像进行形态学操作：腐蚀、膨胀、开运算、闭运算等"

    def get_icon_path(self) -> str:
        return "resources/icons/morphology.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "operation": "opening",
            "kernel_shape": "rectangle",
            "kernel_size": 5,
            "iterations": 1
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "operation": {
                "type": "string",
                "enum": ["erosion", "dilation", "opening", "closing", "gradient", "tophat", "blackhat"],
                "description": "形态学操作类型"
            },
            "kernel_shape": {
                "type": "string",
                "enum": ["rectangle", "ellipse", "cross"],
                "description": "核形状"
            },
            "kernel_size": {
                "type": "integer",
                "minimum": 3,
                "maximum": 25,
                "description": "核大小"
            },
            "iterations": {
                "type": "integer",
                "minimum": 1,
                "maximum": 10,
                "description": "迭代次数"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "形态学处理后的图像")

    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行形态学操作"""
        start_time = time.time()

        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)

            image = inputs["image"]
            parameters = config.parameters

            operation = parameters.get("operation", "opening")
            kernel_shape = parameters.get("kernel_shape", "rectangle")
            kernel_size = parameters.get("kernel_size", 5)
            iterations = parameters.get("iterations", 1)

            # 创建结构元素
            shape_map = {
                "rectangle": cv2.MORPH_RECT,
                "ellipse": cv2.MORPH_ELLIPSE,
                "cross": cv2.MORPH_CROSS
            }

            kernel = cv2.getStructuringElement(
                shape_map.get(kernel_shape, cv2.MORPH_RECT),
                (kernel_size, kernel_size)
            )

            # 执行形态学操作
            op_map = {
                "erosion": cv2.MORPH_ERODE,
                "dilation": cv2.MORPH_DILATE,
                "opening": cv2.MORPH_OPEN,
                "closing": cv2.MORPH_CLOSE,
                "gradient": cv2.MORPH_GRADIENT,
                "tophat": cv2.MORPH_TOPHAT,
                "blackhat": cv2.MORPH_BLACKHAT
            }

            if operation in ["erosion", "dilation"]:
                if operation == "erosion":
                    result = cv2.erode(image, kernel, iterations=iterations)
                else:
                    result = cv2.dilate(image, kernel, iterations=iterations)
            else:
                result = cv2.morphologyEx(image, op_map[operation], kernel, iterations=iterations)

            execution_time = time.time() - start_time

            return create_success_result(
                data={
                    "operation": operation,
                    "kernel_shape": kernel_shape,
                    "kernel_size": kernel_size,
                    "iterations": iterations
                },
                image=result,
                message=f"形态学{operation}操作完成",
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"形态学操作失败: {e}")
            return create_error_result(f"形态学操作失败: {e}", time.time() - start_time)


# 其他图像处理算法的占位符实现
class BilateralFilterAlgorithm(BaseAlgorithm):
    """双边滤波算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "bilateral_filter"

    def get_display_name(self) -> str:
        return "双边滤波"

    def get_description(self) -> str:
        return "保边滤波，在降噪的同时保持边缘清晰"

    def get_icon_path(self) -> str:
        return "resources/icons/bilateral_filter.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "d": 9,
            "sigma_color": 75,
            "sigma_space": 75
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "d": {
                "type": "integer",
                "minimum": 1,
                "maximum": 50,
                "description": "滤波器直径"
            },
            "sigma_color": {
                "type": "number",
                "minimum": 1,
                "maximum": 200,
                "description": "颜色空间标准差"
            },
            "sigma_space": {
                "type": "number",
                "minimum": 1,
                "maximum": 200,
                "description": "坐标空间标准差"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "滤波处理后的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行双边滤波 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        d = parameters.get("d", 9)
        sigma_color = parameters.get("sigma_color", 75)
        sigma_space = parameters.get("sigma_space", 75)

        # 执行双边滤波
        filtered = cv2.bilateralFilter(image, d, sigma_color, sigma_space)

        return create_success_result(
            data={
                "d": d,
                "sigma_color": sigma_color,
                "sigma_space": sigma_space
            },
            image=filtered,
            message=f"双边滤波完成，参数: d={d}, σ_color={sigma_color}, σ_space={sigma_space}"
        )


class ColorSpaceAlgorithm(BaseAlgorithm):
    """颜色空间转换算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "color_space"

    def get_display_name(self) -> str:
        return "颜色空间转换"

    def get_description(self) -> str:
        return "在不同颜色空间之间转换：RGB、HSV、LAB、GRAY等"

    def get_icon_path(self) -> str:
        return "resources/icons/color_space.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "target_space": "HSV",
            "split_channels": False,
            "channel_index": 0
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "target_space": {
                "type": "string",
                "enum": ["GRAY", "HSV", "LAB", "YUV", "XYZ", "HLS"],
                "description": "目标颜色空间"
            },
            "split_channels": {
                "type": "boolean",
                "description": "是否分离通道"
            },
            "channel_index": {
                "type": "integer",
                "minimum": 0,
                "maximum": 2,
                "description": "选择的通道索引（分离通道时）"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "转换后的图像")
        self.add_output_connection("channels", DataType.RESULTS, "通道数据")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行颜色空间转换 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        target_space = parameters.get("target_space", "HSV")
        split_channels = parameters.get("split_channels", False)
        channel_index = parameters.get("channel_index", 0)

        # 颜色空间转换映射
        conversion_map = {
            "GRAY": cv2.COLOR_BGR2GRAY,
            "HSV": cv2.COLOR_BGR2HSV,
            "LAB": cv2.COLOR_BGR2LAB,
            "YUV": cv2.COLOR_BGR2YUV,
            "XYZ": cv2.COLOR_BGR2XYZ,
            "HLS": cv2.COLOR_BGR2HLS
        }

        if target_space not in conversion_map:
            return create_error_result(f"不支持的颜色空间: {target_space}")

        # 执行颜色空间转换
        converted = cv2.cvtColor(image, conversion_map[target_space])

        # 处理通道分离
        channels_data = []
        result_image = converted

        if split_channels and len(converted.shape) == 3:
            # 分离通道
            channels = cv2.split(converted)
            channels_data = [{"channel": i, "data": ch} for i, ch in enumerate(channels)]

            # 如果指定了通道索引，返回该通道
            if 0 <= channel_index < len(channels):
                result_image = channels[channel_index]

        return create_success_result(
            data={
                "target_space": target_space,
                "original_shape": image.shape,
                "converted_shape": converted.shape,
                "channels_count": len(channels_data) if channels_data else (1 if len(converted.shape) == 2 else converted.shape[2])
            },
            image=result_image,
            message=f"颜色空间转换完成: BGR → {target_space}"
        )


class HistogramAlgorithm(BaseAlgorithm):
    """直方图算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "histogram"

    def get_display_name(self) -> str:
        return "直方图处理"

    def get_description(self) -> str:
        return "计算图像直方图并进行均衡化处理"

    def get_icon_path(self) -> str:
        return "resources/icons/histogram.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "operation": "calculate",
            "bins": 256,
            "equalize": False,
            "adaptive_equalize": False,
            "clip_limit": 2.0,
            "tile_grid_size": 8
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "operation": {
                "type": "string",
                "enum": ["calculate", "equalize", "adaptive_equalize"],
                "description": "直方图操作类型"
            },
            "bins": {
                "type": "integer",
                "minimum": 16,
                "maximum": 512,
                "description": "直方图分组数"
            },
            "equalize": {
                "type": "boolean",
                "description": "执行直方图均衡化"
            },
            "adaptive_equalize": {
                "type": "boolean",
                "description": "执行自适应直方图均衡化"
            },
            "clip_limit": {
                "type": "number",
                "minimum": 1.0,
                "maximum": 10.0,
                "description": "CLAHE裁剪限制"
            },
            "tile_grid_size": {
                "type": "integer",
                "minimum": 2,
                "maximum": 16,
                "description": "CLAHE网格大小"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("histogram", DataType.RESULTS, "直方图数据")
        self.add_output_connection("image", DataType.IMAGE, "处理后的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行直方图处理 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        operation = parameters.get("operation", "calculate")
        bins = parameters.get("bins", 256)
        equalize = parameters.get("equalize", False)
        adaptive_equalize = parameters.get("adaptive_equalize", False)
        clip_limit = parameters.get("clip_limit", 2.0)
        tile_grid_size = parameters.get("tile_grid_size", 8)

        # 转换为灰度图像进行处理
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 计算直方图
        hist = cv2.calcHist([gray], [0], None, [bins], [0, 256])
        hist_data = {
            "values": hist.flatten().tolist(),
            "bins": bins,
            "range": [0, 256]
        }

        # 处理图像
        result_image = image.copy()

        if operation == "equalize" or equalize:
            # 直方图均衡化
            if len(image.shape) == 3:
                # 彩色图像：在YUV空间进行均衡化
                yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
                yuv[:,:,0] = cv2.equalizeHist(yuv[:,:,0])
                result_image = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
            else:
                result_image = cv2.equalizeHist(gray)

        elif operation == "adaptive_equalize" or adaptive_equalize:
            # 自适应直方图均衡化 (CLAHE)
            clahe = cv2.createCLAHE(clipLimit=clip_limit, tileGridSize=(tile_grid_size, tile_grid_size))

            if len(image.shape) == 3:
                # 彩色图像：在LAB空间进行CLAHE
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                lab[:,:,0] = clahe.apply(lab[:,:,0])
                result_image = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                result_image = clahe.apply(gray)

        return create_success_result(
            data={
                "histogram": hist_data,
                "operation": operation,
                "mean_intensity": float(np.mean(gray)),
                "std_intensity": float(np.std(gray)),
                "min_intensity": int(np.min(gray)),
                "max_intensity": int(np.max(gray))
            },
            image=result_image,
            message=f"直方图{operation}处理完成"
        )


class ContrastAlgorithm(BaseAlgorithm):
    """对比度调整算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "contrast"

    def get_display_name(self) -> str:
        return "对比度调整"

    def get_description(self) -> str:
        return "调整图像对比度和亮度，支持线性和非线性调整"

    def get_icon_path(self) -> str:
        return "resources/icons/contrast.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "alpha": 1.0,      # 对比度系数
            "beta": 0,         # 亮度偏移
            "gamma": 1.0,      # 伽马校正
            "auto_contrast": False,
            "clip_percent": 1.0
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "alpha": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 5.0,
                "description": "对比度系数 (1.0=原始)"
            },
            "beta": {
                "type": "integer",
                "minimum": -100,
                "maximum": 100,
                "description": "亮度偏移"
            },
            "gamma": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 3.0,
                "description": "伽马校正值 (1.0=原始)"
            },
            "auto_contrast": {
                "type": "boolean",
                "description": "自动对比度调整"
            },
            "clip_percent": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10.0,
                "description": "自动对比度裁剪百分比"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "调整后的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行对比度调整 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        alpha = parameters.get("alpha", 1.0)
        beta = parameters.get("beta", 0)
        gamma = parameters.get("gamma", 1.0)
        auto_contrast = parameters.get("auto_contrast", False)
        clip_percent = parameters.get("clip_percent", 1.0)

        result_image = image.copy().astype(np.float32)

        if auto_contrast:
            # 自动对比度调整
            if len(image.shape) == 3:
                # 彩色图像：转换到LAB空间处理L通道
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB).astype(np.float32)
                l_channel = lab[:,:,0]

                # 计算裁剪值
                low_val = np.percentile(l_channel, clip_percent)
                high_val = np.percentile(l_channel, 100 - clip_percent)

                # 拉伸对比度
                l_channel = np.clip((l_channel - low_val) * 255.0 / (high_val - low_val), 0, 255)
                lab[:,:,0] = l_channel

                result_image = cv2.cvtColor(lab.astype(np.uint8), cv2.COLOR_LAB2BGR).astype(np.float32)
            else:
                # 灰度图像
                low_val = np.percentile(image, clip_percent)
                high_val = np.percentile(image, 100 - clip_percent)
                result_image = np.clip((image.astype(np.float32) - low_val) * 255.0 / (high_val - low_val), 0, 255)
        else:
            # 手动调整
            # 线性对比度和亮度调整: new_image = alpha * image + beta
            result_image = alpha * result_image + beta

        # 伽马校正
        if gamma != 1.0:
            # 归一化到[0,1]，应用伽马校正，再缩放回[0,255]
            normalized = result_image / 255.0
            normalized = np.power(normalized, gamma)
            result_image = normalized * 255.0

        # 确保像素值在有效范围内
        result_image = np.clip(result_image, 0, 255).astype(np.uint8)

        return create_success_result(
            data={
                "alpha": alpha,
                "beta": beta,
                "gamma": gamma,
                "auto_contrast": auto_contrast,
                "original_range": [int(np.min(image)), int(np.max(image))],
                "adjusted_range": [int(np.min(result_image)), int(np.max(result_image))]
            },
            image=result_image,
            message=f"对比度调整完成，α={alpha}, β={beta}, γ={gamma}"
        )


class NoiseReductionAlgorithm(BaseAlgorithm):
    """降噪算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.IMAGE_PROCESSING

    def get_algorithm_name(self) -> str:
        return "noise_reduction"

    def get_display_name(self) -> str:
        return "降噪处理"

    def get_description(self) -> str:
        return "多种降噪算法：高斯降噪、非局部均值、双边滤波等"

    def get_icon_path(self) -> str:
        return "resources/icons/noise_reduction.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "method": "gaussian",
            "kernel_size": 5,
            "sigma": 1.0,
            "h": 10,
            "template_window_size": 7,
            "search_window_size": 21
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "method": {
                "type": "string",
                "enum": ["gaussian", "bilateral", "non_local_means", "median"],
                "description": "降噪方法"
            },
            "kernel_size": {
                "type": "integer",
                "minimum": 3,
                "maximum": 15,
                "description": "核大小（奇数）"
            },
            "sigma": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10.0,
                "description": "高斯标准差"
            },
            "h": {
                "type": "number",
                "minimum": 1,
                "maximum": 30,
                "description": "非局部均值滤波强度"
            },
            "template_window_size": {
                "type": "integer",
                "minimum": 3,
                "maximum": 15,
                "description": "模板窗口大小"
            },
            "search_window_size": {
                "type": "integer",
                "minimum": 7,
                "maximum": 35,
                "description": "搜索窗口大小"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("image", DataType.IMAGE, "降噪后的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行降噪处理 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        method = parameters.get("method", "gaussian")
        kernel_size = parameters.get("kernel_size", 5)
        sigma = parameters.get("sigma", 1.0)
        h = parameters.get("h", 10)
        template_window_size = parameters.get("template_window_size", 7)
        search_window_size = parameters.get("search_window_size", 21)

        # 确保核大小为奇数
        if kernel_size % 2 == 0:
            kernel_size += 1

        if method == "gaussian":
            # 高斯降噪
            denoised = cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)

        elif method == "bilateral":
            # 双边滤波降噪
            denoised = cv2.bilateralFilter(image, kernel_size, sigma*20, sigma*20)

        elif method == "median":
            # 中值滤波降噪
            denoised = cv2.medianBlur(image, kernel_size)

        elif method == "non_local_means":
            # 非局部均值降噪
            if len(image.shape) == 3:
                # 彩色图像
                denoised = cv2.fastNlMeansDenoisingColored(
                    image, None, h, h, template_window_size, search_window_size
                )
            else:
                # 灰度图像
                denoised = cv2.fastNlMeansDenoising(
                    image, None, h, template_window_size, search_window_size
                )
        else:
            return create_error_result(f"不支持的降噪方法: {method}")

        return create_success_result(
            data={
                "method": method,
                "kernel_size": kernel_size,
                "sigma": sigma,
                "h": h if method == "non_local_means" else None,
                "noise_reduction_info": {
                    "original_std": float(np.std(image)),
                    "denoised_std": float(np.std(denoised)),
                    "noise_reduction_ratio": float(np.std(image)) / float(np.std(denoised)) if np.std(denoised) > 0 else 1.0
                }
            },
            image=denoised,
            message=f"{method}降噪处理完成"
        )