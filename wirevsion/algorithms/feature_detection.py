#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
特征检测算法模块

提供各种特征检测算法：模板匹配、角点检测、轮廓检测等
"""

import time
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from loguru import logger

from .base_algorithm import (
    BaseAlgorithm, AlgorithmType, AlgorithmConfig, AlgorithmResult,
    DataType, ROI, create_error_result, create_success_result
)
from .decorators import standard_algorithm_wrapper
from ..utils.image_utils import ImageUtils, TemplateMatchingUtils


class TemplateMatchingAlgorithm(BaseAlgorithm):
    """模板匹配算法 - 使用装饰器优化版本"""

    def __init__(self):
        super().__init__()
        self.templates = {}

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION

    def get_algorithm_name(self) -> str:
        return "template_matching"

    def get_display_name(self) -> str:
        return "模板匹配"

    def get_description(self) -> str:
        return "在图像中搜索匹配的模板"

    def get_icon_path(self) -> str:
        return "resources/icons/template_matching.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "method": "TM_CCOEFF_NORMED",
            "threshold": 0.8,
            "max_matches": 10,
            "enable_multi_scale": True,
            "scale_range": [0.5, 2.0],
            "scale_step": 0.1,
            "enable_rotation": False,
            "rotation_range": [-45, 45],
            "rotation_step": 5
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "method": {
                "type": "string",
                "enum": ["TM_CCOEFF", "TM_CCOEFF_NORMED", "TM_CCORR",
                        "TM_CCORR_NORMED", "TM_SQDIFF", "TM_SQDIFF_NORMED"],
                "description": "匹配方法"
            },
            "threshold": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0,
                "description": "匹配阈值"
            },
            "max_matches": {
                "type": "integer",
                "minimum": 1,
                "maximum": 100,
                "description": "最大匹配数量"
            },
            "enable_multi_scale": {
                "type": "boolean",
                "description": "启用多尺度匹配"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("template", DataType.IMAGE, True, "模板图像")
        self.add_output_connection("matches", DataType.RESULTS, "匹配结果")
        self.add_output_connection("image", DataType.IMAGE, "标记匹配的图像")

    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=True,
        enable_roi_processing=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行模板匹配 - 使用装饰器优化版本"""
        # 装饰器已处理输入验证、时间记录、异常处理等

        image = inputs["image"]
        template = inputs["template"]
        parameters = config.parameters

        # 使用工具类进行模板匹配
        method = getattr(cv2, parameters.get("method", "TM_CCOEFF_NORMED"))
        threshold = parameters.get("threshold", 0.8)
        max_matches = parameters.get("max_matches", 10)

        # 执行模板匹配
        if parameters.get("enable_multi_scale", True):
            matches = TemplateMatchingUtils.multi_scale_template_matching(
                image, template, method, threshold, max_matches,
                scale_range=parameters.get("scale_range", [0.5, 2.0]),
                scale_step=parameters.get("scale_step", 0.1)
            )
        else:
            # 标准模板匹配
            result = cv2.matchTemplate(image, template, method)

            # 根据方法类型处理结果
            if method in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
                locations = np.where(result <= threshold)
            else:
                locations = np.where(result >= threshold)

            matches = []
            for pt in zip(*locations[::-1]):
                confidence = result[pt[1], pt[0]]
                matches.append({
                    'x': int(pt[0]),
                    'y': int(pt[1]),
                    'width': template.shape[1],
                    'height': template.shape[0],
                    'confidence': float(confidence),
                    'scale': 1.0
                })

            # 限制匹配数量
            matches = sorted(matches, key=lambda x: x['confidence'], reverse=True)[:max_matches]

        # 在图像上绘制匹配结果
        result_image = image.copy()
        for match in matches:
            x, y, w, h = match['x'], match['y'], match['width'], match['height']
            cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)

            # 绘制置信度
            confidence_text = f"{match['confidence']:.3f}"
            cv2.putText(result_image, confidence_text, (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        return create_success_result(
            data={
                "matches": matches,
                "match_count": len(matches),
                "method": parameters.get("method"),
                "threshold": threshold
            },
            image=result_image,
            message=f"模板匹配完成，找到 {len(matches)} 个匹配"
        )


class CornerDetectionAlgorithm(BaseAlgorithm):
    """角点检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION

    def get_algorithm_name(self) -> str:
        return "corner_detection"

    def get_display_name(self) -> str:
        return "角点检测"

    def get_description(self) -> str:
        return "检测图像中的角点特征"

    def get_icon_path(self) -> str:
        return "resources/icons/corner_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "method": "harris",
            "max_corners": 100,
            "quality_level": 0.01,
            "min_distance": 10,
            "block_size": 3,
            "k": 0.04
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "method": {
                "type": "string",
                "enum": ["harris", "shi_tomasi"],
                "description": "角点检测方法"
            },
            "max_corners": {
                "type": "integer",
                "minimum": 1,
                "maximum": 1000,
                "description": "最大角点数量"
            },
            "quality_level": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 1.0,
                "description": "角点质量水平"
            },
            "min_distance": {
                "type": "number",
                "minimum": 1,
                "maximum": 100,
                "description": "角点间最小距离"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("corners", DataType.POINTS, "检测到的角点")
        self.add_output_connection("image", DataType.IMAGE, "标记角点的图像")

    @standard_algorithm_wrapper(
        enable_performance_monitor=True,
        enable_cache=False
    )
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行角点检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 使用工具类确保参数有效
        max_corners = parameters.get("max_corners", 100)
        quality_level = parameters.get("quality_level", 0.01)
        min_distance = parameters.get("min_distance", 10)
        block_size = parameters.get("block_size", 3)
        k = parameters.get("k", 0.04)
        method = parameters.get("method", "harris")

        # 检测角点
        if method == "harris":
            corners = cv2.goodFeaturesToTrack(
                gray, max_corners, quality_level, min_distance,
                blockSize=block_size, useHarrisDetector=True, k=k
            )
        else:  # shi_tomasi
            corners = cv2.goodFeaturesToTrack(
                gray, max_corners, quality_level, min_distance,
                blockSize=block_size, useHarrisDetector=False
            )

        # 处理结果
        if corners is not None:
            corners = np.float32(corners).reshape(-1, 2)
            corner_list = [{"x": float(x), "y": float(y)} for x, y in corners]
        else:
            corners = np.array([])
            corner_list = []

        # 在图像上绘制角点
        result_image = image.copy()
        if len(corner_list) > 0:
            for corner in corner_list:
                cv2.circle(result_image, (int(corner["x"]), int(corner["y"])), 3, (0, 255, 0), -1)

        return create_success_result(
            data={
                "corners": corner_list,
                "corner_count": len(corner_list),
                "method": method,
                "parameters": {
                    "max_corners": max_corners,
                    "quality_level": quality_level,
                    "min_distance": min_distance
                }
            },
            image=result_image,
            message=f"角点检测完成，找到 {len(corner_list)} 个角点"
        )


class ContourDetectionAlgorithm(BaseAlgorithm):
    """轮廓检测算法"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION

    def get_algorithm_name(self) -> str:
        return "contour_detection"

    def get_display_name(self) -> str:
        return "轮廓检测"

    def get_description(self) -> str:
        return "检测图像中的轮廓"

    def get_icon_path(self) -> str:
        return "resources/icons/contour_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "threshold_method": "binary",
            "threshold_value": 127,
            "max_value": 255,
            "retrieval_mode": "external",
            "approximation_method": "simple",
            "min_area": 100,
            "max_area": 100000,
            "min_perimeter": 0,
            "filter_by_area": True,
            "filter_by_perimeter": False
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "threshold_method": {
                "type": "string",
                "enum": ["binary", "binary_inv", "adaptive_mean", "adaptive_gaussian"],
                "description": "阈值方法"
            },
            "threshold_value": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "阈值"
            },
            "retrieval_mode": {
                "type": "string",
                "enum": ["external", "tree", "ccomp", "list"],
                "description": "轮廓检索模式"
            },
            "min_area": {
                "type": "number",
                "minimum": 0,
                "description": "最小面积"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_input_connection("roi", DataType.ROI, False, "检测区域")

        self.add_output_connection("contours", DataType.CONTOURS, "检测到的轮廓")
        self.add_output_connection("annotated_image", DataType.IMAGE, "标注图像")

    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行轮廓检测"""
        start_time = time.time()

        try:
            if not self.validate_inputs(inputs):
                return create_error_result("输入验证失败", time.time() - start_time)

            image = inputs["image"]
            parameters = config.parameters

            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # 二值化
            threshold_method = parameters.get("threshold_method", "binary")
            threshold_value = parameters.get("threshold_value", 127)
            max_value = parameters.get("max_value", 255)

            if threshold_method == "binary":
                _, binary = cv2.threshold(gray, threshold_value, max_value, cv2.THRESH_BINARY)
            elif threshold_method == "binary_inv":
                _, binary = cv2.threshold(gray, threshold_value, max_value, cv2.THRESH_BINARY_INV)
            elif threshold_method == "adaptive_mean":
                binary = cv2.adaptiveThreshold(gray, max_value, cv2.ADAPTIVE_THRESH_MEAN_C,
                                             cv2.THRESH_BINARY, 11, 2)
            elif threshold_method == "adaptive_gaussian":
                binary = cv2.adaptiveThreshold(gray, max_value, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                             cv2.THRESH_BINARY, 11, 2)

            # 轮廓检测
            retrieval_mode = parameters.get("retrieval_mode", "external")
            approximation_method = parameters.get("approximation_method", "simple")

            mode_map = {
                "external": cv2.RETR_EXTERNAL,
                "tree": cv2.RETR_TREE,
                "ccomp": cv2.RETR_CCOMP,
                "list": cv2.RETR_LIST
            }

            approx_map = {
                "simple": cv2.CHAIN_APPROX_SIMPLE,
                "none": cv2.CHAIN_APPROX_NONE
            }

            contours, hierarchy = cv2.findContours(
                binary,
                mode_map.get(retrieval_mode, cv2.RETR_EXTERNAL),
                approx_map.get(approximation_method, cv2.CHAIN_APPROX_SIMPLE)
            )

            # 过滤轮廓
            filtered_contours = []
            min_area = parameters.get("min_area", 100)
            max_area = parameters.get("max_area", 100000)
            min_perimeter = parameters.get("min_perimeter", 0)

            for contour in contours:
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)

                # 面积过滤
                if parameters.get("filter_by_area", True):
                    if area < min_area or area > max_area:
                        continue

                # 周长过滤
                if parameters.get("filter_by_perimeter", False):
                    if perimeter < min_perimeter:
                        continue

                filtered_contours.append(contour)

            # 创建结果数据
            contour_data = []
            annotated_image = image.copy()

            for i, contour in enumerate(filtered_contours):
                # 计算轮廓属性
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)

                # 边界矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 最小外接圆
                (center_x, center_y), radius = cv2.minEnclosingCircle(contour)

                contour_info = {
                    "id": i,
                    "area": float(area),
                    "perimeter": float(perimeter),
                    "bounding_rect": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                    "center": {"x": float(center_x), "y": float(center_y)},
                    "radius": float(radius),
                    "points": contour.tolist()
                }
                contour_data.append(contour_info)

                # 绘制轮廓
                cv2.drawContours(annotated_image, [contour], -1, (0, 255, 0), 2)
                cv2.rectangle(annotated_image, (x, y), (x + w, y + h), (255, 0, 0), 1)
                cv2.circle(annotated_image, (int(center_x), int(center_y)), int(radius), (0, 0, 255), 1)

            execution_time = time.time() - start_time

            return create_success_result(
                data={
                    "contours": contour_data,
                    "contour_count": len(filtered_contours),
                    "total_found": len(contours),
                    "filtered_count": len(filtered_contours)
                },
                image=annotated_image,
                message=f"检测到 {len(filtered_contours)} 个轮廓",
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"轮廓检测失败: {e}")
            return create_error_result(f"轮廓检测失败: {e}", time.time() - start_time)


# 其他检测算法的占位符实现
class BlobDetectionAlgorithm(BaseAlgorithm):
    """斑点检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION

    def get_algorithm_name(self) -> str:
        return "blob_detection"

    def get_display_name(self) -> str:
        return "斑点检测"

    def get_description(self) -> str:
        return "使用SimpleBlobDetector检测图像中的斑点特征"

    def get_icon_path(self) -> str:
        return "resources/icons/blob_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "min_threshold": 50,
            "max_threshold": 220,
            "threshold_step": 10,
            "min_area": 25,
            "max_area": 5000,
            "min_circularity": 0.1,
            "max_circularity": 1.0,
            "min_convexity": 0.87,
            "max_convexity": 1.0,
            "min_inertia_ratio": 0.1,
            "max_inertia_ratio": 1.0,
            "filter_by_color": True,
            "blob_color": 0  # 0 for dark blobs, 255 for light blobs
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "min_threshold": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "最小阈值"
            },
            "max_threshold": {
                "type": "integer",
                "minimum": 0,
                "maximum": 255,
                "description": "最大阈值"
            },
            "min_area": {
                "type": "number",
                "minimum": 1,
                "maximum": 10000,
                "description": "最小面积"
            },
            "max_area": {
                "type": "number",
                "minimum": 1,
                "maximum": 50000,
                "description": "最大面积"
            },
            "min_circularity": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 1.0,
                "description": "最小圆度"
            },
            "filter_by_color": {
                "type": "boolean",
                "description": "按颜色过滤"
            },
            "blob_color": {
                "type": "integer",
                "enum": [0, 255],
                "description": "斑点颜色 (0=暗色, 255=亮色)"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("blobs", DataType.RESULTS, "检测到的斑点")
        self.add_output_connection("image", DataType.IMAGE, "标记斑点的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行斑点检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 设置SimpleBlobDetector参数
        params = cv2.SimpleBlobDetector_Params()

        # 阈值参数
        params.minThreshold = parameters.get("min_threshold", 50)
        params.maxThreshold = parameters.get("max_threshold", 220)
        params.thresholdStep = parameters.get("threshold_step", 10)

        # 面积过滤
        params.filterByArea = True
        params.minArea = parameters.get("min_area", 25)
        params.maxArea = parameters.get("max_area", 5000)

        # 圆度过滤
        params.filterByCircularity = True
        params.minCircularity = parameters.get("min_circularity", 0.1)
        params.maxCircularity = parameters.get("max_circularity", 1.0)

        # 凸性过滤
        params.filterByConvexity = True
        params.minConvexity = parameters.get("min_convexity", 0.87)
        params.maxConvexity = parameters.get("max_convexity", 1.0)

        # 惯性比过滤
        params.filterByInertia = True
        params.minInertiaRatio = parameters.get("min_inertia_ratio", 0.1)
        params.maxInertiaRatio = parameters.get("max_inertia_ratio", 1.0)

        # 颜色过滤
        params.filterByColor = parameters.get("filter_by_color", True)
        params.blobColor = parameters.get("blob_color", 0)

        # 创建检测器
        detector = cv2.SimpleBlobDetector_create(params)

        # 检测斑点
        keypoints = detector.detect(gray)

        # 处理检测结果
        blobs = []
        for i, kp in enumerate(keypoints):
            blob_info = {
                "id": i,
                "center": {"x": float(kp.pt[0]), "y": float(kp.pt[1])},
                "size": float(kp.size),
                "angle": float(kp.angle),
                "response": float(kp.response)
            }
            blobs.append(blob_info)

        # 绘制检测结果
        result_image = image.copy()
        if keypoints:
            result_image = cv2.drawKeypoints(
                result_image, keypoints, np.array([]),
                (0, 255, 0), cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS
            )

        return create_success_result(
            data={
                "blobs": blobs,
                "blob_count": len(blobs),
                "detector_params": {
                    "min_area": params.minArea,
                    "max_area": params.maxArea,
                    "min_circularity": params.minCircularity,
                    "blob_color": params.blobColor
                }
            },
            image=result_image,
            message=f"斑点检测完成，找到 {len(blobs)} 个斑点"
        )


class LineDetectionAlgorithm(BaseAlgorithm):
    """直线检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION

    def get_algorithm_name(self) -> str:
        return "line_detection"

    def get_display_name(self) -> str:
        return "直线检测"

    def get_description(self) -> str:
        return "使用霍夫变换检测图像中的直线"

    def get_icon_path(self) -> str:
        return "resources/icons/line_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "method": "standard",  # standard, probabilistic
            "rho": 1,
            "theta": np.pi/180,
            "threshold": 100,
            "min_line_length": 50,
            "max_line_gap": 10,
            "canny_low": 50,
            "canny_high": 150
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "method": {
                "type": "string",
                "enum": ["standard", "probabilistic"],
                "description": "检测方法"
            },
            "rho": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10,
                "description": "距离分辨率"
            },
            "theta": {
                "type": "number",
                "minimum": 0.001,
                "maximum": 0.1,
                "description": "角度分辨率"
            },
            "threshold": {
                "type": "integer",
                "minimum": 1,
                "maximum": 500,
                "description": "累加器阈值"
            },
            "min_line_length": {
                "type": "integer",
                "minimum": 1,
                "maximum": 1000,
                "description": "最小直线长度"
            },
            "max_line_gap": {
                "type": "integer",
                "minimum": 1,
                "maximum": 100,
                "description": "最大直线间隙"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("lines", DataType.RESULTS, "检测到的直线")
        self.add_output_connection("image", DataType.IMAGE, "标记直线的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行直线检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 边缘检测
        canny_low = parameters.get("canny_low", 50)
        canny_high = parameters.get("canny_high", 150)
        edges = cv2.Canny(gray, canny_low, canny_high)

        # 霍夫变换参数
        method = parameters.get("method", "standard")
        rho = parameters.get("rho", 1)
        theta = parameters.get("theta", np.pi/180)
        threshold = parameters.get("threshold", 100)

        lines_data = []
        result_image = image.copy()

        if method == "standard":
            # 标准霍夫变换
            lines = cv2.HoughLines(edges, rho, theta, threshold)

            if lines is not None:
                for i, line in enumerate(lines):
                    rho_val, theta_val = line[0]

                    # 计算直线端点
                    a = np.cos(theta_val)
                    b = np.sin(theta_val)
                    x0 = a * rho_val
                    y0 = b * rho_val

                    # 扩展到图像边界
                    x1 = int(x0 + 1000 * (-b))
                    y1 = int(y0 + 1000 * (a))
                    x2 = int(x0 - 1000 * (-b))
                    y2 = int(y0 - 1000 * (a))

                    line_info = {
                        "id": i,
                        "type": "infinite",
                        "rho": float(rho_val),
                        "theta": float(theta_val),
                        "angle_degrees": float(np.degrees(theta_val)),
                        "points": {"x1": x1, "y1": y1, "x2": x2, "y2": y2}
                    }
                    lines_data.append(line_info)

                    # 绘制直线
                    cv2.line(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

        else:  # probabilistic
            # 概率霍夫变换
            min_line_length = parameters.get("min_line_length", 50)
            max_line_gap = parameters.get("max_line_gap", 10)

            lines = cv2.HoughLinesP(edges, rho, theta, threshold,
                                   minLineLength=min_line_length,
                                   maxLineGap=max_line_gap)

            if lines is not None:
                for i, line in enumerate(lines):
                    x1, y1, x2, y2 = line[0]

                    # 计算直线属性
                    length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
                    angle = np.degrees(np.arctan2(y2-y1, x2-x1))

                    line_info = {
                        "id": i,
                        "type": "segment",
                        "points": {"x1": int(x1), "y1": int(y1), "x2": int(x2), "y2": int(y2)},
                        "length": float(length),
                        "angle_degrees": float(angle),
                        "center": {"x": float((x1+x2)/2), "y": float((y1+y2)/2)}
                    }
                    lines_data.append(line_info)

                    # 绘制直线段
                    cv2.line(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.circle(result_image, (x1, y1), 3, (255, 0, 0), -1)
                    cv2.circle(result_image, (x2, y2), 3, (0, 0, 255), -1)

        return create_success_result(
            data={
                "lines": lines_data,
                "line_count": len(lines_data),
                "method": method,
                "detection_params": {
                    "rho": rho,
                    "theta": theta,
                    "threshold": threshold
                }
            },
            image=result_image,
            message=f"直线检测完成，找到 {len(lines_data)} 条直线"
        )


class CircleDetectionAlgorithm(BaseAlgorithm):
    """圆形检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION

    def get_algorithm_name(self) -> str:
        return "circle_detection"

    def get_display_name(self) -> str:
        return "圆形检测"

    def get_description(self) -> str:
        return "使用霍夫圆变换检测图像中的圆形"

    def get_icon_path(self) -> str:
        return "resources/icons/circle_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "dp": 1,
            "min_dist": 50,
            "param1": 100,
            "param2": 30,
            "min_radius": 10,
            "max_radius": 100,
            "canny_low": 50,
            "canny_high": 150
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "dp": {
                "type": "number",
                "minimum": 0.1,
                "maximum": 10,
                "description": "累加器分辨率比例"
            },
            "min_dist": {
                "type": "integer",
                "minimum": 1,
                "maximum": 500,
                "description": "圆心间最小距离"
            },
            "param1": {
                "type": "integer",
                "minimum": 1,
                "maximum": 500,
                "description": "Canny边缘检测高阈值"
            },
            "param2": {
                "type": "integer",
                "minimum": 1,
                "maximum": 200,
                "description": "累加器阈值"
            },
            "min_radius": {
                "type": "integer",
                "minimum": 1,
                "maximum": 1000,
                "description": "最小半径"
            },
            "max_radius": {
                "type": "integer",
                "minimum": 1,
                "maximum": 1000,
                "description": "最大半径"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("circles", DataType.RESULTS, "检测到的圆形")
        self.add_output_connection("image", DataType.IMAGE, "标记圆形的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行圆形检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 霍夫圆变换参数
        dp = parameters.get("dp", 1)
        min_dist = parameters.get("min_dist", 50)
        param1 = parameters.get("param1", 100)
        param2 = parameters.get("param2", 30)
        min_radius = parameters.get("min_radius", 10)
        max_radius = parameters.get("max_radius", 100)

        # 霍夫圆变换
        circles = cv2.HoughCircles(
            gray,
            cv2.HOUGH_GRADIENT,
            dp=dp,
            minDist=min_dist,
            param1=param1,
            param2=param2,
            minRadius=min_radius,
            maxRadius=max_radius
        )

        circles_data = []
        result_image = image.copy()

        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")

            for i, (x, y, r) in enumerate(circles):
                circle_info = {
                    "id": i,
                    "center": {"x": int(x), "y": int(y)},
                    "radius": int(r),
                    "diameter": int(2 * r),
                    "area": float(np.pi * r * r),
                    "circumference": float(2 * np.pi * r)
                }
                circles_data.append(circle_info)

                # 绘制圆形
                cv2.circle(result_image, (x, y), r, (0, 255, 0), 2)
                cv2.circle(result_image, (x, y), 2, (255, 0, 0), 3)

                # 标注半径
                cv2.putText(result_image, f"r={r}", (x-20, y-r-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

        return create_success_result(
            data={
                "circles": circles_data,
                "circle_count": len(circles_data),
                "detection_params": {
                    "dp": dp,
                    "min_dist": min_dist,
                    "param1": param1,
                    "param2": param2,
                    "radius_range": [min_radius, max_radius]
                }
            },
            image=result_image,
            message=f"圆形检测完成，找到 {len(circles_data)} 个圆形"
        )


class KeyPointDetectionAlgorithm(BaseAlgorithm):
    """关键点检测算法 - 使用装饰器优化版本"""

    def get_algorithm_type(self) -> AlgorithmType:
        return AlgorithmType.FEATURE_DETECTION

    def get_algorithm_name(self) -> str:
        return "keypoint_detection"

    def get_display_name(self) -> str:
        return "关键点检测"

    def get_description(self) -> str:
        return "使用SIFT、ORB、FAST等算法检测图像关键点特征"

    def get_icon_path(self) -> str:
        return "resources/icons/keypoint_detection.png"

    def get_default_parameters(self) -> Dict[str, Any]:
        return {
            "detector": "ORB",
            "max_features": 500,
            "threshold": 10,
            "nonmax_suppression": True,
            "scale_factor": 1.2,
            "n_levels": 8
        }

    def get_parameter_schema(self) -> Dict[str, Any]:
        return {
            "detector": {
                "type": "string",
                "enum": ["ORB", "FAST", "SIFT", "SURF"],
                "description": "关键点检测器类型"
            },
            "max_features": {
                "type": "integer",
                "minimum": 10,
                "maximum": 5000,
                "description": "最大特征点数量"
            },
            "threshold": {
                "type": "integer",
                "minimum": 1,
                "maximum": 100,
                "description": "检测阈值"
            },
            "nonmax_suppression": {
                "type": "boolean",
                "description": "非极大值抑制"
            },
            "scale_factor": {
                "type": "number",
                "minimum": 1.1,
                "maximum": 2.0,
                "description": "尺度因子"
            },
            "n_levels": {
                "type": "integer",
                "minimum": 1,
                "maximum": 16,
                "description": "金字塔层数"
            }
        }

    def _setup_connections(self):
        """设置连接点"""
        self.add_input_connection("image", DataType.IMAGE, True, "输入图像")
        self.add_output_connection("keypoints", DataType.RESULTS, "检测到的关键点")
        self.add_output_connection("descriptors", DataType.RESULTS, "特征描述符")
        self.add_output_connection("image", DataType.IMAGE, "标记关键点的图像")

    @standard_algorithm_wrapper(enable_performance_monitor=True)
    def execute(self, inputs: Dict[str, Any], config: AlgorithmConfig) -> AlgorithmResult:
        """执行关键点检测 - 使用装饰器优化版本"""
        image = inputs["image"]
        parameters = config.parameters

        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        detector_type = parameters.get("detector", "ORB")
        max_features = parameters.get("max_features", 500)
        threshold = parameters.get("threshold", 10)
        nonmax_suppression = parameters.get("nonmax_suppression", True)

        # 创建检测器
        detector = None
        descriptors = None

        try:
            if detector_type == "ORB":
                scale_factor = parameters.get("scale_factor", 1.2)
                n_levels = parameters.get("n_levels", 8)
                detector = cv2.ORB_create(
                    nfeatures=max_features,
                    scaleFactor=scale_factor,
                    nlevels=n_levels
                )
                keypoints, descriptors = detector.detectAndCompute(gray, None)

            elif detector_type == "FAST":
                detector = cv2.FastFeatureDetector_create(
                    threshold=threshold,
                    nonmaxSuppression=nonmax_suppression
                )
                keypoints = detector.detect(gray, None)

            elif detector_type == "SIFT":
                detector = cv2.SIFT_create(nfeatures=max_features)
                keypoints, descriptors = detector.detectAndCompute(gray, None)

            elif detector_type == "SURF":
                # SURF需要opencv-contrib-python
                try:
                    detector = cv2.xfeatures2d.SURF_create(hessianThreshold=threshold)
                    keypoints, descriptors = detector.detectAndCompute(gray, None)
                except AttributeError:
                    return create_error_result("SURF检测器不可用，请安装opencv-contrib-python")

            else:
                return create_error_result(f"不支持的检测器类型: {detector_type}")

        except Exception as e:
            return create_error_result(f"关键点检测失败: {e}")

        # 处理检测结果
        keypoints_data = []
        for i, kp in enumerate(keypoints):
            keypoint_info = {
                "id": i,
                "point": {"x": float(kp.pt[0]), "y": float(kp.pt[1])},
                "size": float(kp.size),
                "angle": float(kp.angle),
                "response": float(kp.response),
                "octave": int(kp.octave),
                "class_id": int(kp.class_id)
            }
            keypoints_data.append(keypoint_info)

        # 处理描述符
        descriptors_data = None
        if descriptors is not None:
            descriptors_data = {
                "shape": descriptors.shape,
                "dtype": str(descriptors.dtype),
                "data": descriptors.tolist()
            }

        # 绘制关键点
        result_image = image.copy()
        if keypoints:
            result_image = cv2.drawKeypoints(
                result_image, keypoints, None,
                color=(0, 255, 0), flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS
            )

        return create_success_result(
            data={
                "keypoints": keypoints_data,
                "keypoint_count": len(keypoints_data),
                "descriptors": descriptors_data,
                "detector_type": detector_type,
                "detector_params": {
                    "max_features": max_features,
                    "threshold": threshold,
                    "nonmax_suppression": nonmax_suppression
                }
            },
            image=result_image,
            message=f"关键点检测完成，找到 {len(keypoints_data)} 个关键点"
        )