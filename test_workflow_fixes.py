#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试工作流编辑器中的节点点击显示功能
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt, QTimer
from loguru import logger

# 配置日志
logger.remove()  # 移除默认处理程序
logger.add(sys.stderr, level="INFO")  # 添加标准错误输出处理程序
logger.add("workflow_test_{time}.log", level="DEBUG")  # 添加文件日志

class TestApp(QMainWindow):
    """简化的测试应用程序"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("工作流节点点击测试")
        self.resize(800, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        self.test_btn = QPushButton("模拟节点点击")
        self.test_btn.clicked.connect(self.simulate_node_click)
        layout.addWidget(self.test_btn)
        
        # 按钮2：测试连续运行
        self.run_btn = QPushButton("测试连续运行")
        self.run_btn.clicked.connect(self.test_continuous_run)
        layout.addWidget(self.run_btn)
        
        # 状态变量
        self.running = False
        self.timer_id = None
        
        logger.info("测试应用初始化完成")
    
    def simulate_node_click(self):
        """模拟节点点击"""
        try:
            logger.info("模拟节点点击")
            # 这里只是模拟，实际不执行任何操作
            logger.info("节点点击模拟成功")
        except Exception as e:
            logger.error(f"模拟节点点击出错: {e}")
            logger.error(traceback.format_exc())
    
    def test_continuous_run(self):
        """测试连续运行功能"""
        try:
            if self.running:
                # 停止运行
                if self.timer_id is not None:
                    self.killTimer(self.timer_id)
                    self.timer_id = None
                
                self.running = False
                self.run_btn.setText("测试连续运行")
                logger.info("停止连续运行")
            else:
                # 开始运行
                self.running = True
                self.timer_id = self.startTimer(1000)  # 1秒间隔
                
                self.run_btn.setText("停止连续运行")
                logger.info("开始连续运行")
        except Exception as e:
            logger.error(f"测试连续运行出错: {e}")
            logger.error(traceback.format_exc())
    
    def timerEvent(self, event):
        """定时器事件处理"""
        if event.timerId() == self.timer_id:
            try:
                logger.info("定时器触发")
                # 执行一些模拟操作
                pass
            except Exception as e:
                logger.error(f"定时器处理出错: {e}")
                logger.error(traceback.format_exc())

def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        window = TestApp()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        logger.error(f"应用程序出错: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main() 