#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVision 综合修复验证测试
验证所有已修复的问题：
1. 函数签名错误修复
2. UI一致性问题修复
3. 预览显示问题修复
4. numpy导入问题修复
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from loguru import logger
import time

# 导入项目模块
try:
    from wirevsion.ui.modern_components import THEME_COLORS
    from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog
    from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
    from wirevsion.ui.theme_manager import theme_manager
    logger.success("✅ 所有核心模块导入成功")
except Exception as e:
    logger.error(f"❌ 模块导入失败: {e}")
    sys.exit(1)


class ComprehensiveFixVerificationWindow(QMainWindow):
    """综合修复验证窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("WireVision 综合修复验证")
        self.setGeometry(100, 100, 1000, 700)
        
        # 应用主题
        self.setStyleSheet(theme_manager.get_global_stylesheet())
        
        self._setup_ui()
        self._test_results = []
        
    def _setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 测试结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {THEME_COLORS["dark_bg_content"]};
                color: {THEME_COLORS["text_primary"]};
                border: 1px solid {THEME_COLORS["dark_border_primary"]};
                border-radius: 8px;
                padding: 10px;
                font-family: "Consolas", "Monaco", monospace;
                font-size: 12px;
            }}
        """)
        layout.addWidget(self.result_text)
        
        # 测试按钮
        test_btn = QPushButton("🚀 开始综合验证测试")
        test_btn.setStyleSheet(theme_manager.get_button_style("primary"))
        test_btn.clicked.connect(self.run_comprehensive_tests)
        layout.addWidget(test_btn)
        
    def log_result(self, message: str):
        """记录测试结果"""
        self._test_results.append(message)
        self.result_text.append(message)
        QApplication.processEvents()
        
    def run_comprehensive_tests(self):
        """运行综合测试"""
        self.result_text.clear()
        self._test_results.clear()
        
        self.log_result("🎯 开始WireVision综合修复验证测试...")
        self.log_result("=" * 60)
        
        # 测试1: 函数签名修复验证
        self.test_function_signature_fix()
        
        # 测试2: UI一致性验证
        self.test_ui_consistency()
        
        # 测试3: 预览功能验证
        self.test_preview_functionality()
        
        # 测试4: numpy导入修复验证
        self.test_numpy_import_fix()
        
        # 测试5: THEME_COLORS使用验证
        self.test_theme_colors_usage()
        
        # 总结测试结果
        self.summarize_test_results()
        
    def test_function_signature_fix(self):
        """测试函数签名修复"""
        self.log_result("\n📝 测试1: 函数签名修复验证")
        self.log_result("-" * 40)
        
        try:
            # 测试 _preview_generic_algorithm 方法签名
            dialog = ModernNodeConfigDialog("test_algorithm", {})
            
            # 检查方法是否存在且可调用
            if hasattr(dialog, '_preview_generic_algorithm'):
                self.log_result("✅ _preview_generic_algorithm 方法存在")
                
                # 测试方法调用（使用正确的参数数量）
                try:
                    # 这应该不会抛出TypeError了
                    dialog._preview_generic_algorithm("test", {}, None)
                    self.log_result("✅ 函数签名修复成功 - 可以正确调用")
                except TypeError as e:
                    self.log_result(f"❌ 函数签名仍有问题: {e}")
                except Exception as e:
                    # 其他异常是可以接受的（比如没有测试图像等）
                    self.log_result("✅ 函数签名正确 - 其他异常可忽略")
            else:
                self.log_result("❌ _preview_generic_algorithm 方法不存在")
                
            dialog.close()
            
        except Exception as e:
            self.log_result(f"❌ 函数签名测试失败: {e}")
            
    def test_ui_consistency(self):
        """测试UI一致性"""
        self.log_result("\n🎨 测试2: UI一致性验证")
        self.log_result("-" * 40)
        
        try:
            # 检查THEME_COLORS完整性
            required_colors = [
                'dark_bg_app', 'dark_bg_content', 'dark_bg_card',
                'text_primary', 'text_secondary', 'primary',
                'primary_hover', 'success', 'danger'
            ]
            
            missing_colors = []
            for color in required_colors:
                if color not in THEME_COLORS:
                    missing_colors.append(color)
                    
            if missing_colors:
                self.log_result(f"❌ 缺失主题颜色: {missing_colors}")
            else:
                self.log_result("✅ 所有必需的主题颜色都已定义")
                
            # 测试主题管理器
            if theme_manager:
                self.log_result("✅ 主题管理器初始化成功")
                
                # 测试样式方法
                button_style = theme_manager.get_button_style("primary")
                if button_style and THEME_COLORS["primary"] in button_style:
                    self.log_result("✅ 按钮样式使用主题颜色")
                else:
                    self.log_result("❌ 按钮样式未使用主题颜色")
            else:
                self.log_result("❌ 主题管理器初始化失败")
                
        except Exception as e:
            self.log_result(f"❌ UI一致性测试失败: {e}")
            
    def test_preview_functionality(self):
        """测试预览功能"""
        self.log_result("\n🖼️ 测试3: 预览功能验证")
        self.log_result("-" * 40)
        
        try:
            # 创建配置对话框
            dialog = ModernNodeConfigDialog("color_detection", {})
            
            # 检查预览相关方法
            preview_methods = [
                '_preview_algorithm',
                '_preview_color_detection',
                '_preview_edge_detection',
                '_preview_gaussian_blur'
            ]
            
            for method_name in preview_methods:
                if hasattr(dialog, method_name):
                    self.log_result(f"✅ {method_name} 方法存在")
                else:
                    self.log_result(f"❌ {method_name} 方法缺失")
                    
            dialog.close()
            
        except Exception as e:
            self.log_result(f"❌ 预览功能测试失败: {e}")
            
    def test_numpy_import_fix(self):
        """测试numpy导入修复"""
        self.log_result("\n🔢 测试4: numpy导入修复验证")
        self.log_result("-" * 40)
        
        try:
            # 测试numpy导入
            import numpy as np
            self.log_result("✅ numpy导入成功")
            
            # 测试ascontiguousarray函数
            test_array = np.array([[1, 2], [3, 4]])
            result = np.ascontiguousarray(test_array)
            if result.flags['C_CONTIGUOUS']:
                self.log_result("✅ ascontiguousarray函数工作正常")
            else:
                self.log_result("❌ ascontiguousarray函数有问题")
                
        except Exception as e:
            self.log_result(f"❌ numpy导入测试失败: {e}")
            
    def test_theme_colors_usage(self):
        """测试THEME_COLORS使用情况"""
        self.log_result("\n🌈 测试5: THEME_COLORS使用验证")
        self.log_result("-" * 40)
        
        try:
            # 检查主要颜色定义
            color_categories = {
                "背景色": ["dark_bg_app", "dark_bg_content", "dark_bg_card"],
                "文本色": ["text_primary", "text_secondary"],
                "主色系": ["primary", "primary_hover", "primary_pressed"],
                "状态色": ["success", "warning", "danger", "info"]
            }
            
            for category, colors in color_categories.items():
                missing = [c for c in colors if c not in THEME_COLORS]
                if missing:
                    self.log_result(f"❌ {category}缺失: {missing}")
                else:
                    self.log_result(f"✅ {category}完整")
                    
        except Exception as e:
            self.log_result(f"❌ THEME_COLORS测试失败: {e}")
            
    def summarize_test_results(self):
        """总结测试结果"""
        self.log_result("\n" + "=" * 60)
        self.log_result("📊 综合修复验证测试总结")
        self.log_result("=" * 60)
        
        # 统计测试结果
        total_tests = len([r for r in self._test_results if r.startswith("✅") or r.startswith("❌")])
        passed_tests = len([r for r in self._test_results if r.startswith("✅")])
        failed_tests = len([r for r in self._test_results if r.startswith("❌")])
        
        self.log_result(f"总测试项: {total_tests}")
        self.log_result(f"通过: {passed_tests}")
        self.log_result(f"失败: {failed_tests}")
        self.log_result(f"成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%")
        
        if failed_tests == 0:
            self.log_result("\n🎉 所有修复验证通过！WireVision项目问题已全部解决。")
        else:
            self.log_result(f"\n⚠️ 还有 {failed_tests} 个问题需要进一步修复。")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("WireVision 综合修复验证")
    app.setApplicationVersion("1.0.0")
    
    # 创建并显示主窗口
    window = ComprehensiveFixVerificationWindow()
    window.show()
    
    # 自动开始测试
    QTimer.singleShot(1000, window.run_comprehensive_tests)
    
    return app.exec_()


if __name__ == "__main__":
    main()
