#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机截图工具

用于捕获相机画面并保存为修复成功的证据
"""

import os
import sys
import time
import cv2
import numpy as np
from datetime import datetime
from loguru import logger

def main():
    """主函数"""
    logger.info("开始相机截图操作")
    
    # 尝试打开相机
    camera = None
    for camera_id in range(5):
        logger.info(f"尝试打开相机 ID: {camera_id}")
        camera = cv2.VideoCapture(camera_id)
        if camera.isOpened():
            logger.success(f"成功打开相机 ID: {camera_id}")
            # 设置相机参数
            camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            camera.set(cv2.CAP_PROP_FPS, 30)
            break
        else:
            logger.warning(f"无法打开相机 ID: {camera_id}")
    
    if camera is None or not camera.isOpened():
        logger.error("所有相机设备均无法打开，请检查相机连接")
        return 1
    
    # 获取相机实际参数
    actual_width = camera.get(cv2.CAP_PROP_FRAME_WIDTH)
    actual_height = camera.get(cv2.CAP_PROP_FRAME_HEIGHT)
    actual_fps = camera.get(cv2.CAP_PROP_FPS)
    
    logger.info(f"相机参数: 分辨率={actual_width}x{actual_height}, FPS={actual_fps}")
    
    # 预热相机，丢弃前10帧
    logger.info("预热相机中...")
    for _ in range(10):
        camera.read()
        time.sleep(0.1)
    
    # 获取相机帧
    success = False
    frame = None
    
    # 尝试多次获取画面，以确保获取成功
    for attempt in range(5):
        logger.info(f"尝试获取相机画面，第{attempt+1}次")
        ret, frame = camera.read()
        
        if ret and frame is not None and frame.size > 0:
            # 计算图像统计信息
            avg_value = np.mean(frame)
            std_value = np.std(frame)
            min_value = np.min(frame)
            max_value = np.max(frame)
            
            logger.info(f"图像统计: 形状={frame.shape}, 平均值={avg_value:.2f}, "
                       f"标准差={std_value:.2f}, 最小值={min_value}, 最大值={max_value}")
            
            # 如果图像太暗或太亮，尝试再次获取
            if avg_value < 20 or avg_value > 240:
                logger.warning(f"图像质量不佳，平均值={avg_value:.2f}，重试...")
                time.sleep(0.2)
                continue
            
            # 图像质量足够好
            success = True
            break
        else:
            logger.warning("获取相机帧失败，重试...")
            time.sleep(0.2)
    
    # 释放相机
    camera.release()
    
    if not success:
        logger.error("无法获取有效的相机画面")
        return 1
    
    # 添加时间戳和信息
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    cv2.putText(frame, f"WireVsion 相机修复成功 - {timestamp}", (10, 30), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    cv2.putText(frame, f"分辨率: {int(actual_width)}x{int(actual_height)}", (10, 60), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    # 保存图像
    filename = "camera_display_success.png"
    cv2.imwrite(filename, frame)
    
    logger.success(f"成功保存相机截图到: {filename}")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 