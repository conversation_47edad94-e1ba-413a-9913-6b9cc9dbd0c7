# WireVsion应用程序配置

# 相机配置
camera:
  default_id: 0
  resolution: 640x480
  fps: 30
  auto_exposure: true
  exposure: -5
  brightness: 128
  contrast: 128
  enable_frame_cache: true
  frame_cache_lifetime: 0.016  # 约60FPS

# UI配置
ui:
  theme: dark
  font_size: 10
  show_fps: true
  show_stats: true
  auto_refresh: true

# 性能监测配置
performance:
  enable_monitoring: true
  log_interval: 60  # 秒
  warning_threshold: 80  # CPU/内存使用率警告阈值

# 工作流配置
workflow:
  auto_save: true
  auto_save_interval: 300  # 秒
  max_history: 10
