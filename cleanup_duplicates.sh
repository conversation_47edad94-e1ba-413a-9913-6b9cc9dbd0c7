#!/bin/bash

# WireVision 重复文件清理脚本
# 保留优化版本，删除旧版本

echo "🧹 开始清理WireVision重复文件..."

# 备份目录
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📦 创建备份目录: $BACKUP_DIR"

# 清理主入口文件 - 保留优化版本
if [ -f "wirevsion/main.py" ] && [ -f "wirevsion/main_optimized.py" ]; then
    echo "🔄 处理主入口文件..."
    cp "wirevsion/main.py" "$BACKUP_DIR/"
    rm "wirevsion/main.py"
    mv "wirevsion/main_optimized.py" "wirevsion/main.py"
    echo "✅ 已使用优化版本替换main.py"
fi

# 清理主窗口文件 - 保留重构版本
if [ -f "wirevsion/ui/main_window.py" ] && [ -f "wirevsion/ui/main_window_refactored.py" ]; then
    echo "🔄 处理主窗口文件..."
    cp "wirevsion/ui/main_window.py" "$BACKUP_DIR/"
    rm "wirevsion/ui/main_window.py"
    mv "wirevsion/ui/main_window_refactored.py" "wirevsion/ui/main_window.py"
    echo "✅ 已使用重构版本替换main_window.py"
fi

# 清理工作流编辑器 - 保留v2版本
if [ -f "wirevsion/ui/workflow_editor.py" ] && [ -f "wirevsion/ui/workflow_editor_v2.py" ]; then
    echo "🔄 处理工作流编辑器..."
    cp "wirevsion/ui/workflow_editor.py" "$BACKUP_DIR/"
    rm "wirevsion/ui/workflow_editor.py"
    mv "wirevsion/ui/workflow_editor_v2.py" "wirevsion/ui/workflow_editor.py"
    echo "✅ 已使用v2版本替换workflow_editor.py"
fi

# 更新导入引用
echo "🔧 更新导入引用..."

# 更新__main__.py中的导入
if [ -f "wirevsion/__main__.py" ]; then
    sed -i.bak 's/from wirevsion.main import main/from wirevsion.main import main/' wirevsion/__main__.py
    rm wirevsion/__main__.py.bak 2>/dev/null || true
fi

# 更新其他可能的导入引用
find . -name "*.py" -type f -exec grep -l "main_window_refactored\|main_optimized\|workflow_editor_v2" {} \; | while read file; do
    echo "🔧 更新文件: $file"
    sed -i.bak \
        -e 's/main_window_refactored/main_window/g' \
        -e 's/main_optimized/main/g' \
        -e 's/workflow_editor_v2/workflow_editor/g' \
        -e 's/MainWindowRefactored/MainWindow/g' \
        -e 's/WorkflowEditorV2/WorkflowEditor/g' \
        "$file"
    rm "${file}.bak" 2>/dev/null || true
done

echo "📊 清理统计:"
echo "   📁 备份目录: $BACKUP_DIR"
echo "   📄 备份文件数: $(ls -1 $BACKUP_DIR 2>/dev/null | wc -l)"

echo ""
echo "✨ 清理完成! 重复文件已删除，保留了最优版本"
echo "💡 备份文件保存在 $BACKUP_DIR 目录中"
echo ""
echo "🚀 建议下一步操作:"
echo "   1. 测试应用程序启动: python -m wirevsion"
echo "   2. 检查功能是否正常"
echo "   3. 如果一切正常，可以删除备份目录" 