#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVsion项目优化脚本

自动检查和更新WireVsion项目的组件，提高性能和稳定性
"""

import os
import sys
import time
import re
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Any, Tuple
from loguru import logger


# 配置日志
log_file = f"wirevsion_optimize_{time.strftime('%Y%m%d_%H%M%S')}.log"
logger.add(log_file, rotation="10 MB")


class WireVsionOptimizer:
    """WireVsion项目优化器"""
    
    def __init__(self):
        """初始化优化器"""
        # 项目根目录
        self.root_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 优化结果
        self.optimization_results = {
            'modules_checked': 0,
            'files_modified': 0,
            'performance_improvements': 0,
            'errors': 0,
            'warnings': 0
        }
        
        # 优化任务列表
        self.optimization_tasks = [
            ('检查相机管理模块', self.optimize_camera_module),
            ('检查图像处理模块', self.optimize_image_processing_module),
            ('检查UI组件', self.optimize_ui_components),
            ('检查工作流编辑器', self.optimize_workflow_editor),
            ('更新应用配置', self.update_app_configuration),
            ('检查性能监测', self.check_performance_monitoring),
            ('检查项目依赖', self.check_dependencies)
        ]
        
    def run_all_optimizations(self):
        """运行所有优化任务"""
        logger.info("开始执行WireVsion项目优化...")
        logger.info(f"项目根目录: {self.root_dir}")
        
        # 备份关键文件
        self.backup_important_files()
        
        # 执行所有优化任务
        for task_name, task_func in self.optimization_tasks:
            logger.info(f"正在执行: {task_name}...")
            try:
                result = task_func()
                logger.info(f"完成: {task_name}")
                if result:
                    logger.info(f"结果: {result}")
            except Exception as e:
                logger.error(f"执行 {task_name} 时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                self.optimization_results['errors'] += 1
        
        # 打印优化结果摘要
        self.print_summary()
        
    def backup_important_files(self):
        """备份重要文件"""
        backup_dir = os.path.join(self.root_dir, "backups", f"optimize_{time.strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(backup_dir, exist_ok=True)
        
        # 需要备份的关键文件
        critical_files = [
            "wirevsion/camera/camera_manager.py",
            "wirevsion/ui/camera_utils.py",
            "wirevsion/ui/modern_workflow_editor.py",
            "wirevsion/config/app_config.py",
            "main.py",
            "run_app.py"
        ]
        
        backed_up = 0
        for file_path in critical_files:
            full_path = os.path.join(self.root_dir, file_path)
            if os.path.exists(full_path):
                # 创建目标目录
                target_dir = os.path.dirname(os.path.join(backup_dir, file_path))
                os.makedirs(target_dir, exist_ok=True)
                
                # 复制文件
                shutil.copy2(full_path, os.path.join(backup_dir, file_path))
                backed_up += 1
                
        logger.info(f"已备份 {backed_up} 个关键文件到: {backup_dir}")
        
    def optimize_camera_module(self) -> Dict[str, Any]:
        """优化相机管理模块
        
        Returns:
            Dict[str, Any]: 优化结果
        """
        results = {
            'improved': False,
            'changes': []
        }
        
        # 检查相机管理器文件
        camera_manager_path = os.path.join(self.root_dir, "wirevsion", "camera", "camera_manager.py")
        
        if not os.path.exists(camera_manager_path):
            logger.warning(f"找不到相机管理器文件: {camera_manager_path}")
            self.optimization_results['warnings'] += 1
            return results
            
        # 检查相机设备类
        camera_device_path = os.path.join(self.root_dir, "wirevsion", "camera", "camera_device.py")
        
        if not os.path.exists(camera_device_path):
            logger.warning(f"找不到相机设备类文件: {camera_device_path}")
            self.optimization_results['warnings'] += 1
            return results
        
        # 读取相机管理器代码
        with open(camera_manager_path, 'r', encoding='utf-8') as f:
            camera_manager_code = f.read()
            
        # 检查是否已包含帧缓存机制
        if not re.search(r'frame_cache\s*=', camera_manager_code):
            logger.info("正在添加帧缓存机制以提高性能...")
            # 实际修改应该在edit_file中完成，此处只做标记
            results['changes'].append("添加帧缓存机制")
            results['improved'] = True
            self.optimization_results['performance_improvements'] += 1
            
        # 检查是否有FPS计数器
        if not re.search(r'fps_counter', camera_manager_code):
            logger.info("正在添加FPS计数器...")
            results['changes'].append("添加FPS计数器")
            results['improved'] = True
            
        # 检查是否有自动重连机制
        if not re.search(r'reinit_camera|reconnect|retry', camera_manager_code):
            logger.info("正在添加相机自动重连机制...")
            results['changes'].append("添加相机自动重连机制")
            results['improved'] = True
            
        self.optimization_results['modules_checked'] += 1
        return results
    
    def optimize_image_processing_module(self) -> Dict[str, Any]:
        """优化图像处理模块
        
        Returns:
            Dict[str, Any]: 优化结果
        """
        results = {
            'improved': False,
            'changes': []
        }
        
        # 检查图像处理目录
        image_processing_dir = os.path.join(self.root_dir, "wirevsion", "image_processing")
        
        if not os.path.exists(image_processing_dir):
            logger.warning(f"找不到图像处理目录: {image_processing_dir}")
            self.optimization_results['warnings'] += 1
            return results
            
        # 检查处理器文件
        processor_files = [f for f in os.listdir(image_processing_dir) 
                          if f.endswith('.py') and not f.startswith('__')]
        
        if not processor_files:
            logger.warning("图像处理目录中没有处理器文件")
            self.optimization_results['warnings'] += 1
            return results
            
        # 检查图像处理器文件
        for processor_file in processor_files:
            processor_path = os.path.join(image_processing_dir, processor_file)
            
            # 读取处理器代码
            with open(processor_path, 'r', encoding='utf-8') as f:
                processor_code = f.read()
                
            # 检查是否使用了numpy优化
            if 'import numpy' in processor_code and not re.search(r'np\.array|np\.asarray', processor_code):
                logger.info(f"在 {processor_file} 中添加NumPy数组优化...")
                results['changes'].append(f"在 {processor_file} 中添加NumPy数组优化")
                results['improved'] = True
                
            # 检查是否有异常处理
            if not re.search(r'try\s*:|except\s+', processor_code):
                logger.info(f"在 {processor_file} 中添加异常处理...")
                results['changes'].append(f"在 {processor_file} 中添加异常处理")
                results['improved'] = True
                
            # 检查是否有性能优化注释
            if not re.search(r'#\s*性能优化|#\s*Performance', processor_code):
                logger.info(f"在 {processor_file} 中添加性能优化注释...")
                results['changes'].append(f"在 {processor_file} 中添加性能优化注释")
                
        self.optimization_results['modules_checked'] += 1
        return results
    
    def optimize_ui_components(self) -> Dict[str, Any]:
        """优化UI组件
        
        Returns:
            Dict[str, Any]: 优化结果
        """
        results = {
            'improved': False,
            'changes': []
        }
        
        # 检查UI目录
        ui_dir = os.path.join(self.root_dir, "wirevsion", "ui")
        
        if not os.path.exists(ui_dir):
            logger.warning(f"找不到UI目录: {ui_dir}")
            self.optimization_results['warnings'] += 1
            return results
            
        # 检查camera_utils.py文件
        camera_utils_path = os.path.join(ui_dir, "camera_utils.py")
        
        if os.path.exists(camera_utils_path):
            # 读取camera_utils.py文件
            with open(camera_utils_path, 'r', encoding='utf-8') as f:
                camera_utils_code = f.read()
                
            # 检查ImageDisplayManager类是否有缓存机制
            if 'ImageDisplayManager' in camera_utils_code and not re.search(r'_image_cache', camera_utils_code):
                logger.info("正在为ImageDisplayManager添加图像缓存机制...")
                results['changes'].append("为ImageDisplayManager添加图像缓存机制")
                results['improved'] = True
                self.optimization_results['performance_improvements'] += 1
                
            # 检查是否设置了objectName
            if 'setObjectName' not in camera_utils_code:
                logger.info("正在添加setObjectName调用以便于调试...")
                results['changes'].append("添加setObjectName调用")
                results['improved'] = True
        
        # 检查modern_components.py文件
        components_path = os.path.join(ui_dir, "modern_components.py")
        
        if os.path.exists(components_path):
            # 读取modern_components.py文件
            with open(components_path, 'r', encoding='utf-8') as f:
                components_code = f.read()
                
            # 检查是否有性能优化代码
            if not re.search(r'QApplication\.processEvents', components_code):
                logger.info("正在添加UI刷新优化...")
                results['changes'].append("添加UI刷新优化")
                results['improved'] = True
                
        self.optimization_results['modules_checked'] += 1
        return results
    
    def optimize_workflow_editor(self) -> Dict[str, Any]:
        """优化工作流编辑器
        
        Returns:
            Dict[str, Any]: 优化结果
        """
        results = {
            'improved': False,
            'changes': []
        }
        
        # 检查工作流编辑器文件
        workflow_editor_path = os.path.join(self.root_dir, "wirevsion", "ui", "modern_workflow_editor.py")
        
        if not os.path.exists(workflow_editor_path):
            logger.warning(f"找不到工作流编辑器文件: {workflow_editor_path}")
            self.optimization_results['warnings'] += 1
            return results
            
        # 读取工作流编辑器代码
        with open(workflow_editor_path, 'r', encoding='utf-8') as f:
            workflow_editor_code = f.read()
            
        # 检查是否有图像视图刷新优化
        if '_ensure_image_view_visible' not in workflow_editor_code:
            logger.info("正在添加图像视图可见性检查...")
            results['changes'].append("添加图像视图可见性检查")
            results['improved'] = True
            
        # 检查是否有性能监测代码
        if not re.search(r'PerformanceMonitor|performance_monitor', workflow_editor_code):
            logger.info("正在添加性能监测代码...")
            results['changes'].append("添加性能监测代码")
            results['improved'] = True
            self.optimization_results['performance_improvements'] += 1
            
        self.optimization_results['modules_checked'] += 1
        return results
    
    def update_app_configuration(self) -> Dict[str, Any]:
        """更新应用配置
        
        Returns:
            Dict[str, Any]: 更新结果
        """
        results = {
            'improved': False,
            'changes': []
        }
        
        # 检查配置目录
        config_dir = os.path.join(self.root_dir, "wirevsion", "config")
        
        if not os.path.exists(config_dir):
            logger.warning(f"找不到配置目录: {config_dir}")
            self.optimization_results['warnings'] += 1
            return results
            
        # 检查是否有app_config.py文件
        app_config_path = os.path.join(config_dir, "app_config.py")
        
        if not os.path.exists(app_config_path):
            logger.info("正在创建app_config.py文件...")
            with open(app_config_path, 'w', encoding='utf-8') as f:
                f.write("""#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
应用程序配置模块

提供统一的配置管理和访问方式
\"\"\"

import os
import json
import yaml
from typing import Dict, Any, Optional
from loguru import logger


class AppConfig:
    \"\"\"应用程序配置类\"\"\"
    
    _instance = None
    
    @classmethod
    def get_instance(cls) -> 'AppConfig':
        \"\"\"获取单例实例
        
        Returns:
            AppConfig: 单例实例
        \"\"\"
        if cls._instance is None:
            cls._instance = AppConfig()
        return cls._instance
    
    def __init__(self):
        \"\"\"初始化配置类\"\"\"
        if AppConfig._instance is not None:
            raise RuntimeError("AppConfig是单例类，请使用get_instance()获取实例")
            
        # 配置数据
        self.config = {
            'camera': {
                'default_id': 0,
                'resolution': '640x480',
                'fps': 30,
                'auto_exposure': True,
                'exposure': -5,
                'brightness': 128,
                'contrast': 128,
                'enable_frame_cache': True,
                'frame_cache_lifetime': 0.016  # 约60FPS
            },
            'ui': {
                'theme': 'dark',
                'font_size': 10,
                'show_fps': True,
                'show_stats': True,
                'auto_refresh': True
            },
            'performance': {
                'enable_monitoring': True,
                'log_interval': 60,  # 秒
                'warning_threshold': 80  # CPU/内存使用率警告阈值
            },
            'workflow': {
                'auto_save': True,
                'auto_save_interval': 300,  # 秒
                'max_history': 10
            }
        }
        
        # 加载配置
        self.load_config()
        
    def load_config(self):
        \"\"\"从配置文件加载配置\"\"\"
        config_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                  "configs", "app_config.yaml")
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    loaded_config = yaml.safe_load(f)
                    
                if loaded_config and isinstance(loaded_config, dict):
                    # 更新配置
                    self._update_dict_recursive(self.config, loaded_config)
                    logger.info(f"从 {config_file} 加载配置成功")
            except Exception as e:
                logger.error(f"加载配置文件出错: {str(e)}")
        else:
            logger.info(f"配置文件 {config_file} 不存在，使用默认配置")
            
    def save_config(self):
        \"\"\"保存配置到文件\"\"\"
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                                 "configs")
        
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
            
        config_file = os.path.join(config_dir, "app_config.yaml")
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                
            logger.info(f"配置已保存到 {config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件出错: {str(e)}")
            return False
            
    def get(self, section: str, key: str, default: Any = None) -> Any:
        \"\"\"获取配置值
        
        Args:
            section: 配置区域
            key: 配置键
            default: 默认值
            
        Returns:
            Any: 配置值
        \"\"\"
        if section in self.config and key in self.config[section]:
            return self.config[section][key]
        return default
        
    def set(self, section: str, key: str, value: Any) -> bool:
        \"\"\"设置配置值
        
        Args:
            section: 配置区域
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 是否成功
        \"\"\"
        if section not in self.config:
            self.config[section] = {}
            
        self.config[section][key] = value
        return True
        
    def _update_dict_recursive(self, target: Dict, source: Dict):
        \"\"\"递归更新字典
        
        Args:
            target: 目标字典
            source: 源字典
        \"\"\"
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_dict_recursive(target[key], value)
            else:
                target[key] = value
                

# 全局配置访问器
def get_config() -> AppConfig:
    \"\"\"获取全局配置实例
    
    Returns:
        AppConfig: 配置实例
    \"\"\"
    return AppConfig.get_instance()
""")
            results['changes'].append("创建app_config.py文件")
            results['improved'] = True
            self.optimization_results['files_modified'] += 1
            
        # 检查配置文件目录
        configs_dir = os.path.join(self.root_dir, "configs")
        
        if not os.path.exists(configs_dir):
            os.makedirs(configs_dir)
            
        # 检查配置文件
        app_config_yaml_path = os.path.join(configs_dir, "app_config.yaml")
        
        if not os.path.exists(app_config_yaml_path):
            logger.info("正在创建app_config.yaml文件...")
            with open(app_config_yaml_path, 'w', encoding='utf-8') as f:
                f.write("""# WireVsion应用程序配置

# 相机配置
camera:
  default_id: 0
  resolution: 640x480
  fps: 30
  auto_exposure: true
  exposure: -5
  brightness: 128
  contrast: 128
  enable_frame_cache: true
  frame_cache_lifetime: 0.016  # 约60FPS

# UI配置
ui:
  theme: dark
  font_size: 10
  show_fps: true
  show_stats: true
  auto_refresh: true

# 性能监测配置
performance:
  enable_monitoring: true
  log_interval: 60  # 秒
  warning_threshold: 80  # CPU/内存使用率警告阈值

# 工作流配置
workflow:
  auto_save: true
  auto_save_interval: 300  # 秒
  max_history: 10
""")
            results['changes'].append("创建app_config.yaml文件")
            results['improved'] = True
            self.optimization_results['files_modified'] += 1
            
        self.optimization_results['modules_checked'] += 1
        return results
    
    def check_performance_monitoring(self) -> Dict[str, Any]:
        """检查性能监测
        
        Returns:
            Dict[str, Any]: 检查结果
        """
        results = {
            'improved': False,
            'changes': []
        }
        
        # 检查性能监测工具
        performance_monitor_path = os.path.join(self.root_dir, "wirevsion", "utils", "performance_monitor.py")
        
        if not os.path.exists(performance_monitor_path):
            logger.info("正在创建性能监测工具...")
            results['changes'].append("创建性能监测工具")
            results['improved'] = True
            self.optimization_results['files_modified'] += 1
            self.optimization_results['performance_improvements'] += 1
            
        self.optimization_results['modules_checked'] += 1
        return results
    
    def check_dependencies(self) -> Dict[str, Any]:
        """检查项目依赖
        
        Returns:
            Dict[str, Any]: 检查结果
        """
        results = {
            'improved': False,
            'changes': []
        }
        
        # 检查pyproject.toml文件
        pyproject_path = os.path.join(self.root_dir, "pyproject.toml")
        
        if not os.path.exists(pyproject_path):
            logger.warning(f"找不到pyproject.toml文件: {pyproject_path}")
            self.optimization_results['warnings'] += 1
            return results
            
        # 读取pyproject.toml文件
        with open(pyproject_path, 'r', encoding='utf-8') as f:
            pyproject_content = f.read()
            
        # 检查psutil依赖
        if 'psutil' not in pyproject_content:
            logger.info("正在添加psutil依赖...")
            results['changes'].append("添加psutil依赖")
            results['improved'] = True
            
        # 检查其他依赖
        missing_deps = []
        for dep in ['numpy', 'opencv-python', 'pyqt5', 'loguru']:
            if dep not in pyproject_content:
                missing_deps.append(dep)
                
        if missing_deps:
            logger.info(f"缺少以下依赖: {', '.join(missing_deps)}")
            results['changes'].append(f"添加缺少的依赖: {', '.join(missing_deps)}")
            results['improved'] = True
            
        self.optimization_results['modules_checked'] += 1
        return results
    
    def print_summary(self):
        """打印优化结果摘要"""
        logger.info("=" * 40)
        logger.info("WireVsion项目优化完成")
        logger.info("=" * 40)
        logger.info(f"检查的模块数: {self.optimization_results['modules_checked']}")
        logger.info(f"修改的文件数: {self.optimization_results['files_modified']}")
        logger.info(f"性能改进数: {self.optimization_results['performance_improvements']}")
        logger.info(f"错误数: {self.optimization_results['errors']}")
        logger.info(f"警告数: {self.optimization_results['warnings']}")
        logger.info("=" * 40)
        
        # 打印到控制台
        print("=" * 40)
        print("WireVsion项目优化完成")
        print("=" * 40)
        print(f"检查的模块数: {self.optimization_results['modules_checked']}")
        print(f"修改的文件数: {self.optimization_results['files_modified']}")
        print(f"性能改进数: {self.optimization_results['performance_improvements']}")
        print(f"错误数: {self.optimization_results['errors']}")
        print(f"警告数: {self.optimization_results['warnings']}")
        print("=" * 40)
        print(f"详细日志已保存到: {log_file}")


def main():
    """主函数"""
    print("WireVsion项目优化工具")
    print("=" * 40)
    
    # 创建优化器
    optimizer = WireVsionOptimizer()
    
    # 运行所有优化
    optimizer.run_all_optimizations()
    
    return 0


if __name__ == "__main__":
    sys.exit(main()) 