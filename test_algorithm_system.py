#!/usr/bin/env python3
"""
WireVision 算法系统测试脚本

测试新重构的算法架构是否正常工作
"""

import sys
import numpy as np
import cv2
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_algorithm_registry():
    """测试算法注册表"""
    print("🔍 测试算法注册表...")
    
    try:
        from wirevsion.algorithms import algorithm_registry
        
        # 检查注册表状态
        stats = algorithm_registry.get_stats()
        print(f"✅ 算法注册表初始化成功")
        print(f"   - 总算法数: {stats['total_algorithms']}")
        print(f"   - 算法类别数: {stats['total_categories']}")
        
        # 显示各类别算法数量
        for category, count in stats['categories'].items():
            print(f"   - {category}: {count} 个算法")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法注册表测试失败: {e}")
        return False


def test_image_source_algorithms():
    """测试图像源算法"""
    print("\n🔍 测试图像源算法...")
    
    try:
        from wirevsion.algorithms import algorithm_registry
        from wirevsion.algorithms.base_algorithm import AlgorithmConfig
        
        # 测试文件源算法
        file_source = algorithm_registry.create_algorithm("image_source", "file")
        if file_source:
            # 创建测试图像
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            test_image[:, :] = [255, 0, 0]  # 红色图像
            
            # 保存临时测试图像
            test_path = "temp_test_image.jpg"
            cv2.imwrite(test_path, test_image)
            
            # 配置参数
            config = file_source.create_config({
                "file_path": test_path,
                "color_mode": "BGR"
            })
            
            # 执行算法
            result = file_source.execute({}, config)
            
            if result.success:
                print("✅ 文件源算法测试成功")
                print(f"   - 图像尺寸: {result.image.shape}")
                print(f"   - 执行时间: {result.execution_time:.3f}s")
            else:
                print(f"❌ 文件源算法执行失败: {result.message}")
                return False
            
            # 清理临时文件
            Path(test_path).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 图像源算法测试失败: {e}")
        return False


def test_image_processing_algorithms():
    """测试图像处理算法"""
    print("\n🔍 测试图像处理算法...")
    
    try:
        from wirevsion.algorithms import algorithm_registry
        from wirevsion.algorithms.base_algorithm import AlgorithmConfig
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
        
        # 测试高斯模糊算法
        gaussian_blur = algorithm_registry.create_algorithm("image_processing", "gaussian_blur")
        if gaussian_blur:
            config = gaussian_blur.create_config({
                "kernel_size_x": 15,
                "kernel_size_y": 15,
                "sigma_x": 2.0
            })
            
            result = gaussian_blur.execute({"image": test_image}, config)
            
            if result.success:
                print("✅ 高斯模糊算法测试成功")
                print(f"   - 处理后图像尺寸: {result.image.shape}")
                print(f"   - 执行时间: {result.execution_time:.3f}s")
            else:
                print(f"❌ 高斯模糊算法执行失败: {result.message}")
                return False
        
        # 测试阈值处理算法
        threshold = algorithm_registry.create_algorithm("image_processing", "threshold")
        if threshold:
            config = threshold.create_config({
                "threshold_value": 127,
                "threshold_type": "binary"
            })
            
            result = threshold.execute({"image": test_image}, config)
            
            if result.success:
                print("✅ 阈值处理算法测试成功")
                print(f"   - 二值化图像尺寸: {result.image.shape}")
                print(f"   - 执行时间: {result.execution_time:.3f}s")
            else:
                print(f"❌ 阈值处理算法执行失败: {result.message}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 图像处理算法测试失败: {e}")
        return False


def test_feature_detection_algorithms():
    """测试特征检测算法"""
    print("\n🔍 测试特征检测算法...")
    
    try:
        from wirevsion.algorithms import algorithm_registry
        from wirevsion.algorithms.base_algorithm import AlgorithmConfig
        
        # 创建包含轮廓的测试图像
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        cv2.rectangle(test_image, (50, 50), (150, 150), (255, 255, 255), -1)
        cv2.circle(test_image, (100, 100), 30, (0, 0, 0), -1)
        
        # 测试轮廓检测算法
        contour_detection = algorithm_registry.create_algorithm("feature_detection", "contour_detection")
        if contour_detection:
            config = contour_detection.create_config({
                "threshold_method": "binary",
                "threshold_value": 127,
                "min_area": 100
            })
            
            result = contour_detection.execute({"image": test_image}, config)
            
            if result.success:
                print("✅ 轮廓检测算法测试成功")
                print(f"   - 检测到轮廓数: {result.data.get('contour_count', 0)}")
                print(f"   - 执行时间: {result.execution_time:.3f}s")
            else:
                print(f"❌ 轮廓检测算法执行失败: {result.message}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 特征检测算法测试失败: {e}")
        return False


def test_workflow_engine():
    """测试工作流引擎"""
    print("\n🔍 测试工作流引擎...")
    
    try:
        # 测试工作流引擎的核心功能，不依赖PyQt5
        from wirevsion.algorithms import algorithm_registry
        
        # 创建简单的工作流配置
        workflow_config = {
            "nodes": [
                {
                    "node_id": "source_1",
                    "category": "image_source", 
                    "algorithm": "file",
                    "metadata": {"display_name": "文件源"},
                    "parameters": {"file_path": "test_image.jpg"}
                }
            ],
            "connections": []
        }
        
        # 测试算法实例化
        file_source = algorithm_registry.create_algorithm("image_source", "file")
        if file_source:
            print(f"✅ 工作流引擎核心功能测试成功")
            print(f"   - 算法实例化: 成功")
            print(f"   - 节点数: {len(workflow_config['nodes'])}")
            print(f"   - 连接数: {len(workflow_config['connections'])}")
        else:
            print(f"❌ 算法实例化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流引擎测试失败: {e}")
        return False


def test_algorithm_connections():
    """测试算法连接点"""
    print("\n🔍 测试算法连接点...")
    
    try:
        from wirevsion.algorithms import algorithm_registry
        
        # 测试算法的输入输出连接点
        gaussian_blur = algorithm_registry.create_algorithm("image_processing", "gaussian_blur")
        if gaussian_blur:
            input_connections = gaussian_blur.get_input_connections()
            output_connections = gaussian_blur.get_output_connections()
            
            print(f"✅ 算法连接点测试成功")
            print(f"   - 输入连接点数: {len(input_connections)}")
            print(f"   - 输出连接点数: {len(output_connections)}")
            
            for conn in input_connections:
                print(f"     输入: {conn.name} ({conn.data_type.value})")
            
            for conn in output_connections:
                print(f"     输出: {conn.name} ({conn.data_type.value})")
        
        return True
        
    except Exception as e:
        print(f"❌ 算法连接点测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 WireVision 算法系统测试开始")
    print("=" * 50)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_algorithm_registry())
    test_results.append(test_image_source_algorithms())
    test_results.append(test_image_processing_algorithms())
    test_results.append(test_feature_detection_algorithms())
    test_results.append(test_workflow_engine())
    test_results.append(test_algorithm_connections())
    
    # 统计测试结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果统计:")
    print(f"   - 通过: {passed}/{total}")
    print(f"   - 成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！算法系统工作正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 