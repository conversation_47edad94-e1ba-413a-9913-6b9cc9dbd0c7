#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试节点交互功能
- 节点拖动
- 端口连接
- 画布平移
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtCore import QPointF
from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
from loguru import logger

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("节点交互测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建工作流编辑器
        self.workflow_editor = ModernWorkflowEditor()
        layout.addWidget(self.workflow_editor)
        
        # 创建测试按钮
        button_layout = QHBoxLayout()
        
        # 添加测试节点按钮
        add_nodes_btn = QPushButton("添加测试节点")
        add_nodes_btn.clicked.connect(self.add_test_nodes)
        button_layout.addWidget(add_nodes_btn)
        
        # 添加连接按钮
        add_connection_btn = QPushButton("添加测试连接")
        add_connection_btn.clicked.connect(self.add_test_connection)
        button_layout.addWidget(add_connection_btn)
        
        # 清空画布按钮
        clear_btn = QPushButton("清空画布")
        clear_btn.clicked.connect(self.clear_canvas)
        button_layout.addWidget(clear_btn)
        
        layout.addLayout(button_layout)
        
        # 添加一些初始节点
        self.add_test_nodes()
        
    def add_test_nodes(self):
        """添加测试节点"""
        canvas = self.workflow_editor.canvas
        
        # 添加输入节点
        node1 = canvas.add_node("camera_1", "input", "相机输入", QPointF(-200, 0))
        
        # 添加处理节点
        node2 = canvas.add_node("blur_1", "process", "高斯模糊", QPointF(0, 0))
        
        # 添加输出节点
        node3 = canvas.add_node("output_1", "output", "结果输出", QPointF(200, 0))
        
        logger.info("已添加3个测试节点")
        
    def add_test_connection(self):
        """添加测试连接"""
        canvas = self.workflow_editor.canvas
        
        # 检查是否有足够的节点
        if len(canvas.nodes) >= 2:
            node_ids = list(canvas.nodes.keys())
            # 连接前两个节点
            canvas.create_connection(node_ids[0], node_ids[1], "right", "left")
            logger.info(f"已创建连接: {node_ids[0]} -> {node_ids[1]}")
            
            # 如果有第三个节点，也连接上
            if len(canvas.nodes) >= 3:
                canvas.create_connection(node_ids[1], node_ids[2], "right", "left")
                logger.info(f"已创建连接: {node_ids[1]} -> {node_ids[2]}")
        else:
            logger.warning("节点数量不足，无法创建连接")
            
    def clear_canvas(self):
        """清空画布"""
        canvas = self.workflow_editor.canvas
        
        # 移除所有节点（这会自动移除连接）
        node_ids = list(canvas.nodes.keys())
        for node_id in node_ids:
            canvas.remove_node(node_id)
            
        logger.info("画布已清空")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示窗口
    window = TestWindow()
    window.show()
    
    # 打印使用说明
    print("\n=== 节点交互测试 ===")
    print("操作说明：")
    print("1. 左键点击节点可以拖动节点")
    print("2. 左键点击端口（节点上的白色圆圈）可以拖出连接线")
    print("3. 中键或右键拖动可以平移画布")
    print("4. 滚轮可以缩放画布")
    print("5. 左键在空白区域拖动可以框选节点")
    print("\n测试重点：")
    print("- 验证节点是否可以正常拖动")
    print("- 验证端口是否可以拖出连接线")
    print("- 验证连接线是否跟随节点移动")
    print("====================\n")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 