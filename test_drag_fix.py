#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试节点拖动修复
验证节点不会跳动
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import QPointF, Qt
from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor
from loguru import logger

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("节点拖动测试 - 验证修复")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("""
        <h3>节点拖动测试</h3>
        <p>测试重点：</p>
        <ul>
            <li>点击节点中心拖动 - 节点应该平滑移动，不应该跳动</li>
            <li>点击节点边缘拖动 - 节点应该保持相对位置</li>
            <li>点击端口（白色圆圈）- 应该开始连接线拖拽</li>
            <li>中键/右键拖动 - 平移画布</li>
        </ul>
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 10px; }")
        layout.addWidget(info_label)
        
        # 创建工作流编辑器
        self.workflow_editor = ModernWorkflowEditor()
        layout.addWidget(self.workflow_editor)
        
        # 添加测试节点
        self.add_test_nodes()
        
    def add_test_nodes(self):
        """添加测试节点"""
        canvas = self.workflow_editor.canvas
        
        # 添加多个节点以测试
        positions = [
            ("input_1", "input", "相机输入 1", QPointF(-300, -100)),
            ("input_2", "input", "图像输入 2", QPointF(-300, 100)),
            ("process_1", "process", "高斯模糊", QPointF(0, -100)),
            ("process_2", "process", "边缘检测", QPointF(0, 100)),
            ("output_1", "output", "结果输出 1", QPointF(300, -100)),
            ("output_2", "output", "图像输出 2", QPointF(300, 100)),
        ]
        
        for node_id, node_type, title, pos in positions:
            canvas.add_node(node_id, node_type, title, pos)
            
        # 添加一些连接
        canvas.create_connection("input_1", "process_1", "right", "left")
        canvas.create_connection("process_1", "output_1", "right", "left")
        canvas.create_connection("input_2", "process_2", "right", "left")
        canvas.create_connection("process_2", "output_2", "right", "left")
        
        logger.info("已添加测试节点和连接")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 创建并显示窗口
    window = TestWindow()
    window.show()
    
    # 打印测试说明
    print("\n=== 节点拖动修复测试 ===")
    print("问题描述：点击节点时，节点会跳动到鼠标位置")
    print("修复内容：")
    print("1. 添加了 shape() 方法定义节点形状")
    print("2. 调整了 boundingRect() 添加边距")
    print("3. 修复了画布的拖动模式设置")
    print("\n请测试以下操作：")
    print("- 点击节点的不同位置并拖动")
    print("- 验证节点是否平滑移动，没有跳动")
    print("- 验证连接线是否正确跟随")
    print("========================\n")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 