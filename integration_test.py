#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
整合测试脚本

用于验证系统所有关键组件是否能正常工作
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QMessageBox
from PyQt6.QtCore import Qt, QPointF
from loguru import logger

# 配置日志
logger.remove()  # 移除默认处理程序
logger.add(sys.stderr, level="INFO")  # 添加标准错误输出处理程序
logger.add("integration_test.log", level="DEBUG")  # 添加文件日志

def run_integration_test():
    """运行整合测试"""
    logger.info("开始运行整合测试")
    
    # 测试导入模块
    try:
        logger.info("测试导入核心模块...")
        from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor, ModernFlowNode
        from wirevsion.ui.camera_utils import CameraManager, ImageDisplayManager
        from wirevsion.ui.node_config_dialog import NodeConfigDialog
        logger.info("核心模块导入成功")
    except ImportError as e:
        logger.error(f"导入模块失败: {e}")
        return False
    
    # 测试相机功能
    try:
        logger.info("测试相机功能...")
        camera_manager = CameraManager()
        if camera_manager.init_camera():
            frame = camera_manager.get_frame()
            if frame is not None:
                logger.info(f"相机初始化成功，获取到帧: 形状={frame.shape}")
                camera_manager.release()
            else:
                logger.warning("相机初始化成功但无法获取帧")
        else:
            logger.warning("相机初始化失败，这可能是由于没有可用的相机设备")
    except Exception as e:
        logger.error(f"测试相机功能时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    # 测试节点配置对话框
    try:
        logger.info("测试节点配置对话框...")
        # 创建一个临时的QApplication
        app = QApplication.instance() or QApplication(sys.argv)
        
        # 创建一个测试节点
        test_node = ModernFlowNode("test_node", "input", "测试节点", QPointF(0, 0))
        
        # 创建一个主窗口作为父窗口
        main_window = QMainWindow()
        
        # 创建节点配置对话框
        dialog = NodeConfigDialog(test_node, main_window)
        logger.info("节点配置对话框创建成功")
        
        # 验证关键方法存在
        methods_to_check = [
            '_toggle_camera_preview', 
            '_reset_params', 
            '_apply_algorithm',
            '_setup_algorithm_tab',
            '_lighten_color',
            '_darken_color'
        ]
        
        for method_name in methods_to_check:
            if hasattr(dialog, method_name) and callable(getattr(dialog, method_name)):
                logger.info(f"方法 {method_name} 存在并可调用")
            else:
                logger.error(f"方法 {method_name} 不存在或不可调用")
                return False
    except Exception as e:
        logger.error(f"测试节点配置对话框时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    
    logger.info("整合测试完成，所有关键组件工作正常")
    return True

if __name__ == "__main__":
    success = run_integration_test()
    if success:
        print("整合测试通过！系统所有关键组件正常工作。")
        sys.exit(0)
    else:
        print("整合测试失败！请查看日志了解详情。")
        sys.exit(1) 