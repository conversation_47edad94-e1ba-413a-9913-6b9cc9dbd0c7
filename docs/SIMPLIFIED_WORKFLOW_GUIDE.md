# 简化工作流配置指南

## 🎯 设计理念

WireVision采用了**简化优先**的配置理念：

- **可视化为主**：主要配置通过拖拽式可视化编辑器完成
- **弹窗为辅**：详细参数配置通过弹窗对话框进行
- **专注配置**：去除界面冗余，专注于工作流设计

## 🖥️ 界面结构

### 主配置界面
```
┌─────────────────────────────────────────────────────┐
│ 配置模式                                             │
├─────────────────┬───────────────────────────────────┤
│ 左侧配置面板     │ 右侧图像显示和结果                   │
│                │                                   │
│ ┌─ 工作流信息 ─┐  │ ┌─ 图像显示 ──────────────────┐   │
│ │ 名称/描述   │  │ │ 实时图像预览               │   │
│ │ 新建/保存   │  │ │ 缩放控制                   │   │
│ └────────────┘  │ │ ROI交互选择                │   │
│                │ └─────────────────────────────┘   │
│ ┌─ 配置选项卡 ─┐  │                                   │
│ │ 相机 | 可视化流程│ ┌─ 检测结果 ──────────────────┐   │
│ └────────────┘  │ │ 统一结果显示                │   │
│                │ │ 实时检测反馈                │   │
│ ┌─ 快速操作 ──┐  │ └─────────────────────────────┘   │
│ │ 捕获/测试   │  │                                   │
│ └────────────┘  │                                   │
└─────────────────┴───────────────────────────────────┘
```

### 可视化流程编辑器
```
┌─────────────────────────────────────────────────────┐
│ 工具栏：图像源 | 模板匹配 | 位置修正 | ROI区域 | 删除 │
├─────────────────────────────────────────────────────┤
│                                                     │
│          可缩放画布区域 (2000x2000)                   │
│                                                     │
│     ┌─────┐    ┌─────┐    ┌─────┐                   │
│     │图像源│───▶│模板  │───▶│ROI  │                   │
│     └─────┘    │匹配  │    │检测  │                   │
│                └─────┘    └─────┘                   │
│                                                     │
├─────────────────────────────────────────────────────┤
│ 状态栏：当前操作提示和指导信息                         │
└─────────────────────────────────────────────────────┘
```

## 🔧 操作流程

### 1. 基础设置
1. **启动应用** → 自动进入配置模式
2. **配置相机** → 点击"相机"选项卡，选择设备
3. **切换到可视化流程** → 点击"可视化流程"选项卡

### 2. 工作流设计
1. **添加模块**
   - 点击工具栏按钮添加不同类型的模块
   - 每个模块代表一个处理步骤

2. **排列模块**
   - 拖拽模块到合适位置
   - 按照数据流方向排列

3. **建立连接**
   - 点击模块显示连接点
   - 拖拽橙色输出点到蓝色输入点
   - 建立数据流连接

### 3. 模块配置
1. **双击模块** → 弹出配置对话框
2. **配置参数** → 在弹窗中设置详细参数
3. **确认保存** → 点击确定保存配置

### 4. 测试验证
1. **捕获图像** → 使用快速操作按钮
2. **测试模板** → 验证模板匹配效果
3. **测试工作流** → 执行完整检测流程

## 📦 模块配置详解

### 🖼️ 图像源模块
- **功能**: 提供图像输入
- **配置**: 弹窗显示相机配置提示
- **建议**: 主要配置在相机选项卡中完成

### 🎯 模板匹配模块  
- **功能**: 基于模板的目标检测
- **配置**: 完整的模板配置界面
  - 模板图像加载
  - 匹配方法选择
  - 阈值参数调整
  - 多尺度匹配设置

### 🎨 ROI区域模块
- **功能**: 区域内颜色或特征检测  
- **配置**: 完整的ROI配置界面
  - ROI区域定义
  - 颜色空间选择
  - 检测参数设置
  - 形态学处理

### 📍 位置修正模块
- **功能**: 坐标系校正和变换
- **配置**: 开发中（显示提示信息）
- **计划**: 将来提供校正参数配置

## 🎮 操作技巧

### 高效配置
1. **先设计后配置**: 先搭建整体流程，再逐个配置模块
2. **分步测试**: 配置一个模块就测试一个模块
3. **保存习惯**: 及时保存工作流配置

### 界面导航
- **快速切换**: 使用选项卡在相机配置和流程设计间切换
- **专注模式**: 可视化编辑器占据主要界面空间
- **弹窗配置**: 双击模块进入专注配置模式

### 连接技巧
- **连接提示**: 悬停连接点查看类型和说明
- **自动连接**: 悬停500ms自动建立连接
- **连接规则**: 橙色→蓝色，输出→输入

## 🔄 配置同步

### 数据流向
```
相机配置 ─┐
         ├─→ 工作流编辑器 ─→ 模块弹窗配置 ─→ 保存到工作流
可视化设计 ─┘
```

### 配置保存
- **自动保存**: 工作流结构变化时自动保存
- **手动保存**: 使用保存按钮保存当前配置
- **配置同步**: 弹窗配置自动同步到工作流

## 💡 最佳实践

### 工作流设计
1. **从左到右**: 按照数据处理顺序排列模块
2. **清晰命名**: 给工作流和模块使用有意义的名称
3. **模块化**: 将复杂流程分解为简单模块

### 配置管理
1. **版本控制**: 定期保存不同版本的工作流
2. **备份习惯**: 重要配置及时备份
3. **文档记录**: 记录配置参数的含义和用途

### 测试策略
1. **单元测试**: 逐个测试每个模块
2. **集成测试**: 测试完整工作流
3. **边界测试**: 测试极端情况和边界条件

## 🚀 未来发展

### 即将实现
- [ ] 工作流模板库
- [ ] 配置参数验证
- [ ] 实时预览功能
- [ ] 批量配置功能

### 长期规划
- [ ] 自定义模块插件
- [ ] 分布式工作流
- [ ] 云端配置同步
- [ ] AI辅助配置

---

*简化配置，专注创造 - WireVision可视化工作流系统* 🎨 