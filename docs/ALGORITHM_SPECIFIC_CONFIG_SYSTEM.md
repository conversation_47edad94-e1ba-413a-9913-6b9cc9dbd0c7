# WireVision 算法专用配置系统

## 概述

为WireVision项目创建了一个完整的算法专用配置系统，为每个算法模块提供定制化的配置界面，支持上级结果输入选择和实时参数验证。

## 🎯 主要特性

### 1. 算法专用配置界面
- **相机源配置**: 相机索引、分辨率、帧率、自动曝光等参数
- **边缘检测配置**: Canny参数、预处理选项、输入源选择
- **高斯模糊配置**: 核大小、Sigma值、输入源选择
- **模板匹配配置**: 模板文件、匹配方法、阈值、多尺度匹配
- **轮廓检测配置**: 阈值方法、检索模式、过滤参数

### 2. 上级结果输入选择
- 自动检测工作流中的连接关系
- 动态显示可用的输入源
- 支持多种数据类型（图像、特征点、轮廓等）
- 实时更新输入源列表

### 3. 智能算法匹配
- 根据节点类型自动选择对应算法
- 节点标题到算法的智能映射
- 支持算法类别分组显示
- 自动加载专用配置界面

## 📁 文件结构

```
wirevsion/ui/
├── algorithm_config_widgets.py     # 算法专用配置界面组件
├── modern_node_config_dialog.py    # 现代化节点配置对话框（已更新）
└── modern_workflow_editor.py       # 工作流编辑器（已集成）

test_algorithm_config_widgets.py    # 算法配置界面测试脚本
docs/
└── ALGORITHM_SPECIFIC_CONFIG_SYSTEM.md  # 本文档
```

## 🔧 核心组件

### 1. BaseAlgorithmConfigWidget
算法配置界面基类，提供：
- 参数管理和验证
- 输入源设置
- 信号连接机制
- UI更新接口

### 2. 专用配置界面类
- `CameraSourceConfigWidget`: 相机源配置
- `EdgeDetectionConfigWidget`: 边缘检测配置
- `GaussianBlurConfigWidget`: 高斯模糊配置
- `TemplateMatchingConfigWidget`: 模板匹配配置
- `ContourDetectionConfigWidget`: 轮廓检测配置

### 3. AlgorithmConfigWidgetFactory
算法配置界面工厂类：
- 根据算法名称创建对应配置界面
- 支持动态注册新的配置界面
- 提供通用配置界面作为后备

### 4. GenericAlgorithmConfigWidget
通用算法配置界面：
- 自动从算法注册表加载参数模式
- 动态生成参数控件
- 支持多种参数类型

## 🎨 界面特性

### 现代化设计
- 统一的暗色主题
- 响应式布局设计
- 现代化控件样式
- 流畅的交互动画

### 用户体验
- 直观的参数分组
- 实时参数验证
- 预览功能支持
- 错误提示和帮助

### 扩展性
- 易于添加新算法配置界面
- 模块化组件设计
- 灵活的参数系统
- 可配置的UI样式

## 🔄 工作流程

### 1. 节点配置流程
```
用户双击节点 → 识别节点类型 → 自动选择算法 → 加载专用配置界面 → 设置输入源 → 配置参数 → 保存配置
```

### 2. 算法匹配逻辑
```
节点标题精确匹配 → 节点类型匹配 → 显示所有算法 → 用户手动选择
```

### 3. 输入源检测
```
扫描工作流连接 → 识别上级节点 → 获取输出类型 → 更新输入选择器
```

## 📊 支持的算法类型

### 图像源 (input)
- 相机输入 (`image_source.camera`)
- 文件输入 (`image_source.file`)
- 视频输入 (`image_source.video`)
- 网络输入 (`image_source.network`)

### 图像处理 (processing)
- Canny边缘检测 (`image_processing.edge_detection`)
- 高斯模糊 (`image_processing.gaussian_blur`)
- 中值滤波 (`image_processing.median_blur`)
- 双边滤波 (`image_processing.bilateral_filter`)
- 形态学操作 (`image_processing.morphology`)
- 阈值处理 (`image_processing.threshold`)

### 特征检测 (detection)
- 模板匹配 (`feature_detection.template_matching`)
- 角点检测 (`feature_detection.corner_detection`)
- 轮廓检测 (`feature_detection.contour_detection`)
- 直线检测 (`feature_detection.line_detection`)
- 圆形检测 (`feature_detection.circle_detection`)

### 目标检测 (object_detection)
- 颜色检测 (`object_detection.color_detection`)
- 形状检测 (`object_detection.shape_detection`)
- 文本检测 (`object_detection.text_detection`)
- 人脸检测 (`object_detection.face_detection`)

### 测量分析 (measurement)
- 距离测量 (`measurement.distance_measurement`)
- 角度测量 (`measurement.angle_measurement`)
- 面积测量 (`measurement.area_measurement`)
- 几何分析 (`measurement.geometry_analysis`)

### 深度学习 (deep_learning)
- YOLO检测 (`deep_learning.yolo_detection`)
- 图像分类 (`deep_learning.classification`)
- 语义分割 (`deep_learning.segmentation`)
- 姿态估计 (`deep_learning.pose_estimation`)

## 🚀 使用方法

### 1. 基本使用
```python
# 在工作流编辑器中双击节点
# 系统会自动：
# 1. 识别节点类型
# 2. 选择对应算法
# 3. 加载专用配置界面
# 4. 设置可用输入源
```

### 2. 添加新算法配置界面
```python
# 1. 创建新的配置界面类
class NewAlgorithmConfigWidget(BaseAlgorithmConfigWidget):
    def setup_ui(self):
        # 实现UI设置
        pass
    
    def setup_connections(self):
        # 实现信号连接
        pass

# 2. 注册到工厂类
AlgorithmConfigWidgetFactory.register_widget(
    "new_algorithm", 
    NewAlgorithmConfigWidget
)
```

### 3. 测试配置界面
```bash
# 运行测试脚本
python test_algorithm_config_widgets.py

# 在WireVision中测试
python main.py
# 1. 点击"工作流编辑"
# 2. 拖拽节点到画布
# 3. 双击节点打开配置对话框
```

## 🔧 技术实现

### 参数管理
- 使用字典存储参数配置
- 支持参数验证和类型转换
- 实时参数更新和同步

### 信号机制
- `parameter_changed`: 参数改变信号
- `preview_requested`: 预览请求信号
- 支持自定义信号扩展

### 错误处理
- 优雅降级机制
- 详细错误日志记录
- 用户友好的错误提示

## 📈 性能优化

### 延迟加载
- 按需创建配置界面
- 动态加载算法参数
- 减少内存占用

### 缓存机制
- 缓存算法参数模式
- 复用配置界面实例
- 优化界面切换速度

## 🔮 未来扩展

### 计划功能
1. **参数模板系统**: 保存和加载常用参数配置
2. **批量配置**: 同时配置多个相同类型节点
3. **参数验证增强**: 更智能的参数约束和建议
4. **可视化预览**: 实时显示算法处理结果
5. **参数优化**: 自动参数调优功能

### 扩展方向
1. **更多算法支持**: 添加更多专用配置界面
2. **自定义算法**: 支持用户自定义算法配置
3. **云端配置**: 支持云端参数同步
4. **AI辅助**: 智能参数推荐系统

## 📝 总结

算法专用配置系统为WireVision提供了：
- ✅ 每个算法的专用配置界面
- ✅ 智能的上级结果输入选择
- ✅ 现代化的用户界面设计
- ✅ 灵活的扩展机制
- ✅ 完整的测试和文档

这个系统大大提升了用户配置算法参数的体验，使得复杂的机器视觉工作流配置变得简单直观。
