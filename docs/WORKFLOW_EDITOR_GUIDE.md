# 可视化工作流编辑器使用指南

## 🎯 概述

WireVision的可视化工作流编辑器是一个强大的画布式流程设计工具，允许用户通过拖拽的方式创建和编辑机器视觉检测工作流。

## 🚀 启动编辑器

### 方式1：主应用中使用
1. 启动WireVision主应用
2. 切换到"配置模式"
3. 点击"可视化流程"选项卡
4. 编辑器会自动加载（延迟加载机制）

### 方式2：独立测试
```bash
# 运行独立测试程序
python test_workflow_editor.py
```

## 🎨 界面布局

### 工具栏
- **图像源**：添加图像输入模块
- **模板匹配**：添加模板匹配检测模块
- **位置修正**：添加位置校正模块  
- **ROI区域**：添加区域检测模块
- **删除**：删除选中的模块或连接
- **清空**：清空整个工作流

### 画布区域
- 可缩放的2D工作区（2000x2000像素）
- 支持鼠标滚轮缩放
- 支持拖拽平移

### 状态栏
- 显示当前操作状态和提示信息

## 📦 模块操作

### 添加模块
1. 点击工具栏对应按钮
2. 模块会在画布中心创建
3. 每个模块都有唯一ID和类型标识

### 移动模块
1. 点击选中模块
2. 拖拽到目标位置
3. 释放鼠标完成移动

### 删除模块
1. 点击选中模块
2. 按`Delete`键删除
3. 或使用工具栏"删除"按钮

### 模块类型

#### 🖼️ 图像源 (image_source)
- **功能**：提供图像输入
- **输出**：原始图像
- **配置**：相机参数、图像路径

#### 🎯 模板匹配 (template_matching)  
- **功能**：基于模板的目标检测
- **输入**：待检测图像
- **输出**：匹配结果、位置信息
- **配置**：模板图像、匹配参数

#### 📍 位置修正 (position_correction)
- **功能**：坐标系校正和变换
- **输入**：检测结果
- **输出**：校正后的位置
- **配置**：校正参数、变换矩阵

#### 🎨 ROI区域 (roi)
- **功能**：区域内颜色或特征检测
- **输入**：图像和位置信息
- **输出**：检测结果
- **配置**：ROI范围、检测参数

## 🔗 连接操作

### 创建连接
1. **点击模块**显示连接点（蓝色=输入，橙色=输出）
2. **拖拽连接点**到目标模块的连接点
3. **释放鼠标**完成连接创建

### 连接规则
- 输出点（橙色）只能连接到输入点（蓝色）
- 每个输入点只能接受一个连接
- 输出点可以连接到多个输入点
- 不能创建循环连接

### 连接反馈
- **悬停高亮**：目标连接点会高亮显示
- **自动连接**：悬停500ms后自动建立连接
- **视觉反馈**：连接成功后会有高亮提示

### 删除连接
1. 点击选中连接线
2. 按`Delete`键删除
3. 或删除相关模块会自动删除连接

## ⚙️ 模块配置

### 弹窗配置（新版本）
1. **双击模块**触发配置弹窗
2. 弹窗中显示对应模块类型的配置界面：
   - `image_source` → 相机配置提示（主配置在相机选项卡）
   - `template_matching` → 完整的模板配置界面
   - `roi` → 完整的ROI配置界面
   - `position_correction` → 开发中提示界面
3. **点击确定**保存配置更改
4. **点击取消**放弃配置更改

### 配置界面特点
- **模态对话框**：配置时阻止其他操作，确保数据安全
- **实时预览**：在配置界面中可以实时查看效果
- **参数验证**：自动验证输入参数的有效性
- **独立实例**：每次打开都是新的配置实例，避免冲突

### 简化流程优势
- **专注性**：一次只配置一个模块，避免干扰
- **直观性**：双击即配置，操作更直观
- **安全性**：模态对话框防止意外操作
- **清晰性**：界面简洁，只显示必要的配置选项

## 🎮 交互技巧

### 鼠标操作
- **左键单击**：选择模块或连接
- **左键拖拽**：移动模块
- **Ctrl+左键拖拽**：在连接点上开始连接
- **鼠标滚轮**：缩放画布
- **中键拖拽**：平移画布

### 键盘快捷键
- **Delete**：删除选中项
- **Ctrl+A**：全选（暂未实现）
- **Ctrl+Z**：撤销（暂未实现）
- **Ctrl+S**：保存工作流（暂未实现）

### 状态提示
- 工具栏状态标签会显示当前操作提示
- 连接操作时会有详细的指导信息

## 💾 工作流管理

### 保存工作流
```python
# 当前通过信号机制自动保存
workflow_editor.workflow_changed.connect(save_handler)
```

### 加载工作流
```python
# 通过WorkflowConfig对象加载
workflow_editor.load_workflow(workflow_config)
```

## 🔧 扩展开发

### 添加新模块类型
1. 在`ModuleItem`构造函数中添加新类型
2. 在工具栏添加对应按钮
3. 实现对应的配置界面

```python
# 在WorkflowEditorWidget中添加
self.add_custom_btn = QToolButton()
self.add_custom_btn.setText("自定义模块")
self.add_custom_btn.clicked.connect(
    lambda: self._add_module("custom_type", "自定义模块")
)
```

### 自定义连接逻辑
```python
# 重写ConnectionPoint的连接验证
def can_connect_to(self, target_point):
    # 实现自定义连接规则
    return True
```

## 🐛 故障排除

### 常见问题

#### 1. 编辑器加载失败
- **原因**：Qt组件初始化时序问题
- **解决**：使用延迟加载机制，在选项卡激活时创建

#### 2. 连接无法创建
- **原因**：连接点类型不匹配或已存在连接
- **解决**：检查连接规则，删除冲突连接

#### 3. 模块拖拽卡顿
- **原因**：画布过大或模块过多
- **解决**：优化渲染性能，减少模块数量

### 性能优化
- 避免创建过多模块（推荐<50个）
- 定期清理不需要的连接
- 使用合适的画布缩放级别

## 📈 未来功能

### 计划中的功能
- [ ] 撤销/重做功能
- [ ] 工作流模板库
- [ ] 模块分组功能
- [ ] 数据流可视化
- [ ] 性能分析工具
- [ ] 导出为图片
- [ ] 打印支持

### 扩展计划
- [ ] 自定义模块插件系统
- [ ] 脚本化模块创建
- [ ] 工作流版本控制
- [ ] 协同编辑功能

---

*WireVision可视化工作流编辑器 - 让流程设计变得直观高效* 🎨 