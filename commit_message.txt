修复: 节点配置对话框和相机显示功能

主要修复:
1. 修复节点配置对话框缺少_toggle_camera_preview方法的问题
2. 添加_lighten_color和_darken_color辅助方法，修复按钮样式问题
3. 简化_setup_algorithm_tab实现，提高稳定性和可靠性
4. 修复相机显示功能中的色域转换问题
5. 减少不必要的日志输出，优化性能

优化:
1. 改进ImageDisplayManager的_apply_current_zoom方法，降低日志输出频率
2. 添加缓存机制减少重复计算
3. 创建整合测试脚本验证系统关键组件功能

相关文件:
- wirevsion/ui/node_config_dialog.py
- wirevsion/ui/camera_utils.py
- test_node_config.py
- integration_test.py
- UI_FIXES_SUMMARY.md 