#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
节点配置对话框测试程序

用于测试节点配置对话框中的相机预览功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt, QPointF
from loguru import logger

# 配置日志
logger.remove()  # 移除默认处理程序
logger.add(sys.stderr, level="INFO")  # 添加标准错误输出处理程序
logger.add("node_config_test.log", level="DEBUG")  # 添加文件日志

# 导入必要的类
try:
    from wirevsion.ui.modern_workflow_editor import ModernFlowNode
    from wirevsion.ui.node_config_dialog import NodeConfigDialog
except ImportError:
    logger.error("无法导入wirevsion模块，请确保已正确安装")
    sys.exit(1)

class TestNodeConfigApp(QMainWindow):
    """测试节点配置对话框的应用程序"""
    
    def __init__(self):
        """初始化测试应用程序"""
        super().__init__()
        
        self.setWindowTitle("节点配置对话框测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        self.test_camera_button = QPushButton("测试相机输入节点配置")
        self.test_camera_button.clicked.connect(self.test_camera_config)
        layout.addWidget(self.test_camera_button)
        
        self.test_gaussian_button = QPushButton("测试高斯模糊节点配置")
        self.test_gaussian_button.clicked.connect(self.test_gaussian_config)
        layout.addWidget(self.test_gaussian_button)
        
        self.test_canny_button = QPushButton("测试Canny边缘节点配置")
        self.test_canny_button.clicked.connect(self.test_canny_config)
        layout.addWidget(self.test_canny_button)
        
        logger.info("节点配置对话框测试应用程序初始化完成")
    
    def test_camera_config(self):
        """测试相机输入节点配置对话框"""
        logger.info("测试相机输入节点配置对话框")
        
        # 创建相机输入节点
        node = ModernFlowNode("camera_input_1", "input", "相机输入", QPointF(0, 0))
        
        # 打开配置对话框
        dialog = NodeConfigDialog(node, self)
        result = dialog.exec_()
        
        if result:
            logger.info("配置已保存")
        else:
            logger.info("配置已取消")
    
    def test_gaussian_config(self):
        """测试高斯模糊节点配置对话框"""
        logger.info("测试高斯模糊节点配置对话框")
        
        # 创建高斯模糊节点
        node = ModernFlowNode("gaussian_blur_1", "process", "高斯模糊", QPointF(0, 0))
        
        # 打开配置对话框
        dialog = NodeConfigDialog(node, self)
        result = dialog.exec_()
        
        if result:
            logger.info("配置已保存")
        else:
            logger.info("配置已取消")
    
    def test_canny_config(self):
        """测试Canny边缘节点配置对话框"""
        logger.info("测试Canny边缘节点配置对话框")
        
        # 创建Canny边缘节点
        node = ModernFlowNode("canny_edge_1", "process", "Canny边缘", QPointF(0, 0))
        
        # 打开配置对话框
        dialog = NodeConfigDialog(node, self)
        result = dialog.exec_()
        
        if result:
            logger.info("配置已保存")
        else:
            logger.info("配置已取消")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = TestNodeConfigApp()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 