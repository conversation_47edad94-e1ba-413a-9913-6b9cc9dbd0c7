#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
算法切换功能测试脚本

测试所有算法配置界面是否能正常加载，验证：
1. 所有算法都有对应的配置界面
2. 算法切换不会产生错误
3. 主题颜色正确应用
4. 配置界面功能正常
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from loguru import logger

from wirevsion.algorithms.registry import AlgorithmRegistry
from wirevsion.ui.algorithm_config_widgets import AlgorithmConfigWidgetFactory
from wirevsion.ui.modern_components import THEME_COLORS

def test_theme_colors():
    """测试主题颜色完整性"""
    logger.info("🎨 测试主题颜色完整性...")

    required_colors = [
        'dark_bg_secondary',  # 这是之前缺失的颜色
        'dark_bg_primary',
        'dark_bg_input',
        'dark_border_primary',
        'dark_border_secondary',
        'text_primary',
        'text_secondary',
        'primary',
        'primary_hover',
        'success',
        'danger'
    ]

    missing_colors = []
    for color_key in required_colors:
        if color_key not in THEME_COLORS:
            missing_colors.append(color_key)

    if missing_colors:
        logger.error(f"❌ 缺失主题颜色: {missing_colors}")
        return False
    else:
        logger.success("✅ 所有必需的主题颜色都已定义")
        return True

def test_algorithm_registry():
    """测试算法注册表"""
    logger.info("📚 测试算法注册表...")

    registry = AlgorithmRegistry()
    algorithms = registry.get_all_algorithms()

    logger.info(f"注册的算法数量: {len(algorithms)}")

    # 按类别统计
    categories = {}
    for algo_name in algorithms.keys():
        category = algo_name.split('.')[0] if '.' in algo_name else 'other'
        categories[category] = categories.get(category, 0) + 1

    for category, count in categories.items():
        logger.info(f"  {category}: {count} 个算法")

    return len(algorithms) > 0

def test_algorithm_config_widgets():
    """测试算法配置界面工厂"""
    logger.info("🏭 测试算法配置界面工厂...")

    factory = AlgorithmConfigWidgetFactory()
    registry = AlgorithmRegistry()
    algorithms = registry.get_all_algorithms()

    success_count = 0
    error_count = 0

    # 获取所有具体的算法名称（包括完整路径）
    all_algorithm_names = []
    for category, algo_list in algorithms.items():
        for algo_info in algo_list:
            # 构建完整的算法名称
            full_name = f"{category}.{algo_info['name']}"
            all_algorithm_names.append(full_name)

    # 测试工厂支持的算法
    supported_algorithms = factory.get_supported_algorithms()
    logger.info(f"工厂支持的算法数量: {len(supported_algorithms)}")

    for algo_name in supported_algorithms[:10]:  # 只测试前10个，避免输出过多
        try:
            # 测试是否能创建配置界面
            widget_class = factory.get_widget_class(algo_name)
            if widget_class:
                logger.debug(f"✅ {algo_name} -> {widget_class.__name__}")
                success_count += 1
            else:
                logger.warning(f"⚠️  {algo_name} -> 无配置界面")
                error_count += 1
        except Exception as e:
            logger.error(f"❌ {algo_name} -> 错误: {e}")
            error_count += 1

    logger.info(f"配置界面测试结果: {success_count} 成功, {error_count} 失败")
    # 只要有成功的就算通过，因为工厂类设计为支持部分算法
    return success_count > 0

def test_widget_creation():
    """测试配置界面实际创建"""
    logger.info("🔧 测试配置界面实际创建...")

    factory = AlgorithmConfigWidgetFactory()
    test_algorithms = [
        'image_source.camera',
        'image_processing.gaussian_blur',
        'image_processing.edge_detection',
        'feature_detection.template_matching',
        'feature_detection.contour_detection'
    ]

    success_count = 0
    error_count = 0

    for algo_name in test_algorithms:
        try:
            widget = factory.create_config_widget(algo_name)
            if widget:
                logger.debug(f"✅ 成功创建 {algo_name} 配置界面")
                success_count += 1
                # 清理
                widget.deleteLater()
            else:
                logger.warning(f"⚠️  无法创建 {algo_name} 配置界面")
                error_count += 1
        except Exception as e:
            logger.error(f"❌ 创建 {algo_name} 配置界面失败: {e}")
            error_count += 1

    logger.info(f"界面创建测试结果: {success_count} 成功, {error_count} 失败")
    return error_count == 0

def run_all_tests():
    """运行所有测试"""
    logger.info("🚀 开始算法切换功能测试...")

    tests = [
        ("主题颜色测试", test_theme_colors),
        ("算法注册表测试", test_algorithm_registry),
        ("配置界面工厂测试", test_algorithm_config_widgets),
        ("界面创建测试", test_widget_creation)
    ]

    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")

        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.success(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))

    # 总结
    logger.info(f"\n{'='*50}")
    logger.info("📊 测试结果总结")
    logger.info(f"{'='*50}")

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")

    logger.info(f"\n总计: {passed}/{total} 测试通过")

    if passed == total:
        logger.success("🎉 所有测试都通过了！算法切换功能正常工作。")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败，需要修复。")
        return False

def main():
    """主函数"""
    # 创建QApplication（测试需要）
    app = QApplication(sys.argv)

    # 设置日志格式
    logger.remove()
    logger.add(sys.stdout, format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | {message}")

    # 运行测试
    success = run_all_tests()

    # 退出
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
