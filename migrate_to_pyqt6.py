#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PyQt5 到 PyQt6 迁移脚本

自动将项目中的 PyQt5 导入语句和API调用更新为 PyQt6
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
from loguru import logger

# 配置日志
logger.remove()
logger.add(sys.stdout, format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | {message}")

class PyQt6Migrator:
    """PyQt5 到 PyQt6 迁移器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.changes_made = []
        
        # PyQt5 到 PyQt6 的导入映射
        self.import_mappings = {
            # 基础模块
            'from PyQt6.QtCore import': 'from PyQt6.QtCore import',
            'from PyQt6.QtGui import': 'from PyQt6.QtGui import',
            'from PyQt6.QtWidgets import': 'from PyQt6.QtWidgets import',
            'from PyQt6.QtOpenGL import': 'from PyQt6.QtOpenGL import',
            'from PyQt6.QtNetwork import': 'from PyQt6.QtNetwork import',
            'from PyQt6.QtSql import': 'from PyQt6.QtSql import',
            'from PyQt6.QtTest import': 'from PyQt6.QtTest import',
            'from PyQt6.QtXml import': 'from PyQt6.QtXml import',
            'from PyQt6.QtPrintSupport import': 'from PyQt6.QtPrintSupport import',
            'from PyQt6.QtMultimedia import': 'from PyQt6.QtMultimedia import',
            'from PyQt6.QtMultimediaWidgets import': 'from PyQt6.QtMultimediaWidgets import',
            
            # 简单导入
            'import PyQt6.QtCore': 'import PyQt6.QtCore',
            'import PyQt6.QtGui': 'import PyQt6.QtGui',
            'import PyQt6.QtWidgets': 'import PyQt6.QtWidgets',
            'import PyQt6': 'import PyQt6',
            
            # 特殊情况
            'PyQt6.QtCore': 'PyQt6.QtCore',
            'PyQt6.QtGui': 'PyQt6.QtGui',
            'PyQt6.QtWidgets': 'PyQt6.QtWidgets',
        }
        
        # API 变更映射
        self.api_mappings = {
            # QApplication
            'QApplication.primaryScreen()': 'QApplication.primaryScreen()',
            'QScreen': 'QScreen',
            
            # QHeaderView
            'QHeaderView.setSectionResizeMode': 'QHeaderView.setSectionResizeMode',
            'QHeaderView.ResizeMode.ResizeToContents': 'QHeaderView.ResizeMode.ResizeToContents',
            'QHeaderView.ResizeMode.Stretch': 'QHeaderView.ResizeMode.Stretch',
            'QHeaderView.ResizeMode.Fixed': 'QHeaderView.ResizeMode.Fixed',
            
            # QComboBox
            'QComboBox.textActivated': 'QComboBox.textActivated',
            'QComboBox.currentTextChanged': 'QComboBox.currentTextChanged',
            
            # QTabWidget
            'QTabWidget.setTabsClosable': 'QTabWidget.setTabsClosable',
            
            # QFileDialog
            'QFileDialog.getOpenFileName': 'QFileDialog.getOpenFileName',
            'QFileDialog.getSaveFileName': 'QFileDialog.getSaveFileName',
            'QFileDialog.getExistingDirectory': 'QFileDialog.getExistingDirectory',
            
            # 枚举值更新
            'Qt.AlignmentFlag.AlignCenter': 'Qt.AlignmentFlag.AlignCenter',
            'Qt.AlignmentFlag.AlignLeft': 'Qt.AlignmentFlag.AlignLeft',
            'Qt.AlignmentFlag.AlignRight': 'Qt.AlignmentFlag.AlignRight',
            'Qt.AlignmentFlag.AlignTop': 'Qt.AlignmentFlag.AlignTop',
            'Qt.AlignmentFlag.AlignBottom': 'Qt.AlignmentFlag.AlignBottom',
            'Qt.AlignmentFlag.AlignVCenter': 'Qt.AlignmentFlag.AlignVCenter',
            'Qt.AlignmentFlag.AlignHCenter': 'Qt.AlignmentFlag.AlignHCenter',
            
            # 窗口标志
            'Qt.WindowType.WindowStaysOnTopHint': 'Qt.WindowType.WindowStaysOnTopHint',
            'Qt.WindowType.FramelessWindowHint': 'Qt.WindowType.FramelessWindowHint',
            'Qt.WindowType.Tool': 'Qt.WindowType.Tool',
            'Qt.WindowType.Dialog': 'Qt.WindowType.Dialog',
            
            # 键盘修饰符
            'Qt.KeyboardModifier.ControlModifier': 'Qt.KeyboardModifier.ControlModifier',
            'Qt.KeyboardModifier.ShiftModifier': 'Qt.KeyboardModifier.ShiftModifier',
            'Qt.KeyboardModifier.AltModifier': 'Qt.KeyboardModifier.AltModifier',
            
            # 鼠标按钮
            'Qt.MouseButton.LeftButton': 'Qt.MouseButton.LeftButton',
            'Qt.MouseButton.RightButton': 'Qt.MouseButton.RightButton',
            'Qt.MouseButton.MiddleButton': 'Qt.MouseButton.MiddleButton',
            
            # 方向
            'Qt.Orientation.Horizontal': 'Qt.Orientation.Horizontal',
            'Qt.Orientation.Vertical': 'Qt.Orientation.Vertical',
            
            # 焦点策略
            'Qt.FocusPolicy.NoFocus': 'Qt.FocusPolicy.NoFocus',
            'Qt.FocusPolicy.TabFocus': 'Qt.FocusPolicy.TabFocus',
            'Qt.FocusPolicy.ClickFocus': 'Qt.FocusPolicy.ClickFocus',
            'Qt.FocusPolicy.StrongFocus': 'Qt.FocusPolicy.StrongFocus',
            
            # 大小策略
            'QSizePolicy.Policy.Expanding': 'QSizePolicy.Policy.Expanding',
            'QSizePolicy.Policy.Fixed': 'QSizePolicy.Policy.Fixed',
            'QSizePolicy.Policy.Minimum': 'QSizePolicy.Policy.Minimum',
            'QSizePolicy.Policy.Maximum': 'QSizePolicy.Policy.Maximum',
            'QSizePolicy.Policy.Preferred': 'QSizePolicy.Policy.Preferred',
        }
    
    def migrate_project(self) -> bool:
        """迁移整个项目"""
        logger.info(f"开始迁移项目: {self.project_root}")
        
        # 查找所有 Python 文件
        python_files = list(self.project_root.rglob("*.py"))
        
        logger.info(f"找到 {len(python_files)} 个 Python 文件")
        
        success_count = 0
        for file_path in python_files:
            try:
                if self.migrate_file(file_path):
                    success_count += 1
            except Exception as e:
                logger.error(f"迁移文件 {file_path} 失败: {e}")
        
        logger.info(f"成功迁移 {success_count}/{len(python_files)} 个文件")
        
        # 显示变更摘要
        self.show_migration_summary()
        
        return success_count > 0
    
    def migrate_file(self, file_path: Path) -> bool:
        """迁移单个文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用导入映射
            for old_import, new_import in self.import_mappings.items():
                if old_import in content:
                    content = content.replace(old_import, new_import)
                    logger.debug(f"在 {file_path} 中替换: {old_import} -> {new_import}")
            
            # 应用 API 映射
            for old_api, new_api in self.api_mappings.items():
                if old_api in content:
                    content = content.replace(old_api, new_api)
                    logger.debug(f"在 {file_path} 中替换: {old_api} -> {new_api}")
            
            # 如果有变更，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.changes_made.append(str(file_path))
                logger.info(f"✅ 已迁移: {file_path}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
            return False
    
    def show_migration_summary(self):
        """显示迁移摘要"""
        logger.info("\n" + "="*60)
        logger.info("🎉 PyQt6 迁移完成!")
        logger.info("="*60)
        
        if self.changes_made:
            logger.info(f"📝 已修改的文件 ({len(self.changes_made)}):")
            for file_path in self.changes_made:
                logger.info(f"  • {file_path}")
        else:
            logger.info("📝 没有文件需要修改")
        
        logger.info("\n🔧 后续步骤:")
        logger.info("1. 运行 'poetry install' 安装 PyQt6 依赖")
        logger.info("2. 测试应用程序是否正常运行")
        logger.info("3. 检查是否有需要手动调整的代码")
        logger.info("4. 运行测试确保功能正常")
    
    def create_backup(self):
        """创建项目备份"""
        import shutil
        import datetime
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = self.project_root.parent / f"wirevsion_backup_{timestamp}"
        
        logger.info(f"创建备份: {backup_dir}")
        shutil.copytree(self.project_root, backup_dir, ignore=shutil.ignore_patterns('__pycache__', '*.pyc', '.git'))
        
        return backup_dir


def main():
    """主函数"""
    # 获取项目根目录
    project_root = Path(__file__).parent
    
    logger.info("🚀 PyQt5 到 PyQt6 迁移工具")
    logger.info("="*60)
    
    # 创建迁移器
    migrator = PyQt6Migrator(project_root)
    
    # 询问是否创建备份
    create_backup = input("是否创建项目备份? (y/N): ").lower().strip() == 'y'
    
    if create_backup:
        backup_dir = migrator.create_backup()
        logger.info(f"✅ 备份已创建: {backup_dir}")
    
    # 开始迁移
    success = migrator.migrate_project()
    
    if success:
        logger.info("\n🎉 迁移完成! 请运行以下命令安装依赖:")
        logger.info("poetry install")
    else:
        logger.warning("⚠️ 迁移过程中没有发现需要更新的文件")


if __name__ == "__main__":
    main()
