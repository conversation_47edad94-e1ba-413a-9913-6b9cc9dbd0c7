#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVsion应用程序启动器
"""

import sys
import os
import time
from loguru import logger

# 添加日志配置
log_file = f"app_{time.strftime('%Y-%m-%d_%H-%M-%S')}.log"
logger.add(log_file, rotation="10 MB")

logger.info("正在启动WireVsion应用...")

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, root_dir)

from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import Qt

try:
    # 禁用旧版OpenGL以提高性能
    from OpenGL import GL
except ImportError:
    logger.warning("OpenGL库不可用，某些视觉效果可能受影响")

from wirevsion.ui.main_window import MainWindow
from wirevsion.ui.splash_screen import SplashScreen
from wirevsion.utils.app_settings import AppSettings

def main():
    """应用程序主入口"""
    
    try:
        # 应用设置
        AppSettings.init_settings()
        
        # 创建应用实例
        app = QApplication(sys.argv)
        app.setApplicationName("WireVsion")
        app.setApplicationVersion("1.0.0")
        
        # 设置应用样式表
        stylesheet_path = os.path.join(root_dir, "resources", "styles", "modern_dark.qss")
        if os.path.exists(stylesheet_path):
            with open(stylesheet_path, "r") as f:
                app.setStyleSheet(f.read())
        
        # 设置应用图标
        icon_path = os.path.join(root_dir, "resources", "icons", "app_icon.png")
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
        
        # 显示启动屏幕
        splash_img_path = os.path.join(root_dir, "resources", "icons", "splash.png")
        splash = None
        
        if os.path.exists(splash_img_path):
            pixmap = QPixmap(splash_img_path)
            splash = SplashScreen(pixmap)
            splash.show()
            app.processEvents()
        
        # 预加载相机管理器，确保相机能正确初始化
        from wirevsion.ui.camera_utils import CameraManager
        camera_manager = CameraManager()
        
        # 提前初始化相机，避免用户界面显示后再初始化造成卡顿
        if splash:
            splash.showMessage("正在初始化相机...", alignment=Qt.AlignBottom | Qt.AlignCenter, color=Qt.white)
            app.processEvents()
        
        # 尝试初始化相机
        camera_init_success = camera_manager.init_camera()
        logger.info(f"相机初始化{'成功' if camera_init_success else '失败'}")
        
        # 创建并显示主窗口
        if splash:
            splash.showMessage("正在加载用户界面...", alignment=Qt.AlignBottom | Qt.AlignCenter, color=Qt.white)
            app.processEvents()
        
        main_window = MainWindow()
        
        # 将相机管理器设置为应用程序属性，以便其他组件访问
        app.setProperty("camera_manager", camera_manager)
        
        # 显示主窗口
        main_window.show()
        
        # 关闭启动屏幕
        if splash:
            splash.finish(main_window)
        
        # 执行应用
        return app.exec_()
        
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main()) 