#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复 PyQt6 API 变更的脚本

处理 PyQt5 到 PyQt6 中的 API 变更，如 QAction 的位置变化等
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple
from loguru import logger

# 配置日志
logger.remove()
logger.add(sys.stdout, format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | {message}")

class PyQt6APIFixer:
    """PyQt6 API 修复器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.changes_made = []
        
        # PyQt6 API 变更映射
        self.api_fixes = {
            # QAction 从 QtWidgets 移动到 QtGui
            'from PyQt6.QtGui import QAction': 'from PyQt6.QtGui import QAction',
            'from PyQt6.QtWidgets import (.*?)
from PyQt6.QtGui import QAction(.*?)': r'from PyQt6.QtWidgets import \1\2\nfrom PyQt6.QtGui import 
from PyQt6.QtGui import QAction',
            
            # QOpenGLWidget 在 PyQt6 中需要额外的模块
            'from PyQt6.QtOpenGLWidgets import QOpenGLWidget': 'from PyQt6.QtOpenGLWidgets import QOpenGLWidget',
            
            # QShortcut 从 QtWidgets 移动到 QtGui
            'from PyQt6.QtGui import QShortcut': 'from PyQt6.QtGui import QShortcut',
            
            # QUndoCommand 从 QtWidgets 移动到 QtGui
            'from PyQt6.QtGui import QUndoCommand': 'from PyQt6.QtGui import QUndoCommand',
            'from PyQt6.QtGui import QUndoStack': 'from PyQt6.QtGui import QUndoStack',
            
            # QActionGroup 从 QtWidgets 移动到 QtGui
            'from PyQt6.QtGui import QActionGroup': 'from PyQt6.QtGui import QActionGroup',
        }
        
        # 需要添加的新导入
        self.additional_imports = {
            'QOpenGLWidget': 'from PyQt6.QtOpenGLWidgets import QOpenGLWidget',
        }
    
    def fix_project(self) -> bool:
        """修复整个项目"""
        logger.info(f"开始修复项目 PyQt6 API 变更: {self.project_root}")
        
        # 查找所有 Python 文件
        python_files = list(self.project_root.rglob("*.py"))
        
        # 排除虚拟环境和备份文件
        python_files = [f for f in python_files if '.venv' not in str(f) and 'backup' not in str(f)]
        
        logger.info(f"找到 {len(python_files)} 个 Python 文件")
        
        success_count = 0
        for file_path in python_files:
            try:
                if self.fix_file(file_path):
                    success_count += 1
            except Exception as e:
                logger.error(f"修复文件 {file_path} 失败: {e}")
        
        logger.info(f"成功修复 {success_count}/{len(python_files)} 个文件")
        
        # 显示修复摘要
        self.show_fix_summary()
        
        return success_count > 0
    
    def fix_file(self, file_path: Path) -> bool:
        """修复单个文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用 API 修复
            for old_api, new_api in self.api_fixes.items():
                if re.search(old_api, content):
                    content = re.sub(old_api, new_api, content)
                    logger.debug(f"在 {file_path} 中修复: {old_api}")
            
            # 检查是否需要添加额外的导入
            for widget_name, import_statement in self.additional_imports.items():
                if widget_name in content and import_statement not in content:
                    # 在文件开头添加导入
                    lines = content.split('\n')
                    import_added = False
                    
                    for i, line in enumerate(lines):
                        if line.startswith('from PyQt6') or line.startswith('import PyQt6'):
                            lines.insert(i + 1, import_statement)
                            import_added = True
                            break
                    
                    if import_added:
                        content = '\n'.join(lines)
                        logger.debug(f"在 {file_path} 中添加导入: {import_statement}")
            
            # 如果有变更，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.changes_made.append(str(file_path))
                logger.info(f"✅ 已修复: {file_path}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {e}")
            return False
    
    def show_fix_summary(self):
        """显示修复摘要"""
        logger.info("\n" + "="*60)
        logger.info("🔧 PyQt6 API 修复完成!")
        logger.info("="*60)
        
        if self.changes_made:
            logger.info(f"📝 已修复的文件 ({len(self.changes_made)}):")
            for file_path in self.changes_made:
                logger.info(f"  • {file_path}")
        else:
            logger.info("📝 没有文件需要修复")
        
        logger.info("\n🔧 主要修复内容:")
        logger.info("1. QAction: QtWidgets → QtGui")
        logger.info("2. QOpenGLWidget: QtOpenGL → QtOpenGLWidgets")
        logger.info("3. QShortcut: QtWidgets → QtGui")
        logger.info("4. QUndoCommand/QUndoStack: QtWidgets → QtGui")
        logger.info("5. QActionGroup: QtWidgets → QtGui")


def main():
    """主函数"""
    # 获取项目根目录
    project_root = Path(__file__).parent
    
    logger.info("🔧 PyQt6 API 修复工具")
    logger.info("="*60)
    
    # 创建修复器
    fixer = PyQt6APIFixer(project_root)
    
    # 开始修复
    success = fixer.fix_project()
    
    if success:
        logger.info("\n🎉 API 修复完成!")
        logger.info("\n🔧 后续步骤:")
        logger.info("1. 运行测试验证修复效果")
        logger.info("2. 检查应用程序是否正常运行")
    else:
        logger.warning("⚠️ 修复过程中没有发现需要更新的文件")


if __name__ == "__main__":
    main()
