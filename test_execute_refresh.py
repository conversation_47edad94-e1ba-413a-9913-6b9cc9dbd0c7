#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图像显示刷新测试脚本

用于测试图像显示刷新功能
"""

import sys
import time
import numpy as np
import cv2
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QWidget
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt, QTimer
from loguru import logger

from wirevsion.ui.camera_utils import ImageDisplayManager, UIRefresher, CameraManager

# 设置日志
logger.add("refresh_test_{time}.log", rotation="100 MB")

class TestRefreshWindow(QMainWindow):
    """测试刷新窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图像显示刷新测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 图像显示标签
        self.image_label = QLabel()
        self.image_label.setObjectName("MainImageView")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(640, 480)
        self.image_label.setText("等待图像...")
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #1e1e1e;
                border: 1px solid #333333;
                border-radius: 4px;
                color: #aaaaaa;
            }
        """)
        main_layout.addWidget(self.image_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 单次执行按钮
        self.run_once_btn = QPushButton("单次执行")
        self.run_once_btn.clicked.connect(self.run_once)
        button_layout.addWidget(self.run_once_btn)
        
        # 持续执行按钮
        self.continuous_run_btn = QPushButton("持续执行")
        self.continuous_run_btn.clicked.connect(self.toggle_continuous_run)
        button_layout.addWidget(self.continuous_run_btn)
        
        # 强制刷新按钮
        self.force_refresh_btn = QPushButton("强制刷新")
        self.force_refresh_btn.clicked.connect(self.force_refresh)
        button_layout.addWidget(self.force_refresh_btn)
        
        main_layout.addLayout(button_layout)
        
        # 初始化相机管理器
        self.camera_manager = CameraManager()
        
        # 初始化图像显示管理器
        self.image_display_manager = ImageDisplayManager(
            image_view=self.image_label,
            image_container=None,
            image_scroll_area=None
        )
        
        # 状态变量
        self.continuous_run_active = False
        self.continuous_run_timer = None
        
        # 定义测试图像
        self.test_images = []
        self._create_test_images()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        main_layout.addWidget(self.status_label)
        
        logger.info("测试窗口初始化完成")
    
    def _create_test_images(self):
        """创建测试图像"""
        # 创建测试图像1：彩色条纹
        img1 = np.zeros((480, 640, 3), dtype=np.uint8)
        for i in range(0, 640, 80):
            # 创建彩色条纹
            if (i // 80) % 3 == 0:
                img1[:, i:i+80] = [0, 0, 255]  # 红色
            elif (i // 80) % 3 == 1:
                img1[:, i:i+80] = [0, 255, 0]  # 绿色
            else:
                img1[:, i:i+80] = [255, 0, 0]  # 蓝色
        
        # 添加文本
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(img1, "测试图像1: 彩色条纹", (50, 50), font, 1, (255, 255, 255), 2)
        
        # 创建测试图像2：灰度渐变
        img2 = np.zeros((480, 640), dtype=np.uint8)
        for i in range(640):
            img2[:, i] = int(255 * i / 640)
        
        # 创建测试图像3：检测框
        img3 = np.zeros((480, 640, 3), dtype=np.uint8)
        # 绘制网格
        for i in range(0, 640, 40):
            cv2.line(img3, (i, 0), (i, 480), (50, 50, 50), 1)
        for i in range(0, 480, 40):
            cv2.line(img3, (0, i), (640, i), (50, 50, 50), 1)
            
        # 绘制检测框
        cv2.rectangle(img3, (100, 120), (280, 240), (0, 255, 0), 2)
        cv2.putText(img3, "正常 (97.5%)", (105, 115), font, 0.6, (0, 255, 0), 2)
        
        cv2.rectangle(img3, (350, 150), (550, 250), (0, 0, 255), 2)
        cv2.putText(img3, "缺陷 (93.2%)", (355, 145), font, 0.6, (0, 0, 255), 2)
        
        # 将图像添加到测试集
        self.test_images = [
            cv2.cvtColor(img1, cv2.COLOR_BGR2RGB),  # 转为RGB
            cv2.cvtColor(img2, cv2.COLOR_GRAY2RGB),  # 灰度转RGB
            cv2.cvtColor(img3, cv2.COLOR_BGR2RGB)   # 转为RGB
        ]
        
        logger.info(f"创建了 {len(self.test_images)} 个测试图像")
    
    def run_once(self):
        """单次执行"""
        try:
            logger.info("单次执行")
            
            # 禁用按钮
            self.run_once_btn.setEnabled(False)
            
            # 获取相机图像或使用测试图像
            try:
                # 尝试获取相机图像
                frame = self.camera_manager.get_frame()
                if frame is not None and frame.size > 0:
                    # 将BGR转为RGB
                    image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    logger.info(f"获取相机图像成功: 形状={image.shape}")
                else:
                    # 使用测试图像
                    image_index = int(time.time()) % len(self.test_images)
                    image = self.test_images[image_index]
                    logger.info(f"使用测试图像 {image_index}: 形状={image.shape}")
            except Exception as e:
                logger.error(f"获取图像出错: {e}")
                # 使用测试图像
                image_index = int(time.time()) % len(self.test_images)
                image = self.test_images[image_index]
                logger.info(f"使用测试图像 {image_index}: 形状={image.shape}")
            
            # 更新图像显示
            self.image_display_manager.update_display(image)
            
            # 强制处理事件队列
            QApplication.processEvents()
            
            # 强制刷新
            self.force_refresh()
            
            # 更新状态
            self.status_label.setText(f"已更新图像: {time.strftime('%H:%M:%S')}")
            
            # 启用按钮
            self.run_once_btn.setEnabled(True)
            
        except Exception as e:
            logger.error(f"单次执行出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.run_once_btn.setEnabled(True)
    
    def toggle_continuous_run(self):
        """切换持续执行状态"""
        if self.continuous_run_active:
            # 停止持续执行
            if self.continuous_run_timer is not None:
                self.killTimer(self.continuous_run_timer)
                self.continuous_run_timer = None
            
            self.continuous_run_active = False
            self.continuous_run_btn.setText("持续执行")
            self.status_label.setText("持续执行已停止")
            logger.info("停止持续执行")
        else:
            # 开始持续执行
            self.continuous_run_active = True
            self.continuous_run_timer = self.startTimer(500)  # 每0.5秒执行一次
            self.continuous_run_btn.setText("停止执行")
            self.status_label.setText("持续执行中...")
            logger.info("开始持续执行")
    
    def timerEvent(self, event):
        """定时器事件"""
        if hasattr(self, 'continuous_run_timer') and event.timerId() == self.continuous_run_timer:
            self.run_once()
    
    def force_refresh(self):
        """强制刷新图像显示"""
        logger.info("强制刷新图像显示")
        
        # 使用ImageDisplayManager强制刷新
        self.image_display_manager.force_refresh()
        
        # 使用UIRefresher直接刷新
        UIRefresher.force_refresh_widget(self.image_label)
        
        # 强制处理事件队列
        QApplication.processEvents()
        
        logger.info("强制刷新完成")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = TestRefreshWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 