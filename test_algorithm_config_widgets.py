#!/usr/bin/env python3
"""
测试算法专用配置界面

验证每个算法的专用配置界面是否正常工作
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QComboBox, QLabel
from PyQt5.QtCore import Qt
from loguru import logger

# 导入算法配置组件
from wirevsion.ui.algorithm_config_widgets import AlgorithmConfigWidgetFactory


class AlgorithmConfigTestWindow(QMainWindow):
    """算法配置测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("算法配置界面测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 算法选择
        layout.addWidget(QLabel("选择算法:"))
        self.algorithm_combo = QComboBox()
        self.algorithm_combo.addItem("相机源", "image_source.camera")
        self.algorithm_combo.addItem("边缘检测", "image_processing.edge_detection")
        self.algorithm_combo.addItem("高斯模糊", "image_processing.gaussian_blur")
        self.algorithm_combo.addItem("模板匹配", "feature_detection.template_matching")
        self.algorithm_combo.addItem("轮廓检测", "feature_detection.contour_detection")
        self.algorithm_combo.addItem("通用算法", "unknown_algorithm")
        layout.addWidget(self.algorithm_combo)
        
        # 创建配置界面按钮
        create_btn = QPushButton("创建配置界面")
        create_btn.clicked.connect(self.create_config_widget)
        layout.addWidget(create_btn)
        
        # 配置界面容器
        self.config_container = QWidget()
        self.config_layout = QVBoxLayout(self.config_container)
        layout.addWidget(self.config_container)
        
        # 当前配置界面
        self.current_config_widget = None
        
        logger.info("算法配置测试窗口初始化完成")
        
    def create_config_widget(self):
        """创建配置界面"""
        algorithm_name = self.algorithm_combo.currentData()
        if not algorithm_name:
            return
            
        try:
            # 清除之前的配置界面
            if self.current_config_widget:
                self.config_layout.removeWidget(self.current_config_widget)
                self.current_config_widget.deleteLater()
                self.current_config_widget = None
            
            # 创建新的配置界面
            config_widget = AlgorithmConfigWidgetFactory.create_config_widget(algorithm_name, self)
            
            if config_widget:
                # 设置测试输入源
                test_input_sources = [
                    {
                        'node_id': 'camera_1',
                        'node_name': '相机输入1',
                        'output_name': 'image',
                        'data_type': 'image'
                    },
                    {
                        'node_id': 'file_1',
                        'node_name': '文件输入1',
                        'output_name': 'image',
                        'data_type': 'image'
                    },
                    {
                        'node_id': 'blur_1',
                        'node_name': '高斯模糊1',
                        'output_name': 'image',
                        'data_type': 'image'
                    }
                ]
                config_widget.set_input_sources(test_input_sources)
                
                # 连接信号
                config_widget.parameter_changed.connect(self.on_parameter_changed)
                config_widget.preview_requested.connect(self.on_preview_requested)
                
                # 添加到布局
                self.config_layout.addWidget(config_widget)
                self.current_config_widget = config_widget
                
                logger.info(f"成功创建配置界面: {algorithm_name}")
            else:
                logger.error(f"创建配置界面失败: {algorithm_name}")
                
        except Exception as e:
            logger.error(f"创建配置界面异常: {e}")
            
    def on_parameter_changed(self, param_name: str, value):
        """参数改变事件"""
        logger.info(f"参数改变: {param_name} = {value}")
        
    def on_preview_requested(self):
        """预览请求事件"""
        logger.info("预览请求")
        if self.current_config_widget:
            parameters = self.current_config_widget.get_parameters()
            logger.info(f"当前参数: {parameters}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = AlgorithmConfigTestWindow()
    window.show()
    
    logger.info("算法配置界面测试启动")
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
