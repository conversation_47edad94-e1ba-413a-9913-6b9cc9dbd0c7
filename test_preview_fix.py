#!/usr/bin/env python3
"""
测试预览功能修复
验证当输入源为相机或上级结果时，预览能正确显示实际画面
"""

import sys
import os
import numpy as np
import cv2
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QImage

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog
from wirevsion.ui.modern_workflow_editor import ModernWorkflowEditor

class TestPreviewWindow(QMainWindow):
    """测试预览功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("预览功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("点击按钮开始测试...")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_camera_btn = QPushButton("测试相机输入预览")
        self.test_camera_btn.clicked.connect(self.test_camera_preview)
        layout.addWidget(self.test_camera_btn)
        
        self.test_upstream_btn = QPushButton("测试上级结果预览")
        self.test_upstream_btn.clicked.connect(self.test_upstream_preview)
        layout.addWidget(self.test_upstream_btn)
        
        # 创建工作流编辑器实例（用于提供相机管理器）
        self.workflow_editor = None
        self.init_workflow_editor()
        
    def init_workflow_editor(self):
        """初始化工作流编辑器"""
        try:
            self.workflow_editor = ModernWorkflowEditor()
            self.status_label.setText("工作流编辑器初始化成功")
        except Exception as e:
            self.status_label.setText(f"工作流编辑器初始化失败: {e}")
            print(f"工作流编辑器初始化失败: {e}")
    
    def test_camera_preview(self):
        """测试相机输入预览"""
        try:
            self.status_label.setText("正在测试相机输入预览...")
            
            # 创建一个模拟的相机输入节点
            class MockCameraNode:
                def __init__(self):
                    self.node_id = "camera_input_1"
                    self.node_type = "input"
                    self.title = "相机输入"
            
            mock_node = MockCameraNode()
            
            # 创建配置对话框
            dialog = ModernNodeConfigDialog(mock_node, parent=self)
            
            # 设置工作流编辑器引用
            if self.workflow_editor:
                dialog.workflow_editor = self.workflow_editor
            
            # 显示对话框
            dialog.show()
            self.status_label.setText("相机输入配置对话框已打开，请测试预览功能")
            
        except Exception as e:
            self.status_label.setText(f"测试相机预览失败: {e}")
            print(f"测试相机预览失败: {e}")
    
    def test_upstream_preview(self):
        """测试上级结果预览"""
        try:
            self.status_label.setText("正在测试上级结果预览...")
            
            # 创建一个模拟的处理节点
            class MockProcessingNode:
                def __init__(self):
                    self.node_id = "edge_detection_1"
                    self.node_type = "processing"
                    self.title = "边缘检测"
            
            mock_node = MockProcessingNode()
            
            # 创建配置对话框
            dialog = ModernNodeConfigDialog(mock_node, parent=self)
            
            # 设置工作流编辑器引用
            if self.workflow_editor:
                dialog.workflow_editor = self.workflow_editor
                
                # 模拟上级节点结果
                mock_upstream_result = self.create_mock_image()
                if hasattr(self.workflow_editor, '_node_results'):
                    self.workflow_editor._node_results["camera_input_1"] = {
                        "node_id": "camera_input_1",
                        "result": "success",
                        "image": mock_upstream_result,
                        "metadata": {"source": "camera"}
                    }
            
            # 显示对话框
            dialog.show()
            self.status_label.setText("处理节点配置对话框已打开，请测试预览功能")
            
        except Exception as e:
            self.status_label.setText(f"测试上级结果预览失败: {e}")
            print(f"测试上级结果预览失败: {e}")
    
    def create_mock_image(self):
        """创建模拟图像"""
        # 创建一个彩色测试图像
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for y in range(480):
            for x in range(640):
                image[y, x] = [
                    int(255 * x / 640),  # R
                    int(255 * y / 480),  # G
                    128                   # B
                ]
        
        # 添加一些几何图形
        cv2.rectangle(image, (100, 100), (200, 200), (0, 255, 0), 3)
        cv2.circle(image, (400, 300), 80, (255, 0, 0), 3)
        cv2.line(image, (50, 400), (590, 400), (255, 255, 0), 3)
        
        # 添加文字
        cv2.putText(image, "Mock Upstream Result", (200, 50),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        return image

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestPreviewWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
