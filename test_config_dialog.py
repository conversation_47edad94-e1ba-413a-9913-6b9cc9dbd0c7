#!/usr/bin/env python3
"""
测试配置对话框功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from wirevsion.ui.modern_node_config_dialog import ModernNodeConfigDialog
from loguru import logger

# 创建一个简单的节点类用于测试
class TestNode:
    def __init__(self, node_id, node_type, title):
        self.node_id = node_id
        self.node_type = node_type
        self.title = title

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("WireVision 配置对话框测试")
        self.setGeometry(100, 100, 400, 300)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 创建测试按钮
        self.create_test_buttons(layout)

    def create_test_buttons(self, layout):
        """创建测试按钮"""

        # 模板匹配测试
        template_btn = QPushButton("测试模板匹配配置")
        template_btn.clicked.connect(self.test_template_matching)
        layout.addWidget(template_btn)

        # 颜色检测测试
        color_btn = QPushButton("测试颜色检测配置")
        color_btn.clicked.connect(self.test_color_detection)
        layout.addWidget(color_btn)

        # 边缘检测测试
        edge_btn = QPushButton("测试边缘检测配置")
        edge_btn.clicked.connect(self.test_edge_detection)
        layout.addWidget(edge_btn)

        # 高斯模糊测试
        blur_btn = QPushButton("测试高斯模糊配置")
        blur_btn.clicked.connect(self.test_gaussian_blur)
        layout.addWidget(blur_btn)

    def test_template_matching(self):
        """测试模板匹配配置"""
        node = TestNode("test_template", "feature_detection", "模板匹配测试")

        dialog = ModernNodeConfigDialog(node, parent=self)

        if dialog.exec_() == dialog.Accepted:
            logger.info(f"模板匹配配置: {dialog.config_data}")

    def test_color_detection(self):
        """测试颜色检测配置"""
        node = TestNode("test_color", "object_detection", "颜色检测测试")

        dialog = ModernNodeConfigDialog(node, parent=self)

        if dialog.exec_() == dialog.Accepted:
            logger.info(f"颜色检测配置: {dialog.config_data}")

    def test_edge_detection(self):
        """测试边缘检测配置"""
        node = TestNode("test_edge", "image_processing", "边缘检测测试")

        dialog = ModernNodeConfigDialog(node, parent=self)

        if dialog.exec_() == dialog.Accepted:
            logger.info(f"边缘检测配置: {dialog.config_data}")

    def test_gaussian_blur(self):
        """测试高斯模糊配置"""
        node = TestNode("test_blur", "image_processing", "高斯模糊测试")

        dialog = ModernNodeConfigDialog(node, parent=self)

        if dialog.exec_() == dialog.Accepted:
            logger.info(f"高斯模糊配置: {dialog.config_data}")

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    # 创建主窗口
    window = TestMainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
