#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机显示修复测试脚本

用于测试相机显示修复功能
"""

import sys
import time
import cv2
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QComboBox, QCheckBox, QSpinBox, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon
from loguru import logger

# 配置日志
logger.add("camera_fix_test_{time}.log", rotation="10 MB")


class CameraFixTestApp(QMainWindow):
    """相机显示修复测试应用"""
    
    def __init__(self):
        """初始化应用程序"""
        super().__init__()
        self.setWindowTitle("相机显示修复测试")
        self.setGeometry(100, 100, 1000, 700)
        
        # 初始化相机
        try:
            from wirevsion.ui.camera_utils import CameraManager, ImageDisplayManager
            self.camera_manager = CameraManager()
            logger.info("成功导入相机管理器")
        except ImportError as e:
            logger.error(f"导入相机管理器失败: {e}")
            QMessageBox.critical(self, "导入错误", f"无法导入相机模块: {e}")
            sys.exit(1)
        
        # 相机状态
        self.camera_running = False
        self.current_camera_id = 0
        
        # 创建UI
        self._setup_ui()
        
        # 初始化定时器
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self._update_frame)
        
        logger.info("应用程序初始化完成")
    
    def _setup_ui(self):
        """设置UI"""
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 相机选择和控制
        control_layout = QHBoxLayout()
        
        # 相机选择
        self.camera_combo = QComboBox()
        self.camera_combo.addItem("相机 0", 0)
        self.camera_combo.addItem("相机 1", 1)
        self.camera_combo.addItem("相机 2", 2)
        self.camera_combo.addItem("相机 3", 3)
        control_layout.addWidget(QLabel("选择相机:"))
        control_layout.addWidget(self.camera_combo)
        
        # 启动/停止按钮
        self.start_btn = QPushButton("启动相机")
        self.start_btn.clicked.connect(self.start_camera)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止相机")
        self.stop_btn.clicked.connect(self.stop_camera)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        # 测试图像按钮
        self.test_img_btn = QPushButton("生成测试图像")
        self.test_img_btn.clicked.connect(self.generate_test_image)
        control_layout.addWidget(self.test_img_btn)
        
        # 截图按钮
        self.capture_btn = QPushButton("截图")
        self.capture_btn.clicked.connect(self.capture_image)
        self.capture_btn.setEnabled(False)
        control_layout.addWidget(self.capture_btn)
        
        main_layout.addLayout(control_layout)
        
        # 图像显示区域
        self.image_label = QLabel("等待图像...")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(640, 480)
        self.image_label.setStyleSheet("background-color: #1a1a1a; color: white; border: 2px solid #3d3d3d;")
        main_layout.addWidget(self.image_label)
        
        # 创建图像显示管理器
        from wirevsion.ui.camera_utils import ImageDisplayManager
        self.image_display_manager = ImageDisplayManager(image_view=self.image_label)
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
        # 设置一些选项
        options_layout = QHBoxLayout()
        
        # 色彩空间切换
        self.rgb_checkbox = QCheckBox("强制RGB")
        options_layout.addWidget(self.rgb_checkbox)
        
        # 亮度调整
        options_layout.addWidget(QLabel("亮度增强:"))
        self.brightness_spin = QSpinBox()
        self.brightness_spin.setRange(0, 100)
        self.brightness_spin.setValue(0)
        self.brightness_spin.setSuffix("%")
        options_layout.addWidget(self.brightness_spin)
        
        # 缩放控制
        options_layout.addWidget(QLabel("缩放:"))
        self.zoom_spin = QSpinBox()
        self.zoom_spin.setRange(10, 200)
        self.zoom_spin.setValue(100)
        self.zoom_spin.setSuffix("%")
        self.zoom_spin.valueChanged.connect(self._on_zoom_changed)
        options_layout.addWidget(self.zoom_spin)
        
        main_layout.addLayout(options_layout)
    
    def start_camera(self):
        """启动相机"""
        if self.camera_running:
            return
            
        # 获取选中的相机ID
        self.current_camera_id = self.camera_combo.currentData()
        logger.info(f"尝试启动相机 ID: {self.current_camera_id}")
        
        # 初始化相机
        try:
            # 使用相机管理器初始化相机
            success = self.camera_manager.init_camera()
            if not success:
                QMessageBox.warning(self, "相机错误", f"无法初始化相机 {self.current_camera_id}")
                logger.error(f"无法初始化相机 {self.current_camera_id}")
                return
                
            # 更新UI状态
            self.camera_running = True
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.capture_btn.setEnabled(True)
            self.camera_combo.setEnabled(False)
            
            # 启动定时器
            self.update_timer.start(33)  # 约30 FPS
            
            self.statusBar().showMessage(f"相机 {self.current_camera_id} 已启动")
            logger.info(f"相机 {self.current_camera_id} 已启动")
            
        except Exception as e:
            logger.error(f"启动相机出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            QMessageBox.critical(self, "相机错误", f"启动相机时出错: {e}")
    
    def stop_camera(self):
        """停止相机"""
        if not self.camera_running:
            return
            
        # 停止定时器
        self.update_timer.stop()
        
        # 释放相机资源
        try:
            self.camera_manager.release()
        except Exception as e:
            logger.error(f"停止相机出错: {e}")
        
        # 更新UI状态
        self.camera_running = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.camera_combo.setEnabled(True)
        self.capture_btn.setEnabled(False)
        
        self.statusBar().showMessage("相机已停止")
        logger.info("相机已停止")
    
    def _update_frame(self):
        """更新相机帧"""
        if not self.camera_running:
            return
            
        try:
            # 获取相机帧
            success, frame = self.camera_manager.get_frame()
            
            if not success or frame is None:
                logger.warning("获取相机帧失败")
                return
                
            # 图像处理
            # 1. 亮度增强
            brightness = self.brightness_spin.value()
            if brightness > 0:
                alpha = 1.0 + (brightness / 100.0)
                beta = brightness
                frame = cv2.convertScaleAbs(frame, alpha=alpha, beta=beta)
                
            # 2. 如果勾选了强制RGB，确保是RGB格式
            if self.rgb_checkbox.isChecked():
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
            # 使用图像显示管理器更新显示
            self.image_display_manager.update_display(frame)
            
        except Exception as e:
            logger.error(f"更新相机帧出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def generate_test_image(self):
        """生成测试图像"""
        try:
            # 创建测试图像
            width, height = 640, 480
            image = np.zeros((height, width, 3), dtype=np.uint8)
            
            # 绘制彩色条纹和网格
            for x in range(0, width, 40):
                # 垂直彩色条纹
                color = [(x * 13) % 256, (x * 7) % 256, (x * 11) % 256]
                cv2.rectangle(image, (x, 0), (x + 40, height), color, -1)
                
                # 网格线
                cv2.line(image, (x, 0), (x, height), (128, 128, 128), 1)
            
            for y in range(0, height, 40):
                # 水平网格线
                cv2.line(image, (0, y), (width, y), (128, 128, 128), 1)
            
            # 添加文本和标记
            font = cv2.FONT_HERSHEY_SIMPLEX
            
            # 中央文本
            text = "测试图像"
            text_size = cv2.getTextSize(text, font, 1, 2)[0]
            text_x = (width - text_size[0]) // 2
            text_y = height // 2
            
            # 绘制文本背景
            cv2.rectangle(image, 
                (text_x - 10, text_y - text_size[1] - 10),
                (text_x + text_size[0] + 10, text_y + 10),
                (0, 0, 0), -1)
                
            # 绘制文本
            cv2.putText(image, text, (text_x, text_y), font, 1, (255, 255, 255), 2)
            
            # 绘制RGB色块，用于检查色彩空间
            block_size = 60
            margin = 20
            
            # 红色块 (B=0, G=0, R=255)
            cv2.rectangle(image, 
                (margin, margin), 
                (margin + block_size, margin + block_size),
                (0, 0, 255), -1)
                
            # 绿色块 (B=0, G=255, R=0)
            cv2.rectangle(image, 
                (margin*2 + block_size, margin), 
                (margin*2 + block_size*2, margin + block_size),
                (0, 255, 0), -1)
                
            # 蓝色块 (B=255, G=0, R=0)
            cv2.rectangle(image, 
                (margin*3 + block_size*2, margin), 
                (margin*3 + block_size*3, margin + block_size),
                (255, 0, 0), -1)
                
            # 标记RGB色块
            cv2.putText(image, "R", (margin + 20, margin + 40), font, 0.8, (255, 255, 255), 2)
            cv2.putText(image, "G", (margin*2 + block_size + 20, margin + 40), font, 0.8, (255, 255, 255), 2)
            cv2.putText(image, "B", (margin*3 + block_size*2 + 20, margin + 40), font, 0.8, (255, 255, 255), 2)
            
            # 显示图像
            self.image_display_manager.update_display(image)
            
            self.statusBar().showMessage("已生成测试图像")
            logger.info("已生成测试图像")
            
        except Exception as e:
            logger.error(f"生成测试图像出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"生成测试图像出错: {e}")
    
    def capture_image(self):
        """截取当前图像"""
        if not self.camera_running:
            return
            
        try:
            # 获取一帧
            success, frame = self.camera_manager.get_frame()
            
            if not success or frame is None:
                logger.warning("截图失败：无法获取相机帧")
                QMessageBox.warning(self, "截图失败", "无法获取相机帧")
                return
                
            # 保存图像
            import os
            from datetime import datetime
            
            # 创建截图目录
            if not os.path.exists("screenshots"):
                os.makedirs("screenshots")
                
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshots/camera_{self.current_camera_id}_{timestamp}.png"
            
            # 保存图像
            cv2.imwrite(filename, frame)
            
            self.statusBar().showMessage(f"截图已保存: {filename}")
            logger.info(f"截图已保存: {filename}")
            
        except Exception as e:
            logger.error(f"截图出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"截图出错: {e}")
    
    def _on_zoom_changed(self, value):
        """缩放值改变时调用"""
        try:
            zoom_factor = value / 100.0
            self.image_display_manager.set_zoom_factor(zoom_factor)
            logger.debug(f"设置缩放因子: {zoom_factor}")
        except Exception as e:
            logger.error(f"设置缩放因子出错: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止相机
        if self.camera_running:
            self.stop_camera()
        
        event.accept()


def main():
    """主函数"""
    logger.info("相机显示修复测试程序启动")
    
    app = QApplication(sys.argv)
    window = CameraFixTestApp()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 