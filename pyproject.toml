[project]
name = "wirevsion"
version = "0.1.0"
description = "基于OpenCV和PyQt5的视觉检测应用程序"
authors = [
    {name = "张玉龙",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "pyqt6 (>=6.4.2,<7.0.0)",
    "pyqt6-tools (>=6.4.0,<7.0.0)",
    "pyopengl (>=3.1.0,<4.0.0)",
    "pyopengl-accelerate (>=3.1.0,<4.0.0)",
    "opencv-python (>=*********,<*******)",
    "numpy (>=2.2.6,<3.0.0)",
    "pillow (>=11.2.1,<12.0.0)",
    "pyyaml (>=6.0.2,<7.0.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "psutil (>=5.9.0,<6.0.0)",
    "ultralytics (>=8.0.0,<9.0.0)",
    "torch (>=2.0.0,<3.0.0)",
    "torchvision (>=0.15.0,<1.0.0)",
    "matplotlib (>=3.7.0,<4.0.0)",
    "seaborn (>=0.12.0,<1.0.0)",
    "scikit-learn (>=1.3.0,<2.0.0)",
    "albumentations (>=1.3.0,<2.0.0)",
    "supervision (>=0.20.0,<1.0.0)",
    "tensorboard (>=2.13.0,<3.0.0)",
]

[tool.poetry.group.dev.dependencies]


[[tool.poetry.source]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "primary"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"