# WireVision 现代化UI系统文档

## 概述

WireVision 现代化UI系统是一个基于PyQt5的全新用户界面框架，采用现代化设计理念，提供美观、高效、易用的视觉检测系统界面。

## 主要特性

### 1. 无边框窗口设计
- **自定义标题栏**：完全自定义的标题栏，支持拖动、最小化、最大化、关闭
- **圆角边框**：美观的圆角窗口设计
- **阴影效果**：窗口阴影增强立体感
- **调整大小**：支持从各个边缘调整窗口大小

### 2. 侧边栏导航
- **折叠/展开**：支持侧边栏折叠，节省空间
- **分组导航**：功能模块分组管理
- **图标支持**：每个导航项支持图标
- **动画效果**：平滑的展开/折叠动画

### 3. 现代化组件库
- **ModernCard**：卡片式布局组件
- **ModernButton**：多种样式的现代化按钮
- **ModernInput**：美观的输入框
- **ModernSwitch**：iOS风格的开关组件
- **ModernProgressBar**：渐变色进度条

### 4. 功能模块

#### 仪表板（Dashboard）
- 系统概览
- 快捷操作
- 实时状态监控

#### 相机管理（Camera Management）
- 实时预览
- 参数调整
- 录制和截图
- ROI选择

#### 工作流编辑器（Workflow Editor）
- 可视化节点编辑
- 拖放式操作
- 实时连线
- 属性配置

#### 算法库（Algorithm Library）
- 算法分类展示
- 算法详情查看
- 一键导入使用

#### 结果分析（Result Analysis）
- 检测结果展示
- 数据统计分析
- 历史记录查询

## 技术架构

### 文件结构
```
wirevsion/ui/
├── modern_frameless_window.py    # 无边框窗口实现
├── modern_sidebar.py             # 侧边栏导航组件
├── modern_components.py          # 基础UI组件库
├── modern_workflow_editor.py     # 工作流编辑器
├── modern_camera_widget.py       # 相机管理界面
└── modern_main_application.py    # 主应用程序
```

### 核心类说明

#### ModernFramelessWindow
无边框窗口基类，提供：
- 自定义标题栏
- 窗口拖动和调整大小
- 阴影和圆角效果

```python
class ModernFramelessWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Window)
        self.setAttribute(Qt.WA_TranslucentBackground)
```

#### ModernSidebar
侧边栏导航组件，提供：
- 导航项管理
- 折叠/展开功能
- 分组支持

```python
class ModernSidebar(QWidget):
    navigation_changed = pyqtSignal(str)
    expanded_changed = pyqtSignal(bool)
    
    def add_navigation_item(self, group_id, item_id, text, icon=None):
        # 添加导航项
```

#### ModernWorkflowEditor
工作流编辑器，提供：
- 节点拖放
- 连线编辑
- 属性配置

```python
class ModernWorkflowEditor(QWidget):
    def add_node(self, node_type, title, pos):
        # 添加节点
    
    def create_connection(self, start_node, end_node):
        # 创建连接
```

## 使用指南

### 1. 运行应用程序
```bash
python test_modern_ui.py
```

### 2. 测试单个组件
```bash
python test_modern_ui.py --components
```

### 3. 自定义主题

修改颜色主题：
```python
# 在 modern_components.py 中定义颜色常量
THEME_COLORS = {
    "primary": "#0d6efd",
    "secondary": "#6c757d",
    "success": "#198754",
    "warning": "#ffc107",
    "danger": "#dc3545",
    "dark": "#212529",
    "light": "#f8f9fa"
}
```

### 4. 添加新页面

在主应用程序中添加新页面：
```python
# 1. 创建页面类
class MyNewPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_ui()

# 2. 在 _create_pages 中添加
self.pages["my_page"] = MyNewPage()
self.page_stack.addWidget(self.pages["my_page"])

# 3. 在导航栏添加入口
self.sidebar.add_navigation_item("main", "my_page", "我的页面")
```

## 设计规范

### 颜色方案
- **主色调**：深色背景 (#1a1a1a, #2b2b2b)
- **强调色**：蓝色 (#0d6efd)
- **文字颜色**：白色/浅灰 (#ffffff, #e0e0e0, #b0b0b0)
- **边框颜色**：深灰 (#3d3d3d)

### 间距规范
- **组件间距**：16px, 24px
- **内边距**：12px, 16px, 24px
- **圆角半径**：4px, 8px, 12px

### 字体规范
- **主字体**：Microsoft YaHei, Arial
- **标题字号**：18px, 16px
- **正文字号**：14px
- **辅助文字**：12px

## 性能优化

### 1. 延迟加载
页面组件采用延迟加载，只在需要时创建：
```python
def _switch_page(self, page_id):
    if page_id not in self.pages:
        self.pages[page_id] = self._create_page(page_id)
```

### 2. 事件优化
使用防抖和节流技术优化频繁触发的事件：
```python
self.update_timer = QTimer()
self.update_timer.timeout.connect(self._update_camera_frame)
self.update_timer.start(33)  # 30 FPS
```

### 3. 内存管理
及时清理不需要的资源：
```python
def closeEvent(self, event):
    self._cleanup()
    event.accept()
```

## 扩展开发

### 1. 自定义组件
继承基础组件类创建自定义组件：
```python
class MyCustomButton(ModernButton):
    def __init__(self, text, parent=None):
        super().__init__(text, ModernButton.PRIMARY, parent)
        self._setup_custom_style()
```

### 2. 插件系统
通过动态加载支持插件扩展：
```python
def load_plugin(plugin_path):
    # 动态加载插件模块
    spec = importlib.util.spec_from_file_location("plugin", plugin_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module.Plugin()
```

### 3. 主题系统
支持动态切换主题：
```python
def apply_theme(theme_name):
    theme = load_theme(theme_name)
    app.setStyleSheet(theme.stylesheet)
```

## 常见问题

### Q: 如何修改窗口圆角大小？
A: 在 `modern_frameless_window.py` 中修改 `border-radius` 值：
```css
#contentContainer {
    border-radius: 10px;  /* 修改这个值 */
}
```

### Q: 如何添加新的导航图标？
A: 在创建导航项时传入图标参数：
```python
icon = QIcon("path/to/icon.png")
self.sidebar.add_navigation_item("main", "item_id", "项目名称", icon)
```

### Q: 如何自定义按钮颜色？
A: 在 `ModernButton` 类中添加新的按钮类型：
```python
class ModernButton(QPushButton):
    CUSTOM = "custom"
    
    # 在 _setup_style 中添加样式
    type_styles[self.CUSTOM] = """
        QPushButton {
            background-color: #your_color;
        }
    """
```

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现无边框窗口
- 完成侧边栏导航
- 基础组件库
- 相机管理界面
- 工作流编辑器

## 未来计划

1. **更多组件**
   - 数据表格组件
   - 图表组件
   - 时间选择器
   - 颜色选择器

2. **功能增强**
   - 多语言支持
   - 主题切换
   - 快捷键系统
   - 拖放文件支持

3. **性能优化**
   - GPU加速渲染
   - 异步加载优化
   - 内存使用优化

## 贡献指南

欢迎贡献代码！请遵循以下规范：

1. 代码风格遵循 PEP 8
2. 添加适当的中文注释
3. 编写单元测试
4. 更新相关文档

## 许可证

本项目采用 MIT 许可证。 