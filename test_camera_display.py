#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机显示功能测试脚本

用于验证WireVsion应用中相机显示功能的修复效果
"""

import sys
import os
import time
from datetime import datetime
import cv2
import numpy as np
from loguru import logger
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, QWidget
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt, QTimer

# 设置日志
logger.add("camera_test_{time}.log", rotation="100 MB")

class CameraTestWindow(QMainWindow):
    """相机测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("WireVsion 相机显示测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始化相机
        self.camera = None
        self.camera_id = 0
        self.camera_running = False
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_frame)
        
        # 创建UI
        self._setup_ui()
        
        logger.info("相机测试窗口初始化完成")
        
    def _setup_ui(self):
        """设置UI界面"""
        # 主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        
        # 图像显示区域
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumSize(640, 480)
        self.image_label.setStyleSheet("background-color: #333333;")
        main_layout.addWidget(self.image_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 开始相机按钮
        self.start_btn = QPushButton("开始相机")
        self.start_btn.clicked.connect(self.start_camera)
        button_layout.addWidget(self.start_btn)
        
        # 停止相机按钮
        self.stop_btn = QPushButton("停止相机")
        self.stop_btn.clicked.connect(self.stop_camera)
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.stop_btn)
        
        # 切换相机按钮
        self.switch_camera_btn = QPushButton("切换相机")
        self.switch_camera_btn.clicked.connect(self.switch_camera)
        button_layout.addWidget(self.switch_camera_btn)
        
        # 截图按钮
        self.capture_btn = QPushButton("截图")
        self.capture_btn.clicked.connect(self.capture_image)
        self.capture_btn.setEnabled(False)
        button_layout.addWidget(self.capture_btn)
        
        # 添加状态标签
        self.status_label = QLabel("相机未启动")
        button_layout.addWidget(self.status_label)
        
        main_layout.addLayout(button_layout)
        self.setCentralWidget(main_widget)
        
    def start_camera(self):
        """启动相机"""
        if self.camera_running:
            return
            
        logger.info(f"尝试启动相机 ID: {self.camera_id}")
        
        # 尝试打开相机
        self.camera = cv2.VideoCapture(self.camera_id)
        
        if not self.camera.isOpened():
            logger.error(f"无法打开相机 ID: {self.camera_id}")
            self.status_label.setText(f"无法打开相机 {self.camera_id}")
            return
            
        # 设置相机参数
        self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.camera.set(cv2.CAP_PROP_FPS, 30)
        
        # 获取实际参数
        actual_width = self.camera.get(cv2.CAP_PROP_FRAME_WIDTH)
        actual_height = self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT)
        actual_fps = self.camera.get(cv2.CAP_PROP_FPS)
        
        logger.info(f"相机已打开: 分辨率={actual_width}x{actual_height}, FPS={actual_fps}")
        
        # 启动定时器
        self.camera_running = True
        self.timer.start(33)  # 约30 FPS
        
        # 更新按钮状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.capture_btn.setEnabled(True)
        self.status_label.setText(f"相机 {self.camera_id} 运行中")
        
    def stop_camera(self):
        """停止相机"""
        if not self.camera_running:
            return
            
        # 停止定时器
        self.timer.stop()
        
        # 释放相机
        if self.camera:
            self.camera.release()
            self.camera = None
            
        self.camera_running = False
        
        # 更新按钮状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.capture_btn.setEnabled(False)
        self.status_label.setText("相机已停止")
        
        logger.info("相机已停止")
        
    def switch_camera(self):
        """切换相机设备"""
        # 如果相机正在运行，先停止
        if self.camera_running:
            self.stop_camera()
            
        # 切换相机ID (循环0-4)
        self.camera_id = (self.camera_id + 1) % 5
        logger.info(f"切换到相机 ID: {self.camera_id}")
        self.status_label.setText(f"已切换到相机 {self.camera_id}")
        
        # 自动启动新相机
        self.start_camera()
        
    def update_frame(self):
        """更新相机画面"""
        if not self.camera_running or not self.camera:
            return
            
        # 获取一帧
        ret, frame = self.camera.read()
        
        if not ret or frame is None or frame.size == 0:
            logger.warning("获取相机帧失败")
            return
            
        # 计算图像统计信息
        avg_value = np.mean(frame)
        min_value = np.min(frame)
        max_value = np.max(frame)
        logger.debug(f"图像统计: 形状={frame.shape}, 平均值={avg_value:.2f}, 最小值={min_value}, 最大值={max_value}")
        
        # 如果图像太暗，尝试增强亮度
        if avg_value < 20:
            logger.warning(f"图像太暗 (平均值={avg_value:.2f})，应用亮度增强")
            # 简单的亮度增强
            frame = cv2.convertScaleAbs(frame, alpha=2.0, beta=10)
        
        # 将BGR转换为RGB (OpenCV使用BGR格式)
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 添加时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        cv2.putText(rgb_frame, timestamp, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 添加相机信息
        cv2.putText(rgb_frame, f"Camera ID: {self.camera_id}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # 转换为QImage
        h, w, c = rgb_frame.shape
        bytes_per_line = c * w
        q_image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
        
        # 显示图像
        pixmap = QPixmap.fromImage(q_image)
        self.image_label.setPixmap(pixmap)
        
    def capture_image(self):
        """截取当前画面"""
        if not self.camera_running or not self.camera:
            return
            
        # 获取一帧
        ret, frame = self.camera.read()
        
        if not ret or frame is None:
            logger.warning("截图失败：无法获取相机帧")
            return
            
        # 创建截图目录
        if not os.path.exists("screenshots"):
            os.makedirs("screenshots")
            
        # 保存图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"screenshots/camera_{self.camera_id}_{timestamp}.png"
        cv2.imwrite(filename, frame)
        
        logger.info(f"截图已保存: {filename}")
        self.status_label.setText(f"截图已保存: {filename}")
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 确保相机被释放
        self.stop_camera()
        event.accept()

def main():
    """主函数"""
    logger.info("相机显示测试程序启动")
    
    app = QApplication(sys.argv)
    window = CameraTestWindow()
    window.show()
    
    exit_code = app.exec_()
    
    logger.info(f"程序退出，代码: {exit_code}")
    return exit_code

if __name__ == "__main__":
    sys.exit(main()) 