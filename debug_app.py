#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WireVsion应用程序调试启动器
用于诊断段错误问题
"""

import sys
import os
import time
import traceback
from loguru import logger

# 配置日志
log_file = f"debug_app_{time.strftime('%Y-%m-%d_%H-%M-%S')}.log"
logger.add(log_file, rotation="10 MB", backtrace=True, diagnose=True)

logger.info("开始调试WireVsion应用启动过程...")

# 添加项目根目录到路径
root_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, root_dir)

def import_and_test_modules():
    """分阶段导入和测试模块，找出导致段错误的模块"""
    try:
        logger.info("阶段1: 导入PyQt5基础模块")
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QIcon, QPixmap
        from PyQt5.QtCore import Qt
        logger.info("PyQt5基础模块导入成功")
        
        # 创建应用实例
        app = QApplication(sys.argv)
        app.setApplicationName("WireVsion Debug")
        logger.info("QApplication创建成功")
        
        try:
            logger.info("阶段2: 测试应用设置模块")
            from wirevsion.utils.app_settings import AppSettings
            AppSettings.init_settings()
            logger.info("应用设置模块测试成功")
        except Exception as e:
            logger.error(f"应用设置模块测试失败: {e}")
            traceback.print_exc()
        
        try:
            logger.info("阶段3: 测试相机管理器")
            from wirevsion.camera.camera_manager import CameraManager
            camera_manager = CameraManager()
            logger.info("相机管理器创建成功")
            
            # 测试初始化
            init_result = camera_manager.connect_camera(0)
            logger.info(f"相机初始化{'成功' if init_result else '失败'}")
            
            # 测试获取帧
            logger.info("尝试获取一帧")
            success, frame = camera_manager.get_frame()
            logger.info(f"获取帧{'成功' if success else '失败'}")
            
            # 释放相机
            camera_manager.close_all_cameras()
            logger.info("相机资源已释放")
        except Exception as e:
            logger.error(f"相机管理器测试失败: {e}")
            traceback.print_exc()
        
        try:
            logger.info("阶段4: 测试UI组件")
            from wirevsion.ui.camera_utils import ImageDisplayManager
            logger.info("ImageDisplayManager导入成功")
        except Exception as e:
            logger.error(f"UI组件测试失败: {e}")
            traceback.print_exc()
        
        try:
            logger.info("阶段5: 测试性能监测")
            from wirevsion.utils.performance_monitor import PerformanceMonitor
            monitor = PerformanceMonitor.get_instance()
            logger.info("性能监测模块测试成功")
        except Exception as e:
            logger.error(f"性能监测模块测试失败: {e}")
            traceback.print_exc()
        
        try:
            logger.info("阶段6: 测试主窗口")
            from wirevsion.ui.main_window import MainWindow
            # 尝试创建主窗口但不显示
            main_window = MainWindow()
            logger.info("主窗口创建成功")
        except Exception as e:
            logger.error(f"主窗口测试失败: {e}")
            traceback.print_exc()
            
        logger.info("所有模块测试完成")
        return True
    except Exception as e:
        logger.error(f"模块导入测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """调试应用程序入口"""
    try:
        logger.info("开始调试应用程序")
        from PyQt5.QtWidgets import QApplication
        
        import_result = import_and_test_modules()
        
        if import_result:
            logger.info("所有模块导入测试成功，尝试创建QApplication")
            app = QApplication(sys.argv)
            
            logger.info("创建QApplication成功，调试完成")
            return 0
        else:
            logger.error("模块导入测试失败，无法继续调试")
            return 1
    except Exception as e:
        logger.error(f"调试过程出错: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    logger.info(f"调试应用程序退出，代码: {exit_code}")
    sys.exit(exit_code) 