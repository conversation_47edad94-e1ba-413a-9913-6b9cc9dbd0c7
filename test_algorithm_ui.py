#!/usr/bin/env python3
"""
算法UI测试程序

测试新完善的算法UI组件和模板系统
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
    QWidget, QPushButton, QListWidget, QListWidgetItem,
    QSplitter, QTextEdit, QLabel, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from wirevsion.ui.algorithm_ui_manager import AlgorithmUIManager
from wirevsion.algorithms.registry import AlgorithmRegistry


class AlgorithmUITestWindow(QMainWindow):
    """算法UI测试窗口"""

    def __init__(self):
        super().__init__()

        self.ui_manager = AlgorithmUIManager()
        self.algorithm_registry = AlgorithmRegistry()

        self.init_ui()
        self.load_algorithms()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("WireVision 算法UI测试")
        self.setGeometry(100, 100, 1200, 800)

        # 中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)

        # 分割器
        splitter = QSplitter(Qt.Horizontal)

        # 左侧：算法列表
        left_panel = self.create_algorithm_list()
        splitter.addWidget(left_panel)

        # 右侧：算法UI
        self.right_panel = QFrame()
        self.right_panel.setFrameStyle(QFrame.StyledPanel)
        self.right_layout = QVBoxLayout(self.right_panel)

        # 默认提示
        default_label = QLabel("请从左侧选择一个算法查看其UI")
        default_label.setAlignment(Qt.AlignCenter)
        default_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 16px;
                padding: 50px;
            }
        """)
        self.right_layout.addWidget(default_label)

        splitter.addWidget(self.right_panel)

        # 设置分割比例
        splitter.setSizes([300, 900])

        main_layout.addWidget(splitter)

        # 状态栏
        self.statusBar().showMessage("就绪")

    def create_algorithm_list(self) -> QWidget:
        """创建算法列表"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMaximumWidth(350)

        layout = QVBoxLayout(panel)

        # 标题
        title = QLabel("算法列表")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        layout.addWidget(title)

        # 算法列表
        self.algorithm_list = QListWidget()
        self.algorithm_list.itemClicked.connect(self.on_algorithm_selected)
        layout.addWidget(self.algorithm_list)

        # 刷新按钮
        refresh_btn = QPushButton("刷新算法列表")
        refresh_btn.clicked.connect(self.load_algorithms)
        layout.addWidget(refresh_btn)

        return panel

    def load_algorithms(self):
        """加载算法列表"""
        self.algorithm_list.clear()

        # 按类型分组显示算法
        algorithm_types = {
            "图像处理": [],
            "特征检测": [],
            "目标检测": [],
            "深度学习": [],
            "测量算法": [],
            "位置校正": []
        }

        # 获取所有算法（扁平化）
        all_algorithms = self.algorithm_registry.get_all_algorithms_flat()

        for full_algorithm_name, algorithm_class in all_algorithms.items():
            try:
                algorithm = algorithm_class()
                algorithm_type = algorithm.get_algorithm_type().value
                display_name = algorithm.get_display_name()

                # 分类
                if "image_processing" in algorithm_type:
                    algorithm_types["图像处理"].append((full_algorithm_name, display_name))
                elif "feature_detection" in algorithm_type:
                    algorithm_types["特征检测"].append((full_algorithm_name, display_name))
                elif "object_detection" in algorithm_type:
                    algorithm_types["目标检测"].append((full_algorithm_name, display_name))
                elif "deep_learning" in algorithm_type:
                    algorithm_types["深度学习"].append((full_algorithm_name, display_name))
                elif "measurement" in algorithm_type:
                    algorithm_types["测量算法"].append((full_algorithm_name, display_name))
                elif "position_correction" in algorithm_type:
                    algorithm_types["位置校正"].append((full_algorithm_name, display_name))

            except Exception as e:
                print(f"加载算法失败 {full_algorithm_name}: {e}")

        # 添加到列表
        for type_name, algorithms in algorithm_types.items():
            if algorithms:
                # 添加类型标题
                type_item = QListWidgetItem(f"📁 {type_name}")
                type_item.setFont(QFont("Arial", 10, QFont.Bold))
                type_item.setBackground(Qt.lightGray)
                type_item.setFlags(Qt.NoItemFlags)  # 不可选择
                self.algorithm_list.addItem(type_item)

                # 添加算法
                for algorithm_name, display_name in algorithms:
                    item = QListWidgetItem(f"  🔧 {display_name}")
                    item.setData(Qt.UserRole, algorithm_name)
                    self.algorithm_list.addItem(item)

        self.statusBar().showMessage(f"加载了 {len(all_algorithms)} 个算法")

    def on_algorithm_selected(self, item: QListWidgetItem):
        """算法选择事件"""
        algorithm_name = item.data(Qt.UserRole)
        if not algorithm_name:
            return

        try:
            # 清除右侧面板
            self.clear_right_panel()

            # 创建算法UI
            algorithm_ui = self.ui_manager.create_algorithm_ui(algorithm_name)

            if algorithm_ui:
                self.right_layout.addWidget(algorithm_ui)
                self.statusBar().showMessage(f"已加载算法UI: {algorithm_name}")
            else:
                error_label = QLabel(f"无法创建算法UI: {algorithm_name}")
                error_label.setStyleSheet("color: red; font-size: 14px;")
                error_label.setAlignment(Qt.AlignCenter)
                self.right_layout.addWidget(error_label)
                self.statusBar().showMessage(f"算法UI创建失败: {algorithm_name}")

        except Exception as e:
            error_label = QLabel(f"错误: {str(e)}")
            error_label.setStyleSheet("color: red; font-size: 14px;")
            error_label.setAlignment(Qt.AlignCenter)
            self.right_layout.addWidget(error_label)
            self.statusBar().showMessage(f"错误: {str(e)}")

    def clear_right_panel(self):
        """清除右侧面板"""
        while self.right_layout.count():
            child = self.right_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #f5f5f5;
        }
        QListWidget {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
        }
        QListWidget::item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        QListWidget::item:selected {
            background-color: #3498db;
            color: white;
        }
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)

    # 创建主窗口
    window = AlgorithmUITestWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
