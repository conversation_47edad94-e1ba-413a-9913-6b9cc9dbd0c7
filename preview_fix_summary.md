# WireVision 预览功能修复总结

## 🔍 问题描述

在WireVision的工作流配置界面中，当模块的输入源设置为"相机"或"上级结果"时，预览区域显示的是测试图像而不是实际的相机画面或上级结果。

## 🛠️ 修复方案

### 1. 新增输入数据获取功能

在 `modern_node_config_dialog.py` 中添加了 `_get_input_data()` 方法：

- **功能**：根据输入源类型获取实际数据
- **支持的输入源**：
  - 相机输入：通过工作流编辑器的相机管理器获取实时帧
  - 上级节点结果：从工作流编辑器的节点结果缓存中获取
  - 自动检测：根据节点类型和连接关系自动判断输入源

### 2. 增强相机帧获取

添加了 `_get_camera_frame()` 方法：

- **主要方案**：从工作流编辑器的相机管理器获取帧
- **备用方案**：直接使用OpenCV获取相机帧
- **格式处理**：确保数据连续性，正确转换BGR到RGB

### 3. 上级结果获取

添加了 `_get_upstream_result()` 方法：

- **数据源**：工作流编辑器的 `_node_results` 缓存
- **结果验证**：检查结果有效性和图像数据
- **格式兼容**：支持多种结果数据格式

### 4. 预览方法增强

修改了所有预览方法的签名，支持输入数据参数：

- `_preview_edge_detection(parameters, input_data=None)`
- `_preview_gaussian_blur(parameters, input_data=None)`
- `_preview_generic_algorithm(algorithm_name, parameters=None, input_data=None)`

### 5. RGB格式处理优化

在 `_show_preview_result()` 方法中：

- **智能格式检测**：通过 `_input_is_rgb` 标志判断输入格式
- **避免重复转换**：相机和上级结果通常已是RGB格式，避免BGR→RGB转换
- **兼容性保持**：保持对传统BGR格式的支持

## 🔧 技术实现细节

### 输入数据获取流程

```python
def _get_input_data(self):
    # 1. 检查输入源选择器
    if hasattr(self, 'input_selector'):
        current_input = self.input_selector.currentData()
        if current_input:
            # 处理上级节点结果或相机输入
    
    # 2. 检查节点类型
    if self.node.node_type == "input" and "camera" in self.node.node_id:
        return self._get_camera_frame()
    
    # 3. 检查工作流连接
    for connection in self.workflow_editor.connections:
        if connection.end_node.node_id == current_node_id:
            # 获取上级节点结果
```

### 相机帧获取优化

```python
def _get_camera_frame(self):
    # 主要方案：使用工作流编辑器的相机管理器
    if hasattr(self.workflow_editor, 'camera_manager'):
        success, frame = camera_manager.get_frame()
        if success:
            # 转换BGR到RGB，确保数据连续性
            return cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    
    # 备用方案：直接OpenCV获取
    cap = cv2.VideoCapture(0)
    # ...
```

### RGB格式智能处理

```python
def _show_preview_result(self, result_image, title="预览结果"):
    # 检查RGB标志
    if hasattr(self, '_input_is_rgb') and self._input_is_rgb:
        # 输入已是RGB格式，直接使用
        rgb_image = result_image.copy()
    else:
        # 默认BGR格式，转换为RGB
        rgb_image = cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB)
```

## ✅ 修复效果

### 预期改进

1. **相机输入预览**：显示实际的相机画面而非测试图像
2. **上级结果预览**：显示上级节点的实际处理结果
3. **格式正确性**：避免颜色通道错误（红蓝颠倒）
4. **实时性**：预览内容与实际工作流数据同步

### 兼容性保证

- 保持对现有测试图像预览的支持
- 向后兼容原有的预览功能
- 不影响其他模块的正常运行

## 🧪 测试验证

创建了 `test_preview_fix.py` 测试脚本：

- **相机输入测试**：验证相机预览功能
- **上级结果测试**：验证上级节点结果预览
- **模拟数据**：提供测试用的模拟图像数据

## 📝 使用说明

### 对于用户

1. **配置相机输入节点**：预览将显示实际相机画面
2. **配置处理节点**：如果有上级连接，预览将显示上级结果
3. **无输入源时**：仍然显示测试图像作为演示

### 对于开发者

1. **扩展输入源**：可以在 `_get_input_data()` 中添加新的输入源类型
2. **自定义预览**：新的算法预览方法应支持 `input_data` 参数
3. **格式处理**：注意设置 `_input_is_rgb` 标志以正确处理图像格式

## 🔮 后续优化建议

1. **缓存机制**：为输入数据添加缓存，避免重复获取
2. **异步处理**：大图像的预览处理可以异步化
3. **错误恢复**：增强输入源失败时的错误处理和恢复机制
4. **性能优化**：对高分辨率图像进行适当的缩放处理

---

**修复完成时间**：2024年12月19日  
**影响范围**：WireVision工作流配置界面预览功能  
**测试状态**：待验证
