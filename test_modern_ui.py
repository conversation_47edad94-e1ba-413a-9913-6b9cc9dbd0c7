#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
现代化UI测试脚本

测试和展示新的现代化无边框UI
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from loguru import logger

# 配置日志
logger.add("logs/modern_ui_test.log", rotation="10 MB")


def test_modern_ui():
    """测试现代化UI"""
    try:
        # 导入主应用程序
        from wirevsion.ui.modern_main_application import ModernMainApplication
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 设置应用属性
        app.setApplicationName("WireVision Modern UI Test")
        app.setOrganizationName("WireVision")
        
        # 启用高DPI支持
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 设置全局字体
        font = QFont("Microsoft YaHei", 10)
        app.setFont(font)
        
        # 创建并显示主窗口
        logger.info("启动现代化UI...")
        window = ModernMainApplication()
        window.show()
        
        # 运行应用
        logger.info("现代化UI运行中...")
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"运行现代化UI失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_individual_components():
    """测试单个组件"""
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout
    
    app = QApplication(sys.argv)
    
    # 测试窗口
    test_window = QWidget()
    test_window.setWindowTitle("组件测试")
    test_window.resize(800, 600)
    
    layout = QVBoxLayout(test_window)
    
    # 测试现代化组件
    from wirevsion.ui.modern_components import (
        ModernCard, ModernButton, ModernInput,
        ModernProgressBar, ModernSwitch
    )
    
    # 卡片组件
    card = ModernCard("测试卡片", "这是一个现代化卡片组件")
    
    # 添加内容
    content_layout = QVBoxLayout()
    
    # 按钮
    btn_layout = QHBoxLayout()
    btn_layout.addWidget(ModernButton("主要按钮", ModernButton.PRIMARY))
    btn_layout.addWidget(ModernButton("次要按钮", ModernButton.SECONDARY))
    btn_layout.addWidget(ModernButton("成功按钮", ModernButton.SUCCESS))
    btn_layout.addWidget(ModernButton("警告按钮", ModernButton.WARNING))
    btn_layout.addWidget(ModernButton("危险按钮", ModernButton.DANGER))
    content_layout.addLayout(btn_layout)
    
    # 输入框
    input_widget = ModernInput("请输入内容", "用户名")
    content_layout.addWidget(input_widget)
    
    # 开关
    switch = ModernSwitch("启用功能")
    content_layout.addWidget(switch)
    
    # 进度条
    progress = ModernProgressBar()
    progress.setValue(65)
    content_layout.addWidget(progress)
    
    card.content_layout.addLayout(content_layout)
    layout.addWidget(card)
    
    test_window.show()
    sys.exit(app.exec_())


def test_sidebar():
    """测试优化后的侧边栏"""
    from PyQt5.QtWidgets import QMainWindow, QWidget, QHBoxLayout, QLabel
    from PyQt5.QtGui import QIcon
    from wirevsion.ui.modern_sidebar import ModernSidebar
    
    app = QApplication(sys.argv)
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("WireVsion - 智能视觉检测系统")
    main_window.resize(1200, 800)
    main_window.setStyleSheet("background-color: #1e1e1e;")
    
    # 创建中央部件
    central_widget = QWidget()
    main_layout = QHBoxLayout(central_widget)
    main_layout.setContentsMargins(0, 0, 0, 0)
    main_layout.setSpacing(0)
    
    # 创建侧边栏
    sidebar = ModernSidebar()
    
    # 添加Logo
    sidebar.logo_label.setPixmap(QIcon("resources/icons/logo.svg").pixmap(26, 26))
    
    # 添加导航组和项目
    main_group = sidebar.add_navigation_group("main", "主要功能")
    main_group.add_item(sidebar.add_navigation_item("main", "dashboard", "仪表板", "resources/icons/dashboard.svg"))
    main_group.add_item(sidebar.add_navigation_item("main", "workflow", "工作流编辑", "resources/icons/workflow.svg"))
    
    tools_group = sidebar.add_navigation_group("tools", "工具")
    tools_group.add_item(sidebar.add_navigation_item("tools", "tools", "工具", "resources/icons/tools.svg"))
    
    library_group = sidebar.add_navigation_group("library", "算法库")
    library_group.add_item(sidebar.add_navigation_item("library", "algorithm", "算法库", "resources/icons/algorithm.svg"))
    
    analysis_group = sidebar.add_navigation_group("analysis", "结果分析")
    analysis_group.add_item(sidebar.add_navigation_item("analysis", "results", "结果分析", "resources/icons/results.svg"))
    
    # 设置设置按钮图标
    sidebar.settings_btn.setIcon(QIcon("resources/icons/settings.svg"))
    
    # 设置默认选中项
    sidebar.set_current_item("dashboard")
    
    # 创建内容区域
    content_widget = QWidget()
    content_widget.setStyleSheet("background-color: #1e1e1e; padding: 20px;")
    content_layout = QHBoxLayout(content_widget)
    
    # 添加内容标签
    content_label = QLabel("选择左侧菜单查看功能")
    content_label.setStyleSheet("color: #e0e0e0; font-size: 24px; font-weight: bold;")
    content_label.setAlignment(Qt.AlignCenter)
    content_layout.addWidget(content_label)
    
    # 连接信号
    def on_nav_changed(item_id):
        content_label.setText(f"您选择了: {item_id}")
    
    sidebar.navigation_changed.connect(on_nav_changed)
    
    # 添加组件到主布局
    main_layout.addWidget(sidebar)
    main_layout.addWidget(content_widget, 1)
    
    main_window.setCentralWidget(central_widget)
    main_window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="现代化UI测试")
    parser.add_argument("--components", action="store_true", 
                       help="测试单个组件")
    parser.add_argument("--sidebar", action="store_true",
                       help="测试侧边栏")
    
    args = parser.parse_args()
    
    if args.components:
        test_individual_components()
    elif args.sidebar:
        test_sidebar()
    else:
        test_modern_ui() 