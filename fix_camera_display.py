#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
相机显示修复测试程序

用于测试相机显示和节点点击功能的修复效果
"""

import sys
import os
import traceback
import numpy as np
import cv2
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
    QLabel, QPushButton, QComboBox, QMessageBox, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QImage, QPixmap
from loguru import logger

# 配置日志
logger.remove()  # 移除默认处理程序
logger.add(sys.stderr, level="INFO")  # 添加标准错误输出处理程序
logger.add("camera_display_fix.log", level="DEBUG")  # 添加文件日志

# 导入必要的类
try:
    from wirevsion.ui.camera_utils import CameraManager, ImageDisplayManager, ImageProcessor
except ImportError:
    logger.error("无法导入wirevsion模块，请确保已正确安装")
    sys.exit(1)


class TestImageProvider:
    """测试图像提供程序，用于生成各种测试图像"""
    
    @staticmethod
    def create_test_image(width=640, height=480, text="测试图像"):
        """创建带有文本的测试图像
        
        Args:
            width: 图像宽度
            height: 图像高度
            text: 显示的文本
            
        Returns:
            BGR格式的图像
        """
        # 创建黑色背景
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 添加彩色网格
        for x in range(0, width, 50):
            cv2.line(image, (x, 0), (x, height), (0, 0, 50), 1)
        for y in range(0, height, 50):
            cv2.line(image, (0, y), (width, y), (0, 0, 50), 1)
        
        # 在中心绘制一个彩色矩形
        center_x, center_y = width // 2, height // 2
        rect_width, rect_height = 200, 150
        
        # 左上角和右下角坐标
        pt1 = (center_x - rect_width//2, center_y - rect_height//2)
        pt2 = (center_x + rect_width//2, center_y + rect_height//2)
        
        # 绘制矩形
        cv2.rectangle(image, pt1, pt2, (0, 255, 0), 2)
        
        # 添加文本
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(image, text, (center_x - 100, center_y), font, 1, (255, 255, 255), 2)
        
        # 添加时间戳
        from PyQt6.QtCore import QDateTime
        current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        cv2.putText(image, current_time, (10, height - 20), font, 0.6, (200, 200, 200), 1)
        
        # 注意：返回的是BGR格式图像
        return image
    
    @staticmethod
    def apply_gaussian_blur(image, kernel_size=15):
        """应用高斯模糊
        
        Args:
            image: 输入图像
            kernel_size: 高斯核大小
            
        Returns:
            处理后的图像
        """
        try:
            # 应用高斯模糊
            blurred = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
            
            # 添加处理标记
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(blurred, "高斯模糊处理", (20, 30), font, 1, (0, 255, 255), 2)
            
            return blurred
        except Exception as e:
            logger.error(f"应用高斯模糊出错: {e}")
            return image
    
    @staticmethod
    def apply_canny_edge(image, threshold1=50, threshold2=150):
        """应用Canny边缘检测
        
        Args:
            image: 输入图像
            threshold1: 低阈值
            threshold2: 高阈值
            
        Returns:
            处理后的图像
        """
        try:
            # 将BGR转换为灰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 应用Canny边缘检测
            edges = cv2.Canny(gray, threshold1, threshold2)
            
            # 转换回BGR
            edges_bgr = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
            
            # 添加处理标记
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(edges_bgr, "Canny边缘检测", (20, 30), font, 1, (0, 255, 255), 2)
            
            return edges_bgr
        except Exception as e:
            logger.error(f"应用Canny边缘检测出错: {e}")
            return image
    
    @staticmethod
    def apply_threshold(image, threshold=127):
        """应用阈值处理
        
        Args:
            image: 输入图像
            threshold: 阈值
            
        Returns:
            处理后的图像
        """
        try:
            # 将BGR转换为灰度
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 应用阈值处理
            _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
            
            # 转换回BGR
            binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
            
            # 添加处理标记
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(binary_bgr, "阈值处理", (20, 30), font, 1, (0, 255, 255), 2)
            
            return binary_bgr
        except Exception as e:
            logger.error(f"应用阈值处理出错: {e}")
            return image


class ImageDisplayFixApp(QMainWindow):
    """相机显示修复测试应用程序"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("相机显示修复测试")
        self.resize(800, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 控制区域
        control_layout = QHBoxLayout()
        
        # 初始化相机管理器
        self.camera_manager = CameraManager()
        
        # 初始化模拟节点处理结果
        self.node_results = {}
        
        # 添加按钮：获取相机帧
        self.camera_btn = QPushButton("获取相机帧")
        self.camera_btn.clicked.connect(self.capture_camera_frame)
        control_layout.addWidget(self.camera_btn)
        
        # 添加按钮：创建测试图像
        self.test_btn = QPushButton("创建测试图像")
        self.test_btn.clicked.connect(self.create_test_image)
        control_layout.addWidget(self.test_btn)
        
        # 添加按钮：生成测试结果
        self.generate_btn = QPushButton("生成节点结果")
        self.generate_btn.clicked.connect(self.generate_test_results)
        control_layout.addWidget(self.generate_btn)
        
        # 下拉菜单：选择节点
        self.node_selector = QComboBox()
        self.node_selector.addItem("原始图像")
        self.node_selector.addItem("高斯模糊")
        self.node_selector.addItem("Canny边缘")
        self.node_selector.addItem("阈值处理")
        self.node_selector.currentIndexChanged.connect(self.on_node_selected)
        control_layout.addWidget(self.node_selector)
        
        # 持续运行按钮
        self.run_btn = QPushButton("持续运行")
        self.run_btn.clicked.connect(self.toggle_continuous_run)
        control_layout.addWidget(self.run_btn)
        
        # 添加控制区域
        layout.addLayout(control_layout)
        
        # 图像显示区域
        self.image_scroll_area = QScrollArea()
        self.image_scroll_area.setWidgetResizable(True)
        self.image_container = QWidget()
        image_container_layout = QVBoxLayout(self.image_container)
        
        # 图像标签
        self.image_view = QLabel()
        self.image_view.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_view.setText("等待图像...")
        self.image_view.setMinimumSize(640, 480)
        self.image_view.setStyleSheet("background-color: #222; color: white;")
        image_container_layout.addWidget(self.image_view)
        
        self.image_scroll_area.setWidget(self.image_container)
        layout.addWidget(self.image_scroll_area, 1)  # 1表示拉伸
        
        # 初始化图像显示管理器
        self.image_display_manager = ImageDisplayManager(
            image_view=self.image_view,
            image_container=self.image_container,
            image_scroll_area=self.image_scroll_area
        )
        
        # 状态区域
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        # 添加截图按钮
        screenshot_btn = QPushButton("保存截图")
        screenshot_btn.clicked.connect(self.save_screenshot)
        status_layout.addWidget(screenshot_btn)
        
        layout.addLayout(status_layout)
        
        # 设置定时器相关变量
        self.continuous_run_active = False
        self.continuous_run_timer = None
        
        logger.info("相机显示修复测试应用初始化完成")
    
    def generate_test_results(self):
        """生成测试节点结果"""
        try:
            logger.info("生成测试节点结果")
            
            # 检查是否有原始图像
            if not hasattr(self, 'original_image') or self.original_image is None:
                # 创建测试图像
                self.original_image = TestImageProvider.create_test_image(
                    text="原始测试图像"
                )
                self.status_label.setText("已创建测试图像")
            
            # 存储原始图像结果
            self.node_results["原始图像"] = {
                "image": self.original_image.copy(),
                "node_id": "camera_input_1"
            }
            
            # 应用各种处理并存储结果
            # 高斯模糊
            blurred = TestImageProvider.apply_gaussian_blur(self.original_image)
            self.node_results["高斯模糊"] = {
                "image": blurred,
                "node_id": "gaussian_blur_1"
            }
            
            # Canny边缘检测
            edges = TestImageProvider.apply_canny_edge(self.original_image)
            self.node_results["Canny边缘"] = {
                "image": edges,
                "node_id": "canny_edge_1"
            }
            
            # 阈值处理
            binary = TestImageProvider.apply_threshold(self.original_image)
            self.node_results["阈值处理"] = {
                "image": binary,
                "node_id": "threshold_1"
            }
            
            # 显示原始图像
            self.display_node_result("原始图像")
            
            logger.info("测试节点结果生成完成")
            self.status_label.setText("节点结果生成完成")
            
        except Exception as e:
            logger.error(f"生成测试节点结果出错: {e}")
            logger.error(traceback.format_exc())
            self.status_label.setText(f"错误: {str(e)}")
    
    def display_node_result(self, node_id):
        """显示指定节点的处理结果
        
        Args:
            node_id: 节点ID或名称
        """
        try:
            logger.info(f"显示节点 {node_id} 的处理结果")
            
            if node_id in self.node_results:
                result = self.node_results[node_id]
                
                if "image" in result and result["image"] is not None:
                    # 获取BGR格式图像
                    bgr_image = result["image"]
                    
                    # 转换为RGB用于显示 (直接传入BGR，让ImageDisplayManager处理转换)
                    self.image_display_manager.update_display(bgr_image)
                    
                    self.status_label.setText(f"显示节点 {node_id} 的结果")
                    logger.info(f"节点 {node_id} 的结果已显示")
                else:
                    logger.warning(f"节点 {node_id} 的结果中不包含图像")
                    self.status_label.setText(f"警告: 节点 {node_id} 无图像结果")
            else:
                logger.warning(f"找不到节点 {node_id} 的结果")
                self.status_label.setText(f"警告: 找不到节点 {node_id} 的结果")
                
        except Exception as e:
            logger.error(f"显示节点结果出错: {e}")
            logger.error(traceback.format_exc())
            self.status_label.setText(f"错误: {str(e)}")
    
    def on_node_selected(self, index):
        """处理节点选择变更
        
        Args:
            index: 选中的索引
        """
        try:
            node_id = self.node_selector.currentText()
            logger.info(f"选择节点: {node_id}")
            
            self.display_node_result(node_id)
            
        except Exception as e:
            logger.error(f"处理节点选择出错: {e}")
            logger.error(traceback.format_exc())
    
    def capture_camera_frame(self):
        """获取相机帧并显示"""
        try:
            logger.info("获取相机帧")
            
            # 获取相机帧
            frame = self.camera_manager.get_frame()
            
            if frame is not None:
                # 保存为原始图像
                self.original_image = frame.copy()
                
                # 更新原始图像结果
                self.node_results["原始图像"] = {
                    "image": self.original_image.copy(),
                    "node_id": "camera_input_1"
                }
                
                # 显示图像 (直接传入BGR，让ImageDisplayManager处理转换)
                self.image_display_manager.update_display(frame)
                
                self.status_label.setText("已获取相机帧")
                logger.info("相机帧已获取和显示")
            else:
                logger.warning("无法获取相机帧")
                self.status_label.setText("警告: 无法获取相机帧")
                
        except Exception as e:
            logger.error(f"获取相机帧出错: {e}")
            logger.error(traceback.format_exc())
            self.status_label.setText(f"错误: {str(e)}")
    
    def create_test_image(self):
        """创建测试图像并显示"""
        try:
            logger.info("创建测试图像")
            
            # 创建测试图像
            image = TestImageProvider.create_test_image(
                text="测试图像"
            )
            
            # 保存为原始图像
            self.original_image = image.copy()
            
            # 更新原始图像结果
            self.node_results["原始图像"] = {
                "image": self.original_image.copy(),
                "node_id": "camera_input_1"
            }
            
            # 显示图像 (直接传入BGR，让ImageDisplayManager处理转换)
            self.image_display_manager.update_display(image)
            
            self.status_label.setText("已创建测试图像")
            logger.info("测试图像已创建和显示")
            
        except Exception as e:
            logger.error(f"创建测试图像出错: {e}")
            logger.error(traceback.format_exc())
            self.status_label.setText(f"错误: {str(e)}")
    
    def toggle_continuous_run(self):
        """切换持续运行状态"""
        try:
            if self.continuous_run_active:
                # 停止持续运行
                if self.continuous_run_timer is not None:
                    self.killTimer(self.continuous_run_timer)
                    self.continuous_run_timer = None
                
                self.continuous_run_active = False
                self.run_btn.setText("持续运行")
                self.status_label.setText("持续运行已停止")
                logger.info("持续运行已停止")
            else:
                # 开始持续运行
                self.continuous_run_active = True
                self.continuous_run_timer = self.startTimer(1000)  # 1秒间隔
                
                self.run_btn.setText("停止运行")
                self.status_label.setText("持续运行中...")
                logger.info("开始持续运行，刷新率=1秒/次")
                
                # 立即执行一次
                self.capture_camera_frame()
                
        except Exception as e:
            logger.error(f"切换持续运行状态出错: {e}")
            logger.error(traceback.format_exc())
            self.status_label.setText(f"错误: {str(e)}")
    
    def timerEvent(self, event):
        """定时器事件处理"""
        if hasattr(self, 'continuous_run_timer') and event.timerId() == self.continuous_run_timer:
            try:
                # 获取相机帧
                self.capture_camera_frame()
                
                # 如果已有原始图像，自动重新生成节点结果
                if hasattr(self, 'original_image') and self.original_image is not None:
                    self.generate_test_results()
                    
                    # 显示当前选中的节点结果
                    node_id = self.node_selector.currentText()
                    self.display_node_result(node_id)
                    
            except Exception as e:
                logger.error(f"定时器处理出错: {e}")
                logger.error(traceback.format_exc())
    
    def save_screenshot(self):
        """保存当前显示的图像为截图"""
        try:
            if self.image_view.pixmap() is not None:
                # 获取当前显示的图像
                pixmap = self.image_view.pixmap()
                
                # 保存图像
                filename = "camera_display_success.png"
                pixmap.save(filename)
                
                self.status_label.setText(f"截图已保存: {filename}")
                logger.info(f"截图已保存: {filename}")
                
                # 显示消息框
                QMessageBox.information(
                    self, "保存成功", f"截图已保存为: {filename}"
                )
            else:
                logger.warning("没有图像可以保存")
                self.status_label.setText("警告: 没有图像可以保存")
                
        except Exception as e:
            logger.error(f"保存截图出错: {e}")
            logger.error(traceback.format_exc())
            self.status_label.setText(f"错误: {str(e)}")


def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)
        window = ImageDisplayFixApp()
        window.show()
        
        # 添加一个定时器延迟截图，给应用足够的时间加载和显示
        def save_screenshot():
            if hasattr(window, 'save_screenshot'):
                window.generate_test_results()  # 先生成一些测试结果
                QTimer.singleShot(500, window.save_screenshot)  # 0.5秒后截图
        
        # 3秒后自动生成测试结果并截图
        QTimer.singleShot(3000, save_screenshot)
        
        sys.exit(app.exec_())
    except Exception as e:
        logger.error(f"应用程序出错: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main() 